---
description: 
globs: 
alwaysApply: false
---
# 编码标准和最佳实践

本项目遵循Vue 3最佳实践，使用Composition API和TypeScript。

## 一般原则

- 使用Vue 3 Composition API与`<script setup>`语法
- 利用TypeScript进行类型检查和提高代码质量
- 遵循基于组件的架构
- 使用Tailwind CSS实现响应式设计

## 组件结构

组件应遵循以下结构：
```vue
<script setup lang="ts">
// 导入
import { ref, computed, onMounted } from 'vue'
import type { YourType } from '@/types'

// Props和emits
const props = defineProps<{
  property: string
}>()
const emit = defineEmits<{
  'update': [payload: any]
}>()

// 响应式状态
const state = ref('')

// 计算属性
const derivedValue = computed(() => {
  return state.value.toUpperCase()
})

// 方法
const handleClick = () => {
  emit('update', state.value)
}

// 生命周期钩子
onMounted(() => {
  // 初始化逻辑
})
</script>

<template>
  <div>
    <!-- 你的模板内容 -->
  </div>
</template>
```

## 排版标准

项目中定义了预设的字体样式，以保持排版一致性，详见[README.md](mdc:README.md)：

- 使用适当的文本类进行一致的排版
- 对于中文文本，使用`text-title`、`text-content`和`text-action`类
- 对于英文文本，使用`text-title-en`、`text-content-en`和`text-action-en`类

## 状态管理

- 使用Pinia进行状态管理
- 在[src/store](mdc:src/store)目录中创建模块化的stores
- 利用`defineStore` API并遵循组合模式

## API通信

- API调用应集中在[src/api](mdc:src/api)目录中
- 使用Alova或Axios进行HTTP请求

## CSS指南

- 使用Tailwind CSS进行样式设计
- 避免内联样式
- 使用Tailwind提供的utility-first方法
- 对于组件特定的样式，使用scoped样式或CSS模块

## 错误处理

- 对API调用实现适当的错误处理
- 在适当的地方使用try/catch块
- 提供用户友好的错误消息

## 性能考虑

- 适当时懒加载路由和组件
- 对不需要深度响应性的大型对象使用`shallowRef`
- 使用`computed`和适当的依赖关系优化昂贵的计算


