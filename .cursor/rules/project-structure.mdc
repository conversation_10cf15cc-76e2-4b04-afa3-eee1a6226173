---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

这是一个基于Vue 3 + TypeScript + Vite的项目，具有良好组织的结构，有助于开发。

## 关键文件和目录

- [src/main.ts](mdc:src/main.ts) - Vue应用程序的主入口点
- [src/App.vue](mdc:src/App.vue) - 根Vue组件
- [vite.config.ts](mdc:vite.config.ts) - Vite配置

## 核心目录

- [src/components/](mdc:src/components) - 可复用的Vue组件
- [src/views/](mdc:src/views) - 页面组件（路由视图）
- [src/router/](mdc:src/router) - Vue Router配置
- [src/store/](mdc:src/store) - Pinia状态管理模块
- [src/api/](mdc:src/api) - API客户端和端点
- [src/utils/](mdc:src/utils) - 工具函数
- [src/hooks/](mdc:src/hooks) - 自定义Vue组合式函数
- [src/assets/](mdc:src/assets) - 静态资源，如图片和全局CSS
- [src/types/](mdc:src/types) - TypeScript类型定义
- [src/layouts/](mdc:src/layouts) - 布局组件
- [src/directives/](mdc:src/directives) - 自定义Vue指令
- [src/locales/](mdc:src/locales) - 国际化文件

## 配置文件

- [package.json](mdc:package.json) - 项目依赖和脚本
- [tsconfig.json](mdc:tsconfig.json) - TypeScript配置
- [tailwind.config.js](mdc:tailwind.config.js) - Tailwind CSS配置
- [components.json](mdc:components.json) - Shadcn UI组件配置


