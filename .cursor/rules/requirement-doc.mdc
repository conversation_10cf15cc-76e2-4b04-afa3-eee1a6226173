---
description: 
globs: 
alwaysApply: false
---
# 需求文档生成规则
## 文档格式根据用户输入，生成的需求文档应包含以下部分：
1. **项目概述**
   - 项目背景
   - 项目目标
   - 目标用户

2. **功能需求**
 - 核心功能：详细列出产品的核心功能模块，每个功能模块需包含功能描述、操作流程、输入输出内容等。例如，若为社交类产品，核心功能可能包括用户注册登录、好友添加、消息发送等，需说明用户注册的步骤、好友添加的方式、消息发送的格式及接收反馈等。​
 - 次要功能：列出产品的次要功能，同样进行功能描述，说明其与核心功能的关联以及对产品的辅助作用。​
 - 功能优先级：对所有功能进行优先级排序，可采用高、中、低三级划分，并说明排序依据。​

3. **用户场景与流程​**
 - 描述典型的用户使用场景，每个场景需包含用户角色、场景描述、用户在该场景下的操作步骤以及期望达成的结果。​
 - 绘制用户操作流程图，清晰展示用户从进入产品到完成某个任务的整个流程，包括各个环节的分支和判断条件。

4. **非功能需求**
 - 性能需求：明确产品的性能指标，如响应时间（页面加载时间、操作响应时间等需具体到毫秒或秒）、并发用户数、数据处理速度等。​
 - 兼容性需求：说明产品需要兼容的操作系统（如 Windows 的不同版本、iOS、Android 的不同版本等）、浏览器（如 Chrome、Firefox、Safari 等）以及硬件设备（如手机型号、电脑配置等）。​
 - 安全性需求：阐述产品在数据安全（如用户信息加密、数据备份与恢复等）、访问安全（如权限管理、登录验证等）方面的要求。​
 - 可用性需求：说明产品的易用性要求，如用户学习使用产品的时间、操作失误率等。

5. **Product Backlog(SCRUM)**
 - 史诗（Epic）​
 **定义**：史诗是大型的、模糊的、高层次的产品需求，通常涵盖多个相关的用户故事，跨越多个 Sprint 才能完成，体现产品的核心业务方向或重大功能模块。​
 **编写要求**：
  a). ​需围绕产品的核心目标和战略方向，描述 “是什么” 级别的需求，不涉及具体实现细节。
  b). 应具有一定的概括性和包容性，能清晰反映其涵盖的业务范围，让团队明确该史诗的价值和重要性。​
  c). 需注明该史诗与产品目标的关联，说明其对实现产品愿景的贡献。​
  **与用户故事的关联**：一个史诗可分解为多个用户故事，史诗是用户故事的集合体。在编写时需明确史诗未来将分解为哪些方向的用户故事，为后续细化提供指引。​
 - 用户故事（User Story）​
  **定义**：用户故事是史诗分解后的具体需求，聚焦于用户的具体场景和需求，可在一个 Sprint 内完成，是 Product Backlog 的核心组成部分。​
  **编写要求**：​
  a). 严格遵循 “作为 [用户角色]，我希望 [完成某功能 / 操作]，以便 [实现某价值 / 达成某目标]” 的结构，如 “作为电商平台用户，我希望能根据浏览历史收到商品推荐，以便快速找到心仪商品”。​
  b). 具备明确的验收标准，可量化、可验证，如上述推荐功能的验收标准可包括 “推荐商品与浏览历史的匹配度不低于 80%”“每页推荐商品数量为 20 个”。​
  c). 进行工作量估算，采用故事点或理想人天 / 人时，由开发团队结合技术难度等因素确定。​
  d). 需明确归属哪个史诗，与所属史诗形成清晰的层级关系。​
  **与任务的关联**：
  一个用户故事可分解为多个任务，用户故事是任务的汇总，任务是完成用户故事的具体步骤。​
 - 任务（Task）​
  **定义**：任务是用户故事分解后的具体行动项，是开发团队为完成用户故事所需要执行的具体工作步骤，通常由团队成员独立完成，耗时较短（一般在几小时到 1-2 天内）。​
  **编写要求**：​
  a).描述具体的操作内容，如 “设计推荐算法的核心逻辑”“开发商品推荐列表的前端页面”“编写推荐数据的接口测试用例”。​
  b).需具有可执行性，明确任务的负责人（在 Sprint 规划时确定）、预计完成时间，确保团队成员清楚自己需要做什么。​
  c).与用户故事紧密关联，每个任务都应是为了实现对应用户故事的某个环节或满足某个验收标准。​
  **与上下层级的关联**：任务是用户故事的细化，所有任务完成后，对应的用户故事应能满足验收标准；多个用户故事的完成推动所属史诗的逐步实现。

## 标准模板用户提供信息时，请按照以下结构生成文档：
```markdown
# 项目名称：[项目名]
## 1. 项目概述
### 1.1 项目背景
    [根据用户输入填写]
### 1.2 项目目标
    [根据用户输入填写]
### 1.3 目标用户
    [根据用户输入填写]
## 2. 功能需求
### 2.1 核心功能
  - [功能1]
  - [功能2]
  - [功能3]
### 2.2 功能详细描述
#### 2.2.1 [功能1]
    [详细描述]
#### 2.2.2 [功能2]
    [详细描述]
### 2.3 用户流程
    [描述用户如何使用系统完成主要任务]
## 3. 用户场景与流程​
### 3.1 [场景1]
    [包含用户角色、场景描述、用户在该场景下的操作步骤以及期望达成的结果]
### 3.2 [场景2]
    [包含用户角色、场景描述、用户在该场景下的操作步骤以及期望达成的结果]
## 4. 非功能需求
### 4.1 性能需求
    [响应时间、并发数等]
### 4.2 安全要求
    [数据安全、身份验证等]
### 4.3 可用性要求
    [易用性、可访问性等]
## 5. Product Backlog：
### 5.1 史诗 [史诗1]
    [史诗故事描述]
#### 5.1.1 用户故事 [史诗1故事1]
    [用户故事描述]
##### 5.1.1.1 任务 [史诗1故事1任务1]
    [任务描述]
##### 5.1.1.2 任务 [史诗1故事1任务2]
    [任务描述]
#### 5.1.2 用户故事 [史诗1故事2]
    [用户故事描述]
##### 5.1.2.1 任务 [史诗1故事2任务1]
    [任务描述]
##### 5.1.2.2 任务 [史诗1故事2任务2]
    [任务描述]
…………
### 5.N 史诗 [史诗N]
    [史诗故事描述]
#### 5.N.1 用户故事 [史诗N故事1]
    [用户故事描述]
##### 5.N.1.1 任务 [史诗N故事1任务1]
    [任务描述]
##### 5.N.1.2 任务 [史诗N故事1任务2]
    [任务描述]
#### 5.N.2 用户故事 [史诗N故事2]
    [用户故事描述]
##### 5.N.2.1 任务 [史诗N故事2任务1]
    [任务描述]
##### 5.N.2.2 任务 [史诗N故事2任务2]
    [任务描述]
```