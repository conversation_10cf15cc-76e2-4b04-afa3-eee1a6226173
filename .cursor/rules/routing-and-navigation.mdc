---
description: 
globs: 
alwaysApply: false
---
# 路由和导航

本项目使用Vue Router管理不同视图之间的导航。

## 路由配置

主要路由配置位于[src/router/index.ts](mdc:src/router/index.ts)。

- 路由定义为具有以下属性的对象：
  - `path`：URL路径
  - `name`：用于编程式导航的路由名称
  - `component`：要渲染的Vue组件
  - `meta`：额外的元数据（认证要求等）
  - `children`：嵌套路由

## 导航守卫

- 全局导航守卫在路由配置中定义
- 用于：
  - 身份验证检查
  - 权限验证
  - 数据分析跟踪
  - 加载状态管理

## 路由结构

- 视图（页面组件）存储在[src/views/](mdc:src/views)中
- 每个视图通常对应一个路由
- 嵌套路由应在视图目录中使用嵌套文件夹

## 布局

- 布局组件存储在[src/layouts/](mdc:src/layouts)中
- 它们提供一致的页面结构（页眉、页脚、侧边栏）
- 布局可以：
  - 在App.vue中全局应用
  - 使用嵌套路由和视图组件按路由应用

## 编程式导航

在代码中进行导航，使用以下方法：

```vue
<script setup>
import { useRouter, useRoute } from 'vue-router'

// 访问路由器
const router = useRouter()
const route = useRoute()

// 导航到特定路由
const goToHome = () => {
  router.push('/')
}

// 带参数导航
const goToUser = (userId) => {
  router.push({ name: 'user', params: { id: userId } })
}

// 带查询参数导航
const goToSearch = (query) => {
  router.push({ path: '/search', query: { q: query } })
}
</script>
```

## 路由元字段

使用路由元字段获取有关路由的额外信息：

```typescript
const routes = [
  {
    path: '/admin',
    component: AdminPanel,
    meta: { 
      requiresAuth: true,
      roles: ['admin']
    }
  }
]
```

## 进度指示器

本项目使用NProgress进行路由变更指示：

- 导航期间显示进度条
- 在路由设置中配置

