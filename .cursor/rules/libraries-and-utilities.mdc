---
description: 
globs: 
alwaysApply: false
---
# 库和工具

本项目使用多个关键库和工具来增强开发体验和用户体验。

## UI组件和样式

- **Tailwind CSS** - 实用优先的CSS框架
  - 配置在[tailwind.config.js](mdc:tailwind.config.js)中
  - 使用工具类进行样式设计

- **Radix Vue** - Vue的无样式、可访问组件
  - 对构建可访问的UI组件很重要
  - 与Shadcn UI一起使用

- **Shadcn UI** - 使用Tailwind CSS和Radix的组件集合
  - 配置在[components.json](mdc:components.json)中
  - 提供一致的设计系统

- **Lucide Icons** - 图标库
  - 使用`<LucideIcon />`组件

- **Iconify** - 统一图标框架
  - 与`@iconify/vue`一起使用以获取额外图标

## 状态管理

- **Pinia** - Vue的状态管理库
  - Store文件应组织在[src/store/](mdc:src/store)中
  - 使用`defineStore`模式

- **Pinia Persisted State** - 用于持久化存储
  - 用于在会话之间保持状态

## 表单处理

- **VeeValidate** - 表单验证库
  - 与Zod配对进行模式验证
  - 使用`useForm`组合式函数进行表单管理

- **Zod** - TypeScript优先的模式验证
  - 定义验证规则的模式
  - 与VeeValidate集成

## HTTP客户端

- **Alova** - 请求缓存库
  - 用于API请求管理
  - 实现请求去重和缓存

- **Axios** - 基于Promise的HTTP客户端
  - API请求的替代方案

## 实用工具

- **VueUse** - Vue组合式工具集合
  - 从`@vueuse/core`导入
  - 提供现成的组合式函数

- **Vue I18n** - 国际化插件
  - 翻译文件在[src/locales/](mdc:src/locales)中
  - 使用`useI18n`组合式函数进行翻译

## 附加库

- **Embla Carousel** - 轮播/滑块组件
  - 用于图片画廊和滑块

- **Vue Calendar** - 日期选择器和日历组件
  - 用于日期选择界面

- **Vue Sonner** - 通知提示
  - 用于用户反馈和通知

