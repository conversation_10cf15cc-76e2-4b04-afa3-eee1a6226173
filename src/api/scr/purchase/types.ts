import type { ModelBase } from '@/types/core';

/**
 * 单据状态
 */
export enum DocStatus {
    DRAFT = '草稿',
    PENDING_REVIEW = '待审核',
    UNDER_REVIEW = '审核中',
    APPROVED = '已审核',
    REJECTED = '已驳回',
    CANCELLED = '已作废',
}

/**
 * 采购状态
 */
export enum PurchaseStatus {
    PendingReceipt = '待收货',
    PartialReceipt = '部分收货',
    BeOverdue = '已逾期',
    PartialBeOverdue = '部分逾期',
    Completed = '已完成',
}

/**
 * 运费承担方
 */
export enum ShippingFeeBy {
    Supplier = '供应商',
    Purchaser = '采购方',
}

/** 对象类型 */
interface ObjectType {
    name?: string;
    factory_name?: string;
    id?: number;
    code?: string;
}

// Purchase 数据接口定义
export interface Purchase extends ModelBase {
    id?: number;
    version?: number; // 乐观锁版本号
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    supplier_id?: number; // 供应商
    factory_id?: number; // 所属组织
    purchase_no?: string; // 采购订单号
    contract_no?: string; // 采购合同号
    purchase_date?: string | Date; // 采购日期
    delivery_date?: string | Date; // 交货日期
    doc_status?: DocStatus; // 单据状态
    purchase_status?: PurchaseStatus; // 采购状态
    project_id?: number; // 所属项目
    customer_id?: number; // 所属客户
    is_include_tax?: boolean; // 是否含税
    currency_id?: number; // 币别
    exchange_rate?: number; // 汇率
    tax_rate_id?: number;
    tax_rate?: number; // 税率
    tax_amount?: number; // 税额
    tax_amount_text?: string; // 税额格式化展示
    discount_rate?: number; // 折扣率
    discount_amount?: number; // 折扣金额
    discount_amount_text?: string; // 折扣金额格式化展示
    deposit_amount?: number; // 定金
    payment_terms_id?: number; // 付款条款
    amount_including_tax?: number; // 总金额（含税）
    amount_including_tax_text?: string; // 总金额（含税）格式化展示
    amount_not_taxed?: number; // 总金额（不含税）
    amount_not_taxed_text?: string; // 总金额（不含税）格式化展示
    base_currency_amount?: number; // 本位币金额
    transport_id?: number; // 交货方式
    shipping_fee_by?: ShippingFeeBy; // 运费承担方
    delivery_address?: string; // 交货地址
    notes?: string; // 备注
    supplier?: ObjectType; // supplier
    project?: ObjectType; // project
    payment_terms?: ObjectType; // payment_terms
    currency?: ObjectType; // currency
    transport?: ObjectType; // transport
    factory?: ObjectType; // factory
    customer?: ObjectType; // customer
    items?: any[]; // items
}

export interface PurchaseCreate extends Omit<Purchase, 'id'> {}

export interface PurchaseUpdate extends Purchase {}

// API 响应接口定义
