import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { Purchase, PurchaseCreate, PurchaseUpdate } from './types';

/**
 * @description 获取Purchase元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getPurchaseMetadata = () => {
    return alovaInstance.Get<any>('/v1/scr/purchase/get_metadata');
};

/**
 * @description 获取Purchase列表
 * @param {PurchaseQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Purchase>>} 返回包含Purchase信息的Promise对象
 * @example
 * // 使用示例
 * const purchaseList = await getPurchaseList({ start: 1, limit: 20 });
 */
const getPurchaseList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Purchase>>(
        '/v1/scr/purchase/query',
        params
    );
};

/**
 * @description 获取Purchase详情
 * @param {number} id Purchase ID
 * @returns {Promise<Purchase>} 返回Purchase详情信息
 */
const getPurchase = (data: { id?: number; max_depth?: number }) => {
    return alovaInstance.Get<Purchase>('/v1/scr/purchase/get', {
        params: data,
    });
};

/**
 * @description 创建Purchase
 * @param {PurchaseCreate} data 创建数据
 * @returns {Promise<Purchase>} 返回创建的Purchase信息
 */
const createPurchase = (data: PurchaseCreate) => {
    return alovaInstance.Post<Purchase>('/v1/scr/purchase/create', data);
};

/**
 * @description 更新Purchase
 * @param {PurchaseUpdate} data 更新数据
 * @returns {Promise<Purchase>} 返回更新后的Purchase信息
 */
const updatePurchase = (data: PurchaseUpdate) => {
    return alovaInstance.Put<Purchase>('/v1/scr/purchase/update', data);
};

/**
 * @description 删除Purchase
 * @param {number} id Purchase ID
 * @returns {Promise<any>} 返回删除结果
 */
const removePurchase = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/scr/purchase/delete/${id}`);
};

/**
 * @description 批量删除Purchase
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeletePurchase = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/scr/purchase/bulk_delete', ids);
};

/** 采购订单反审核 */
const purchaseAntiApproval = (id: number) => {
    return alovaInstance.Post<any>(`/v1/scr/purchase/anti_approval`, {
        relation_id: id,
    });
};

// /**
//  * @description 导出Purchase数据
//  * @param {PurchaseQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportPurchase = (params?: PurchaseQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/scr/purchase/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Purchase数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importPurchase = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/scr/purchase/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getPurchaseMetadata,
    getPurchaseList,
    getPurchase,
    createPurchase,
    updatePurchase,
    removePurchase,
    batchDeletePurchase,
    purchaseAntiApproval,
    // exportPurchase,
    // importPurchase,
};
