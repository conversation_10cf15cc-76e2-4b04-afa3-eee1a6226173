import type { ModelBase } from "@/types/core"

/**
 * 供应商属性
 */
export enum SupplierAttribute {
  Sale = '经销商',
  Assist = '委外商'
}

// supplier 数据接口定义
export interface supplier extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  factory_id?: number; // 所属组织
  code?: string; // 供应商编码
  name?: string; // 供应商名称
  type_id?: number; // 供应商类型
  label_id?: number; // 供应商标签
  attribute?: SupplierAttribute; // 供应商属性
  uscc?: string; // 统一社会信用代码
  address?: string; // 供应商地址
  opening_bank?: string; // 开户行
  bank_card_no?: string; // 银行卡号
  currency_id?: number; // 结算货币
  exchange_rate?: number; // 汇率
  payment_terms_id?: number; // 结算方式
  count?: number; // 累计采购次数
  defect_rate?: number; // 累计不良率
  amount?: number; // 累计采购金额
  valid: boolean; // 是否生效
  notes?: string; // 备注
  items: SupplierItem[]; // items
  factory?: Factory; // factory
  type?: DictData; // type
  label?: DictData; // label
  currency?: Currency; // currency
  payment_terms?: PaymentTerms; // payment_terms
}

export interface supplierCreate extends Omit<supplier, 'id'> {

}

export interface supplierUpdate extends supplier {

}

// API 响应接口定义
