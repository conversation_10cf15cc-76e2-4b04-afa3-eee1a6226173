import alovaInstance from '@/api';
import type { ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { supplier } from './types';

/**
 * @description 获取supplier元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getMetadata = () => {
    return alovaInstance.Get<any>('/v1/scr/supplier/get_metadata');
};

/**
 * @description 获取supplier列表
 * @param {supplierQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<supplier>>} 返回包含supplier信息的Promise对象
 * @example
 * // 使用示例
 * const supplierList = await getList({ page: 1, size: 20 });
 */
const getList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<supplier>>(
        '/v1/scr/supplier/query',
        params
    );
};

/**
 * @description 获取supplier详情
 * @param {number} id supplier ID
 * @returns {Promise<supplier>} 返回supplier详情信息
 */
const getSingleSupplier = (id?: number) => {
    //   const url = id ? `/v1/scr/supplier/get/${id}` : '/v1/scr/supplier/get';
    //   return alovaInstance.Get<supplier>(url);
    return alovaInstance.Get<Recordable>(
        `/v1/scr/supplier/get?id=${id}&max_depth=2`,
        { cacheFor: 0 }
    );
};

/**
 * @description 创建supplier
 * @param {supplierCreateParams} data 创建数据
 * @returns {Promise<supplier>} 返回创建的supplier信息
 */
const create = (data: Recordable) => {
    return alovaInstance.Post<supplier>('/v1/scr/supplier/create', data);
};

/**
 * @description 更新supplier
 * @param {supplierUpdateParams} data 更新数据
 * @returns {Promise<supplier>} 返回更新后的supplier信息
 */
const update = (data: Recordable) => {
    return alovaInstance.Put<supplier>('/v1/scr/supplier/update', data);
};

/**
 * @description 删除supplier
 * @param {number} id supplier ID
 * @returns {Promise<any>} 返回删除结果
 */
const remove = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/scr/supplier/delete/${id}`);
};

/**
 * @description 批量删除supplier
 * @param {number[]} ids supplier ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
// const batchDelete = (ids: number[]) => {
//   return alovaInstance.Delete<any>('/v1/scr/supplier/bulk_delete', { ids });
// };
const batchDelete = (params: { ids: string[] }) => {
    return alovaInstance.Delete<Recordable>(
        '/v1/scr/supplier/bulk_delete',
        params.ids
    );
};
/**
 * @description 切换供应商状态
 * @param {Partial<boolean>} data - 供应商状态
 * @returns {Promise<Recordable>}
 */
const switchStatus = (data: Recordable) => {
    return alovaInstance.Put<Recordable>('/v1/scr/supplier/set_enable_status', {
        params: data,
    });
};
/**
 * @description 导出supplier数据
 * @param {supplierQueryParams} params 查询参数
 * @returns {Promise<Blob>} 返回导出文件
 */
// const exportData = (params?: supplierQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/scr/supplier/export', params, {
//     responseType: 'blob'
//   });
// };

/**
 * @description 导入supplier数据
 * @param {File} file 导入文件
 * @returns {Promise<any>} 返回导入结果
 */
// const importData = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/scr/supplier/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getMetadata,
    getList,
    getSingleSupplier,
    create,
    update,
    remove,
    batchDelete,
    switchStatus,
    //   exportData,
    //   importData,
};
