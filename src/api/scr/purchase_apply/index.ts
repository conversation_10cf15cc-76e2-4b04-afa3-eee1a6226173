/** 采购申请 api */
import alovaInstance from '@/api';
import type { ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type {
    PurchaseApply,
    PurchaseApplyCreate,
    PurchaseApplyMaterialItem,
    PurchaseApplyUpdate,
} from './types';

/**
 * @description 获取PurchaseApply元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getPurchaseApplyMetadata = () => {
    return alovaInstance.Get<any>('/v1/scr/purchase_apply/get_metadata');
};

/**
 * @description 获取PurchaseApply列表
 * @param {PurchaseApplyQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<PurchaseApply>>} 返回包含PurchaseApply信息的Promise对象
 * @example
 * // 使用示例
 * const purchaseApplyList = await getPurchaseApplyList({ start: 1, limit: 20 });
 */
const getPurchaseApplyList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<PurchaseApply>>(
        '/v1/scr/purchase_apply/query',
        params
    );
};

/**
 * @description 获取PurchaseApply详情
 * @param {number} id PurchaseApply ID
 * @returns {Promise<PurchaseApply>} 返回PurchaseApply详情信息
 */
const getPurchaseApply = (params: DetailQuery) => {
    return alovaInstance.Get<PurchaseApply>('/v1/scr/purchase_apply/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * @description 创建PurchaseApply
 * @param {PurchaseApplyCreate} data 创建数据
 * @returns {Promise<PurchaseApply>} 返回创建的PurchaseApply信息
 */
const createPurchaseApply = (data: PurchaseApplyCreate) => {
    return alovaInstance.Post<PurchaseApply>(
        '/v1/scr/purchase_apply/create',
        data
    );
};

/**
 * @description 更新PurchaseApply
 * @param {PurchaseApplyUpdate} data 更新数据
 * @returns {Promise<PurchaseApply>} 返回更新后的PurchaseApply信息
 */
const updatePurchaseApply = (data: PurchaseApplyUpdate) => {
    return alovaInstance.Put<PurchaseApply>(
        '/v1/scr/purchase_apply/update',
        data
    );
};

/**
 * @description 删除PurchaseApply
 * @param {number} id PurchaseApply ID
 * @returns {Promise<any>} 返回删除结果
 */
const removePurchaseApply = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/scr/purchase_apply/delete/${id}`);
};

/**
 * @description 批量删除PurchaseApply
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeletePurchaseApply = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/scr/purchase_apply/batch-delete', {
        ids,
    });
};

/** 采购申请物料metadata */
const getPurchaseApplyMaterialMetadata = () => {
    return alovaInstance.Get<any>('/v1/scr/purchase_source/get_metadata');
};
/** 获取采购申请物料数据 */
const getPurchaseApplyMaterialList = (data: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<PurchaseApplyMaterialItem[]>>(
        '/v1/scr/purchase_source/query',
        data
    );
};

/**
 * @description 反审核PurchaseApply
 * @param {number} id PurchaseApply ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApprovalPurchaseApply = (id: number) => {
    return alovaInstance.Post<Recordable>(
        `/v1/scr/purchase_apply/anti_approval`,
        {
            relation_id: id,
        }
    );
};

// /**
//  * @description 导出PurchaseApply数据
//  * @param {PurchaseApplyQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportPurchaseApply = (params?: PurchaseApplyQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/scr/purchase_apply/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入PurchaseApply数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importPurchaseApply = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/scr/purchase_apply/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getPurchaseApplyMetadata,
    getPurchaseApplyList,
    getPurchaseApply,
    createPurchaseApply,
    updatePurchaseApply,
    removePurchaseApply,
    batchDeletePurchaseApply,
    getPurchaseApplyMaterialList,
    getPurchaseApplyMaterialMetadata,
    antiApprovalPurchaseApply,
    // exportPurchaseApply,
    // importPurchaseApply,
};
