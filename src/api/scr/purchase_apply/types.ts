import type { ModelBase } from '@/types/core';

/**
 * 单据状态
 */
export enum DocStatus {
    DRAFT = '草稿',
    PENDING_REVIEW = '待审核',
    UNDER_REVIEW = '审核中',
    APPROVED = '已审核',
    REJECTED = '已驳回',
    CANCELLED = '已作废',
}

/** 对象类型 */
interface ObjectType {
    name?: string;
    factory_name?: string;
    id?: number;
    code?: string;
}

export interface PurchaseApplyMaterialItem {
    customer_id?: number;
    variant_specs?: string;
    weight?: string;
    plan_delivery_date?: string;
    customer_name?: string;
    specs?: string;
    uom_id?: null;
    priority_id?: number;
    id?: number;
    purchase_type?: string;
    height?: string;
    uom?: null;
    apply_no?: string;
    material_id?: number;
    width?: string;
    qty?: string;
    apply_date?: string;
    product_id?: null;
    deep?: string;
    org_type?: string;
    priority?: string;
    variant_id?: number;
    color_id?: number;
    org_no?: string;
    project_id?: number;
    code?: string;
    color?: string;
    org_id?: number;
    project_name?: string;
    name?: string;
    material_quality?: string;
    pic?: null;
}

export interface PurchaseApplyItem {
    seq_no: number; // 序号
    purchase_type: string; // 采购类型，固定为 '材料采购'
    material_id?: number; // 物料ID
    product_id?: number; // 产品ID（当前未使用）
    variant_id?: number; // 变体规格ID
    name: string; // 物料名称
    code: string; // 物料编码
    variant_specs?: string; // 变体规格编码
    specs?: string; // 规格型号
    height?: string | number; // 长/高(mm)
    width?: string | number; // 宽(mm)
    deep?: string | number; // 厚度(mm)
    color_id?: number; // 颜色ID
    material_quality?: string; // 材质
    weight?: string | number; // 重量(kg)
    uom_id: number; // 单位ID
    qty: number; // 数量
}

// PurchaseApply 数据接口定义
export interface PurchaseApply extends ModelBase {
    id: number;
    version?: number; // 版本号
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    factory_id?: number; // 所属组织
    apply_no?: string; // 采购申请单号
    apply_date?: string | Date; // 申请日期
    plan_delivery_date?: string | Date; // 计划交货日期
    doc_status?: DocStatus; // 单据状态
    priority_id?: number; // 优先级
    org_no?: string; // 源单单号
    state_type?: number; // 物料种类
    project_id?: number; // 所属项目
    customer_id?: number; // 所属客户
    notes?: string; // 备注
    factory?: ObjectType; // factory
    priority?: ObjectType; // priority
    customer?: ObjectType; // customer
    project?: ObjectType; // project
    items: PurchaseApplyItem[]; // items
}

export interface PurchaseApplyCreate extends Omit<PurchaseApply, 'id'> {}

export interface PurchaseApplyUpdate extends PurchaseApply {}

// API 响应接口定义
