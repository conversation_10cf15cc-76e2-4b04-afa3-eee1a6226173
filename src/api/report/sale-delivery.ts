import alovaInstance, { type ResponseSucess } from '../index'
import type { QueryParams } from '@/types/api/queryParams'

const getSummaryMetaData = () => {
  return alovaInstance.Get<ResponseSucess<Recordable>>(
    `/v1/sal/delivery/get_metadata`
  )
}

const getDetailMetaData = () => {
  return alovaInstance.Get<ResponseSucess<Recordable>>(
    `/v1/sal/delivery_item/get_metadata`
  )
}

const getSaleDeliverySummary = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<any>('/v1/sal/delivery/query', data)
}

const getSaleDeliveryDetail = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<any>('/v1/sal/delivery_item/query', data)
}

export {
  getSaleDeliverySummary,
  getSummaryMetaData,
  getSaleDeliveryDetail,
  getDetailMetaData,
}
