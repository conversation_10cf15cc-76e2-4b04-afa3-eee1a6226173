import alovaInstance, { type ResponseSucess } from '../index'
import type { QueryParams } from '@/types/api/queryParams'

const getSummaryMetaData = () => {
  return alovaInstance.Get<ResponseSucess<Recordable>>(
    `/v1/mes/material_requisition/get_metadata`
  )
}

const getDetailMetaData = () => {
  return alovaInstance.Get<ResponseSucess<Recordable>>(
    `/v1/mes/material_requisition_item/get_metadata`
  )
}

const getMaterialRequisitionSummary = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<any>('/v1/mes/material_requisition/query', data)
}

const getMaterialRequisitionDetail = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<any>(
    '/v1/mes/material_requisition_item/query',
    data
  )
}

export {
  getMaterialRequisitionSummary,
  getSummaryMetaData,
  getMaterialRequisitionDetail,
  getDetailMetaData,
}
