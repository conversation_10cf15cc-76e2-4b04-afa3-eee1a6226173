import alovaInstance, { type ResponseSucess } from '../index'
import type { QueryParams } from '@/types/api/queryParams'

const getSummaryMetaData = () => {
  return alovaInstance.Get<ResponseSucess<Recordable>>(
    `/v1/mes/production_notice/get_metadata`
  )
}

const getDetailMetaData = () => {
  return alovaInstance.Get<ResponseSucess<Recordable>>(
    `/v1/mes/production_notice_item/get_metadata`
  )
}

const getProductionNoticeSummary = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<any>('/v1/mes/production_notice/query', data)
}

const getProductionNoticeDetail = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<any>('/v1/mes/production_notice_item/query', data)
}

export {
  getProductionNoticeSummary,
  getSummaryMetaData,
  getProductionNoticeDetail,
  getDetailMetaData,
}
