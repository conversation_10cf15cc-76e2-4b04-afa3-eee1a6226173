import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/**
 * 获取库存汇总元数据
 * @returns {Promise<Recordable>} 返回包含库存汇总元数据的Promise对象
 * @example
 * // 使用示例
 * const inventorySummaryMetaData = await getPageMetaData();
 */
const getSummaryMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/wms/inventory_summary/get_metadata`
    );
};

const getDetailMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/wms/inventory/get_metadata`
    );
};

/**
 * 获取库存汇总
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<any>} 返回包含库存汇总的Promise对象
 * @example
 * // 使用示例
 * const inventorySummary = await getInventorySummary(data);
 */
const getInventorySummary = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/wms/inventory_summary/query', data);
};

const getInventoryDetail = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/wms/inventory/query', data);
};

export {
    getInventorySummary,
    getSummaryMetaData,
    getInventoryDetail,
    getDetailMetaData,
};
