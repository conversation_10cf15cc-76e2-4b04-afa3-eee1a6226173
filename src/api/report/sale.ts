import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';

const getSummaryMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/sal/order/get_metadata`
    );
};

const getDetailMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/sal/order_item/get_metadata`
    );
};

const getSaleSummary = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/sal/order/query', data);
};

const getSaleDetail = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/sal/order_item/query', data);
};

export { getSaleSummary, getSummaryMetaData, getSaleDetail, getDetailMetaData };
