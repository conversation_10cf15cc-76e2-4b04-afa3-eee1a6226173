import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';

const getSummaryMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/scr/purchase/get_metadata`
    );
};

const getDetailMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_item/get_metadata`
    );
};

const getPurchaseOrderSummary = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/scr/purchase/query', data);
};

const getPurchaseOrderDetail = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/scr/purchase_item/query', data);
};

export {
    getSummaryMetaData,
    getDetailMetaData,
    getPurchaseOrderSummary,
    getPurchaseOrderDetail,
};
