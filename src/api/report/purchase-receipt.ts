import alovaInstance, { type ResponseSucess } from '../index'
import type { QueryParams } from '@/types/api/queryParams'

const getSummaryMetaData = () => {
  return alovaInstance.Get<ResponseSucess<Recordable>>(
    `/v1/scr/purchase_receipt/get_metadata`
  )
}

const getDetailMetaData = () => {
  return alovaInstance.Get<ResponseSucess<Recordable>>(
    `/v1/scr/purchase_receipt_item/get_metadata`
  )
}

const getPurchaseReceiptSummary = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<any>('/v1/scr/purchase_receipt/query', data)
}

const getPurchaseReceiptDetail = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<any>('/v1/scr/purchase_receipt_item/query', data)
}

export {
  getPurchaseReceiptSummary,
  getSummaryMetaData,
  getPurchaseReceiptDetail,
  getDetailMetaData,
}
