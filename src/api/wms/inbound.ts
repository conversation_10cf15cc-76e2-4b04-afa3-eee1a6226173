import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/** == 成品入库 =================================================================================== */
const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/wms/inbound/get_metadata`
    );
};
/**
 * 获取成品入库列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<any>} 返回包含成品入库的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getInboundList(data);
 */
const getInboundList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/wms/inbound/query', data);
};

/**
 * 创建成品入库
 * @param {Partial<any>} data - 成品入库
 * @returns {Promise<any>} 返回包含创建成品入库的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createInbound(data);
 */
const createInbound = (data: Partial<any>) => {
    return alovaInstance.Post<any>('/v1/wms/inbound/create', data);
};

/**
 * 更新成品入库
 * @param {Partial<any>} data - 成品入库
 * @returns {Promise<any>} 返回包含更新成品入库的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateInbound(data);
 */
const updateInbound = (data: Partial<any>) => {
    return alovaInstance.Put<any>('/v1/wms/inbound/update', data);
};

/**
 * 删除成品入库
 * @param {Partial<any>} data - 成品入库
 * @returns {Promise<any>} 返回包含删除成品入库的Promise对象
 * @example
 * // 使用示例
 */
const deleteInbound = (id: string) => {
    return alovaInstance.Delete<any>(`/v1/wms/inbound/delete/${id}`);
};

/**
 * 批量删除成品入库
 * @param {Partial<any>} data - 成品入库
 * @returns {Promise<any>} 返回包含批量删除成品入库的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteInboundBatch(data);
 */
const deleteInboundBatch = (data: Partial<any>) => {
    // const { ids } = params;
    // let stringIds = ids.map((id: string) => `ids=${id}`).join('&');
    return alovaInstance.Delete<any>('/v1/wms/inbound/bulk_delete', data);
};

/**
 * 获取单个成品入库
 * @param {Partial<any>} data - 成品入库
 * @returns {Promise<any>} 返回包含单个成品入库的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleInbound(data);
 */
const getSingleInbound = (params: { id: string }) => {
    return alovaInstance.Get<any>('/v1/wms/inbound/get', {
        params,
        cacheFor: 0,
    });
};

/** == 入库数据源 =================================================================================== */

/**
 * 获取成品入库列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<any>} 返回包含成品入库的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getInboundList(data);
 */
const getInboundSourceList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/wms/inbound_source/query', data);
};
/**
 * 获取单个销售单
 * @param {Partial<any>} data - 销售出库
 * @returns {Promise<any>} 返回包含销售单的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleInboundSource(data);
 */
const getSingleInboundSource = (params: { id: string }) => {
    return alovaInstance.Get<any>('/v1/wms/inbound_source/get', { params ,cacheFor:0});
};

const getInboundSourcePageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/wms/inbound_source/get_metadata`
    );
};

/** == 采购入库数据源 =================================================================================== */
/**
 * 获取采购入库列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<any>} 返回包含采购入库的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getInboundList(data);
 */
const getPurchaseInboundSourceList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/wms/purchase_inbound_source/query', data);
};
/**
 * 获取单个采购入库
 * @param {Partial<any>} data - 采购入库
 * @returns {Promise<any>} 返回包含采购入库的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleInboundSource(data);
 */
const getSinglePurchaseInboundSourceSource = (params: { id: string }) => {
    return alovaInstance.Get<any>('/v1/wms/purchase_inbound_source/get', { params ,cacheFor:0});
};

/**
 * @description 反审核Inbound
 * @param {number} id Inbound ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(`/v1/wms/inbound/anti_approval`, {
        relation_id: id,
    });
};
const getPurchasePageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/wms/purchase_inbound_source/get_metadata`
    );
};

export {
    getInboundList,
    createInbound,
    updateInbound,
    deleteInbound,
    deleteInboundBatch,
    getSingleInbound,
    getInboundSourceList,
    getSingleInboundSource,
    getPageMetaData,
    antiApproval,
    getPurchaseInboundSourceList,
    getSinglePurchaseInboundSourceSource,
    getPurchasePageMetaData,
    getInboundSourcePageMetaData,
};
