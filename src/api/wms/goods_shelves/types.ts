import type { ModelBase } from '@/types/core'

/**
 * 状态
 */
export enum StoreStatus {
  Enable = '启用',
  Disable = '停用',
  Maintain = '维护',
}

// GoodsShelves 数据接口定义
export interface GoodsShelves extends ModelBase {
  id?: number
  parent_id?: number
  created_by?: number // 创建者
  updated_by?: number // 更新者
  region_id?: number // 区域ID
  store_id?: number // 仓库ID
  code?: string // 编码
  name?: string // 名称
  description?: string // 描述
  security_level_id?: number // 安全等级
  shelves_status?: StoreStatus // 状态
  usage?: number // 利用率
  shelves_type_id?: number // 货架类型
  shelves_material_id?: number // 材料
  layers_number?: number // 层数
  capacity_per_layer?: number // 每层容量
  capacity?: number // 容量
  usable_capacity?: number // 可用容量
  use_capacity?: number // 使用容量
  unit_load?: number // 单位承重
  max_load?: number // 最大承重
  install_date?: string // 安装日期
  is_support_stacking?: boolean // 支持堆叠
  max_stacking_layers?: number // 最大堆叠层数
  is_fpd?: boolean // 消防设施
  is_lbs?: boolean // 承重传感器
  is_thm?: boolean // 温湿度监控
  last_maint_date?: string // 上次检查日期
  next_maint_date?: string // 下次维护日期
  store?: any // store
  shelves_type?: string // shelves_type
  shelves_material?: string // shelves_material
  security_level?: string // security_level
}

export interface GoodsShelvesCreate extends Omit<GoodsShelves, 'id'> {}

export interface GoodsShelvesUpdate extends GoodsShelves {}

/** 库存查询参数 */
export interface InvenToryQuery {
  shelves_id: number
  location_id?: number
  code?: string
  name?: string
  location_status?: string
  location_type?: string
}

/** 库存列表数据类型 */
export interface InventoryInfo {
  shelves_id?: number
  code?: string
  name?: string
  location_status?: string
  location_type?: string
  id?: number
  location_code?: string
  reserve_qty?: number
  stock_qty?: number
  batch_no?: number
  unit_price?: number
}

/** 合并后的库存数据 */
export interface InventoryInfoMerge {
  id?: number
  shelves_id?: number
  code?: string
  name?: string
  location_status?: string
  location_type?: string
  total_qty?: number
  total_reserve_qty?: number
  infos?: InventoryInfo[]
}

interface BindShelf {
  id?: number
  name?: string
  code?: string
}
/** 货架储位的数据类型 */
export interface PositionInfo {
  id?: number
  code?: string
  location_type?: string
  number?: number
  created_by?: number
  shelves_id?: number
  location_status?: string
  layers_number?: number
  notes?: string
  bind_shelf?: BindShelf
}

/** 整合后的层级类型 */
export interface LocationInfoMerge {
  layers_number?: number
  occupiedCount?: number
  positions?: PositionInfo[]
}

/** 储位详情 */
export interface LocationDetail extends InventoryInfo {
  /** 货架号 */
  location_code?: string
}
