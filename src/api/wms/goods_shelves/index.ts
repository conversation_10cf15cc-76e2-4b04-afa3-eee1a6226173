import alovaInstance from '@/api'
import type { ModelBase, ResponseListModel } from '@/types/core'
import type { DetailQuery, QueryParams } from '@/types/api/queryParams'

import type {
  GoodsShelves,
  GoodsShelvesCreate,
  GoodsShelvesUpdate,
  InventoryInfo,
  InvenToryQuery,
  PositionInfo,
} from './types'

/**
 * @description 获取GoodsShelves元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getGoodsShelvesMetadata = () => {
  return alovaInstance.Get<any>('/v1/wms/goods_shelves/get_metadata')
}

/**
 * @description 获取GoodsShelves列表
 * @param {GoodsShelvesQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<GoodsShelves>>} 返回包含GoodsShelves信息的Promise对象
 * @example
 * // 使用示例
 * const goodsShelvesList = await getGoodsShelvesList({ start: 1, limit: 20 });
 */
const getGoodsShelvesList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<GoodsShelves>>(
    '/v1/wms/goods_shelves/query',
    params
  )
}

/**
 * @description 获取GoodsShelves详情
 * @param {number} id GoodsShelves ID
 * @returns {Promise<GoodsShelves>} 返回GoodsShelves详情信息
 */
const getGoodsShelves = (params: DetailQuery) => {
  return alovaInstance.Get<GoodsShelves>('/v1/wms/goods_shelves/get', {
    params,
    cacheFor: 0,
  })
}

/**
 * @description 创建GoodsShelves
 * @param {GoodsShelvesCreate} data 创建数据
 * @returns {Promise<GoodsShelves>} 返回创建的GoodsShelves信息
 */
const createGoodsShelves = (data: GoodsShelvesCreate) => {
  return alovaInstance.Post<GoodsShelves>('/v1/wms/goods_shelves/create', data)
}

/**
 * @description 更新GoodsShelves
 * @param {GoodsShelvesUpdate} data 更新数据
 * @returns {Promise<GoodsShelves>} 返回更新后的GoodsShelves信息
 */
const updateGoodsShelves = (data: GoodsShelvesUpdate) => {
  return alovaInstance.Put<GoodsShelves>('/v1/wms/goods_shelves/update', data)
}

/**
 * @description 删除GoodsShelves
 * @param {number} id GoodsShelves ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeGoodsShelves = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/wms/goods_shelves/delete/${id}`)
}

/**
 * @description 批量删除GoodsShelves
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteGoodsShelves = (ids: number[]) => {
  return alovaInstance.Delete<any>('/wms/goods_shelves/bulk_delete', ids)
}

/** 获取货架库存数据 */
const getGoodsShelvesInventory = (params: InvenToryQuery) => {
  return alovaInstance.Post<InventoryInfo[]>(
    '/v1/wms/goods_shelves/inventory',
    params
  )
}

/** 获取储位信息 */
const getLocationInfo = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<PositionInfo>>(
    '/v1/wms/store_location/query',
    params
  )
}

/** 获取储位详情 */
const getLocationDetail = (params: DetailQuery) => {
  return alovaInstance.Get<PositionInfo>('/v1/wms/store_location/get', {
    params,
    cacheFor: 0,
  })
}

// /**
//  * @description 导出GoodsShelves数据
//  * @param {GoodsShelvesQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportGoodsShelves = (params?: GoodsShelvesQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/wms/goods_shelves/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入GoodsShelves数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importGoodsShelves = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/wms/goods_shelves/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getGoodsShelvesMetadata,
  getGoodsShelvesList,
  getGoodsShelves,
  createGoodsShelves,
  updateGoodsShelves,
  removeGoodsShelves,
  bulkDeleteGoodsShelves,
  getGoodsShelvesInventory,
  getLocationInfo,
  getLocationDetail,
  // exportGoodsShelves,
  // importGoodsShelves,
}
