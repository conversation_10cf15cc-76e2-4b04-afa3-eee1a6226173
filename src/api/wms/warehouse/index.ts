import alovaInstance from '@/api'
import type { ModelBase, ResponseListModel } from '@/types/core'
import type { DetailQuery, QueryParams } from '@/types/api/queryParams'

import type { Warehouse } from './types'

/**
 * @description 获取Warehouse元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getWarehouseMetadata = () => {
  return alovaInstance.Get<any>('/v1/wms/store/get_metadata')
}

/**
 * @description 获取Warehouse列表
 * @param {WarehouseQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Warehouse>>} 返回包含Warehouse信息的Promise对象
 * @example
 * // 使用示例
 * const warehouseList = await getWarehouseList({ start: 1, limit: 20 });
 */
const getWarehouseList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<Warehouse>>(
    '/v1/wms/store/query',
    params
  )
}

/**
 * @description 获取Warehouse详情
 * @param {number} id Warehouse ID
 * @returns {Promise<Warehouse>} 返回Warehouse详情信息
 */
const getWarehouse = (params: DetailQuery) => {
  return alovaInstance.Get<Warehouse>('/v1/wms/store/get', {
    params,
    cacheFor: 0,
  })
}

/**
 * @description 创建Warehouse
 * @param {Warehouse} data 创建数据
 * @returns {Promise<Warehouse>} 返回创建的Warehouse信息
 */
const createWarehouse = (data: Warehouse) => {
  return alovaInstance.Post<Warehouse>('/v1/wms/store/create', data)
}

/**
 * @description 更新Warehouse
 * @param {Warehouse} data 更新数据
 * @returns {Promise<Warehouse>} 返回更新后的Warehouse信息
 */
const updateWarehouse = (data: Warehouse) => {
  return alovaInstance.Put<Warehouse>('/v1/wms/store/update', data)
}

/**
 * @description 删除Warehouse
 * @param {number} id Warehouse ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeWarehouse = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/wms/store/delete/${id}`)
}

/**
 * @description 批量删除Warehouse
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteWarehouse = (ids: number[]) => {
  return alovaInstance.Delete<any>('/wms/store/bulk_delete', ids)
}

/** 获取仓库树形 */
const getStoreTree = (data?: { root_id: number; max_depth?: number }) => {
  return alovaInstance.Get<Warehouse[]>('/v1/wms/store/tree', {
    params: data,
    cacheFor: 0,
  })
}

// /**
//  * @description 导出Warehouse数据
//  * @param {WarehouseQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportWarehouse = (params?: WarehouseQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/wms/store/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Warehouse数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importWarehouse = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/wms/store/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getWarehouseMetadata,
  getWarehouseList,
  getWarehouse,
  createWarehouse,
  updateWarehouse,
  removeWarehouse,
  bulkDeleteWarehouse,
  getStoreTree,
  // exportWarehouse,
  // importWarehouse,
}
