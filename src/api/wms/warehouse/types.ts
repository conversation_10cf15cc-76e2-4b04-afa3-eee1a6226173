import type { ModelBase } from '@/types/core'

/**
 * 仓库类型
 */
export enum StoreType {
  Company = '公司仓库',
  Dept = '部门超领仓',
  ODM = '外协超领仓',
}

interface ObjectType {
  id?: number
  name?: string
  factory_id?: number
  factory_name?: string
}

// Warehouse 数据接口定义
export interface Warehouse extends ModelBase {
  id?: number
  code?: string
  name?: string
  expanded?: boolean
  parent_id?: number
  store_type?: string
  total_area?: number
  use_area?: number
  max_capacity?: number
  maint_by?: string
  phone?: string
  address?: string
  description?: string
  security_level_id?: number
  store_status?: string
  usage?: number
  special_request?: string
  min_humidity?: number
  max_humidity?: number
  min_temperature?: number
  max_temperature?: number
  children?: Warehouse[]
  region_id?: number
  shelves_status?: string
}
