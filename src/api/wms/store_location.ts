import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/** == 仓位资料 =================================================================================== */
const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/wms/store_location/get_metadata`
    );
};
/**
 * 获取仓位资料列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<any>} 返回包含仓位资料的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getStoreLocationList(data);
 */
const getStoreLocationList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/wms/store_location/query', data);
};

/**
 * 创建仓位资料
 * @param {Partial<any>} data - 仓位资料
 * @returns {Promise<any>} 返回包含创建仓位资料的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createStoreLocation(data);
 */
const createStoreLocation = (data: Partial<any>) => {
    return alovaInstance.Post<any>('/v1/wms/store_location/create', data);
};

/**
 * 更新仓位资料
 * @param {Partial<any>} data - 仓位资料
 * @returns {Promise<any>} 返回包含更新仓位资料的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateStoreLocation(data);
 */
const updateStoreLocation = (data: Partial<any>) => {
    return alovaInstance.Put<any>('/v1/wms/store_location/update', data);
};

/**
 * 删除仓位资料
 * @param {Partial<any>} data - 仓位资料
 * @returns {Promise<any>} 返回包含删除仓位资料的Promise对象
 * @example
 * // 使用示例
 */
const deleteStoreLocation = (id: string) => {
    return alovaInstance.Delete<any>(`/v1/wms/store_location/delete/${id}`);
};

/**
 * 批量删除仓位资料
 * @param {Partial<any>} data - 仓位资料
 * @returns {Promise<any>} 返回包含批量删除仓位资料的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteStoreLocationBatch(data);
 */
const deleteStoreLocationBatch = (data: Partial<any>) => {
    //const { ids } = params;
    //let stringIds = ids.map((id: string) => `ids=${id}`).join('&');
    // return alovaInstance.Delete<any>(`/v1/wms/store_location/bulk_delete?${stringIds}`);
    return alovaInstance.Delete<any>(
        '/v1/wms/store_location/bulk_delete',
        data
    );
};

/**
 * 获取单个仓位资料
 * @param {Partial<any>} data - 仓位资料
 * @returns {Promise<any>} 返回包含单个仓位资料的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleStoreLocation(data);
 */
const getSingleStoreLocation = (params: { id: string }) => {
    return alovaInstance.Get<any>('/v1/wms/store_location/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * @description 切换仓位资料状态
 * @param {Partial<boolean>} data - 仓位资料状态
 * @returns {Promise<any>}
 */
const switchStatus = (data: any) => {
    return alovaInstance.Put<any>('/v1/wms/store_location/set_enable_status', {
        params: data,
    });
};

export {
    getStoreLocationList,
    createStoreLocation,
    updateStoreLocation,
    deleteStoreLocation,
    deleteStoreLocationBatch,
    getSingleStoreLocation,
    switchStatus,
    getPageMetaData,
};
