import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/** == 仓库资料 =================================================================================== */
const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/wms/store/get_metadata`
    );
};
/**
 * 获取仓库资料列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含仓库资料的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getStoreList(data);
 */
const getStoreList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/wms/store/query', { offset: 0,
    limit: 1000,});
};

/**
 * 创建仓库资料
 * @param {Partial<Recordable>} data - 仓库资料
 * @returns {Promise<Recordable>} 返回包含创建仓库资料的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createStore(data);
 */
const createStore = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/wms/store/create', data);
};

/**
 * 更新仓库资料
 * @param {Partial<Recordable>} data - 仓库资料
 * @returns {Promise<Recordable>} 返回包含更新仓库资料的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateStore(data);
 */
const updateStore = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/wms/store/update', data);
};

/**
 * 删除仓库资料
 * @param {Partial<Recordable>} data - 仓库资料
 * @returns {Promise<Recordable>} 返回包含删除仓库资料的Promise对象
 * @example
 * // 使用示例
 */
const deleteStore = (id: string) => {
    return alovaInstance.Delete<Recordable>(`/v1/wms/store/delete/${id}`);
};

/**
 * 批量删除仓库资料
 * @param {Partial<Recordable>} data - 仓库资料
 * @returns {Promise<Recordable>} 返回包含批量删除仓库资料的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteStoreBatch(data);
 */
const deleteStoreBatch = (data: Partial<any>) => {
    return alovaInstance.Delete<any>('/v1/wms/store/bulk_delete', data);
};

/**
 * 获取单个仓库资料
 * @param {Partial<Recordable>} data - 仓库资料
 * @returns {Promise<Recordable>} 返回包含单个仓库资料的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleStore(data);
 */
const getSingleStore = (params: { id: string }) => {
    return alovaInstance.Get<Recordable>('/v1/wms/store/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * @description 切换仓库资料状态
 * @param {Partial<boolean>} data - 仓库资料状态
 * @returns {Promise<Recordable>}
 */
const switchStatus = (data: Recordable) => {
    return alovaInstance.Put<Recordable>('/v1/wms/store/set_enable_status', {
        params: data,
    });
};
/**
 * 获取仓库类别树
 * @param {Partial<Recordable>} data - 仓库类别信息
 * @returns {Promise<Recordable>} 返回包含仓库类别信息的Promise对象
 * @example
 * // 使用示例
 * const materialTypeTree = await getProductTypeTree(data);
 */
const getStoreTree = (data:Recordable) => {
    return alovaInstance.Get<any>('/v1/wms/store/tree',{params: data,cacheFor:0});
};
export {
    getPageMetaData,
    getStoreList,
    createStore,
    updateStore,
    deleteStore,
    deleteStoreBatch,
    getSingleStore,
    switchStatus,
    getStoreTree,
};

