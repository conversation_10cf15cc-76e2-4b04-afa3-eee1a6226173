import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/** == 销售出库 =================================================================================== */
const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/wms/outbound/get_metadata`
    );
};
/**
 * 获取销售出库列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<any>} 返回包含销售出库的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getOutboundList(data);
 */
const getOutboundList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/wms/outbound/query', data);
};

/**
 * 创建销售出库
 * @param {Partial<any>} data - 销售出库
 * @returns {Promise<any>} 返回包含创建销售出库的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createOutbound(data);
 */
const createOutbound = (data: Partial<any>) => {
    return alovaInstance.Post<any>('/v1/wms/outbound/create', data);
};

/**
 * 更新销售出库
 * @param {Partial<any>} data - 销售出库
 * @returns {Promise<any>} 返回包含更新销售出库的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateOutbound(data);
 */
const updateOutbound = (data: Partial<any>) => {
    return alovaInstance.Put<any>('/v1/wms/outbound/update', data);
};

/**
 * 删除销售出库
 * @param {Partial<any>} data - 销售出库
 * @returns {Promise<any>} 返回包含删除销售出库的Promise对象
 * @example
 * // 使用示例
 */
const deleteOutbound = (id: string) => {
    return alovaInstance.Delete<any>(`/v1/wms/outbound/delete/${id}`);
};

/**
 * 批量删除销售出库
 * @param {Partial<any>} data - 销售出库
 * @returns {Promise<any>} 返回包含批量删除销售出库的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteOutboundBatch(data);
 */
const deleteOutboundBatch = (data: Partial<any>) => {
    // const { ids } = params;
    // let stringIds = ids.map((id: string) => `ids=${id}`).join('&');
    return alovaInstance.Delete<any>('/v1/wms/outbound/bulk_delete', data);
};

/**
 * 获取单个销售出库
 * @param {Partial<any>} data - 销售出库
 * @returns {Promise<any>} 返回包含单个销售出库的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleOutbound(data);
 */
const getSingleOutbound = (params: { id: string }) => {
    return alovaInstance.Get<any>('/v1/wms/outbound/get', {
        params,
        cacheFor: 0,
    });
};

/** == 入库数据源 =================================================================================== */

/**
 * 获取送货单列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<any>} 返回包含销售出库的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getOutboundSourceList(data);
 */
const getOutboundSourceList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/wms/outbound_source/query', data);
};

/**
 * 获取单个送货单
 * @param {Partial<any>} data - 销售出库
 * @returns {Promise<any>} 返回包含送货单的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleOutboundSource(data);
 */
const getSingleOutboundSource = (params: { id: string }) => {
    return alovaInstance.Get<any>('/v1/wms/outbound_source/get', { params });
};

/**
 * @description 反审核Outbound
 * @param {number} id Outbound ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(`/v1/wms/outbound/anti_approval`, {
        relation_id: id,
    });
};

export {
    getOutboundList,
    createOutbound,
    updateOutbound,
    deleteOutbound,
    deleteOutboundBatch,
    getSingleOutbound,
    getOutboundSourceList,
    getSingleOutboundSource,
    getPageMetaData,
    antiApproval,
};
