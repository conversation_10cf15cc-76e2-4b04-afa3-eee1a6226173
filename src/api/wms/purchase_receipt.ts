import alovaInstance, { type ResponseSucess } from '@/api';

const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt/get_metadata`
    );
};

const getPurchaseReceipt = (id: string) => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt/get?id=${id}&max_depth=2`,
        {
            cacheFor: 0,
        }
    );
};

const queryPurchaseReceipt = (data: Recordable) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt/query`,
        data
    );
};

const createPurchaseReceipt = (data: Recordable) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt/create`,
        data
    );
};

const updatePurchaseReceipt = (data: Recordable) => {
    return alovaInstance.Put<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt/update`,
        data
    );
};

const deletePurchaseReceipt = (id: string) => {
    return alovaInstance.Delete<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt/delete/${id}`
    );
};

const bulkDeletePurchaseReceipt = (ids: number[]) => {
    return alovaInstance.Delete<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt/bulk_delete`,
        ids
    );
};

const antiApproval = (id: string) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt/anti_approval`,
        {
            relation_id: id,
        }
    );
};

/*
采购源数据
*/

const querySourceOrder = (data: Recordable) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt_source/query`,
        data
    );
};

const getSourceOrderMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/scr/purchase_receipt_source/get_metadata`
    );
};

export {
    getPageMetaData,
    getPurchaseReceipt,
    queryPurchaseReceipt,
    createPurchaseReceipt,
    updatePurchaseReceipt,
    deletePurchaseReceipt,
    bulkDeletePurchaseReceipt,
    antiApproval,
    querySourceOrder,
    getSourceOrderMetaData,
};
