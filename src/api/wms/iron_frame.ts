import alovaInstance, { type ResponseSucess } from '@/api';
import type { QueryParams } from '@/types/api/queryParams';

const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/wms/iron_frame/get_metadata`
    );
};
/**
 * 获取铁架信息列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含铁架信息的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getIronFrameList(data);
 */
const getIronFrameList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/wms/iron_frame/query', data);
};
/**
 * 创建铁架信息
 * @param {Partial<Recordable>} data - 铁架信息
 * @returns {Promise<Recordable>} 返回包含创建铁架信息的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createIronFrame(data);
 */
const createIronFrame = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/wms/iron_frame/create', data);
};

/**
 * 更新铁架信息
 * @param {Partial<Recordable>} data - 铁架信息
 * @returns {Promise<Recordable>} 返回包含更新铁架信息的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateIronFrame(data);
 */
const updateIronFrame = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/wms/iron_frame/update', data);
};

/**
 * 删除铁架信息
 * @param {Partial<Recordable>} data - 铁架信息
 * @returns {Promise<Recordable>} 返回包含删除铁架信息的Promise对象
 * @example
 * // 使用示例
 */
const deleteIronFrame = (id: string) => {
    return alovaInstance.Delete<Recordable>(`/v1/wms/iron_frame/delete/${id}`);
};

/**
 * 批量删除铁架信息
 * @param {Partial<Recordable>} data - 铁架信息
 * @returns {Promise<Recordable>} 返回包含批量删除铁架信息的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteIronFrameBatch(data);
 */
const deleteIronFrameBatch = (data: Partial<any>) => {
    return alovaInstance.Delete<any>('/v1/wms/iron_frame/bulk_delete', data);
};

/**
 * 获取单个铁架信息
 * @param {Partial<Recordable>} data - 铁架信息
 * @returns {Promise<Recordable>} 返回包含单个铁架信息的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleIronFrame(data);
 */
const getSingleIronFrame = (params: { id: number; max_depth?: number }) => {
    return alovaInstance.Get<Recordable>('/v1/wms/iron_frame/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * 获取获取统计信息
 * @param {Partial<Recordable>} data - 获取统计信息
 * @returns {Promise<Recordable>} 返回包含获取统计信息的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleProject(data);
 */
const getIronFrameStatistics = () => {
    return alovaInstance.Get<Recordable>('/v1/wms/iron_frame/get_iron_statistics', { cacheFor: 0 });
};



/**************************** 维护信息 *************************/

/**
 * 获取维护信息列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含维护信息的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getIronFrameList(data);
 */
const getMaintList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/wms/maint/query', data);
};
/**
 * 创建维护信息
 * @param {Partial<Recordable>} data - 维护信息
 * @returns {Promise<Recordable>} 返回包含创建维护信息的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createIronFrame(data);
 */
const createMaint = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/wms/maint/create', data);
};

/**
 * 更新维护信息
 * @param {Partial<Recordable>} data - 维护信息
 * @returns {Promise<Recordable>} 返回包含更新维护信息的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateIronFrame(data);
 */
const updateMaint = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/wms/maint/update', data);
};

/**
 * 删除维护信息
 * @param {Partial<Recordable>} data - 维护信息
 * @returns {Promise<Recordable>} 返回包含删除维护信息的Promise对象
 * @example
 * // 使用示例
 */
const deleteMaint = (id: string) => {
    return alovaInstance.Delete<Recordable>(`/v1/wms/maint/delete/${id}`);
};


/**
 * 获取单个维护信息
 * @param {Partial<Recordable>} data - 维护信息
 * @returns {Promise<Recordable>} 返回包含单个维护信息的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleIronFrame(data);
 */
const getSingleMaint = (params: { id: number; max_depth?: number }) => {
    return alovaInstance.Get<Recordable>('/v1/wms/maint/get', {
        params,
        cacheFor: 0,
    });
};

export {
    getIronFrameList, createIronFrame, updateIronFrame, deleteIronFrame, deleteIronFrameBatch, getSingleIronFrame, getIronFrameStatistics, getPageMetaData,
    getMaintList, createMaint, updateMaint, deleteMaint, getSingleMaint
}
