# API 服务中心

API 服务中心提供了一个统一的方式来获取和管理项目中的各种 API 接口。通过传入 `"module/model"` 格式的字符串，可以自动从 API 目录的子目录中获取对应的 API 接口。

## 主要特性

- 🚀 **自动加载**: 根据 `module/model` 格式自动加载对应的 API 模块
- 🔄 **智能缓存**: 自动缓存已加载的 API，提高性能
- 📦 **标准化接口**: 提供统一的 API 接口规范
- 🔧 **灵活扩展**: 支持自定义方法和高级用法
- ⚡ **预加载**: 支持预加载常用的 API 接口
- 🎯 **类型安全**: 完整的 TypeScript 类型支持

## 项目结构要求

API 服务中心期望 API 目录具有以下结构：

```
src/api/
├── apiService.ts          # API 服务中心
├── module1/               # 模块目录
│   ├── model1/           # 模型目录
│   │   ├── index.ts      # API 实现
│   │   ├── types.ts      # 类型定义
│   │   └── main.ts       # 可选的主要逻辑
│   └── model2/
│       ├── index.ts
│       ├── types.ts
│       └── main.ts
└── module2/
    └── model1/
        ├── index.ts
        ├── types.ts
        └── main.ts
```

例如现有的结构：
```
src/api/
├── apiService.ts
└── demo/
    ├── demo/
    │   ├── index.ts      # 导出 getDemoMetadata, getDemoList, getDemo, createDemo 等
    │   ├── types.ts      # Demo, DemoCreate, DemoUpdate 类型
    │   └── main.ts
    └── demo_item/
        ├── index.ts      # 导出 getDemoItemMetadata, getDemoItemList 等
        ├── types.ts      # DemoItem, DemoItemCreate 类型
        └── main.ts
```

## API 模块规范

每个 API 模块的 `index.ts` 文件应该导出以下标准方法（遵循命名规范）：

### 必需方法
- `get{Model}Metadata()` - 获取元数据
- `get{Model}List(params)` - 获取列表

### 可选方法
- `get{Model}(id)` - 获取详情
- `create{Model}(data)` - 创建
- `update{Model}(data)` - 更新
- `remove{Model}(id)` 或 `delete{Model}(id)` - 删除
- `bulkDelete{Model}(ids)` - 批量删除

### 命名规范
- 模型名称：将 kebab-case 转换为 PascalCase
  - `demo` → `Demo`
  - `demo_item` → `DemoItem`
  - `user-profile` → `UserProfile`

## 使用方法

### 基础用法

```typescript
import { getApi } from '@/api/apiService'

// 获取 demo 模块的 demo 接口
const demoApi = await getApi('demo/demo')

// 获取元数据
const metadata = await demoApi.getMetadata()

// 获取列表
const list = await demoApi.getList({
  offset: 0,
  limit: 20
})

// 如果支持详情查询
if (demoApi.getDetail) {
  const detail = await demoApi.getDetail(1)
}
```

### 预加载 API

```typescript
import { preloadApis } from '@/api/apiService'

// 在应用启动时预加载常用的 API
await preloadApis([
  'demo/demo',
  'demo/demo_item',
  'user/profile'
])
```

### 缓存管理

```typescript
import { 
  clearApiCache, 
  getCachedApis, 
  isApiCached 
} from '@/api/apiService'

// 检查是否已缓存
console.log(isApiCached('demo/demo')) // true/false

// 获取所有已缓存的 API
console.log(getCachedApis()) // ['demo/demo', 'demo/demo_item']

// 清除特定缓存
clearApiCache('demo/demo')

// 清除所有缓存
clearApiCache()
```

### 错误处理

```typescript
try {
  const api = await getApi('invalid/module')
  const data = await api.getList()
} catch (error) {
  if (error.message.includes('Failed to load API')) {
    console.error('API 模块不存在或加载失败')
  } else if (error.message.includes('Invalid module/model format')) {
    console.error('模块路径格式错误')
  }
}
```

## API 接口规范

所有通过服务中心获取的 API 都会实现 `ModelApi` 接口：

```typescript
interface ModelApi<T = any, C = any, U = any, CMetadata = any> {
  /** 获取元数据 */
  getMetadata: () => Promise<ResponseModel<CMetadata>>
  
  /** 获取列表 */
  getList: (params?: QueryParams) => Promise<ResponseListModel<T>>
  
  /** 获取详情 */
  getDetail?: (id: string | number) => Promise<T>
  
  /** 创建 */
  create?: (data: C) => Promise<T>
  
  /** 更新 */
  update?: (id: string | number, data: U) => Promise<T>
  
  /** 删除 */
  delete?: (id: string | number) => Promise<void>
  
  /** 批量删除 */
  bulkDelete?: (ids: (string | number)[]) => Promise<void>
  
  /** 自定义方法 */
  [method: string]: any
}
```

## 高级用法

### 直接使用服务中心实例

```typescript
import { ApiServiceCenter } from '@/api/apiService'

// 获取服务中心实例
const center = ApiServiceCenter

// 使用实例方法
const api = await center.getApi('demo/demo')
await center.preloadApis(['demo/demo', 'demo/demo_item'])
center.clearCache()
```

### 自定义方法调用

API 模块中导出的所有方法都会自动添加到 API 对象中：

```typescript
const demoApi = await ApiService.getApi('demo/demo')

// 如果 demo/demo/index.ts 导出了 exportDemo 方法
if (demoApi.exportDemo) {
  const result = await demoApi.exportDemo()
}

// 批量删除
if (demoApi.bulkDeleteDemo) {
  await demoApi.bulkDeleteDemo([1, 2, 3])
}
```

## 最佳实践

1. **预加载常用 API**: 在应用启动时预加载经常使用的 API 接口
2. **错误处理**: 总是包含适当的错误处理逻辑
3. **类型安全**: 利用 TypeScript 的类型检查确保代码安全
4. **缓存管理**: 在内存压力大时适当清理缓存
5. **命名规范**: 确保 API 模块遵循标准的命名规范

## 故障排除

### 常见问题

1. **模块加载失败**
   - 检查模块路径是否正确
   - 确认 API 模块文件是否存在
   - 验证导出的函数名称是否符合规范

2. **类型错误**
   - 确保导入了正确的类型定义
   - 检查 QueryParams 的结构是否匹配

3. **缓存问题**
   - 使用 `clearApiCache()` 清除缓存
   - 检查是否有内存泄漏

### 调试技巧

```typescript
// 启用调试模式查看加载过程
console.log('已缓存的APIs:', ApiService.getCachedApis())
console.log('demo/demo是否已缓存:', ApiService.isApiCached('demo/demo'))
```
