import { ReceivableFormData } from '@/views/fin/receivable/config';
import alovaInstance from '../index';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';
import { AddCollectionForm, WriteOffForm } from '@/views/fin/collection/config';

/**
 * 创建收款数据
 * @param params 收款数据
 * @returns 收款数据
 */
const createCollection = (params: AddCollectionForm) => {
    return alovaInstance.Post<any>('/v1/fin/collection/create', params);
};

/**
 * 更新收款数据
 * @param params 收款数据
 * @returns 收款数据
 */
const updateCollection = (params: AddCollectionForm) => {
    return alovaInstance.Put<any>('/v1/fin/collection/update', params);
};

/**
 * 通过应收账款列表页选中数据后收款冲销生成收款数据
 * @param params 收款数据
 * @returns 收款数据
 */
const collectionElimination = (params: ReceivableFormData) => {
    return alovaInstance.Post<any>(
        '/v1/fin/collection/collection_elimination',
        params
    );
};

/**
 * 通过收款管理列表页选中数据后冲销生成收款数据
 * @param params 收款数据
 * @returns 收款数据
 */
const writeOffCollection = (params: WriteOffForm) => {
    return alovaInstance.Post<any>('/v1/fin/collection/elimination', params);
};

/**
 * 获取收款数据列表
 * @param params 收款数据
 * @returns 收款数据
 */
const getCollectionList = (params: QueryParams) => {
    return alovaInstance.Post<any>('/v1/fin/collection/query', params);
};

/**
 * 获取收款数据详情
 * @param params 收款数据
 * @returns 收款数据
 */
const getCollectionById = (params: DetailQuery) => {
    return alovaInstance.Get<any>('/v1/fin/collection/get', {
        params,
        cacheFor: 0,
    });
};

/** 获取 metadata */
const getCollectionMetadata = () => {
    return alovaInstance.Get<any>('/v1/fin/collection/get_metadata');
};

/**
 * @description 反审核Collection
 * @param {number} id Collection ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(`/v1/fin/collection/anti_approval`, {
        relation_id: id,
    });
};

export {
    createCollection,
    updateCollection,
    collectionElimination,
    getCollectionList,
    getCollectionById,
    writeOffCollection,
    getCollectionMetadata,
    antiApproval,
};
