import alovaInstance from '@/api';
import type { ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type {
    Payment,
    PaymentCreate,
    PaymentElimination,
    PaymentUpdate,
} from './types';

/**
 * @description 获取Payment元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getPaymentMetadata = () => {
    return alovaInstance.Get<any>('/v1/fin/payment/get_metadata');
};

/**
 * @description 获取Payment列表
 * @param {PaymentQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Payment>>} 返回包含Payment信息的Promise对象
 * @example
 * // 使用示例
 * const paymentList = await getPaymentList({ start: 1, limit: 20 });
 */
const getPaymentList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Payment>>(
        '/v1/fin/payment/query',
        params
    );
};

/**
 * @description 获取Payment详情
 * @param {number} id Payment ID
 * @returns {Promise<Payment>} 返回Payment详情信息
 */
const getPayment = (params: DetailQuery) => {
    return alovaInstance.Get<Payment>('/v1/fin/payment/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * @description 创建Payment
 * @param {PaymentCreate} data 创建数据
 * @returns {Promise<Payment>} 返回创建的Payment信息
 */
const createPayment = (data: PaymentCreate) => {
    return alovaInstance.Post<Payment>('/v1/fin/payment/create', data);
};

/**
 * @description 更新Payment
 * @param {PaymentUpdate} data 更新数据
 * @returns {Promise<Payment>} 返回更新后的Payment信息
 */
const updatePayment = (data: PaymentUpdate) => {
    return alovaInstance.Put<Payment>('/v1/fin/payment/update', data);
};

/**
 * @description 删除Payment
 * @param {number} id Payment ID
 * @returns {Promise<any>} 返回删除结果
 */
const removePayment = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/fin/payment/delete/${id}`);
};

/**
 * @description 批量删除Payment
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeletePayment = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/fin/payment/batch-delete', ids);
};

/**
 * @description 通过应付账款列表页的数据快速付款生成付款数据
 * @param params 付款数据
 * @returns 付款数据
 */
const createPaymentElimination = (params: PaymentElimination) => {
    return alovaInstance.Post<any>(
        '/v1/fin/payment/payment_elimination',
        params
    );
};

/**
 * @description 通过付款列表页的数据进行付款冲销
 * @param params 付款冲销数据
 */
const paymentWriteOff = (params: PaymentElimination) => {
    return alovaInstance.Post<any>('/v1/fin/payment/elimination', params);
};

/** 反审核 */
const paymentAntiApproval = (id: number) => {
    return alovaInstance.Post<any>('/v1/fin/payment/anti_approval', {
        relation_id: id,
    });
};

// /**
//  * @description 导出Payment数据
//  * @param {PaymentQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportPayment = (params?: PaymentQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/fin/payment/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Payment数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importPayment = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/fin/payment/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getPaymentMetadata,
    getPaymentList,
    getPayment,
    createPayment,
    updatePayment,
    removePayment,
    batchDeletePayment,
    createPaymentElimination,
    paymentWriteOff,
    paymentAntiApproval,
    // exportPayment,
    // importPayment,
};
