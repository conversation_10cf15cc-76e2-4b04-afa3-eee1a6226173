import type { ModelBase } from '@/types/core';

/**
 * 单据状态
 */
export enum DocStatus {
    DRAFT = '草稿',
    PENDING_REVIEW = '待审核',
    UNDER_REVIEW = '审核中',
    APPROVED = '已审核',
    REJECTED = '已驳回',
    CANCELLED = '已作废',
}

/**
 * 冲销状态
 */
export enum EliminationStatus {
    WAITING = '待冲销',
    PART = '部分冲销',
    FINISHED = '已冲销',
}

/** 对象类型 */
interface ObjectType {
    name?: string;
    factory_name?: string;
    id?: number;
    code?: string;
}

// Payment 数据接口定义
export interface Payment extends ModelBase {
    id?: number;
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    doc_status?: DocStatus; // 单据状态
    elimination_status?: EliminationStatus; // 冲销状态
    accounting_status?: boolean; // 分录状态
    payment_no?: string; // 付款单号
    payment_date?: string; // 付款日期
    factory_id?: number; // 所属组织
    supplier_id?: number; // 供应商ID
    payment_way_id?: number; // 付款方式
    payment_type_id?: number; // 付款类型
    currency_id?: number; // 币种
    exchange_rate?: number; // 汇率
    base_currency_amount?: number; // 本位币金额
    payment_bank?: string; // 付款银行
    payment_account?: string; // 付款账号
    account_name?: string; // 付款账号名称
    collection_bank?: string; // 收款银行
    collection_account?: string; // 收款账号
    collection_name?: string; // 收款账号名称
    payment_days?: number; // 账期
    payment_amount?: number; // 付款金额
    un_audit_amount?: number; // 未核销金额
    audit_amount?: number; // 核销金额
    notes?: string; // 备注
    factory?: ObjectType; // factory
    currency?: ObjectType; // currency
    supplier?: ObjectType; // supplier
    payment_way?: ObjectType; // payment_way
    payment_type?: ObjectType; // payment_type
}

export interface PaymentCreate extends Omit<Payment, 'id'> {}

export interface PaymentUpdate extends Payment {}

export interface EliminationItem {
    payable_id?: number;
    payed_amount?: number;
    erase_zero_amount?: number;
}
/** 应付账款发起的快速付款 */
export interface PaymentElimination {
    payable_id?: number;
    payment_no?: string;
    payment_date?: string;
    collection_type_id?: number;
    collection_way_id?: number;
    payment_bank?: string;
    payment_account?: string;
    account_name?: string;
    currency_id?: number;
    base_currency_id?: number;
    base_currency_amount?: number;
    exchange_rate?: number;
    payment_amount?: number;
    elimination_items?: EliminationItem[];
}

// API 响应接口定义
