/** 应收账款相关API */
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';
import alovaInstance from '../index';
import { AddReceivableFormData } from '@/views/fin/receivable/add-config';

/** 获取应收账款列表 */
const getReceivableList = async (params: QueryParams) => {
    return alovaInstance.Post<any>('/v1/fin/receivable/query', params);
};

/** 根据ID获取应收账款 */
const getReceivableById = async (params: DetailQuery) => {
    return alovaInstance.Get<any>('/v1/fin/receivable/get', { params });
};

/** 创建应收账款 */
const createReceivable = async (params: AddReceivableFormData) => {
    return alovaInstance.Post<any>('/v1/fin/receivable/create', params);
};

/** 更新应收账款 */
const updateReceivable = async (params: AddReceivableFormData) => {
    return alovaInstance.Put<any>('/v1/fin/receivable/update', params);
};

/** 作废应收账款 */
const invalidReceivable = async (params: { ids: string }) => {
    return alovaInstance.Get<any>('/v1/fin/receivable/cancel', { params });
};

/** 获取 metadata */
const getReceivableMetadata = async () => {
    return alovaInstance.Get<any>('/v1/fin/receivable/get_metadata');
};

/** 销售订单/发货生成应收账款 */
const saleOrderToReceivable = async (params: Recordable) => {
    return alovaInstance.Post<any>(
        '/v1/fin/receivable/sale_order_to_receivable',
        params
    );
};

export {
    getReceivableList,
    getReceivableById,
    createReceivable,
    updateReceivable,
    invalidReceivable,
    getReceivableMetadata,
    saleOrderToReceivable,
};
