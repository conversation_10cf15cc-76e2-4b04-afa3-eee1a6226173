/** 应收账款相关API */
import type { QueryParams } from '@/types/api/queryParams';
import alovaInstance from '../index';
import { EntryFormData } from '@/views/fin/accounting-entry/config';
import { CollectionEntryForm } from '@/views/fin/collection/config';
import { PayableEntry } from '@/views/fin/payable/component/config';
import { PaymentEntry } from '@/views/fin/payment/component/config';

/**
 * 生成分录
 */
const createEntry = async (params: EntryFormData) => {
    return alovaInstance.Post<any>('/v1/fin/accounting_entry/create', params);
};

/**
 * 生成应收分录
 */
const createReceivableEntry = async (params: EntryFormData) => {
    return alovaInstance.Post<any>(
        '/v1/fin/accounting_entry/receivable_entry',
        params
    );
};

/**
 * 批量生成应收分录
 */
const createReceivableEntryBatch = async (params: EntryFormData[]) => {
    return alovaInstance.Post<any>(
        '/v1/fin/accounting_entry/receivable_entry_batch',
        params
    );
};

/** 生成应付分录 */
const createPayableEntry = async (params: PayableEntry) => {
    return alovaInstance.Post<any>(
        '/v1/fin/accounting_entry/payable_entry',
        params
    );
};
/** 批量生成应付分录 */
const createPayableEntryBatch = async (params: PayableEntry[]) => {
    return alovaInstance.Post<any>(
        '/v1/fin/accounting_entry/payable_entry_batch',
        params
    );
};

/**
 * 生成收款分录
 */
const createCollectionEntry = async (params: CollectionEntryForm) => {
    return alovaInstance.Post<any>(
        '/v1/fin/accounting_entry/collection_entry',
        params
    );
};

/**
 * 批量生成收款分录
 */
const createCollectionEntryBatch = async (params: EntryFormData[]) => {
    return alovaInstance.Post<any>(
        '/v1/fin/accounting_entry/collection_entry_batch',
        params
    );
};

/** 生成付款分录 */
const createPaymentEntry = async (params: PaymentEntry) => {
    return alovaInstance.Post<any>(
        '/v1/fin/accounting_entry/payment_entry',
        params
    );
};

/** 批量生成付款分录 */
const createPaymentEntryBatch = async (params: PaymentEntry[]) => {
    return alovaInstance.Post<any>(
        '/v1/fin/accounting_entry/payment_entry_batch',
        params
    );
};

/**
 * 获取分录列表
 */
const getAccountingEntryList = async (params: QueryParams) => {
    return alovaInstance.Post<any>('/v1/fin/accounting_entry/query', params);
};

/** 获取 metadata */
const getAccountingEntryMetadata = async () => {
    return alovaInstance.Get<any>('/v1/fin/accounting_entry/get_metadata');
};

export {
    createEntry,
    createReceivableEntry,
    createCollectionEntry,
    createPayableEntry,
    createPayableEntryBatch,
    getAccountingEntryList,
    createReceivableEntryBatch,
    createCollectionEntryBatch,
    getAccountingEntryMetadata,
    createPaymentEntry,
    createPaymentEntryBatch,
};
