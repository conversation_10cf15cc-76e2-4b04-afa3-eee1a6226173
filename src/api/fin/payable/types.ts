import type { ModelBase } from '@/types/core';

/**
 * 付款状态
 */
export enum PayableStatus {
    Pending = '待付款',
    Paid = '已付款',
    Part = '部分付款',
    Overdue = '逾期付款',
    PartOverdue = '部分逾期付款',
    Cancel = '已作废',
}

/** 对象类型 */
interface ObjectType {
    name?: string;
    factory_name?: string;
    id?: number;
    code?: string;
}

/**
 * 应付类型
 */
export enum PayableType {
    FIN_PAYABLE = '应付账单',
    FIN_PAYMENT = '付款单',
    OTHER = '其他',
}

// Payable 数据接口定义
export interface Payable extends ModelBase {
    id: number;
    created_at: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    status: PayableStatus; // 付款状态
    accounting_status: boolean; // 分录状态
    payable_no: string; // 付款单号
    payable_date?: string; // 应付单日期
    source_order_type: PayableType; // 应付类型
    source_order_no?: string; // 采购订单号
    factory_id?: number; // 所属组织
    supplier_id?: number; // 供应商ID
    expected_payment_time?: string; // 预计付款时间
    account_age?: number; // 账龄
    currency_id?: number; // 币种
    base_currency_name?: string; // 本位币
    base_currency_id?: number; // 本位币id
    exchange_rate?: number; // 汇率
    base_currency_amount?: number; // 本位币金额
    payable_amount: number; // 应付金额
    payed_amount: number; // 已付金额
    un_audit_amount: number; // 未核销金额
    audit_amount: number; // 核销金额
    factory?: ObjectType; // factory
    currency?: ObjectType; // currency
    supplier?: ObjectType; // supplier
    unpay_amount?: number; // 未付金额
    notes?: string; // 备注
}

export interface PayableCreate {
    id?: number;
    status?: string;
    accounting_status?: boolean;
    payable_no?: string;
    payable_date?: string | Date;
    source_order_type?: string;
    source_order_no?: string;
    factory_id?: number;
    factory?: ObjectType;
    supplier_id?: number;
    expected_payment_time?: string | Date;
    account_age?: number;
    currency_id?: number;
    base_currency_id?: number;
    base_currency_name?: string;
    exchange_rate?: number;
    base_currency_amount?: number;
    payable_amount?: number;
    payed_amount?: number;
    un_audit_amount?: number;
    audit_amount?: number;
}

export interface PayableUpdate extends PayableCreate {
    id: number;
    create_at: string | Date;
    updated_at?: string | Date;
}

// API 响应接口定义
