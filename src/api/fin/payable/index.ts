import alovaInstance from '@/api';
import type { ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type { Payable, PayableCreate, PayableUpdate } from './types';

/**
 * @description 获取Payable元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getPayableMetadata = () => {
    return alovaInstance.Get<any>('/v1/fin/payable/get_metadata');
};

/**
 * @description 获取Payable列表
 * @param {PayableQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Payable>>} 返回包含Payable信息的Promise对象
 * @example
 * // 使用示例
 * const payableList = await getPayableList({ start: 1, limit: 20 });
 */
const getPayableList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Payable>>(
        '/v1/fin/payable/query',
        params
    );
};

/**
 * @description 获取Payable详情
 * @param {number} id Payable ID
 * @returns {Promise<Payable>} 返回Payable详情信息
 */
const getPayable = (params: DetailQuery) => {
    return alovaInstance.Get<Payable>('/v1/fin/payable/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * @description 创建Payable
 * @param {PayableCreate} data 创建数据
 * @returns {Promise<Payable>} 返回创建的Payable信息
 */
const createPayable = (data: PayableCreate) => {
    return alovaInstance.Post<Payable>('/v1/fin/payable/create', data);
};

/**
 * @description 更新Payable
 * @param {PayableUpdate} data 更新数据
 * @returns {Promise<Payable>} 返回更新后的Payable信息
 */
const updatePayable = (data: PayableUpdate) => {
    return alovaInstance.Put<Payable>('/v1/fin/payable/update', data);
};

/**
 * @description 删除Payable
 * @param {number} id Payable ID
 * @returns {Promise<any>} 返回删除结果
 */
const removePayable = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/fin/payable/delete/${id}`);
};

/**
 * @description 批量删除Payable
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeletePayable = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/fin/payable/batch-delete', ids);
};

/** 作废 */
const cancelPayable = (params: { ids: string }) => {
    return alovaInstance.Post<any>('/v1/fin/payable/cancel', { params });
};

// /**
//  * @description 导出Payable数据
//  * @param {PayableQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportPayable = (params?: PayableQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/fin/payable/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Payable数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importPayable = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/fin/payable/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getPayableMetadata,
    getPayableList,
    getPayable,
    createPayable,
    updatePayable,
    removePayable,
    batchDeletePayable,
    cancelPayable,
    // exportPayable,
    // importPayable,
};
