import { SubjectFormData } from '@/views/fin/subject/config';
import alovaInstance from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/**
 * 获取科目列表
 * @param params 查询参数
 * @returns 科目列表
 */
const getSubjectList = (params: QueryParams) => {
    return alovaInstance.Post<any>('/v1/fin/subject/query', params);
};

/**
 * 获取科目树形数据
 * @param params 查询参数
 * @returns 科目树形数据
 */
const getSubjectTree = (params: {
    root_id: number | null;
    max_depth: number;
}) => {
    return alovaInstance.Get<any>('/v1/fin/subject/tree', { params });
};

/**
 * 创建科目
 * @param params 创建参数
 * @returns 创建结果
 */
const createSubject = (params: SubjectFormData) => {
    return alovaInstance.Post<any>('/v1/fin/subject/create', params);
};

/**
 * 更新科目
 * @param params 更新参数
 * @returns 更新结果
 */
const updateSubject = (params: SubjectFormData) => {
    return alovaInstance.Put<any>('/v1/fin/subject/update', params);
};

/**
 * 删除科目
 * @param params 删除参数
 * @returns 删除结果
 */
const deleteSubject = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/fin/subject/delete/${id}`);
};

/** 获取 metadata */
const getSubjectMetadata = () => {
    return alovaInstance.Get<any>('/v1/fin/subject/get_metadata');
};

export {
    getSubjectList,
    getSubjectTree,
    createSubject,
    updateSubject,
    deleteSubject,
    getSubjectMetadata,
};
