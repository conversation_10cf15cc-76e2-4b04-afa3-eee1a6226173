/** 财务账户管理Api */
import type {
    DetailQuery,
    QueryParams,
    QueryResponse,
} from '@/types/api/queryParams';
import alovaInstance from '../index';
import { AccountForm, TableRowItem } from '@/views/fin/account-manage/config';

/**
 * 获取财务账户列表
 */
const getAccountList = async (params: QueryParams) => {
    return alovaInstance.Post<QueryResponse<TableRowItem>>(
        '/v1/fin/financial_account/query',
        params
    );
};

/**
 * 添加财务账户
 */
const addAccount = async (params: AccountForm) => {
    return alovaInstance.Post<TableRowItem>(
        '/v1/fin/financial_account/create',
        params
    );
};

/**
 * 编辑财务账户
 */
const editAccount = async (params: AccountForm) => {
    return alovaInstance.Put<TableRowItem>(
        '/v1/fin/financial_account/update',
        params
    );
};

/**
 * 删除财务账户
 */
const deleteAccount = async (id: number) => {
    return alovaInstance.Delete(`/v1/fin/financial_account/delete/${id}`);
};

/**
 * 批量删除财务账户
 */
const deleteAccountBatch = async (ids: number[]) => {
    return alovaInstance.Delete(`/v1/fin/financial_account/bulk_delete?`, ids);
};

/**
 * 获取财务账户详情
 */
const getAccountDetail = async (params: DetailQuery) => {
    return alovaInstance.Get<TableRowItem>('/v1/fin/financial_account/get', {
        params,
    });
};

/** 获取 metadata */
const getAccountMetadata = async () => {
    return alovaInstance.Get<any>('/v1/fin/financial_account/get_metadata');
};

export {
    getAccountList,
    addAccount,
    editAccount,
    deleteAccount,
    deleteAccountBatch,
    getAccountDetail,
    getAccountMetadata,
};
