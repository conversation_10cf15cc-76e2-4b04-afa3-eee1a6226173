import { useRequest } from 'alova/client';
import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';
// import type { DefaultCurrent, CurrencyItem } from "@/views/hr/currency/config";

const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/hr/shop_calendar/get_metadata`
    );
};

const getMetaData: (url: string) => Promise<Recordable> = (url: string) => {
    return new Promise((resolve, reject) => {
        const { onSuccess, onError } = useRequest(getPageMetaData(), {
            immediate: true, // 立即发送请求
        });
        onSuccess((res) => {
            resolve(res.data);
        });
        onError((err) => {
            reject(err);
        });
    });
};

/**
 * @description 创建工厂日历
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const createCalendar = (params: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>(
        '/v1/hr/shop_calendar/create',
        params
    );
};

/**
 * @description 批量创建工厂日历
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const bulkCreateCalendar = (params: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>(
        '/v1/hr/shop_calendar/bulk_create',
        params
    );
};

/**
 * @description 更新工厂日历
 * @param {Partial<Recordable>} data
 * @returns {Promise<Recordable>}
 */
const updateCalendar = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/hr/shop_calendar/update', data);
};

/**
 * @description 删除工厂日历
 * @param {Partial<CurrencyItem>}
 * @returns {Promise<Recordable>}
 */
const deleteCalendar = (id?: string | number) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/hr/shop_calendar/delete/${id}`
    );
};

/**
 * @description 批量删除工厂日历
 * @param {Partial<string>}
 * @returns {Promise<Recordable>}
 */
const bulkDeleteCalendar = (ids: string[]) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/hr/shop_calendar/bulk_delete`,
        ids
    );
};

/**
 * @description 查询工厂日历
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getCalendarList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/hr/shop_calendar/query', data);
};

/**
 * @description 查询工厂日历
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getCalendar = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Recordable>(`/v1/hr/shop_calendar/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 设置启用状态
 * @param {Partial<QueryParams>} params
 * @returns {Promise<Recordable>}
 * @example
 */
const setEnableStatus = (params: Recordable) => {
    return alovaInstance.Put<Recordable>(
        `/v1/hr/shop_calendar/set_enable_status`,
        {
            params,
        }
    );
};

export {
    getPageMetaData,
    getMetaData,
    createCalendar,
    bulkCreateCalendar,
    updateCalendar,
    deleteCalendar,
    bulkDeleteCalendar,
    getCalendarList,
    getCalendar,
    setEnableStatus,
};
