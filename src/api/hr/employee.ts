import alovaInstance, { ResponseSucess } from '@/api';
import { ResponseListModel } from '@/types/core';
import { Employee } from '@/views/hr/employee/type';
import { useRequest } from 'alova/client';

const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/hr/employee/get_metadata`
    );
};

const getMetaData: (url: string) => Promise<Recordable> = (url: string) => {
    return new Promise((resolve, reject) => {
        const { onSuccess, onError } = useRequest(getPageMetaData(), {
            immediate: true, // 立即发送请求
        });
        onSuccess((res) => {
            resolve(res.data);
        });
        onError((err) => {
            reject(err);
        });
    });
};

function getEmployeeList(params: any) {
    return alovaInstance.Post<ResponseListModel<Employee>>(
        '/v1/hr/employee/query',
        params
    );
}

function createEmployee(params: any) {
    return alovaInstance.Post('/v1/hr/employee/create', params);
}

function updateEmployee(params: any) {
    return alovaInstance.Put('/v1/hr/employee/update', params);
}

function deleteEmployee(id: number) {
    return alovaInstance.Delete(`/v1/hr/employee/delete/${id}`);
}

function getEmployeeDetail(id: number) {
    return alovaInstance.Get(`/v1/hr/employee/get?id=${id}`, {
        cacheFor: 0,
    });
}

function deleteEmployeeBulk(ids: number[]) {
    return alovaInstance.Delete(`/v1/hr/employee/bulk_delete`, ids);
}

export {
    getPageMetaData,
    getEmployeeList,
    createEmployee,
    deleteEmployee,
    updateEmployee,
    getEmployeeDetail,
    deleteEmployeeBulk,
    getMetaData,
};
