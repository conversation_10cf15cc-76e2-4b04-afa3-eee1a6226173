import type { TreeModelBase } from '@/types/core';
import alovaInstance from '@/api';
import type { Method } from 'alova';
/**************************************************
 *接口定义
 ***************************************************/

interface Dept extends TreeModelBase {
    name: string;
    code: string;
    sort_order: number;
    notes?: string;
}

interface DeptCreate extends Dept {}

interface DeptUpdate extends Dept {
    id: number;
}

/**************************************************
 *方法定义
 ***************************************************/

/**
 * 获取部门列表
 * @param params 查询参数
 * @returns 部门列表
 */

export const getPageMetaData = () => {
    return alovaInstance.Get('/v1/hr/dept/get_metadata');
};

export const getDeptList = (params: any): Method => {
    return alovaInstance.Post('/v1/hr/dept/query', params);
};

/**
 * 创建部门
 * @param data 部门数据
 * @returns 创建结果
 */
export const createDept = (data: any): Method => {
    return alovaInstance.Post('/v1/hr/dept/create', data);
};

/**
 * 更新部门
 * @param data 部门数据
 * @returns 更新结果
 */
export const updateDept = (data: any): Method => {
    return alovaInstance.Put(`/v1/hr/dept/update`, data);
};

/**
 * 删除部门
 * @param id 部门ID
 * @returns 删除结果
 */
export const deleteDept = (id: number): Method => {
    return alovaInstance.Delete(`/v1/hr/dept/delete/${id}`);
};

/**
 * 获取部门详情
 * @param id 部门ID
 * @returns 部门详情
 */
export const getDeptDetail = (id: number): Method => {
    return alovaInstance.Get(`/v1/hr/dept/get?id=${id}`);
};

/**
 * 获取部门树
 * @param {Partial<any>} data - 部门
 * @returns {Promise<any>} 返回包含部门的Promise对象
 * @example
 * // 使用示例
 * const materialTypeTree = await getProductTypeTree(data);
 */
export const getDeptTree = () => {
    return alovaInstance.Get<any>('/v1/hr/dept/tree');
};

/**************************************************
 *导出接口和方法
 ***************************************************/

export type { Dept, DeptCreate, DeptUpdate };
