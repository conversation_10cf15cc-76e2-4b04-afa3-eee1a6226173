import type { ModelBase, ResponseListModel } from '@/types/core';
import alovaInstance from '@/api';

/**************************************************
 *接口定义
 ***************************************************/

/**
 * RolePermission，角色-权限关联表
 */
interface RolePermission {
    role_id: number;
    permission_id: number;
}

/**************************************************
 *方法定义
 ***************************************************/
const createRolePermission = (data: RolePermission[]) => {
    return alovaInstance.Post<RolePermission[]>(
        '/v1/sys/role_permission/bulk_create',
        data
    );
};

const queryRolePermission = (data: any) => {
    return alovaInstance.Post<ResponseListModel<RolePermission>>(
        '/v1/sys/role_permission/query',
        data
    );
};

const deleteRolePermission = (data: any) => {
    return alovaInstance.Delete('/v1/sys/role_permission/bulk_delete', data);
};

/**************************************************
 *导出接口和方法
 ***************************************************/

export type { RolePermission };
export { createRolePermission, queryRolePermission, deleteRolePermission };
