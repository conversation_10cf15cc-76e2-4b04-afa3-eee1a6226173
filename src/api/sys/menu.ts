import alovaInstance from '@/api';
import type { ModelBase } from '@/types/core';

/**************************************************
 *接口定义
 ***************************************************/
interface MenuItem extends ModelBase {
    id?: number;
    name: string;
    route_icon?: string | null;
    type: 'menu' | 'api' | 'group';
    sort_order?: number;
    perm_code?: string | null;
    route_path?: string | null;
    route_component?: string | null;
    parent_id?: number | null;
    children?: MenuItem[];
}

// 接口-菜单创建
type MenuCreate = Omit<MenuItem, 'id'>;

//接口-菜单更新
interface MenuUpdate extends MenuItem {
    id: number;
}

/**************************************************
 *方法定义
 ***************************************************/

/**
 * @description 获取菜单列表
 * @returns {Promise<MenuItem[]>} 返回包含菜单信息的Promise对象
 */
function getMenuList() {
    return alovaInstance.Get<MenuItem[]>('/v1/sys/menu/list');
}

/**
 * @description 创建新菜单
 * @param {MenuCreate} data - 菜单信息
 * @returns {Promise<MenuItem>} 返回包含创建菜单信息的Promise对象
 */
async function createMenu(data: MenuCreate) {
    return alovaInstance.Post<MenuItem>('/v1/sys/menu/create', data);
}

/**
 * @description 删除菜单
 * @param {number} id - 菜单ID
 * @returns {Promise<any>} 返回包含删除结果的Promise对象
 */
async function deleteMenu(id: number) {
    return alovaInstance.Delete<any>('/v1/sys/menu/delete', { id });
}

/**
 * @description 更新菜单信息
 * @param {MenuUpdate} data - 菜单信息
 * @returns {Promise<MenuItem>} 返回包含更新菜单信息的Promise对象
 */
async function updateMenu(data: MenuUpdate) {
    return alovaInstance.Put<MenuItem>('/v1/sys/menu/update', data);
}

/**************************************************
 *导出接口和方法
 ***************************************************/

export type { MenuItem, MenuCreate, MenuUpdate };

export { getMenuList, createMenu, deleteMenu, updateMenu };
