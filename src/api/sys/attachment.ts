import alovaInstance from '../index';
import type { QueryParams } from '@/types/api/queryParams';
import type { Method } from 'alova';

/** 上传文件返回数据 */
export interface UploadFileResponse {
    updated_at?: null;
    created_by?: number;
    id?: number;
    created_at?: string;
    file_path?: string;
    file_size?: number;
    md5?: string;
    related_id?: number;
    updated_by?: null;
    name?: string;
    file_name?: string;
    file_type?: string;
    related_model?: string;
    file?: File | Blob;
}

/**
 * @description 根据关联模型和关联ID获取附件列表
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const getByRelation = (params: {
    related_model?: string;
    related_ids?: number[];
}) => {
    return alovaInstance.Post<UploadFileResponse[]>(
        '/v1/sys/attachment/get_by_relation',
        params
    );
};

/**
 * @description 上传附件
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const uploadAttachment = (
    params: FormData,
    related_id: Number,
    related_model: String
) => {
    return alovaInstance.Post<UploadFileResponse>(
        '/v1/sys/attachment/upload?related_id=' +
            related_id +
            '&related_model=' +
            related_model,
        params
    );
};

/**
 * 删除文件
 * @param id 附件id
 * @returns 删除结果
 */
const deleteAttachment = (id: number): Method => {
    return alovaInstance.Delete(
        `/v1/sys/attachment/delete?attachment_id=${id}`
    );
};

export { getByRelation, uploadAttachment, deleteAttachment };
