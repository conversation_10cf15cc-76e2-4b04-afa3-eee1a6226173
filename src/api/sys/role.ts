import alovaInstance from '@/api';
import { type ResponseSucess } from '@/api';

/**************************************************
 *接口定义
 ***************************************************/

interface Role {
    code: string;
    name: string;
}

interface RoleCreate extends Role {}

interface RoleUpdate extends Role {
    id: string;
}

/**************************************************
 *方法定义
 ***************************************************/

export const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/sys/role/get_metadata`
    );
};

export const getRoleList = (params: any) => {
    return alovaInstance.Post<any>('/v1/sys/role/query', params);
};

export const createRole = (params: any) => {
    return alovaInstance.Post<any>('/v1/sys/role/create', params);
};

export const updateRole = (params: any) => {
    return alovaInstance.Put<any>('/v1/sys/role/update', params);
};

export const deleteRole = (id: string) => {
    return alovaInstance.Delete<any>('/v1/sys/role/delete/' + id);
};

/** 设置角色状态 */
export const setRoleStatus = (data: { ids: string; status: boolean }) => {
    return alovaInstance.Put<any>('/v1/sys/role/set_enable_status', {
        params: data,
    });
};

/**************************************************
 *导出接口和方法
 ***************************************************/

export type { Role, RoleCreate, RoleUpdate };
