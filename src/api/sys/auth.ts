import alovaInstance from '@/api/index';
import type { UserGetWithRoles } from '@/api/sys/user';

/**************************************************
 *接口定义
 ***************************************************/

/**
 * @description 登录参数接口定义
 * @interface LoginParams
 * @property {string} username - 用户名
 * @property {string} password - 密码
 */
interface LoginParams {
    username: string;
    password: string;
}

/**
 * @description 登录响应接口定义
 * @interface GetLoginToken
 * @property {string} access_token - 访问令牌
 * @property {string} access_token_type - 访问令牌类型
 * @property {string | Date} access_token_expire_time - 访问令牌过期时间
 * @property {UserGetWithRoles} user - 用户信息
 */
interface GetLoginToken {
    access_token: string;
    access_token_type: string;
    access_token_expire_time: string | Date;
    user: UserGetWithRoles;
}

/**************************************************
 * 方法定义
 ***************************************************/

/**
 * @description 登录方法
 * @param {LoginParams} params - 登录参数
 * @returns {Promise<any>} 返回包含登录信息的Promise对象
 * @example
 * // 使用示例
 * const loginResult = await loginApi({ username: "admin", password: "123456" });
 */
async function loginApi(params: LoginParams) {
    return alovaInstance.Post<any>('/v1/sys/user/login', params);
}

/**
 * @description 刷新accessToken方法
 * @returns {Promise<any>} 返回包含刷新accessToken信息的Promise对象
 * @example
 * // 使用示例
 * const refreshTokenResult = await refreshTokenApi();
 */
async function refreshTokenApi() {
    return alovaInstance.Post<any>('/v1/sys/user/refresh');
}

/**
 * @description 退出登录方法
 * @returns {Promise<any>} 返回包含退出登录信息的Promise对象
 * @example
 * // 使用示例
 * const logoutResult = await logoutApi();
 */
async function logoutApi() {
    return alovaInstance.Post<any>('/v1/sys/user/logout');
}

/**************************************************
 * 导出接口和方法
 ***************************************************/

export type { LoginParams, GetLoginToken };

export { loginApi, refreshTokenApi, logoutApi };
