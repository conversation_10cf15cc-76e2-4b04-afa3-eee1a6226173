import alovaInstance from '@/api/index';

import type { ResponseListModel, TreeModelBase } from '@/types/core';
import type { PermissionType } from '@/types/enum';
import type { ResponseSucess } from '@/api';

/**************************************************
 *接口定义
 ***************************************************/
/**
 * Permission，权限表
 */
interface Permission extends TreeModelBase {
    api_method?: null | string; // Api Method，HTTP方法
    api_path?: null | string; // Api Path，API路径
    code: string; // Code，权限编码/路由名称
    notes?: null | string; // Description
    id: number | null; // Id，主键ID
    name: string; // Name，权限名称
    perm_code?: null | string; // Perm Code，权限编码
    route_always_show?: boolean; // Route Always Show，是否总是显示根路由
    route_component?: null | string; // Route Component，前端组件路径
    route_hidden?: boolean; // Route Hidden，是否在菜单中隐藏
    route_icon?: null | string; // Route Icon，路由图标
    route_keep_alive?: boolean; // Route Keep Alive，是否缓存该路由
    route_name?: null | string; // Route Name，路由名称
    route_path?: null | string; // Route Path，前端路由路径
    route_redirect?: null | string; // Route Redirect，路由重定向路径
    route_title?: null | string; // Route Title，路由标题
    sort_order?: number; // Sort Order，排序
    type: PermissionType; // PermissionType，权限类型
}

interface PermissionCreate extends Permission {
    id: number | null;
}

interface PermissionUpdate extends Permission {
    id: number;
}

interface PermissionTree extends Permission {
    children: PermissionTree[];
}

/**************************************************
 *方法定义
 ***************************************************/

const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/sys/permission/get_metadata`
    );
};

const permissionGet = (data: any) => {
    return alovaInstance.Post<ResponseListModel<Permission>>(
        '/v1/sys/permission/query',
        data
    );
};

const permissionGetTree = (rootId?: number | null, maxDepth?: number) => {
    return alovaInstance.Get<PermissionTree[]>('/v1/sys/permission/tree', {
        params: {
            rootId,
            maxDepth,
        },
        cacheFor: 0,
    });
};

// /api/v1/sys/permission/siblings/{node_id}
async function createPermission(data: PermissionCreate) {
    return alovaInstance.Post<Permission>('/v1/sys/permission/create', data);
}

// /api/v1/sys/permission/ancestors/{node_id}
async function updatePermission(data: PermissionCreate) {
    return alovaInstance.Put<Permission>('/v1/sys/permission/update', data);
}

const deletePermission = (id: number) => {
    return alovaInstance.Delete<Permission>('/v1/sys/permission/delete/' + id);
};

/**************************************************
 *导出接口和方法
 ***************************************************/

export type { Permission, PermissionCreate, PermissionUpdate, PermissionTree };

export {
    permissionGet,
    permissionGetTree,
    createPermission,
    updatePermission,
    deletePermission,
    getPageMetaData,
};
