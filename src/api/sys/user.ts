import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel, ResponseModel } from '@/types/core';
import type { UserEmpType, UserStatus } from '@/types/enum';
import type { Role } from './role';

/**************************************************
 *接口定义
 ***************************************************/
interface UserInfo extends ModelBase {
    avatar?: null | string;
    dept_id?: number | null;
    email?: null | string;
    emp_type?: UserEmpType;
    is_multi_login?: boolean;
    is_superuser?: boolean;
    is_user?: boolean;
    last_login?: Date | null;
    name: string;
    phone?: null | string;
    status?: UserStatus;
    username?: null | string;
    home_path: string;
    uuid?: string;
}

//接口-用户创建
interface UserCreate extends UserInfo { }

//接口-用户更新
interface UserUpdate extends UserInfo {
    id: number;
}

//接口-用户获取，带角色
interface UserGetWithRoles extends UserInfo {
    id: number;
    roles: Role[];
}

interface AllUsersName extends ModelBase {
    id: number;
    username: string;
}

const getAllUsersName = () => {
    return alovaInstance.Get<AllUsersName[]>('/v1/sys/user/all_users');
};

/**************************************************
 *方法定义
 ***************************************************/

const getPageMetaData = () => {
    return alovaInstance.Get<any>('/v1/sys/user/get_metadata');
};
/**
 * @description 获取当前登录用户的个人信息
 * @returns {Promise<any>} 返回包含用户信息的Promise对象
 * @example
 * // 使用示例
 * const userInfo = await getMeApi();
 */
function getMeApi() {
    console.log('调用getMeApi');
    return alovaInstance.Get<any>('/v1/sys/user/me', {
        cacheFor: 0,
    });
}

/**
 * @description 获取用户列表
 * @returns {Promise<UserInfo[]>} 返回包含用户信息的Promise对象
 * @example
 * // 使用示例
 * const userList = await getUserList();
 */
function getUserList(params: any) {
    return alovaInstance.Post<ResponseListModel<UserInfo>>(
        '/v1/sys/user/query',
        params
    );
}

/**
 * @description 创建新用户
 * @param {Partial<UserInfo>} data - 用户信息
 * @returns {Promise<UserInfo>} 返回包含创建用户信息的Promise对象
 * @example
 * // 使用示例
 * const newUser = await createUser({ name: "新用户", email: "<EMAIL>" });
 */
const createUser = (data: any) => {
    return alovaInstance.Post<UserInfo>('/v1/sys/user/create', data);
};

/**
 * @description 删除用户
 * @param {number} id - 用户ID
 * @returns {Promise<UserInfo>} 返回包含删除用户信息的Promise对象
 * @example
 * // 使用示例
 * const deletedUser = await deleteUser(123);
 */
const deleteUser = (id: number) => {
    return alovaInstance.Delete<UserInfo>('/v1/sys/user/delete/' + id);
};

/**
 * @description 更新用户信息
 * @param {Partial<UserInfo>} data - 用户信息
 * @returns {Promise<UserInfo>} 返回包含更新用户信息的Promise对象
 * @example
 * // 使用示例
 * const updatedUser = await updateUser({ id: 123, name: "更新用户" });
 */
const updateUser = (data: Partial<UserInfo>) => {
    return alovaInstance.Put<UserInfo>('/v1/sys/user/update', data);
};

/**
 * @description 更新用户信息
 * @param {Partial<UserInfo>} data - 用户信息
 * @returns {Promise<UserInfo>} 返回包含更新用户信息的Promise对象
 * @example
 * // 使用示例
 * const updatedUser = await updateUserInfo({ id: 123, name: "更新用户" });
 */
async function updateUserInfo(data: Partial<UserInfo>) {
    return alovaInstance.Put<UserInfo>('/v1/user/info', data);
}

/**
 * 设置用户状态
 * @param data
 * @returns
 */
const setUserStatus = (data: { ids: string; status: boolean }) => {
    return alovaInstance.Put<any>('/v1/sys/user/set_enable_status', {
        params: data,
    });
};

const setAsUser = (data: any) => {
    return alovaInstance.Post<any>('/v1/sys/user/set_as_user', data);
};

const getSingleUser = (params: { id: string | number; max_depth?: number }) => {
    return alovaInstance.Get<Recordable>('/v1/sys/user/get', {
        params,
        cacheFor: 0,
    });
};

/**************************************************
 *导出接口和方法
 ***************************************************/

export type {
    UserInfo,
    UserCreate,
    UserUpdate,
    UserEmpType,
    UserStatus,
    UserGetWithRoles,
    AllUsersName,
    Role,
};
export {
    getMeApi,
    getUserList,
    createUser,
    deleteUser,
    updateUser,
    updateUserInfo,
    getPageMetaData,
    setAsUser,
    getAllUsersName,
    setUserStatus,
    getSingleUser,
};
