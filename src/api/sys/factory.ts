import alovaInstance from '@/api/index';
import type { QueryParams } from '@/types/api/queryParams';
import { ResponseListModel } from '@/types/core';
import { Factory } from '@visactor/vtable/es/vrender';
/**************************************************
 *方法定义
 ***************************************************/

/**
 * 获取工厂信息列表
 * @param params 查询参数
 * @returns 工厂信息列表
 */
const getFactoryList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<ResponseListModel<any>>(
        '/v1/sys/factory/query',
        data
    );
};

/**
 * 根据id获取工厂信息
 * @param params  查询参数
 * @returns 工厂信息
 */
const getFactoryById = (params: { id: number; max_depth: number }) => {
    return alovaInstance.Get('/v1/sys/factory/get', { params });
};

export { getFactoryList, getFactoryById };
