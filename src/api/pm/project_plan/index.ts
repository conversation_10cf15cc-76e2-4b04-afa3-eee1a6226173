import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
  Project_plan,
  Project_planCreate,
  Project_planUpdate,
} from './types';

/**
 * @description 获取Project_plan元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getProject_planMetadata = () => {
  return alovaInstance.Get<any>('/v1/pm/project_plan/get_metadata');
};

/**
 * @description 获取Project_plan列表
 * @param {Project_planQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Project_plan>>} 返回包含Project_plan信息的Promise对象
 * @example
 * // 使用示例
 * const project_planList = await getProject_planList({ start: 1, limit: 20 });
 */
const getProject_planList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<Project_plan>>('/v1/pm/project_plan/query', params);
};

/**
 * @description 获取Project_plan详情
 * @param {number} id Project_plan ID
 * @returns {Promise<Project_plan>} 返回Project_plan详情信息
 */
const getProject_plan = (id?: number) => {
  const url = id ? `/v1/pm/project_plan/get/${id}` : '/v1/pm/project_plan/get';
  return alovaInstance.Get<Project_plan>(url);
};

/**
 * @description 创建Project_plan
 * @param {Project_planCreate} data 创建数据
 * @returns {Promise<Project_plan>} 返回创建的Project_plan信息
 */
const createProject_plan = (data: Project_planCreate) => {
  return alovaInstance.Post<Project_plan>('/v1/pm/project_plan/create', data);
};

/**
 * @description 更新Project_plan
 * @param {Project_planUpdate} data 更新数据
 * @returns {Promise<Project_plan>} 返回更新后的Project_plan信息
 */
const updateProject_plan = (data: Project_planUpdate) => {
  return alovaInstance.Put<Project_plan>('/v1/pm/project_plan/update', data);
};

/**
 * @description 删除Project_plan
 * @param {number} id Project_plan ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeProject_plan = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/pm/project_plan/delete/${id}`);
};

/**
 * @description 批量删除Project_plan
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteProject_plan = (ids: number[]) => {
  return alovaInstance.Post<any>('/v1/pm/project_plan/batch-delete', { ids });
};

// /**
//  * @description 导出Project_plan数据
//  * @param {Project_planQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportProject_plan = (params?: Project_planQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/pm/project_plan/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Project_plan数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importProject_plan = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/pm/project_plan/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getProject_planMetadata,
  getProject_planList,
  getProject_plan,
  createProject_plan,
  updateProject_plan,
  removeProject_plan,
  batchDeleteProject_plan,
  // exportProject_plan,
  // importProject_plan,
};