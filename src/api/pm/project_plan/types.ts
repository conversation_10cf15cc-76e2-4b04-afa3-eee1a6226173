import type { ModelBase } from "@/types/core"

/**
 * 方案状态
 */
export enum DocStatus {
  DRAFT = '草稿',
  PENDING_REVIEW = '待审核',
  UNDER_REVIEW = '审核中',
  APPROVED = '已审核',
  REJECTED = '已驳回',
  CANCELLED = '已作废'
}

// Project_plan 数据接口定义
export interface Project_plan extends ModelBase {
  id: number;
  version: number; // 乐观锁版本号
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  project_id?: number; // 项目ID
  designer_id?: number; // 设计师
  doc_status?: DocStatus; // 方案状态
  project?: Project; // project
  designer?: Employee; // designer
  items: ProjectPlanItem[]; // items
}

export interface Project_planCreate extends Omit<Project_plan, 'id'> {

}

export interface Project_planUpdate extends Project_plan {

}

// API 响应接口定义
