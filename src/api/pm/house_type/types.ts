import type { ModelBase } from '@/types/core';

interface DataType {
    id?: number;
    name?: string;
}

// HouseType 数据接口定义
export interface HouseType extends ModelBase {
    id?: number;
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    project_id?: number; // 项目ID
    name?: string; // 户型名称
    actual_area?: number; // 总面积
    type_id?: number; // 户型id
    notes?: string; // 户型描述
    type?: DataType; // type
    project?: DataType; // project
}

export interface HouseTypeCreate extends Omit<HouseType, 'id'> {}

export interface HouseTypeUpdate extends HouseType {}

// API 响应接口定义
