import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
  HouseType,
  HouseTypeCreate,
  HouseTypeUpdate,
} from './types';

/**
 * @description 获取HouseType元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getHouseTypeMetadata = () => {
  return alovaInstance.Get<any>('/v1/pm/house_type/get_metadata');
};

/**
 * @description 获取HouseType列表
 * @param {HouseTypeQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<HouseType>>} 返回包含HouseType信息的Promise对象
 * @example
 * // 使用示例
 * const houseTypeList = await getHouseTypeList({ start: 1, limit: 20 });
 */
const getHouseTypeList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<HouseType>>('/v1/pm/house_type/query', params);
};

/**
 * @description 获取HouseType详情
 * @param {number} id HouseType ID
 * @returns {Promise<HouseType>} 返回HouseType详情信息
 */
const getHouseType = (id?: number) => {
  const url = id ? `/v1/pm/house_type/get/${id}` : '/v1/pm/house_type/get';
  return alovaInstance.Get<HouseType>(url);
};

/**
 * @description 创建HouseType
 * @param {HouseTypeCreate} data 创建数据
 * @returns {Promise<HouseType>} 返回创建的HouseType信息
 */
const createHouseType = (data: HouseTypeCreate) => {
  return alovaInstance.Post<HouseType>('/v1/pm/house_type/create', data);
};

/**
 * @description 更新HouseType
 * @param {HouseTypeUpdate} data 更新数据
 * @returns {Promise<HouseType>} 返回更新后的HouseType信息
 */
const updateHouseType = (data: HouseTypeUpdate) => {
  return alovaInstance.Put<HouseType>('/v1/pm/house_type/update', data);
};

/**
 * @description 删除HouseType
 * @param {number} id HouseType ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeHouseType = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/pm/house_type/delete/${id}`);
};

/**
 * @description 批量删除HouseType
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteHouseType = (ids: number[]) => {
  return alovaInstance.Delete<any>('/v1/pm/house_type/batch-delete', { ids });
};

// /**
//  * @description 导出HouseType数据
//  * @param {HouseTypeQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportHouseType = (params?: HouseTypeQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/pm/house_type/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入HouseType数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importHouseType = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/pm/house_type/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getHouseTypeMetadata,
  getHouseTypeList,
  getHouseType,
  createHouseType,
  updateHouseType,
  removeHouseType,
  batchDeleteHouseType,
  // exportHouseType,
  // importHouseType,
};