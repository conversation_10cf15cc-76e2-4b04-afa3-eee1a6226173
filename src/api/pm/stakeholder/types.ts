import type { ModelBase } from '@/types/core';

interface ObjectType {
    id?: number;
    name?: string;
    factory_name?: number;
}
// stakeholder 数据接口定义
export interface stakeholder extends ModelBase {
    id: number;
    created_at: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    customer_id?: number; // 客户
    project_role?: string; // 项目角色
    company?: string; // 公司
    position?: string; // 职位
    influence?: string; // 影响力
    company_address?: string; // 公司地址
    contact?: string; // 联系人
    phone?: string; // 联系电话
    mailbox?: string; // 邮箱
    fax?: string; // 传真
    notes?: string; // 备注
    customer?: ObjectType; // customer
}

export interface stakeholderCreate extends Omit<stakeholder, 'id'> {}

export interface stakeholderUpdate extends stakeholder {}

// API 响应接口定义
