import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
  stakeholder,
  stakeholderCreate,
  stakeholderUpdate,
} from './types';

/**
 * @description 获取stakeholder元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getPageMetaData = () => {
  return alovaInstance.Get<any>('/v1/pm/stakeholder/get_metadata');
};

/**
 * @description 获取stakeholder列表
 * @param {stakeholderQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<stakeholder>>} 返回包含stakeholder信息的Promise对象
 * @example
 * // 使用示例
 * const stakeholderList = await getstakeholderList({ start: 1, limit: 20 });
 */
const getStakeholderList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<stakeholder>>('/v1/pm/stakeholder/query', params);
};

/**
 * @description 获取stakeholder详情
 * @param {number} id stakeholder ID
 * @returns {Promise<stakeholder>} 返回stakeholder详情信息
 */
const getSingleStakeholder = (params: { id: string | number, max_depth : 2 }) => {
    return alovaInstance.Get<Recordable>('/v1/pm/stakeholder/get', { params,cacheFor:0 });
};

/**
 * @description 创建stakeholder
 * @param {stakeholderCreate} data 创建数据
 * @returns {Promise<stakeholder>} 返回创建的stakeholder信息
 */
const createStakeholder = (data: stakeholderCreate) => {
  return alovaInstance.Post<stakeholder>('/v1/pm/stakeholder/create', data);
};

/**
 * @description 更新stakeholder
 * @param {stakeholderUpdate} data 更新数据
 * @returns {Promise<stakeholder>} 返回更新后的stakeholder信息
 */
const updateStakeholder = (data: stakeholderUpdate) => {
  return alovaInstance.Put<stakeholder>('/v1/pm/stakeholder/update', data);
};

/**
 * @description 删除stakeholder
 * @param {number} id stakeholder ID
 * @returns {Promise<any>} 返回删除结果
 */
const deleteStakeholder = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/pm/stakeholder/delete/${id}`);
};

/**
 * @description 批量删除stakeholder
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const deleteStakeholderBatch = (data: Partial<any>) => {
  return alovaInstance.Delete<any>('/v1/pm/stakeholder/bulk_delete', data);
};
/**
 * 获取根据客户ID获取干系人列表
 * @param {Partial<Recordable>} id - 客户ID
 * @returns {Promise<Recordable>}干系人列表
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleStakeholder(data);
 */
const getByCustomeStakeholder = (id: number) => {
    return alovaInstance.Get<Recordable>('/v1/pm/stakeholder/get_by_customer_id?customer_id='+id,{cacheFor: 0, });
};

/**
 * 获取获取干系人参与的项目
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含干系人的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getStakeholderProjects(data);
 */
const getStakeholderProjects = (data:Object) => {
    return alovaInstance.Post<Recordable>('/v1/pm/stakeholder/get_projects',data,{cacheFor: 0, });
};
/**
 * 批量创建干系人
 * @param {Partial<Recordable>} data - 干系人
 * @returns {Promise<Recordable>} 返回包含批量创建干系人的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createStakeholderBatch(data);
 * */
const createStakeholderBatch = (data: Partial<any>) => {
    return alovaInstance.Post('/v1/pm/stakeholder/bulk_create', data);
};
/**
 * 获取批量新增，删除，修改的干系人
 * @param {Partial<Recordable>} data - 干系人
 * @returns {Promise<Recordable>} 返回包含单个干系人的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleStakeholder(data);
 * */

const stakeholderBatch = (data: Partial<any>) => {
    return alovaInstance.Post('/v1/pm/stakeholder/change_stakeholders',data);
};
// /**
//  * @description 导出stakeholder数据
//  * @param {stakeholderQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportstakeholder = (params?: stakeholderQueryParams) => {
//   return alovaInstance.Post<Blob>('/pm/stakeholder/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入stakeholder数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importstakeholder = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/pm/stakeholder/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getStakeholderList,
    createStakeholder,
    updateStakeholder,
    deleteStakeholder,
    deleteStakeholderBatch,
    getSingleStakeholder,
    createStakeholderBatch,
    stakeholderBatch,
    getByCustomeStakeholder,
    getStakeholderProjects,
    getPageMetaData,
  // exportstakeholder,
  // importstakeholder,
};
