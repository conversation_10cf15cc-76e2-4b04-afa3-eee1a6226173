import type { ModelBase } from "@/types/core"

/**
 * 合同状态
 */
export enum ContractStatus {
  DRAFT = '草稿',
  EXECUTING = '执行中'
}
interface Customer {
  id: number;
  name: string;
}
// ProjectContract 数据接口定义
export interface ProjectContract extends ModelBase {
  id: number;
  created_at?: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  contract_no?: string; // 合同编码
  contract_status?: ContractStatus; // 合同状态
  project_id?: number; // 所属项目
  customer_id?: number; // 所属客户
  sign_at?: string | Date; // 签订日期
  quotation_no?: string; // 报价单号
  quotation_name?: string; // 报价单名称
  total_amount?: number; // 合同总金额
  deposit_ratio?: number; // 定金比例
  deposit_amount?: number; // 定金金额
  deposit_deduction?: number; // 已抵扣定金
  deposit_surplus?: number; // 剩余定金
  deposit_progress?: number; // 定金使用进度
  notes?: string; // 备注
  attachment?: string; // 附件地址
  customer?: Customer; // customer
}

export interface ProjectContractCreate extends Omit<ProjectContract, 'id'> {

}

export interface ProjectContractUpdate extends ProjectContract {

}

// API 响应接口定义
