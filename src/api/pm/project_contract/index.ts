import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type {
    ProjectContract,
    ProjectContractCreate,
    ProjectContractUpdate,
} from './types';

/**
 * @description 获取ProjectContract元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getProjectContractMetadata = () => {
    return alovaInstance.Get<any>('/v1/pm/project_contract/get_metadata');
};

/**
 * @description 获取ProjectContract列表
 * @param {ProjectContractQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<ProjectContract>>} 返回包含ProjectContract信息的Promise对象
 * @example
 * // 使用示例
 * const projectContractList = await getProjectContractList({ start: 1, limit: 20 });
 */
const getProjectContractList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<ProjectContract>>(
        '/v1/pm/project_contract/query',
        params
    );
};

/**
 * @description 获取ProjectContract详情
 * @param {number} id ProjectContract ID
 * @returns {Promise<ProjectContract>} 返回ProjectContract详情信息
 */
const getProjectContract = (params: DetailQuery) => {
    return alovaInstance.Get<ProjectContract>('/v1/pm/project_contract/get', {
        params,
    });
};

/**
 * @description 创建ProjectContract
 * @param {ProjectContractCreate} data 创建数据
 * @returns {Promise<ProjectContract>} 返回创建的ProjectContract信息
 */
const createProjectContract = (data: ProjectContractCreate) => {
    return alovaInstance.Post<ProjectContract>(
        '/v1/pm/project_contract/create',
        data
    );
};

/**
 * @description 更新ProjectContract
 * @param {ProjectContractUpdate} data 更新数据
 * @returns {Promise<ProjectContract>} 返回更新后的ProjectContract信息
 */
const updateProjectContract = (data: ProjectContractUpdate) => {
    return alovaInstance.Put<ProjectContract>(
        '/v1/pm/project_contract/update',
        data
    );
};

/**
 * @description 删除ProjectContract
 * @param {number} id ProjectContract ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeProjectContract = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/pm/project_contract/delete/${id}`);
};

/**
 * @description 批量删除ProjectContract
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteProjectContract = (ids: number[]) => {
    return alovaInstance.Delete<any>('/pm/project_contract/bulk_delete', ids);
};

/**
 * @description 生成订单
 * @param {contractId} params 查询参数
 * @returns {Promise<Blob>} 返回导出文件
 */
const generateOrder = (params: { contract_id: number }) => {
    return alovaInstance.Put<Recordable>(
        '/v1/pm/project_contract/generate_order',
        { params }
    );
};

// /**
//  * @description 导出ProjectContract数据
//  * @param {ProjectContractQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportProjectContract = (params?: ProjectContractQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/pm/project_contract/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入ProjectContract数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importProjectContract = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/pm/project_contract/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getProjectContractMetadata,
    getProjectContractList,
    getProjectContract,
    createProjectContract,
    updateProjectContract,
    removeProjectContract,
    bulkDeleteProjectContract,
    generateOrder,
    // exportProjectContract,
    // importProjectContract,
};
