import alovaInstance from '@/api';

interface ProjectQuote {
    id?: number;
    doc_status?: string | null;
    quotation_status?: string | null;
    project_id?: number | null;
    customer_id?: number | null;
    quotation_no?: string | null;
    name?: string | null;
    effective_date?: string | null;
    product_qty?: number | null;
    total_qty?: number | string | null;
    total_amount?: number | string | null;
    total_cost?: number | string | null;
    gross_margin?: number | string | null;
    reject_reason?: string | null;
    customer_feedback?: string | null;
    notes?: string | null;
    items?: any[];
}

const createProjectQuote = (data: ProjectQuote) => {
    return alovaInstance.Post('/v1/pm/project_quote/create', data);
};

const getProjectQuote = (id: number) => {
    return alovaInstance.Get(`/v1/pm/project_quote/get?id=${id}&max_depth=3`, {
        cacheFor: 0,
    });
};

const updateProjectQuote = (data: any) => {
    return alovaInstance.Put('/v1/pm/project_quote/update', data);
};

const deleteProjectQuote = (id: number) => {
    return alovaInstance.Delete(`/v1/pm/project_quote/delete?id=${id}`);
};

const queryProjectQuote = (data: any) => {
    return alovaInstance.Post('/v1/pm/project_quote/query', data);
};

const getProjectQuoteMetadata = () => {
    return alovaInstance.Get('/v1/pm/project_quote/get_metadata');
};

const getProjectDesignProducts = (data: any) => {
    return alovaInstance.Get(
        `/v1/pm/project_quote/get_quote_products?project_id=${data.project_id}&house_type_id=${data.house_type_id}`
    );
};

const getProjectQuoteCode = (doc_type: string) => {
    return alovaInstance.Get(
        `/v1/sys/code_trace/get_code?doc_type=${doc_type}`
    );
};

export {
    createProjectQuote,
    getProjectQuote,
    updateProjectQuote,
    deleteProjectQuote,
    queryProjectQuote,
    getProjectQuoteMetadata,
    getProjectDesignProducts,
    getProjectQuoteCode,
    type ProjectQuote,
};
