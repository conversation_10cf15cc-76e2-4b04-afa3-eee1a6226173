import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type { Room, RoomCreate, RoomUpdate } from './types';

/**
 * @description 获取Room元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getRoomMetadata = () => {
    return alovaInstance.Get<any>('/v1/pm/room/get_metadata');
};

/**
 * @description 获取Room列表
 * @param {RoomQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Room>>} 返回包含Room信息的Promise对象
 * @example
 * // 使用示例
 * const roomList = await getRoomList({ start: 1, limit: 20 });
 */
const getRoomList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Room>>(
        '/v1/pm/room/query',
        params
    );
};

/**
 * @description 获取Room详情
 * @param {number} id Room ID
 * @returns {Promise<Room>} 返回Room详情信息
 */
const getRoom = (params: DetailQuery) => {
    return alovaInstance.Get<Room>('/v1/pm/room/get/', { params });
};

/**
 * @description 创建Room
 * @param {RoomCreate} data 创建数据
 * @returns {Promise<Room>} 返回创建的Room信息
 */
const createRoom = (data: RoomCreate) => {
    return alovaInstance.Post<Room>('/v1/pm/room/create', data);
};

/**
 * @description 批量创建Room
 * @param {RoomCreate[]} data 创建数据
 * @returns {Promise<Room[]>} 返回创建的Room信息
 */
const batchCreateRoom = (data: RoomCreate[]) => {
    return alovaInstance.Post<Room[]>('/v1/pm/room/bulk_create', data);
};

/**
 * @description 更新Room
 * @param {RoomUpdate} data 更新数据
 * @returns {Promise<Room>} 返回更新后的Room信息
 */
const updateRoom = (data: RoomUpdate) => {
    return alovaInstance.Put<Room>('/v1/pm/room/update', data);
};

/** 批量更新/分配房间 */
const batchUpdateRoom = (data: RoomUpdate[]) => {
    return alovaInstance.Post<Room[]>('/v1/pm/room/bulk_update', data);
};

/**
 * @description 删除Room
 * @param {number} id Room ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeRoom = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/pm/room/delete/${id}`);
};

/**
 * @description 批量删除Room
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteRoom = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/pm/room/bulk_delete', ids);
};

// /**
//  * @description 导出Room数据
//  * @param {RoomQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportRoom = (params?: RoomQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/pm/room/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Room数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importRoom = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/pm/room/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getRoomMetadata,
    getRoomList,
    getRoom,
    createRoom,
    batchCreateRoom,
    updateRoom,
    removeRoom,
    bulkDeleteRoom,
    batchUpdateRoom,
    // exportRoom,
    // importRoom,
};
