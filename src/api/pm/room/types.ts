import type { ModelBase } from '@/types/core';
interface ObjectType {
    id?: number;
    name?: string;
}
// Room 数据接口定义
export interface Room extends ModelBase {
    id: number;
    created_at: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    project_id: number; // 项目ID
    room_no: string; // 房间号
    roomName?: string;
    building: string; // 楼栋
    floor: string; // 楼层
    house_type_id?: number; // 户型ID
    house_type?: ObjectType; // house_type
    project?: ObjectType; // project
}

export interface RoomCreate
    extends Omit<Room, 'id' | 'created_at' | 'updated_at'> {}

export interface RoomUpdate extends Room {}

interface TypeItem {
    house_type_id: number;
    house_type_name: string;
    total: number;
}

/**
 * 楼层节点类型定义
 * 包含楼层信息、房间总数和房间列表
 */
export interface FloorNode {
    floor: string;
    roomTotal: number;
    houseTypeArr: TypeItem[];
    children: Room[];
}

/**
 * 楼栋节点类型定义
 * 包含楼栋信息、楼层总数和楼层列表
 */
export interface BuildingNode {
    building: string;
    floorTotal: number;
    roomTotal: number;
    assigned: number;
    unassigned: number;
    children: FloorNode[];
}

/**
 * 房间树形结构根类型
 * 由多个楼栋节点组成的数组
 */
export type RoomTree = BuildingNode[];

// API 响应接口定义
