import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
    project,
    projectCreate,
    projectUpdate,
    UploadFileResponse,
} from './types';

/** == 项目信息 =================================================================================== */
const getPageMetaData = () => {
    return alovaInstance.Get<Recordable>('/v1/pm/project/get_metadata');
};
/**
 * 获取项目信息列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含项目信息的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);
 */
const getProjectList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/pm/project/query', data);
};

/**
 * 创建项目信息
 * @param {Partial<Recordable>} data - 项目信息
 * @returns {Promise<Recordable>} 返回包含创建项目信息的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createProject(data);
 */
const createProject = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/pm/project/create', data);
};

/**
 * 更新项目信息
 * @param {Partial<Recordable>} data - 项目信息
 * @returns {Promise<Recordable>} 返回包含更新项目信息的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const updateProject = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/pm/project/update', data);
};

/**
 * 删除项目信息
 * @param {Partial<Recordable>} data - 项目信息
 * @returns {Promise<Recordable>} 返回包含删除项目信息的Promise对象
 * @example
 * // 使用示例
 */
const deleteProject = (id: string) => {
    return alovaInstance.Delete<Recordable>(`/v1/pm/project/delete/${id}`);
};

/**
 * 批量删除项目信息
 * @param {Partial<Recordable>} data - 项目信息
 * @returns {Promise<Recordable>} 返回包含批量删除项目信息的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteProjectBatch(data);
 */
const deleteProjectBatch = (data: Partial<any>) => {
    /*const { ids } = params;
    let stringIds = ids.map((id: string) => `ids=${id}`).join('&');*/

    return alovaInstance.Delete<any>('/v1/pm/project/bulk_delete', data);
};

/**
 * 获取单个项目信息
 * @param {Partial<Recordable>} data - 项目信息
 * @returns {Promise<Recordable>} 返回包含单个项目信息的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleProject(data);
 */
const getSingleProject = (params: { id: number; max_depth?: number }) => {
    return alovaInstance.Get<Recordable>('/v1/pm/project/get', {
        params,
        cacheFor: 0,
    });
};

/** == 项目跟进信息 =================================================================================== */

/**
 * 获取项目跟进列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含项目跟进的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);/api/v1/pm/follow_up/create
 */
const getFollowUpList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/pm/follow_up/query', data);
};

/**
 * 创建项目跟进
 * @param {Partial<Recordable>} data - 项目跟进
 * @returns {Promise<Recordable>} 返回包含创建项目跟进的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createProject(data);
 */
const createFollowUp = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/pm/follow_up/create', data);
};

/**
 * 更新项目跟进
 * @param {Partial<Recordable>} data - 项目跟进
 * @returns {Promise<Recordable>} 返回包含更新项目跟进的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const updateFollowUp = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/pm/follow_up/update', data);
};

/**
 * 删除项目跟进
 * @param {Partial<Recordable>} data - 项目跟进
 * @returns {Promise<Recordable>} 返回包含删除项目跟进的Promise对象
 * @example
 * // 使用示例
 */
const deleteFollowUp = (id: string) => {
    return alovaInstance.Delete<Recordable>(`/v1/pm/follow_up/delete/${id}`);
};

/**
 * 批量删除项目跟进
 * @param {Partial<Recordable>} data - 项目跟进
 * @returns {Promise<Recordable>} 返回包含批量删除项目跟进的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteProjectBatch(data);
 */
const deleteFollowUpBatch = (data: Partial<any>) => {
    /*const { ids } = params;
    let stringIds = ids.map((id: string) => `ids=${id}`).join('&');*/

    return alovaInstance.Delete<any>('/v1/pm/follow_up/bulk_delete', data);
};

/**
 * 获取单个项目跟进
 * @param {Partial<Recordable>} data - 项目跟进
 * @returns {Promise<Recordable>} 返回包含单个项目跟进的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleProject(data);
 */
const getSingleFollowUp = (params: { id: string | number }) => {
    return alovaInstance.Get<Recordable>('/v1/pm/follow_up/get', {
        params,
        cacheFor: 0,
    });
};

/*  分配设计师 */
const assignDesigners = (data: Recordable, designer_id: string | number) => {
    return alovaInstance.Post<Recordable>(
        '/v1/pm/project/assign_designers?designer_id=' + designer_id,
        data
    );
};

/* 关闭项目 */
const cancelProject = (data: Recordable) => {
    return alovaInstance.Post<Recordable>('/v1/pm/project/cancel', data);
};

/****************    产品设计 ***************/
/**
 * 获取产品设计列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含产品设计的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);
 */
const getProjectDesign = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>(
        '/v1/pm/production_design/query',
        data
    );
};

/**
 * 创建产品设计
 * @param {Partial<Recordable>} data - 产品设计
 * @returns {Promise<Recordable>} 返回包含创建产品设计的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createProject(data);
 */
const createProjectDesign = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>(
        '/v1/pm/production_design/create',
        data
    );
};

/**
 * 更新产品设计
 * @param {Partial<Recordable>} data - 产品设计
 * @returns {Promise<Recordable>} 返回包含更新产品设计的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const updateProjectDesign = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>(
        '/v1/pm/production_design/update',
        data
    );
};

/**
 * 删除已配置的产品设计
 */
const deleteProjectDesign = (id: number) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/pm/production_design/delete/${id}`
    );
};

/**
 * 设置产品复尺状态
 * @param {Partial<Recordable>} data - 设置产品复尺状态
 * @returns {Promise<Recordable>} 返回包含设置产品复尺状态的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const ProjectDesignSetReSizeStatus = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>(
        '/v1/pm/production_design/set_re_size_status',
        data
    );
};


/************** 产品设计版本管理  *******************************/

/**
 * 获取 产品设计版本管理
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含 产品设计版本管理的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);
 */
const getProjectDesignVersion = (data: Recordable) => {
    return alovaInstance.Post<Recordable>(
        '/v1/pm/production_design_version/query',
        data
    );
};

/**
 * 获取 上传设计文件
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含 上传设计文件的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);
 */
const getProjectDesignUpload = (
    params: FormData,
    related_id: Number,
    change_notes: String
) => {
    return alovaInstance.Post<UploadFileResponse>(
        '/v1/pm/production_design_version/upload?related_id=' +
        related_id +
        '&change_notes=' +
        change_notes,
        params
    );
};

/************** 产品设计BOM  *******************************/

/**
 * 获取 产品设计版本管理
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含 产品设计版本管理的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);
 */
const getProjectDesignBom = (data: Recordable) => {
    return alovaInstance.Post<Recordable>(
        '/v1/pm/production_design_bom/query',
        data
    );
};

/**
 * @description 反审核项目设计
 * @param {number} id 项目设计 ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number, notes: string) => {
    return alovaInstance.Post<Recordable>(
        `/v1/pm/production_design/anti_approval`,
        {
            relation_id: id,
            notes: notes,
        }
    );
};

/******************** 项目管理/设计命名  *******************/
/**
 * 获取 设计命名管理
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含 设计命名管理的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);*/
const getDesignName = (data: Recordable) => {
    return alovaInstance.Post<Recordable>('/v1/pm/design_naming/query', data);
};

/**
 * 创建设计命名
 * @param {Partial<Recordable>} data - 设计命名
 * @returns {Promise<Recordable>} 返回包含创建设计命名的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createProject(data);
 */
const createDesignName = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/pm/design_naming/create', data);
};

/**
 * 更新设计命名
 * @param {Partial<Recordable>} data - 设计命名
 * @returns {Promise<Recordable>} 返回包含更新设计命名的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const updateDesignName = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/pm/design_naming/update', data);
};

/**
 * 获取设计命名
 * @param {Partial<Recordable>} data - 设计命名
 * @returns {Promise<Recordable>} 返回包含单个设计命名的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleProject(data);
 */
const getSingleDesignName = (params: {
    id: string | number;
    max_depth: number;
}) => {
    return alovaInstance.Get<Recordable>('/v1/pm/design_naming/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * 删除设计命名
 * @param {Partial<Recordable>} data - 设计命名
 * @returns {Promise<Recordable>} 返回包含删除设计命名的Promise对象
 * @example
 * // 使用示例
 */
const deleteDesignName = (id: string) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/pm/design_naming/delete/${id}`
    );
};

/**
 * 批量删除设计命名
 * @param {Partial<Recordable>} data - 设计命名
 * @returns {Promise<Recordable>} 返回包含批量删除设计命名的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteProjectBatch(data);
 */
const deleteDesignNameBatch = (data: Partial<any>) => {
    return alovaInstance.Delete<any>('/v1/pm/design_naming/bulk_delete', data);
};


/* 归档 */
/**
 * 归档设计
 * @param {Partial<Recordable>} data - 归档设计
 * @returns {Promise<Recordable>} 返回归档设计的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const designPlace = (design_id: number) => {
    return alovaInstance.Put<Recordable>('/v1/pm/production_design/place?design_id=' + design_id);
};

/******************** 项目管理/项目复尺工单  *******************/

/**
 * 获取 复尺工单列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含 复尺工单管理的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);*/
const getProjectCheckScale = (data: Recordable) => {
    return alovaInstance.Post<Recordable>('/v1/pm/project_check_scale/query', data);
};

/**
 * 更新复尺工单
 * @param {Partial<Recordable>} data - 复尺工单
 * @returns {Promise<Recordable>} 返回包含更新复尺工单的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const updateProjectCheckScale = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/pm/project_check_scale/update', data);
};

/**
 * 生成复尺工单
 * @param {Partial<Recordable>} data - 生成复尺工单
 * @returns {Promise<Recordable>} 返回生成复尺工单的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const projectCheckScaleGenerate = (design_id: number) => {
    return alovaInstance.Post<Recordable>('/v1/pm/project_check_scale/generate?design_id=' + design_id);
};

/**
 * 获取复尺工单
 * @param {Partial<Recordable>} data - 复尺工单
 * @returns {Promise<Recordable>} 返回包含单个复尺工单的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleProject(data);
 */
const getSingleProjectCheckScale = (params: {
    id: string | number;
    max_depth: number;
}) => {
    return alovaInstance.Get<Recordable>('/v1/pm/project_check_scale/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * 删除复尺工单
 * @param {Partial<Recordable>} data - 复尺工单
 * @returns {Promise<Recordable>} 返回包含删除复尺工单的Promise对象
 * @example
 * // 使用示例
 */
const deleteProjectCheckScale = (id: string) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/pm/project_check_scale/delete/${id}`
    );
};

/**
 * 批量删除复尺工单
 * @param {Partial<Recordable>} data - 复尺工单
 * @returns {Promise<Recordable>} 返回包含批量删除复尺工单的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteProjectBatch(data);
 */
const deleteProjectCheckScaleBatch = (data: Partial<any>) => {
    return alovaInstance.Delete<any>('/v1/pm/project_check_scale/bulk_delete', data);
};

/*  复尺工单指派 */
const assignProjectCheckScale = (data: Recordable) => {
    return alovaInstance.Post<Recordable>(
        '/v1/pm/project_check_scale/assign',
        data
    );
};


/**
 * 获取 异常上报上传设计文件
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含 异常上报上传设计文件的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);
 */
const getProjectCheckScaleAbnormal = (
    params: FormData,
    related_id: Number,
    abnormal_reason: String
) => {
    return alovaInstance.Post<UploadFileResponse>(
        '/v1/pm/project_check_scale/abnormal?related_id=' +
        related_id +
        '&abnormal_reason=' +
        abnormal_reason,
        params
    );
};

/**
 * 复尺完成关闭工单
 * @param {Partial<Recordable>} data - 复尺完成关闭工单
 * @returns {Promise<Recordable>} 返回包含复尺完成关闭工单的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);/api/v1/pm/project_check_scale/complete
 */
const projectCheckScaleComplete = (id: String | Number) => {
    return alovaInstance.Put<Recordable>(
        '/v1/pm/project_check_scale/complete?id=' + id,
    );
};


/******************** 项目管理/项目测量师  *******************/
/**
 * 获取项目测量师列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含项目测量师的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);
 */
const getProjectSurveyorList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/pm/project_surveyor/query', data);
};

/**
 * 创建项目测量师
 * @param {Partial<Recordable>} data - 项目测量师
 * @returns {Promise<Recordable>} 返回包含创建项目测量师的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createProject(data);
 */
const createProjectSurveyor = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/pm/project_surveyor/create', data);
};

/**
 * 更新项目测量师
 * @param {Partial<Recordable>} data - 项目测量师
 * @returns {Promise<Recordable>} 返回包含更新项目测量师的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const updateProjectSurveyor = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/pm/project_surveyor/update', data);
};

/**
 * 删除项目测量师
 * @param {Partial<Recordable>} data - 项目测量师
 * @returns {Promise<Recordable>} 返回包含删除项目测量师的Promise对象
 * @example
 * // 使用示例
 */
const deleteProjectSurveyor = (id: string) => {
    return alovaInstance.Delete<Recordable>(`/v1/pm/project_surveyor/delete/${id}`);
};

/**
 * 批量删除项目测量师
 * @param {Partial<Recordable>} data - 项目测量师
 * @returns {Promise<Recordable>} 返回包含批量删除项目测量师的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteProjectBatch(data);
 */
const deleteProjectSurveyorBatch = (data: Partial<any>) => {
    return alovaInstance.Delete<any>('/v1/pm/project_surveyor/bulk_delete', data);
};

/**
 * 获取单个项目测量师
 * @param {Partial<Recordable>} data - 项目测量师
 * @returns {Promise<Recordable>} 返回包含单个项目测量师的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleProject(data);
 */
const getSingleProjectSurveyor = (params: { id: number; max_depth?: number }) => {
    return alovaInstance.Get<Recordable>('/v1/pm/project_surveyor/get', {
        params,
        cacheFor: 0,
    });
};


/******************** 项目管理/项目测量  *******************/
/**
 * 获取项目测量列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含项目测量的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProjectList(data);
 */
const getProjectMeasureList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/pm/project_measure/query', data);
};

/**
 * 创建项目测量
 * @param {Partial<Recordable>} data - 项目测量
 * @returns {Promise<Recordable>} 返回包含创建项目测量的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createProject(data);
 */
const createProjectMeasure = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/pm/project_measure/create', data);
};

/**
 * 更新项目测量
 * @param {Partial<Recordable>} data - 项目测量
 * @returns {Promise<Recordable>} 返回包含更新项目测量的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const updateProjectMeasure = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/pm/project_measure/update', data);
};

/**
 * 删除项目测量
 * @param {Partial<Recordable>} data - 项目测量
 * @returns {Promise<Recordable>} 返回包含删除项目测量的Promise对象
 * @example
 * // 使用示例
 */
const deleteProjectMeasure = (id: string) => {
    return alovaInstance.Delete<Recordable>(`/v1/pm/project_measure/delete/${id}`);
};

/**
 * 批量删除项目测量
 * @param {Partial<Recordable>} data - 项目测量
 * @returns {Promise<Recordable>} 返回包含批量删除项目测量的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteProjectBatch(data);
 */
const deleteProjectMeasureBatch = (data: Partial<any>) => {
    return alovaInstance.Delete<any>('/v1/pm/project_measure/bulk_delete', data);
};

/**
 * 获取单个项目测量
 * @param {Partial<Recordable>} data - 项目测量
 * @returns {Promise<Recordable>} 返回包含单个项目测量的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleProject(data);
 */
const getSingleProjectMeasure = (params: { id: number; max_depth?: number }) => {
    return alovaInstance.Get<Recordable>('/v1/pm/project_measure/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * 同步项目测量信息
 * @param {Partial<Recordable>} data - 同步项目测量信息
 * @returns {Promise<Recordable>} 返回包含同步项目测量信息的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProject(data);
 */
const projectMeasureSyncMeasure = (id: String | Number) => {
    return alovaInstance.Put<Recordable>(
        '/v1/pm/project_measure/sync_measure?project_id=' + id,
    );
};

/**
 * 获取获取统计信息
 * @param {Partial<Recordable>} data - 获取统计信息
 * @returns {Promise<Recordable>} 返回包含获取统计信息的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleProject(data);
 */
const getProjectMeasureStatistics = () => {
    return alovaInstance.Get<Recordable>('/v1/pm/project_measure/statistics');
};

/* 根据测量师傅id获取项目复尺工单列表 */
const getMeasurementMission = (surveyor_id: String | Number) => {
    return alovaInstance.Get<Recordable>('/v1/pm/project_check_scale/measurement_mission?surveyor_id=' + surveyor_id);
};
// /**
//  * @description 导出project数据
//  * @param {projectQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportproject = (params?: projectQueryParams) => {
//   return alovaInstance.Post<Blob>('/pm/project/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入project数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importproject = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/pm/project/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getProjectList,
    createProject,
    updateProject,
    deleteProject,
    deleteProjectBatch,
    getSingleProject,
    getFollowUpList,
    createFollowUp,
    updateFollowUp,
    deleteFollowUp,
    deleteFollowUpBatch,
    getSingleFollowUp,
    getPageMetaData,
    assignDesigners,
    cancelProject,
    getProjectDesign,
    createProjectDesign,
    updateProjectDesign,
    deleteProjectDesign,
    ProjectDesignSetReSizeStatus,
    getProjectDesignVersion,
    getProjectDesignUpload,
    getProjectDesignBom,
    antiApproval,
    getDesignName,
    createDesignName,
    updateDesignName,
    getSingleDesignName,
    deleteDesignName,
    deleteDesignNameBatch,
    designPlace,
    projectCheckScaleGenerate,
    getSingleProjectCheckScale,
    getProjectCheckScale,
    updateProjectCheckScale,
    deleteProjectCheckScale,
    deleteProjectCheckScaleBatch,
    assignProjectCheckScale,
    getProjectCheckScaleAbnormal,
    projectCheckScaleComplete,
    getProjectSurveyorList,
    createProjectSurveyor,
    updateProjectSurveyor,
    deleteProjectSurveyor,
    deleteProjectSurveyorBatch,
    getSingleProjectSurveyor,
    getProjectMeasureList,
    createProjectMeasure,
    updateProjectMeasure,
    deleteProjectMeasure,
    deleteProjectMeasureBatch,
    getSingleProjectMeasure,
    projectMeasureSyncMeasure,
    getProjectMeasureStatistics,
    getMeasurementMission,
    // exportproject,
    // importproject,
};
