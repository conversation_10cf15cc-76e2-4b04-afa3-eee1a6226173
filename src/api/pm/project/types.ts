import type { ModelBase } from '@/types/core';

/**
 * 单据状态
 */
export enum DocStatus {
    DRAFT = '草稿',
    PENDING_REVIEW = '待审核',
    UNDER_REVIEW = '审核中',
    APPROVED = '已审核',
    REJECTED = '已驳回',
    CANCELLED = '已作废',
}

/** 项目干系人 */
export interface ProjectContact {
    id?: number;
    project_id?: number; // 项目id
    stakeholder_id?: number; // 干系人id
    project_role?: string; // 项目角色
    contact?: string;
    stakeholder?: any;
    phone?: string; // 手机号
}

interface ObjectType {
    id?: number;
    name?: string;
    nick_name?: string;
    factory_name?: number;
    phone_number?: string;
}

// project 数据接口定义
export interface project extends ModelBase {
    id?: number;
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    doc_status?: DocStatus; // 单据状态
    factory_id?: number; // 所属组织
    customer_id?: number; // 所属客户
    status?: string; // 项目状态
    name?: string; // 项目名称
    project_type_id?: number; // 项目类型
    priority_id?: number; // 项目优先级
    brand_id?: number; // 品牌
    salesman_id?: number; // 业务员
    layers?: number; // 层数
    rooms?: number; // 房间数
    expect_deal_date?: string; // 预计成交时间
    total_area?: number; // 总面积
    project_address?: string; // 项目地址
    project_start_time?: string; // 交项目开始时间货日期
    project_end_time?: string; // 项目结束时间
    plan_start_time?: string; // 计划开工时间
    plan_end_time?: string; // 计划结束时间
    design_cycle?: number; // 设计周期(天)
    production_cycle?: number; // 生产周期(天)
    general_budget?: number; // 总预算
    unit_cost?: number; // 单位造价(元/㎡)
    material_budget_ratio?: number; // 材料预算比例(%)
    artificial_budget_ratio?: number; // 人工预算比例(%)
    payment_terms_id?: number; // 付款方式
    budgetary_statement?: string; // 预算说明
    notes?: string; // 备注
    factory?: ObjectType; // factory
    project_type?: ObjectType; // project_type
    priority?: ObjectType; // priority
    brand?: ObjectType; // brand
    salesman?: ObjectType; // salesman
    manager_id?: number; // 项目经理
    designer_id?: number; // 设计人
    manager?: ObjectType; // 项目经理
    designer?: ObjectType; // 设计人
    payment_terms?: ObjectType; // payment_terms
    customer?: ObjectType; // customer
    source?: any; // 项目来源
    contacts?: ProjectContact[]; // contacts
}

/** 上传文件返回数据 */
export interface UploadFileResponse {
    updated_at?: null;
    created_by?: number;
    id?: number;
    created_at?: string;
    file_path?: string;
    file_size?: number;
    md5?: string;
    related_id?: number;
    updated_by?: null;
    name?: string;
    file_name?: string;
    file_type?: string;
    related_model?: string;
    file?: File | Blob;
    change_notes?: string;
}

export interface projectCreate extends Omit<project, 'id'> { }

export interface projectUpdate extends project { }

// API 响应接口定义
