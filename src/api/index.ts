import { createAlova } from 'alova';
import adapterFetch from 'alova/fetch';
import VueHook from 'alova/vue';
import { useAuthStore } from '@/store/auth';
import { useToast } from '@/components/ui/toast/use-toast';
import type { Method } from 'alova';
//import { clearToken, getToken, setToken, isLogin } from "@/utils";

export interface ResponseSucess<T> {
    code: number;
    message: string;
    data: T;
}

const alovaInstance = createAlova({
    baseURL: '/api',
    // baseURL: import.meta.env.VITE_GLOB_API_URL,
    statesHook: VueHook,
    requestAdapter: adapterFetch(),
    beforeRequest(method: Method) {
        const authStore = useAuthStore();
        // 需要添加token到请求头
        if (authStore.isLogin()) {
            const token = authStore.getToken();
            method.config.headers.Authorization = `Bearer ${token}`;
            //method.config.headers['Content-Type'] = 'application/json';
        }
        // 控制参数是作为查询字符串发送还是作为请求体发送
        const data = method.data as any;
        if (data?.params) {
            method.config.params = data.params;
            delete data.params;
        }
    },
    // 使用 responded 对象分别指定请求成功的拦截器和请求失败的拦截器
    responded: {
        // 请求成功的拦截器
        // 当使用 `alova/fetch` 请求适配器时，第一个参数接收Response对象
        // 第二个参数为当前请求的method实例，你可以用它同步请求前后的配置信息
        onSuccess: async (response, method) => {
            const json = await response.json();
            if (response.status >= 400) {
                // token过期
                if (response.status === 401) {
                    const authStore = useAuthStore();
                    authStore.logout();
                    return;
                }
                // 同一抛错
                const toast = useToast();
                if (json.msg === '关联数据【id】无效，请检查相关数据！') {
                    toast.toast({
                        title: '',
                        description: '数据被引用，不能删除！',
                        // variant: 'destructive',
                        duration: 2000,
                    });
                    throw new Error(response.statusText);
                }

                toast.toast({
                    title: '操作失败',
                    description: json.data || json.msg,
                    variant: 'destructive',
                    duration: 2000,
                });
                throw new Error(response.statusText);
            }
            if (json.code !== 200) {
                // 抛出错误或返回reject状态的Promise实例时，此请求将抛出错误
                throw new Error(json.msg);
            }
            // if (json.code === 200) {
            //     toast.toast({
            //         title: '操作成功',
            //         description: json.message,
            //     });
            // }

            // 解析的响应数据将传给method实例的transform钩子函数
            return json.data as ResponseSucess<any>;
        },

        // 请求失败的拦截器
        // 请求错误时将会进入该拦截器。
        // 第二个参数为当前请求的method实例，你可以用它同步请求前后的配置信息
        onError: (err: any) => {
            throw new Error(err.message);
        },

        // 请求完成的拦截器
        // 当你需要在请求不论是成功、失败、还是命中缓存都需要执行的逻辑时，可以在创建alova实例时指定全局的`onComplete`拦截器，例如关闭请求 loading 状态。
        // 接收当前请求的method实例
        onComplete: async (method) => {
            // 处理请求完成逻辑
        },
    },
    // 开启缓存日志，在开发环境中方便调试
    cacheLogger: true,
} as any);

export default alovaInstance;
