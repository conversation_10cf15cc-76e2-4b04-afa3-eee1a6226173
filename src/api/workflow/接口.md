后端API接口：

1、创建工作流定义：[post] /workflow/workflow_define/create

参数：
{
"name": "string",
"code": "string",
"document_type": "销售订单",
"notes": "string",
"status": "未激活",
"relation_model": [
{}
],
"edges_json": [
{}
],
"workflow_metadata": {}
}

2、更新工作流定义：[put] /workflow/workflow_define/update
参数：
{
"name": "string",
"code": "string",
"document_type": "销售订单",
"notes": "string",
"status": "未激活",
"relation_model": [
{}
],
"edges_json": [
{}
],
"workflow_metadata": {},
"id": 0
}

3、删除工作流定义：[delete] /workflow/workflow_define/delete/{id}
参数
id

4、查询工作流定义：[post] /workflow/workflow_define/query
参数
{
"filters": {
"couple": "and",
"conditions": [
{
"field": "string",
"op": "eq",
"value": "string"
},
"string"
]
},
"sort": [
{
"field": "id",
"order": "desc"
}
],
"offset": 0,
"limit": 100
}

5、获取工作流定义：[get] /workflow/workflow_define/get?id=xxx

6、工作流节点的创建：[post] /workflow/workflow_node/create
参数：
{
"define_id": 0,
"name": "string",
"code": "string",
"node_type": "开始",
"previous_node_id": 0,
"config": {},
"position": {}
}

7、工作流节点的更新：[put] /workflow/workflow_node/update

参数：
{
"define_id": 0,
"name": "string",
"code": "string",
"node_type": "开始",
"previous_node_id": 0,
"config": {},
"position": {},
"id": 0
}

8、工作流节点删除：[delete] /workflow/workflow_node/delete/{id}

9、工作流节点查询：[post] /workflow/workflow_node/query
参数
{
"filters": {
"couple": "and",
"conditions": [
{
"field": "string",
"op": "eq",
"value": "string"
},
"string"
]
},
"sort": [
{
"field": "id",
"order": "desc"
}
],
"offset": 0,
"limit": 100
}

10、工作流节点获取：[get] /workflow/workflow_node/get?id=xx
