import alovaInstance, { type ResponseSucess } from '@/api';
import type {
    Workflow,
    WorkflowNode,
    WorkflowResponse,
    WorkflowListResponse,
    WorkflowNodesResponse,
    ApiResponse,
} from '@/views/workflow/types';

/**************************************************
 * LogicFlow 2.0 工作流API接口定义
 * 根据后端接口文档更新 - 2024-12-19
 ***************************************************/

// 获取页面元数据
const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/workflow/workflow_define/get_metadata`
    );
};

//获取表模型数据
const getModels = (modelName: string) => {
    return alovaInstance.Put<WorkflowResponse>(
        `/v1/utils/relation/get_model_by_name?model_name=${modelName}`
    );
};

// 获取工作流定义列表 - 使用POST查询
const getWorkflowList = (params?: {
    filters?: {
        couple?: 'and' | 'or';
        conditions?: Array<{
            field: string;
            op: 'eq' | 'ne' | 'gt' | 'lt' | 'like';
            value: string;
        }>;
    };
    sort?: Array<{
        field: string;
        order: 'asc' | 'desc';
    }>;
    offset?: number;
    limit?: number;
}) => {
    return alovaInstance.Post<WorkflowListResponse>(
        '/v1/workflow/workflow_define/query',
        params || {
            offset: 0,
            limit: 100,
        }
    );
};

// 获取工作流定义详情
const getWorkflow = (id: number) => {
    return alovaInstance.Get<WorkflowResponse>(
        `/v1/workflow/workflow_define/get?id=${id}&max_depth=3`,
        {
            cacheFor: 0,
        }
    );
};

// 创建工作流定义
const createWorkflow = (params: {
    name: string;
    code: string;
    document_type: string;
    notes?: string;
    status?: string;
    relation_model?: any[];
    edges_json?: any[];
    workflow_metadata?: any;
}) => {
    return alovaInstance.Post<WorkflowResponse>(
        '/v1/workflow/workflow_define/create',
        params
    );
};

// 更新工作流定义、保存边信息
const updateWorkflow = (params: {
    id: number;
    name: string;
    code: string;
    document_type: string;
    notes?: string;
    status?: string;
    relation_model?: any[];
    edges_json?: any[];
    workflow_metadata?: any;
}) => {
    if (!params.id) {
        throw new Error('更新工作流时必须提供ID');
    }
    return alovaInstance.Put<WorkflowResponse>(
        '/v1/workflow/workflow_define/update',
        params
    );
};

// 设置工作流状态
const setWorkflowStatus = (id: number, status: string) => {
    return alovaInstance.Put<ApiResponse>(
        `/v1/workflow/workflow_define/set_status?define_id=${id}&status=${status}`
    );
};

// 删除工作流定义
const deleteWorkflow = (id: number) => {
    return alovaInstance.Delete<ApiResponse>(
        `/v1/workflow/workflow_define/delete/${id}`
    );
};

// 获取工作流节点列表 - 使用POST查询
const getWorkflowNodes = (params: {
    filters?: {
        couple: 'and' | 'or';
        conditions: Array<{
            field: string;
            op: 'eq' | 'ne' | 'gt' | 'lt' | 'like';
            value: string;
        }>;
    };
    sort?: Array<{
        field: string;
        order: 'asc' | 'desc';
    }>;
    offset?: number;
    limit?: number;
}) => {
    return alovaInstance.Post<WorkflowNodesResponse>(
        '/v1/workflow/workflow_node/query',
        params
    );
};

// 获取单个工作流节点
const getWorkflowNode = (id: number) => {
    return alovaInstance.Get<ApiResponse<WorkflowNode>>(
        `/v1/workflow/workflow_node/get?id=${id}`
    );
};

// 创建工作流节点
const createWorkflowNode = (params: {
    define_id: number;
    name: string;
    code: string;
    node_type: string;
    previous_node_id?: number;
    config?: any;
    position?: any;
}) => {
    return alovaInstance.Post<ApiResponse<WorkflowNode>>(
        '/v1/workflow/workflow_node/create',
        params
    );
};

// 更新工作流节点
const updateWorkflowNode = (params: {
    id: number;
    define_id: number;
    name: string;
    code: string;
    node_type: string;
    previous_node_id?: number;
    config?: any;
    position?: any;
}) => {
    if (!params.id) {
        throw new Error('更新节点时必须提供ID');
    }
    return alovaInstance.Put<ApiResponse<WorkflowNode>>(
        '/v1/workflow/workflow_node/update',
        params
    );
};

// 查找工作流节点
const queryWorkflowNode = (params: {
    filters?: {
        conditions: Array<{
            field: string;
            op: 'eq' | 'ne' | 'gt' | 'lt' | 'like';
            value: number | string;
        }>;
    };
    offset?: number;
    limit?: number;
}) => {
    return alovaInstance.Post<ApiResponse<WorkflowNode[]>>(
        '/v1/workflow/workflow_node/query',
        params
    );
};

// 删除工作流节点
const deleteWorkflowNode = (id: number) => {
    return alovaInstance.Delete<ApiResponse>(
        `/v1/workflow/workflow_node/delete/${id}`
    );
};

// 验证工作流
const validateWorkflow = (workflowId: number) => {
    return alovaInstance.Post<
        ApiResponse<{
            valid: boolean;
            errors: string[];
            warnings: string[];
        }>
    >('/v1/workflow/workflow_define/validate', { id: workflowId });
};

// 激活工作流
const activateWorkflow = (workflowId: number) => {
    return alovaInstance.Put<ApiResponse>(
        '/v1/workflow/workflow_define/update',
        {
            id: workflowId,
            status: '激活',
        }
    );
};

// 停用工作流
const deactivateWorkflow = (workflowId: number) => {
    return alovaInstance.Put<ApiResponse>(
        '/v1/workflow/workflow_define/update',
        {
            id: workflowId,
            status: '未激活',
        }
    );
};

const createApproval = (params: {
    document_type: string;
    relation_model: Recordable;
    define_id: number;
    relation_no: string;
}) => {
    return alovaInstance.Put<ApiResponse>(
        '/v1/workflow/workflow_instance/initiate_approval',
        params
    );
};

export {
    // 基础CRUD
    getWorkflowList,
    getWorkflow,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    setWorkflowStatus,

    // 节点管理
    getWorkflowNodes,
    getWorkflowNode,
    createWorkflowNode,
    updateWorkflowNode,
    deleteWorkflowNode,
    queryWorkflowNode,

    // 工作流操作
    validateWorkflow,
    activateWorkflow,
    deactivateWorkflow,
    createApproval,

    // 元数据
    getPageMetaData,
    getModels,
};
