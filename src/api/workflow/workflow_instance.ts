import alovaInstance, { type ResponseSucess } from '@/api';
import type {
    Workflow,
    WorkflowNode,
    WorkflowResponse,
    WorkflowListResponse,
    WorkflowNodesResponse,
    ApiResponse,
} from '@/views/workflow/types';

// 获取页面元数据
const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/workflow/workflow_instance_log/get_metadata`
    );
};

// 获取工作流实例审批日志（头部审批任务列表）
const getWorkflowInstanceLog = (params: {
    filters?: {
        conditions: Array<{
            field: string;
            op: 'eq' | 'ne' | 'gt' | 'lt' | 'like';
            value: number | string;
        }>;
    };
    offset?: number;
    limit?: number;
}) => {
    return alovaInstance.Post<ApiResponse>(
        '/v1/workflow/workflow_instance_log/query',
        params
    );
};

const checkApproval = (params: {
    log_id: number;
    approve_result: string;
    notes: string;
}) => {
    return alovaInstance.Put<ApiResponse>(
        '/v1/workflow/workflow_instance_log/approval',
        params
    );
};

const getWorkflowInstance = (id: number) => {
    return alovaInstance.Get<ApiResponse>(
        `/v1/workflow/workflow_instance/get?id=${id}`
    );
};

export {
    getWorkflowInstanceLog,
    getPageMetaData,
    checkApproval,
    getWorkflowInstance,
};
