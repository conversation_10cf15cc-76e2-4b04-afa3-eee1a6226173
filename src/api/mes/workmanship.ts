import { useRequest } from 'alova/client';
import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';
// import type { DefaultCurrent, CurrencyItem } from "@/views/bas/currency/config";

const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/bas/workmanship/get_metadata`
    );
};

const getMetaData: (url: string) => Promise<Recordable> = (url: string) => {
    return new Promise((resolve, reject) => {
        const { onSuccess, onError } = useRequest(getPageMetaData(), {
            immediate: true, // 立即发送请求
        });
        onSuccess((res) => {
            resolve(res.data);
        });
        onError((err) => {
            reject(err);
        });
    });
};

/**
 * @description 创建工艺
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const createWorkmanship = (params: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/bas/workmanship/create', params);
};

/**
 * @description 批量创建工艺
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const bulkCreateWorkmanship = (params: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>(
        '/v1/bas/workmanship/bulk_create',
        params
    );
};

/**
 * @description 更新工艺
 * @param {Partial<Recordable>} data
 * @returns {Promise<Recordable>}
 */
const updateWorkmanship = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/bas/workmanship/update', data);
};

/**
 * @description 删除工艺
 * @param {Partial<CurrencyItem>}
 * @returns {Promise<Recordable>}
 */
const deleteWorkmanship = (id?: string | number) => {
    return alovaInstance.Delete<Recordable>(`/v1/bas/workmanship/delete/${id}`);
};

/**
 * @description 批量删除工艺
 * @param {Partial<string>}
 * @returns {Promise<Recordable>}
 */
const bulkDeleteWorkmanship = (ids: string[]) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/bas/workmanship/bulk_delete`,
        ids
    );
};

/**
 * @description 查询工艺
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getWorkmanshipList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/bas/workmanship/query', data);
};

/**
 * @description 查询工艺
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getWorkmanship = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Recordable>(`/v1/bas/workmanship/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

export {
    getPageMetaData,
    getMetaData,
    createWorkmanship,
    bulkCreateWorkmanship,
    updateWorkmanship,
    deleteWorkmanship,
    bulkDeleteWorkmanship,
    getWorkmanshipList,
    getWorkmanship,
};
