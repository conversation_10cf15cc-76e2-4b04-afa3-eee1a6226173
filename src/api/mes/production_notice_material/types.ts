import type { ModelBase } from "@/types/core"

// ProductionNoticeMaterial 数据接口定义
export interface ProductionNoticeMaterial extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  production_notice_id?: number; // 生产通知单id
  type_id?: number; // 物料类别ID
  code?: string; // 物料编码
  name?: string; // 物料名称
  specs_json?: string; // 规格
  material_quality?: string; // 材质
  width?: number; // 宽
  height?: number; // 长/高
  deep?: number; // 厚度
  weight?: number; // 重量
  model?: string; // 型号
  sale_unit_price?: number; // 销售单价
  unit_id?: number; // 单位
  uom_id?: number; // 单位
  qty: number; // 数量
  unit_area?: number; // 单件面积(m²)
  total_weight?: number; // 总重量(kg)
  total_area?: number; // 总面积(m²)
  uom?: DictData; // uom
  base_unit?: DictData; // base_unit
}

export interface ProductionNoticeMaterialCreate extends Omit<ProductionNoticeMaterial, 'id'> {

}

export interface ProductionNoticeMaterialUpdate extends ProductionNoticeMaterial {

}

// API 响应接口定义
