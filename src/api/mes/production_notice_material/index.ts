import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
    ProductionNoticeMaterial,
    ProductionNoticeMaterialCreate,
    ProductionNoticeMaterialUpdate,
} from './types';

/**
 * @description 获取ProductionNoticeMaterial元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getProductionNoticeMaterialMetadata = () => {
    return alovaInstance.Get<any>(
        '/v1/mes/production_notice_material/get_metadata'
    );
};

/**
 * @description 获取ProductionNoticeMaterial列表
 * @param {ProductionNoticeMaterialQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<ProductionNoticeMaterial>>} 返回包含ProductionNoticeMaterial信息的Promise对象
 * @example
 * // 使用示例
 * const productionNoticeMaterialList = await getProductionNoticeMaterialList({ start: 1, limit: 20 });
 */
const getProductionNoticeMaterialList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<ProductionNoticeMaterial>>(
        '/v1/mes/production_notice_material/query',
        params
    );
};

/**
 * @description 获取ProductionNoticeMaterial列表
 * @param {ProductionNoticeMaterialQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<ProductionNoticeMaterial>>} 返回包含ProductionNoticeMaterial信息的Promise对象
 * @example
 * // 使用示例
 * const productionNoticeMaterialList = await getProductionNoticeMaterialList({ start: 1, limit: 20 });
 */
const getMaterials = (notice_id: number) => {
    return alovaInstance.Put<ResponseListModel<ProductionNoticeMaterial>>(
        `/v1/mes/production_notice_material/get_materials?notice_id=${notice_id}`,
        {}
    );
};

/**
 * @description 获取ProductionNoticeMaterial详情
 * @param {number} id ProductionNoticeMaterial ID
 * @returns {Promise<ProductionNoticeMaterial>} 返回ProductionNoticeMaterial详情信息
 */
const getProductionNoticeMaterial = (id?: number) => {
    const url = id
        ? `/v1/mes/production_notice_material/get/${id}`
        : '/v1/mes/production_notice_material/get';
    return alovaInstance.Get<ProductionNoticeMaterial>(url);
};

/**
 * @description 创建ProductionNoticeMaterial
 * @param {ProductionNoticeMaterialCreate} data 创建数据
 * @returns {Promise<ProductionNoticeMaterial>} 返回创建的ProductionNoticeMaterial信息
 */
const createProductionNoticeMaterial = (
    data: ProductionNoticeMaterialCreate
) => {
    return alovaInstance.Post<ProductionNoticeMaterial>(
        '/v1/mes/production_notice_material/create',
        data
    );
};

/**
 * @description 更新ProductionNoticeMaterial
 * @param {ProductionNoticeMaterialUpdate} data 更新数据
 * @returns {Promise<ProductionNoticeMaterial>} 返回更新后的ProductionNoticeMaterial信息
 */
const updateProductionNoticeMaterial = (
    data: ProductionNoticeMaterialUpdate
) => {
    return alovaInstance.Put<ProductionNoticeMaterial>(
        '/v1/mes/production_notice_material/update',
        data
    );
};

/**
 * @description 批量更新ProductionNoticeMaterial
 * @param {ProductionNoticeMaterialUpdate} data 更新数据
 * @returns {Promise<ProductionNoticeMaterial>} 返回更新后的ProductionNoticeMaterial信息
 */
const bulkUpdateProductionNoticeMaterial = (
    data: ProductionNoticeMaterialUpdate
) => {
    return alovaInstance.Post<ProductionNoticeMaterial>(
        '/v1/mes/production_notice_material/bulk_update',
        data
    );
};

/**
 * @description 删除ProductionNoticeMaterial
 * @param {number} id ProductionNoticeMaterial ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeProductionNoticeMaterial = (id: number) => {
    return alovaInstance.Delete<any>(
        `/v1/mes/production_notice_material/delete/${id}`
    );
};

/**
 * @description 批量删除ProductionNoticeMaterial
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteProductionNoticeMaterial = (ids: number[]) => {
    return alovaInstance.Delete<any>(
        '/v1/mes/production_notice_material/bulk_delete',
        ids
    );
};

// /**
//  * @description 导出ProductionNoticeMaterial数据
//  * @param {ProductionNoticeMaterialQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportProductionNoticeMaterial = (params?: ProductionNoticeMaterialQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/mes/production_notice_material/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入ProductionNoticeMaterial数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importProductionNoticeMaterial = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/mes/production_notice_material/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getProductionNoticeMaterialMetadata,
    getProductionNoticeMaterialList,
    getProductionNoticeMaterial,
    createProductionNoticeMaterial,
    updateProductionNoticeMaterial,
    removeProductionNoticeMaterial,
    bulkDeleteProductionNoticeMaterial,
    bulkUpdateProductionNoticeMaterial,
    getMaterials
    // exportProductionNoticeMaterial,
    // importProductionNoticeMaterial,
};
