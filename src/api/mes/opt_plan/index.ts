import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { OptPlan, OptPlanCreate, OptPlanUpdate } from './types';

/**
 * @description 获取OptPlan元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getOptPlanMetadata = () => {
    return alovaInstance.Get<any>('/v1/mes/opt_plan/get_metadata');
};

/**
 * @description 获取OptPlan列表
 * @param {OptPlanQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<OptPlan>>} 返回包含OptPlan信息的Promise对象
 * @example
 * // 使用示例
 * const optPlanList = await getOptPlanList({ start: 1, limit: 20 });
 */
const getOptPlanList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<OptPlan>>(
        '/v1/mes/opt_plan/query',
        params
    );
};

/**
 * @description 获取OptPlan详情
 * @param {number} id OptPlan ID
 * @returns {Promise<OptPlan>} 返回OptPlan详情信息
 */
const getOptPlan = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<OptPlan>(`/v1/mes/opt_source/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 创建OptPlan
 * @param {OptPlanCreate} data 创建数据
 * @returns {Promise<OptPlan>} 返回创建的OptPlan信息
 */
const createOptPlan = (data: OptPlanCreate) => {
    return alovaInstance.Post<OptPlan>('/v1/mes/opt_plan/create', data);
};

/**
 * @description 更新OptPlan
 * @param {OptPlanUpdate} data 更新数据
 * @returns {Promise<OptPlan>} 返回更新后的OptPlan信息
 */
const updateOptPlan = (data: OptPlanUpdate) => {
    return alovaInstance.Put<OptPlan>('/v1/mes/opt_plan/update', data);
};

/**
 * @description 删除OptPlan
 * @param {number} id OptPlan ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeOptPlan = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/mes/opt_plan/delete/${id}`);
};

/**
 * @description 批量删除OptPlan
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteOptPlan = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/mes/opt_plan/batch-delete', { ids });
};

// /**
//  * @description 导出OptPlan数据
//  * @param {OptPlanQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportOptPlan = (params?: OptPlanQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/mes/opt_plan/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入OptPlan数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importOptPlan = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/mes/opt_plan/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getOptPlanMetadata,
    getOptPlanList,
    getOptPlan,
    createOptPlan,
    updateOptPlan,
    removeOptPlan,
    batchDeleteOptPlan,
    // exportOptPlan,
    // importOptPlan,
};
