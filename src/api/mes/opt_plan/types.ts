import type { ModelBase } from "@/types/core"

// OptPlan 数据接口定义
export interface OptPlan extends ModelBase {
  id: number;
  name?: string;
  abs?: number; // 公差
  edging?: number; // 磨边量
  width_edging?: number; // 宽磨边
  height_edging?: number; // 高磨边
  is_default?: boolean; // 默认方案
}

export interface OptPlanCreate extends Omit<OptPlan, 'id'> {

}

export interface OptPlanUpdate extends OptPlan {

}

// API 响应接口定义
