import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
    ProductionNotice,
    ProductionNoticeCreate,
    ProductionNoticeUpdate,
} from './types';
import { UploadFileResponse } from '@/api/sys/attachment';
/**
 * @description 获取ProductionNotice元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getProductionNoticeSourceMetadata = () => {
    return alovaInstance.Get<any>(
        '/v1/mes/production_notice_source/get_metadata'
    );
};

/**
 * @description 获取生产通知单数据源
 * @param {ProductionNoticeQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<ProductionNotice>>} 返回包含ProductionNotice信息的Promise对象
 * @example
 * // 使用示例
 * const productionNoticeList = await getProductionNoticeList({ start: 1, limit: 20 });
 */
const getProductionNoticeSourceList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<ProductionNotice>>(
        '/v1/mes/production_notice_source/query',
        params
    );
};

/**
 * @description 获取ProductionNotice详情
 * @param {number} id ProductionNotice ID
 * @returns {Promise<ProductionNotice>} 返回ProductionNotice详情信息
 */
const getProductionNoticeSource = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<ProductionNotice>(
        `/v1/mes/production_notice_source/get`,
        {
            params: {
                id,
                max_depth,
            },
        }
    );
};

/**
 * @description 获取ProductionNotice元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getProductionNoticeMetadata = () => {
    return alovaInstance.Get<any>('/v1/mes/production_notice/get_metadata');
};

/**
 * @description 获取ProductionNotice列表
 * @param {ProductionNoticeQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<ProductionNotice>>} 返回包含ProductionNotice信息的Promise对象
 * @example
 * // 使用示例
 * const productionNoticeList = await getProductionNoticeList({ start: 1, limit: 20 });
 */
const getProductionNoticeList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<ProductionNotice>>(
        '/v1/mes/production_notice/query',
        params
    );
};

/**
 * @description 获取ProductionNotice详情
 * @param {number} id ProductionNotice ID
 * @returns {Promise<ProductionNotice>} 返回ProductionNotice详情信息
 */
const getProductionNotice = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<ProductionNotice>(
        `/v1/mes/production_notice/get`,
        {
            params: {
                id,
                max_depth,
            },
        }
    );
};

/**
 * @description 创建ProductionNotice
 * @param {ProductionNoticeCreate} data 创建数据
 * @returns {Promise<ProductionNotice>} 返回创建的ProductionNotice信息
 */
const createProductionNotice = (data: ProductionNoticeCreate) => {
    return alovaInstance.Post<ProductionNotice>(
        '/v1/mes/production_notice/create',
        data
    );
};

/**
 * @description 更新ProductionNotice
 * @param {ProductionNoticeUpdate} data 更新数据
 * @returns {Promise<ProductionNotice>} 返回更新后的ProductionNotice信息
 */
const updateProductionNotice = (data: ProductionNoticeUpdate) => {
    return alovaInstance.Put<ProductionNotice>(
        '/v1/mes/production_notice/update',
        data
    );
};

/**
 * @description 删除ProductionNotice
 * @param {number} id ProductionNotice ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeProductionNotice = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/mes/production_notice/delete/${id}`);
};

/**
 * @description 批量删除ProductionNotice
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteProductionNotice = (ids: number[]) => {
    return alovaInstance.Delete<any>(
        '/v1/mes/production_notice/bulk_delete',
        ids
    );
};
/**
 * @description 设置采购状态
 * @param {Recordable} data
 * @returns {Promise<any>}
 */
const setPurStatus = (params: Recordable) => {
    return alovaInstance.Put<Record<string, any>>(
        '/v1/mes/production_notice/set_pur_status',
        { params }
    );
};

/**
 * @description 上传物料清单
 * @param {Recordable} data
 * @returns {Promise<any>}
 */
const uploadMaterial = (data: FormData, notice_id: number) => {
    return alovaInstance.Post<UploadFileResponse>(
        `/v1/mes/production_notice/upload_material?notice_id=${notice_id}`,
        data
    );
};

/**
 * @description 反审核Order
 * @param {number} id Order ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(
        `/v1/mes/production_notice/anti_approval`,
        {
            relation_id: id,
        }
    );
};

// /**
//  * @description 导出ProductionNotice数据
//  * @param {ProductionNoticeQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportProductionNotice = (params?: ProductionNoticeQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/mes/production_notice/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入ProductionNotice数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importProductionNotice = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/mes/production_notice/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getProductionNoticeSourceMetadata,
    getProductionNoticeSourceList,
    getProductionNoticeSource,
    getProductionNoticeMetadata,
    getProductionNoticeList,
    getProductionNotice,
    createProductionNotice,
    updateProductionNotice,
    removeProductionNotice,
    batchDeleteProductionNotice,
    setPurStatus,
    uploadMaterial,
    antiApproval,
    // exportProductionNotice,
    // importProductionNotice,
};
