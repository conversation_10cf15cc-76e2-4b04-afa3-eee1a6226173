import type { ModelBase } from "@/types/core"

/**
 * 单据状态
 */
export enum DocStatus {
  DRAFT = '草稿',
  PENDING_REVIEW = '待审核',
  UNDER_REVIEW = '审核中',
  APPROVED = '已审核',
  REJECTED = '已驳回',
  CANCELLED = '已作废'
}

// ProductionNotice 数据接口定义
export interface ProductionNotice extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  factory_id?: number; // 所属机构
  doc_status: DocStatus; // 单据状态
  is_give: boolean; // 下达否
  is_case_close: boolean; // 结案否
  notice_date?: string; // 通知日期
  notice_no?: string; // 生产单号
  business_id?: number; // 业务类型
  dept_id?: number; // 部门id
  plan_start_date?: string; // 计划开工日期
  plan_end_date?: string; // 计划完工日期
  plan_qty?: number; // 计划数量
  is_related: boolean; // 关联下达半成品否
  notes?: string; // 备注
  factory?: Factory; // factory
  business?: DictData; // business
  dept?: Dept; // dept
  items: ProductionNoticeItem[]; // items
}

export interface ProductionNoticeCreate extends Omit<ProductionNotice, 'id'> {

}

export interface ProductionNoticeUpdate extends ProductionNotice {

}

// API 响应接口定义
