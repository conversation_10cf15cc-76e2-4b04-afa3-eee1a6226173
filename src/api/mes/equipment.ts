import alovaInstance from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/**
 * @description 获取工控相机资料列表数据
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<any>} 返回包含工控相机资料信息的Promise对象
 * @example
 * // 使用示例
 * const cameraList = await getEquipmentList(data);
 */
const getEquipmentList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/mes/equipment/query', data);
};
export { getEquipmentList };
