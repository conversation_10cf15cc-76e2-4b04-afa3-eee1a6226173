import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type {
    EquipmentInfo,
    EquipmentInfoCreate,
    EquipmentInfoUpdate,
} from './types';

/**
 * @description 获取EquipmentInfo元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getEquipmentInfoMetadata = () => {
    return alovaInstance.Get<any>('/v1/mes/equipment/get_metadata');
};

/**
 * @description 获取EquipmentInfo列表
 * @param {EquipmentInfoQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<EquipmentInfo>>} 返回包含EquipmentInfo信息的Promise对象
 * @example
 * // 使用示例
 * const equipmentInfoList = await getEquipmentInfoList({ start: 1, limit: 20 });
 */
const getEquipmentInfoList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<EquipmentInfo>>(
        '/v1/mes/equipment/query',
        params
    );
};

/**
 * @description 获取EquipmentInfo详情
 * @param {number} id EquipmentInfo ID
 * @returns {Promise<EquipmentInfo>} 返回EquipmentInfo详情信息
 */
const getEquipmentInfo = (params: DetailQuery) => {
    return alovaInstance.Get<EquipmentInfo>('/v1/mes/equipment/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * @description 创建EquipmentInfo
 * @param {EquipmentInfoCreate} data 创建数据
 * @returns {Promise<EquipmentInfo>} 返回创建的EquipmentInfo信息
 */
const createEquipmentInfo = (data: EquipmentInfoCreate) => {
    return alovaInstance.Post<EquipmentInfo>('/v1/mes/equipment/create', data);
};

/**
 * @description 更新EquipmentInfo
 * @param {EquipmentInfoUpdate} data 更新数据
 * @returns {Promise<EquipmentInfo>} 返回更新后的EquipmentInfo信息
 */
const updateEquipmentInfo = (data: EquipmentInfoUpdate) => {
    return alovaInstance.Put<EquipmentInfo>('/v1/mes/equipment/update', data);
};

/**
 * @description 删除EquipmentInfo
 * @param {number} id EquipmentInfo ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeEquipmentInfo = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/mes/equipment/delete/${id}`);
};

/**
 * @description 批量删除EquipmentInfo
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteEquipmentInfo = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/mes/equipment/bulk_delete', ids);
};

/*
 * @description 设置状态
 * @param {number} id EquipmentInfo ID
 * @param {boolean} status 状态
 * @returns {Promise<any>} 返回设置状态结果
 */
const setEquipmentInfoStatus = (params: { ids: string; status: boolean }) => {
    return alovaInstance.Put<any>('/v1/mes/equipment/set_enable_status', {
        params,
    });
};
// /**
//  * @description 导出EquipmentInfo数据
//  * @param {EquipmentInfoQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportEquipmentInfo = (params?: EquipmentInfoQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/mes/equipment/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入EquipmentInfo数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importEquipmentInfo = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/mes/equipment/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getEquipmentInfoMetadata,
    getEquipmentInfoList,
    getEquipmentInfo,
    createEquipmentInfo,
    updateEquipmentInfo,
    removeEquipmentInfo,
    batchDeleteEquipmentInfo,
    setEquipmentInfoStatus,
    // exportEquipmentInfo,
    // importEquipmentInfo,
};
