import type { ModelBase } from '@/types/core';

/**
 * 设备类型
 */
export enum EquipmentType {
    Production = '生产设备',
    Auxiliary = '辅助设备',
}

/**
 * 辅助设备类型
 */
export enum AuxiliaryEquipmentType {
    Coding = '打码设备',
    IndustrialCamera = '工控相机',
    IndustrialComputer = '工控电脑',
    CachingDevice = '缓存设备',
    ActivityCage = '活动笼',
    SliceCage = '理片笼',
    Ladder = '云梯',
    SmartCar = '智能小车',
    Other = '其他',
}

/**
 * 产能计算类型
 */
export enum CapacityCalculationType {
    Quantity = '数量',
    Volume = '面积',
}

/** 对象类型 */
interface ObjectType {
    name?: string;
    factory_name?: string;
    id?: number;
    code?: string;
}

// EquipmentInfo 数据接口定义
export interface EquipmentInfo extends ModelBase {
    id: number;
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    factory_id?: number;
    dept_id?: number; // 部门
    workmanship_id?: number; // 工艺
    work_team_id?: number; // 工队
    equipment_type?: string; // 设备类型
    auxiliary_equipment_type?: string; // 辅助设备类型
    code?: string; // 设备编码
    name?: string; // 设备名称
    staff_qty?: number; // 员工数量
    capacity?: number; // 产能
    capacity_calculation?: string; // 产能计算类型
    length?: number; // 产品长度
    width?: number; // 产品宽度
    height?: number; // 产品高度
    weight?: number; // 产品重量
    grinding_type?: string; // 磨削类型
    material_code?: string; // 物料编码
    valid?: boolean; // 有效
    notes?: string; // 备注
    is_size_control?: boolean; // 产品尺寸控制否
    max_length?: number; // 产品长度范围
    min_length?: number; // 产品长度范围
    is_deep_control?: boolean; // 产品厚度控制否
    max_deep?: number; // 产品厚度范围
    min_deep?: number; // 产品厚度范围
    batch_max_deep?: number; // 同批最大厚度
    batch_min_deep?: number; // 同批最小厚度
    min_space?: number; // 最小间距
    is_fixed_work?: boolean; // 固定工作时间否
    standard_work_hours?: number; // 标准工作时间
    wait_time?: number; // 等待时间
    max_storage?: number; // 最大存储量
    capacity_notes?: string; // 产能备注
    items?: ObjectType[]; // items
    workmanship?: ObjectType; // workmanship
    dept?: ObjectType; // dept
    factory?: ObjectType; // factory
    work_team?: ObjectType; // work_team
}

export interface EquipmentInfoCreate extends Omit<EquipmentInfo, 'id'> {}

export interface EquipmentInfoUpdate extends EquipmentInfo {}

/** 生成原始数据对象 */
export const createEquipmentInfoWithDefaults = (
    partialData: Partial<EquipmentInfo> = {}
): EquipmentInfo => {
    return {
        id: undefined,
        created_at: undefined,
        updated_at: undefined,
        created_by: undefined,
        updated_by: undefined,
        factory_id: undefined,
        dept_id: undefined,
        workmanship_id: undefined,
        work_team_id: undefined,
        equipment_type: undefined,
        auxiliary_equipment_type: undefined,
        code: undefined,
        name: undefined,
        staff_qty: undefined,
        capacity: undefined,
        capacity_calculation: undefined,
        length: undefined,
        width: undefined,
        height: undefined,
        weight: undefined,
        grinding_type: undefined,
        material_code: undefined,
        valid: undefined,
        notes: undefined,
        is_size_control: undefined,
        max_length: undefined,
        min_length: undefined,
        is_deep_control: undefined,
        max_deep: undefined,
        min_deep: undefined,
        batch_max_deep: undefined,
        batch_min_deep: undefined,
        min_space: undefined,
        is_fixed_work: undefined,
        standard_work_hours: undefined,
        wait_time: undefined,
        max_storage: undefined,
        capacity_notes: undefined,
        ...partialData, // 覆盖传入的部分数据
    };
};
