import type { ModelBase } from "@/types/core"

// WorkShift 数据接口定义
export interface WorkShift extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  code?: string; // 班次编号
  name?: string; // 班次名称
  type?: string; // 班次类型
  factory_id?: number; // 所属机构
  valid: boolean; // 启用否
  notes?: string; // 备注
  items: WorkShiftItem[]; // items
  factory?: Factory; // factory
}

export interface WorkShiftCreate extends Omit<WorkShift, 'id'> {

}

export interface WorkShiftUpdate extends WorkShift {

}

// API 响应接口定义
