import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { WorkShift, WorkShiftCreate, WorkShiftUpdate } from './types';

/**
 * @description 获取WorkShift元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getWorkShiftMetadata = () => {
    return alovaInstance.Get<any>('/v1/mes/work_shift/get_metadata');
};

/**
 * @description 获取WorkShift列表
 * @param {WorkShiftQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<WorkShift>>} 返回包含WorkShift信息的Promise对象
 * @example
 * // 使用示例
 * const workShiftList = await getWorkShiftList({ start: 1, limit: 20 });
 */
const getWorkShiftList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<WorkShift>>(
        '/v1/mes/work_shift/query',
        params
    );
};

/**
 * @description 获取WorkShift详情
 * @param {number} id WorkShift ID
 * @returns {Promise<WorkShift>} 返回WorkShift详情信息
 */
const getWorkShift = (id?: number) => {
    const url = id ? `/v1/mes/work_shift/get/${id}` : '/v1/mes/work_shift/get';
    return alovaInstance.Get<WorkShift>(url);
};

/**
 * @description 创建WorkShift
 * @param {WorkShiftCreate} data 创建数据
 * @returns {Promise<WorkShift>} 返回创建的WorkShift信息
 */
const createWorkShift = (data: WorkShiftCreate) => {
    return alovaInstance.Post<WorkShift>('/v1/mes/work_shift/create', data);
};

/**
 * @description 更新WorkShift
 * @param {WorkShiftUpdate} data 更新数据
 * @returns {Promise<WorkShift>} 返回更新后的WorkShift信息
 */
const updateWorkShift = (data: WorkShiftUpdate) => {
    return alovaInstance.Put<WorkShift>('/v1/mes/work_shift/update', data);
};

/**
 * @description 删除WorkShift
 * @param {number} id WorkShift ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeWorkShift = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/mes/work_shift/delete/${id}`);
};

/**
 * @description 批量删除WorkShift
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteWorkShift = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/mes/work_shift/bulk_delete', ids);
};

/**
 * @description 设置启用状态
 * @param {Partial<QueryParams>} params
 * @returns {Promise<Recordable>}
 * @example
 */
const setEnableStatus = (params: Recordable) => {
    return alovaInstance.Put<Recordable>(
        `/v1/mes/work_shift/set_enable_status`,
        {
            params,
        }
    );
};

// /**
//  * @description 导出WorkShift数据
//  * @param {WorkShiftQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportWorkShift = (params?: WorkShiftQueryParams) => {
//   return alovaInstance.Post<Blob>('/mes/work_shift/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入WorkShift数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importWorkShift = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/mes/work_shift/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getWorkShiftMetadata,
    getWorkShiftList,
    getWorkShift,
    createWorkShift,
    updateWorkShift,
    removeWorkShift,
    batchDeleteWorkShift,
    setEnableStatus,
    // exportWorkShift,
    // importWorkShift,
};
