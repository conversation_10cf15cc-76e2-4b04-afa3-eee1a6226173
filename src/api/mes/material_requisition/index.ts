import alovaInstance from '@/api'
import type { ResponseListModel } from '@/types/core'
import type { DetailQuery, QueryParams } from '@/types/api/queryParams'

import type {
  MaterialListData,
  MaterialRequisition,
  MaterialRequisitionCreate,
  MaterialRequisitionUpdate,
} from './types'

/**
 * @description 获取MaterialRequisition元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getMaterialRequisitionMetadata = () => {
  return alovaInstance.Get<any>('/v1/mes/material_requisition/get_metadata')
}

/**
 * @description 获取MaterialRequisition列表
 * @param {MaterialRequisitionQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<MaterialRequisition>>} 返回包含MaterialRequisition信息的Promise对象
 * @example
 * // 使用示例
 * const materialRequisitionList = await getMaterialRequisitionList({ start: 1, limit: 20 });
 */
const getMaterialRequisitionList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<MaterialRequisition>>(
    '/v1/mes/material_requisition/query',
    params
  )
}

/**
 * @description 获取MaterialRequisition详情
 * @param {number} id MaterialRequisition ID
 * @returns {Promise<MaterialRequisition>} 返回MaterialRequisition详情信息
 */
const getMaterialRequisition = (params: DetailQuery) => {
  return alovaInstance.Get<MaterialRequisition>(
    '/v1/mes/material_requisition/get',
    { params }
  )
}

/**
 * @description 创建MaterialRequisition
 * @param {MaterialRequisitionCreate} data 创建数据
 * @returns {Promise<MaterialRequisition>} 返回创建的MaterialRequisition信息
 */
const createMaterialRequisition = (data: MaterialRequisitionCreate) => {
  return alovaInstance.Post<MaterialRequisition>(
    '/v1/mes/material_requisition/create',
    data
  )
}

/**
 * @description 更新MaterialRequisition
 * @param {MaterialRequisitionUpdate} data 更新数据
 * @returns {Promise<MaterialRequisition>} 返回更新后的MaterialRequisition信息
 */
const updateMaterialRequisition = (data: MaterialRequisitionUpdate) => {
  return alovaInstance.Put<MaterialRequisition>(
    '/v1/mes/material_requisition/update',
    data
  )
}

/**
 * @description 删除MaterialRequisition
 * @param {number} id MaterialRequisition ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeMaterialRequisition = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/mes/material_requisition/delete/${id}`)
}

/**
 * @description 批量删除MaterialRequisition
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteMaterialRequisition = (ids: number[]) => {
  return alovaInstance.Delete<any>(
    '/v1/mes/material_requisition/bulk_delete',
    ids
  )
}

/** 获取领料类型 */
const getRequisitionTypeList = () => {
  return alovaInstance.Get<any>(
    '/v1/mes/material_requisition/get_requisition_types'
  )
}

/** 反审核 */
const requisitionAntiApproval = (id: number) => {
  return alovaInstance.Post<any>('/v1/mes/material_requisition/anti_approval', {
    relation_id: id,
  })
}

/** 领料数据的metadata */
const getRequisitionDataMetadata = () => {
  return alovaInstance.Get<any>(
    '/v1/mes/material_requisition_source/get_metadata'
  )
}

/** 根据仓库id获取领料数据 */
const getRequisitionData = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<MaterialListData>>(
    '/v1/mes/material_requisition_source/query',
    params
  )
}
// /**
//  * @description 导出MaterialRequisition数据
//  * @param {MaterialRequisitionQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportMaterialRequisition = (params?: MaterialRequisitionQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/mes/material_requisition/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入MaterialRequisition数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importMaterialRequisition = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/mes/material_requisition/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getMaterialRequisitionMetadata,
  getMaterialRequisitionList,
  getMaterialRequisition,
  createMaterialRequisition,
  updateMaterialRequisition,
  removeMaterialRequisition,
  bulkDeleteMaterialRequisition,
  requisitionAntiApproval,
  getRequisitionTypeList,
  getRequisitionData,
  getRequisitionDataMetadata,
  // exportMaterialRequisition,
  // importMaterialRequisition,
}
