import type { ModelBase } from '@/types/core'

/**
 * 单据状态
 */
export enum DocStatus {
  DRAFT = '草稿',
  PENDING_REVIEW = '待审核',
  UNDER_REVIEW = '审核中',
  APPROVED = '已审核',
  REJECTED = '已驳回',
  CANCELLED = '已作废',
}

interface ObjectType {
  id: number
  name?: string
  factory_id?: number
  factory_name?: string
  nick_name?: string
}

// MaterialRequisition 数据接口定义
export interface MaterialRequisition extends ModelBase {
  id: number
  created_at?: string // 创建时间
  updated_at?: string // 更新时间
  created_by?: number // 创建者
  updated_by?: number // 更新者
  factory_id?: number // 所属机构
  requisition_no?: string // 领料单号
  requisition_date?: string // 领料日期
  doc_status?: DocStatus // 单据状态
  store_id?: number // 领料仓库
  requisition_type?: string // 领料类型
  dept_id?: number // 领料部门
  requisition_at?: number // 领料人
  keeper_id?: number // 仓管员
  manual_no?: string // 手工单号
  total_qty?: number // 领料数量
  total_area?: number // 领料面积(㎡)
  total_weight?: number // 领料重量(kg)
  total_amount?: number // 领料金额
  notes?: string // 备注
  dept?: ObjectType // dept
  keeper?: ObjectType // keeper
  factory?: ObjectType // factory
  store?: ObjectType // store
  items: any[] // items
}

export interface MaterialRequisitionCreate
  extends Omit<
    MaterialRequisition,
    'id' | 'dept' | 'keeper' | 'factory' | 'store'
  > {}

export interface MaterialRequisitionUpdate extends MaterialRequisition {}

/** 物料列表数据 */
export interface MaterialListData {
  id?: number
  store_id?: number
  qty?: number | string
  purchase_no?: null
  code?: string
  unit_area?: string
  total_area?: number
  batch_no?: number
  name?: string
  unit_weight?: string
  total_weight?: number
  material_id?: number
  specs?: string
  uom_id?: number
  variant_id?: null
  width?: string
  uom_name?: string
  product_id?: null
  height?: string
  price?: string
  total_amount?: number
  deep?: string
  pricing_mode?: string
  requisition_type?: string
  stock_qty?: string
  notice_no?: string
}
