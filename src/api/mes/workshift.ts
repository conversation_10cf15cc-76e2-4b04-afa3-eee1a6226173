import { useRequest } from 'alova/client';
import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';
// import type { DefaultCurrent, CurrencyItem } from "@/views/mes/currency/config";

const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/mes/work_shift/get_metadata`
    );
};

const getMetaData: (url: string) => Promise<Recordable> = (url: string) => {
    return new Promise((resolve, reject) => {
        const { onSuccess, onError } = useRequest(getPageMetaData(), {
            immediate: true, // 立即发送请求
        });
        onSuccess((res) => {
            resolve(res.data);
        });
        onError((err) => {
            reject(err);
        });
    });
};

/**
 * @description 创建班次
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const createWorkshift = (params: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/mes/work_shift/create', params);
};

/**
 * @description 批量创建班次
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const bulkCreateWorkshift = (params: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>(
        '/v1/mes/work_shift/bulk_create',
        params
    );
};

/**
 * @description 更新班次
 * @param {Partial<Recordable>} data
 * @returns {Promise<Recordable>}
 */
const updateWorkshift = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/mes/work_shift/update', data);
};

/**
 * @description 删除班次
 * @param {Partial<CurrencyItem>}
 * @returns {Promise<Recordable>}
 */
const deleteWorkshift = (id?: string | number) => {
    return alovaInstance.Delete<Recordable>(`/v1/mes/work_shift/delete/${id}`);
};

/**
 * @description 批量删除班次
 * @param {Partial<string>}
 * @returns {Promise<Recordable>}
 */
const bulkDeleteWorkshift = (ids: string[]) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/mes/work_shift/bulk_delete`,
        ids
    );
};

/**
 * @description 查询班次
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getWorkshiftList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/mes/work_shift/query', data);
};

/**
 * @description 查询班次
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getWorkshift = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Recordable>(`/v1/mes/work_shift/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 设置启用状态
 * @param {Partial<QueryParams>} params
 * @returns {Promise<Recordable>}
 * @example
 */
const setEnableStatus = (params: Recordable) => {
    return alovaInstance.Put<Recordable>(
        `/v1/mes/work_shift/set_enable_status`,
        {
            params,
        }
    );
};

export {
    getPageMetaData,
    getMetaData,
    createWorkshift,
    bulkCreateWorkshift,
    updateWorkshift,
    deleteWorkshift,
    bulkDeleteWorkshift,
    getWorkshiftList,
    getWorkshift,
    setEnableStatus,
};
