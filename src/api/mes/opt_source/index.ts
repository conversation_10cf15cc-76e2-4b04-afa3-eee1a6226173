import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { OptSource, OptSourceCreate, OptSourceUpdate } from './types';

/**
 * @description 获取OptSource元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getOptSourceMetadata = () => {
    return alovaInstance.Get<any>('/v1/mes/opt_source/get_metadata');
};

/**
 * @description 获取OptSource列表
 * @param {OptSourceQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<OptSource>>} 返回包含OptSource信息的Promise对象
 * @example
 * // 使用示例
 * const optSourceList = await getOptSourceList({ start: 1, limit: 20 });
 */
const getOptSourceList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<OptSource>>(
        '/v1/mes/opt_source/query',
        params
    );
};

/**
 * @description 获取OptSource详情
 * @param {number} id OptSource ID
 * @returns {Promise<OptSource>} 返回OptSource详情信息
 */
const getOptSource = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<OptSource>(`/v1/mes/opt_source/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

export { getOptSourceMetadata, getOptSourceList, getOptSource };
