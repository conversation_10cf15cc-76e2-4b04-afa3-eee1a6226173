import type { ModelBase } from "@/types/core"

/**
 * 源单类型
 */
export enum DocumentType {
  SALE_ORDER = '销售订单',
  SALE_QUOTATION = '销售报价',
  SALE_CONTRACT = '销售合同',
  SALE_DELIVERY = '销售送货',
  SALE_RETURN = '销售退货',
  PURCHASE_APPLY = '采购申请',
  PURCHASE_ORDER = '采购订单',
  PURCHASE_RECEIPT = '采购收货',
  PURCHASE_RETURN = '采购退货',
  PURCHASE_CONTRACT = '采购合同',
  INVENTORY_LOCATION_INFO = '仓位资料',
  INVENTORY_INFO = '仓库资料',
  INBOUND = '入库',
  OUTBOUND = '出库',
  ADJUSTMENT = '调拨',
  RETURN = '退货',
  FIN_RECEIVABLE = '应收账单',
  FIN_COLLECTION = '收款单',
  FIN_PAYABLE = '应付账单',
  FIN_PAYMENT = '付款单',
  FIN_ACCOUNTING_ENTRY = '财务分录',
  FIN_VOUCHER = '财务凭证',
  FIN_ANALYSIS = '财务分析',
  FIN_BUDGET = '财务预算',
  FIN_FORECAST = '财务预测',
  MES_PRODUCTION_NOTICE = '生产通知单',
  MES_FLOW_CARD = '流程卡',
  MES_OPTIMIZATION = '切割优化',
  OPS_MATERIAL = '物料',
  OPS_PRODUCT = '产品',
  OPS_PROJECT = '项目',
  OPS_BOM = 'BOM',
  OTHER = '其他'
}

// OptSource 数据接口定义
export interface OptSource extends ModelBase {
  id: number;
  org_id?: number; // 来源ID
  org_type?: DocumentType; // 源单类型
  org_no?: string; // 源单单号
  org_date?: string; // 源单日期
  org_qty?: number; // 源单数量
  qty?: number; // 未优化数量
  unit_area?: number; // 单件面积(m²)
  unit_weight?: number; // 单件重量(kg)
  unit_girth?: number; // 单件周长(m)
  specs?: string; // 规格型号
  width?: number; // 宽(mm)
  height?: number; // 高(mm)
  order_no?: string; // 订单编号
  order_date?: string; // 订单日期
  customer_id?: number; // 客户ID
  customer_name?: string; // 客户名称
  material_code?: string; // 物料编号
  material_name?: string; // 物料名称
  uom_id?: number; // 计量单位
  uom?: string; // 计量单位
}

export interface OptSourceCreate extends Omit<OptSource, 'id'> {

}

export interface OptSourceUpdate extends OptSource {

}

// API 响应接口定义
