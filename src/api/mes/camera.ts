import alovaInstance from '../index';
import type { QueryParams } from '@/types/api/queryParams';
import type { DefaultCurrent, CurrentItem } from '@/views/mes/camera/config';

/**
 * @description 获取工控相机资料列表数据
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含工控相机资料信息的Promise对象
 * @example
 * // 使用示例
 * const cameraList = await getCameraList(data);
 */
const getCameraList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/mes/camera/query', data);
};

/**
 * @description 创建工控相机资料数据
 * @param {Partial<Recordable>} data - 工控相机资料信息
 * @returns {Promise<Recordable>} 返回包含创建工控相机资料信息的Promise对象
 * @example
 * // 使用示例
 * const newCamer = await createCamerData({ name: "新工控相机资料信息", value: "newCamer" });
 */
const createCamerData = (data: Partial<DefaultCurrent>) => {
    return alovaInstance.Post<Recordable>('/v1/mes/camera/create', data);
};

/**
 * @description 更新工控相机资料数据
 * @param {Partial<CurrentItem>} data - 工控相机资料信息
 * @returns {Promise<Recordable>} 返回包含更新工控相机资料信息的Promise对象
 * @example
 * // 使用示例
 * const updatedCamer = await updateCamerData({ id: 1, name: "更新工控相机资料" });
 */
const updateCamerData = (data: Partial<CurrentItem>) => {
    return alovaInstance.Put<Recordable>('/v1/mes/camera/update', data);
};

/**
 * @description 删除工控相机资料数据
 * @param {Partial<CurrentItem>} data - 工控相机资料信息
 * @returns {Promise<Recordable>} 返回包含删除工控相机资料信息的Promise对象
 */
const deleteCamerData = (id?: string | number) => {
    return alovaInstance.Delete<Recordable>(`/v1/mes/camera/delete/${id}`);
};

/**
 * @description 批量删除工控相机资料数据
 * @param {Partial<string>} data - 工控相机资料Id
 * @returns {Promise<Recordable>} 返回包含删除工控相机资料信息的Promise对象
 */
const deleteBulkData = (params: { ids: string[] }) => {
    const { ids } = params;
    const paramsIds = ids.map((id: string) => `ids=${id}`).join('&');
    return alovaInstance.Delete<Recordable>(
        `/v1/mes/camera/bulk_delete?${paramsIds}`
    );
};
/**
 * @description 切换工控相机资料状态
 * @param {Partial<boolean>} data - 工控相机资料状态
 * @returns {Promise<Recordable>}
 */
const switchStatus = (data: Recordable) => {
    return alovaInstance.Put<Recordable>('/v1/mes/camera/set_enable_status', {
        params: data,
    });
};
export {
    getCameraList,
    createCamerData,
    deleteCamerData,
    deleteBulkData,
    updateCamerData,
    switchStatus,
};
