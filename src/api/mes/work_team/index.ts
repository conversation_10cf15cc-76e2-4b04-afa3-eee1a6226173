import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { WorkTeam, WorkTeamCreate, WorkTeamUpdate } from './types';

/**
 * @description 获取WorkTeam元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getWorkTeamMetadata = () => {
    return alovaInstance.Get<any>('/v1/mes/work_team/get_metadata');
};

/**
 * @description 获取WorkTeam列表
 * @param {WorkTeamQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<WorkTeam>>} 返回包含WorkTeam信息的Promise对象
 * @example
 * // 使用示例
 * const workTeamList = await getWorkTeamList({ start: 1, limit: 20 });
 */
const getWorkTeamList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<WorkTeam>>(
        '/v1/mes/work_team/query',
        params
    );
};

/**
 * @description 获取WorkTeam详情
 * @param {number} id WorkTeam ID
 * @returns {Promise<WorkTeam>} 返回WorkTeam详情信息
 */
const getWorkTeam = (id?: number) => {
    const url = id ? `/v1/mes/work_team/get/${id}` : '/v1/mes/work_team/get';
    return alovaInstance.Get<WorkTeam>(url);
};

/**
 * @description 创建WorkTeam
 * @param {WorkTeamCreate} data 创建数据
 * @returns {Promise<WorkTeam>} 返回创建的WorkTeam信息
 */
const createWorkTeam = (data: WorkTeamCreate) => {
    return alovaInstance.Post<WorkTeam>('/v1/mes/work_team/create', data);
};

/**
 * @description 更新WorkTeam
 * @param {WorkTeamUpdate} data 更新数据
 * @returns {Promise<WorkTeam>} 返回更新后的WorkTeam信息
 */
const updateWorkTeam = (data: WorkTeamUpdate) => {
    return alovaInstance.Put<WorkTeam>('/v1/mes/work_team/update', data);
};

/**
 * @description 删除WorkTeam
 * @param {number} id WorkTeam ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeWorkTeam = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/mes/work_team/delete/${id}`);
};

/**
 * @description 批量删除WorkTeam
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteWorkTeam = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/mes/work_team/bulk_delete', ids);
};

/**
 * @description 设置启用状态
 * @param {Partial<QueryParams>} params
 * @returns {Promise<Recordable>}
 * @example
 */
const setEnableStatus = (params: Recordable) => {
    return alovaInstance.Put<Recordable>(
        `/v1/mes/work_team/set_enable_status`,
        {
            params,
        }
    );
};

// /**
//  * @description 导出WorkTeam数据
//  * @param {WorkTeamQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportWorkTeam = (params?: WorkTeamQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/mes/work_team/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入WorkTeam数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importWorkTeam = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/mes/work_team/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getWorkTeamMetadata,
    getWorkTeamList,
    getWorkTeam,
    createWorkTeam,
    updateWorkTeam,
    removeWorkTeam,
    batchDeleteWorkTeam,
    setEnableStatus,
    // exportWorkTeam,
    // importWorkTeam,
};
