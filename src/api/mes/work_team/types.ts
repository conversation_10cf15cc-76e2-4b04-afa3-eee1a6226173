import type { ModelBase } from "@/types/core"

// WorkTeam 数据接口定义
export interface WorkTeam extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  code?: string; // 班组编号
  name?: string; // 班组名称
  team_type_id?: number; // 班组类型
  factory_id?: number; // 所属机构
  valid: boolean; // 启用否
  notes?: string; // 备注
  items: WorkTeamItem[]; // items
  factory?: Factory; // factory
  team_type?: DictData; // team_type
}

export interface WorkTeamCreate extends Omit<WorkTeam, 'id'> {

}

export interface WorkTeamUpdate extends WorkTeam {

}

// API 响应接口定义
