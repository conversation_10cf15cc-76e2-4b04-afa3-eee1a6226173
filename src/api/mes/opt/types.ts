import type { ModelBase } from "@/types/core"

/**
 * 单据状态
 */
export enum DocStatus {
  DRAFT = '草稿',
  PENDING_REVIEW = '待审核',
  UNDER_REVIEW = '审核中',
  APPROVED = '已审核',
  REJECTED = '已驳回',
  CANCELLED = '已作废'
}

// Opt 数据接口定义
export interface Opt extends ModelBase {
  id: number;
  version: number; // 乐观锁版本号
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  doc_status?: DocStatus; // 单据状态
  factory_id?: number; // 所属机构
  opt_date?: string; // 优化日期
  opt_no?: string; // 优化单号
  product_names?: string; // 产品名称汇总
  total_qty?: number; // 总数量
  total_specs_qty?: number; // 总规格数
  total_special_qty?: number; // 总异形数
  total_area?: number; // 总面积(m²)
  total_weight?: number; // 总重量(kg)
  total_girth?: number; // 总周长(m)
  abs?: number; // 公差
  edging?: number; // 磨边量
  width_edging?: number; // 宽磨边
  height_edging?: number; // 高磨边
  notes?: string; // 备注
  items: OptItem[]; // items
  material: OptMaterial[]; // material
}

export interface OptCreate extends Omit<Opt, 'id'> {

}

export interface OptUpdate extends Opt {

}

// API 响应接口定义
