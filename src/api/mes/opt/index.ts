import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { Opt, OptCreate, OptUpdate } from './types';

/**
 * @description 获取Opt元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getOptMetadata = () => {
    return alovaInstance.Get<any>('/v1/mes/opt/get_metadata');
};

/**
 * @description 获取Opt列表
 * @param {OptQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Opt>>} 返回包含Opt信息的Promise对象
 * @example
 * // 使用示例
 * const optList = await getOptList({ start: 1, limit: 20 });
 */
const getOptList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Opt>>(
        '/v1/mes/opt/query',
        params
    );
};

/**
 * @description 获取Opt详情
 * @param {number} id Opt ID
 * @returns {Promise<Opt>} 返回Opt详情信息
 */
const getOpt = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Opt>(`/v1/mes/opt/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 创建Opt
 * @param {OptCreate} data 创建数据
 * @returns {Promise<Opt>} 返回创建的Opt信息
 */
const createOpt = (data: OptCreate) => {
    return alovaInstance.Post<Opt>('/v1/mes/opt/create', data);
};

/**
 * @description 更新Opt
 * @param {OptUpdate} data 更新数据
 * @returns {Promise<Opt>} 返回更新后的Opt信息
 */
const updateOpt = (data: OptUpdate) => {
    return alovaInstance.Put<Opt>('/v1/mes/opt/update', data);
};

/**
 * @description 删除Opt
 * @param {number} id Opt ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeOpt = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/mes/opt/delete/${id}`);
};

/**
 * @description 批量删除Opt
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteOpt = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/mes/opt/bulk_delete', ids);
};

/**
 * @description 反审核Order
 * @param {number} id Order ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(`/v1/bas/opt/anti_approval`, {
        relation_id: id,
    });
};

// /**
//  * @description 导出Opt数据
//  * @param {OptQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportOpt = (params?: OptQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/mes/opt/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Opt数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importOpt = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/mes/opt/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getOptMetadata,
    getOptList,
    getOpt,
    createOpt,
    updateOpt,
    removeOpt,
    batchDeleteOpt,
    antiApproval,
    // exportOpt,
    // importOpt,
};
