import { useRequest } from 'alova/client';
import alovaInstance, { type ResponseSucess } from '../index';
import type { QueryParams } from '@/types/api/queryParams';
// import type { DefaultCurrent, CurrencyItem } from "@/views/mes/currency/config";

const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/mes/work_team/get_metadata`
    );
};

const getMetaData: (url: string) => Promise<Recordable> = (url: string) => {
    return new Promise((resolve, reject) => {
        const { onSuccess, onError } = useRequest(getPageMetaData(), {
            immediate: true, // 立即发送请求
        });
        onSuccess((res) => {
            resolve(res.data);
        });
        onError((err) => {
            reject(err);
        });
    });
};

/**
 * @description 创建班组
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const createWorkteam = (params: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/mes/work_team/create', params);
};

/**
 * @description 批量创建班组
 * @param {Partial<Recordable>} params
 * @returns {Promise<Recordable>}
 */
const bulkCreateWorkteam = (params: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>(
        '/v1/mes/work_team/bulk_create',
        params
    );
};

/**
 * @description 更新班组
 * @param {Partial<Recordable>} data
 * @returns {Promise<Recordable>}
 */
const updateWorkteam = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/mes/work_team/update', data);
};

/**
 * @description 删除班组
 * @param {Partial<CurrencyItem>}
 * @returns {Promise<Recordable>}
 */
const deleteWorkteam = (id?: string | number) => {
    return alovaInstance.Delete<Recordable>(`/v1/mes/work_team/delete/${id}`);
};

/**
 * @description 批量删除班组
 * @param {Partial<string>}
 * @returns {Promise<Recordable>}
 */
const bulkDeleteWorkteam = (ids: string[]) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/mes/work_team/bulk_delete`,
        ids
    );
};

/**
 * @description 查询班组
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getWorkteamList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/mes/work_team/query', data);
};

/**
 * @description 查询班组
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getWorkteam = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Recordable>(`/v1/mes/work_team/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 设置启用状态
 * @param {Partial<QueryParams>} params
 * @returns {Promise<Recordable>}
 * @example
 */
const setEnableStatus = (params: Recordable) => {
    return alovaInstance.Put<Recordable>(
        `/v1/mes/work_team/set_enable_status`,
        {
            params,
        }
    );
};

export {
    getPageMetaData,
    getMetaData,
    createWorkteam,
    bulkCreateWorkteam,
    updateWorkteam,
    deleteWorkteam,
    bulkDeleteWorkteam,
    getWorkteamList,
    getWorkteam,
    setEnableStatus,
};
