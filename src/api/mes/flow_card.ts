import alovaInstance, { type ResponseSucess } from '@/api';

const getPageMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/get_metadata`
    );
};

const getFlowCard = (id: string) => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/get?id=${id}&max_depth=2`,
        {
            cacheFor: 0,
        }
    );
};

const queryFlowCard = (data: Recordable) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/query`,
        data
    );
};

const createFlowCard = (data: Recordable) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/create`,
        data
    );
};

const updateFlowCard = (data: Recordable) => {
    return alovaInstance.Put<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/update`,
        data
    );
};

const deleteFlowCard = (id: string) => {
    return alovaInstance.Delete<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/delete/${id}`
    );
};

const bulkDeleteFlowCard = (ids: number[]) => {
    return alovaInstance.Delete<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/bulk_delete`,
        ids
    );
};

const antiApproval = (id: string) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/anti_approval`,
        {
            relation_id: id,
        }
    );
};

/*
生产通知单源数据
*/

const queryProductionNotice = (data: Recordable) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card_source/query`,
        data
    );
};

const getProductionNoticeMetaData = () => {
    return alovaInstance.Get<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card_source/get_metadata`
    );
};

/*
拆分流程卡
*/
const splitFlowCard = (data: Recordable) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/split_flow_card`,
        data
    );
};
/*
保存流程卡
*/
const saveFlowCard = (data: Recordable) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/save_flow_card`,
        data
    );
};

/*
保存调整流程卡
*/
const saveAdjustFlowCard = (data: Recordable) => {
    return alovaInstance.Post<ResponseSucess<Recordable>>(
        `/v1/mes/flow_card/adjustment`,
        data
    );
};

export {
    getPageMetaData,
    getFlowCard,
    queryFlowCard,
    createFlowCard,
    updateFlowCard,
    deleteFlowCard,
    bulkDeleteFlowCard,
    antiApproval,
    queryProductionNotice,
    getProductionNoticeMetaData,
    splitFlowCard,
    saveFlowCard,
    saveAdjustFlowCard,
};
