import alovaInstance from './index';
import type { ResponseSucess } from './index';
import { Order } from './sal/order/types';

/**
 * 获取页面 metaData 配置
 */
const getMetaData = (url: string) => {
    return alovaInstance.Get<ResponseSucess<any>>(`${url}/get_metadata`);
};

export interface RelationQuery {
    model_name: string;
    filters: any;
    limit: number;
    offset: number;
}
/** 根据名称获取关联数据 */
const getRelatedData = (params: RelationQuery) => {
    return alovaInstance.Get<ResponseSucess<any>>(
        '/v1/utils/relation/get_by_model',
        {
            params,
        }
    );
};

/**
 * @description 获取详情
 * @param {number} id ID
 * @returns {Promise<Order>} 返回Order详情信息
 */
const getDetail = (url: string, id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Order>(url, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

export { getMetaData, getRelatedData, getDetail };
