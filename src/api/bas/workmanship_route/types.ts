import type { ModelBase } from "@/types/core"

// WorkmanshipRoute 数据接口定义
export interface WorkmanshipRoute extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  factory_id?: number; // 所属组织
  code?: string; // 工艺编码
  name?: string; // 工艺名称
  business_id?: number; // 业务类型
  line_id?: number; // 生产线id
  route_version?: string; // 版本
  notes?: string; // 备注
  factory?: Factory; // factory
  business?: DictData; // business
  line?: Line; // line
  items: WorkmanshipRouteItem[]; // items
}

export interface WorkmanshipRouteCreate extends Omit<WorkmanshipRoute, 'id'> {

}

export interface WorkmanshipRouteUpdate extends WorkmanshipRoute {

}

// API 响应接口定义
