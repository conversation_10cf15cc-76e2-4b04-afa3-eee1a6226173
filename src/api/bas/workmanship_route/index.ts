import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
    WorkmanshipRoute,
    WorkmanshipRouteCreate,
    WorkmanshipRouteUpdate,
} from './types';

/**
 * @description 获取WorkmanshipRoute元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getWorkmanshipRouteMetadata = () => {
    return alovaInstance.Get<any>('/v1/bas/workmanship_route/get_metadata');
};

/**
 * @description 获取WorkmanshipRoute列表
 * @param {WorkmanshipRouteQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<WorkmanshipRoute>>} 返回包含WorkmanshipRoute信息的Promise对象
 * @example
 * // 使用示例
 * const workmanshipRouteList = await getWorkmanshipRouteList({ start: 1, limit: 20 });
 */
const getWorkmanshipRouteList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<WorkmanshipRoute>>(
        '/v1/bas/workmanship_route/query',
        params
    );
};

/**
 * @description 获取WorkmanshipRoute详情
 * @param {number} id WorkmanshipRoute ID
 * @returns {Promise<WorkmanshipRoute>} 返回WorkmanshipRoute详情信息
 */
const getWorkmanshipRoute = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<WorkmanshipRoute>(
        `/v1/bas/workmanship_route/get`,
        {
            params: {
                id,
                max_depth,
            },
        }
    );
};

/**
 * @description 创建WorkmanshipRoute
 * @param {WorkmanshipRouteCreate} data 创建数据
 * @returns {Promise<WorkmanshipRoute>} 返回创建的WorkmanshipRoute信息
 */
const createWorkmanshipRoute = (data: WorkmanshipRouteCreate) => {
    return alovaInstance.Post<WorkmanshipRoute>(
        '/v1/bas/workmanship_route/create',
        data
    );
};

/**
 * @description 更新WorkmanshipRoute
 * @param {WorkmanshipRouteUpdate} data 更新数据
 * @returns {Promise<WorkmanshipRoute>} 返回更新后的WorkmanshipRoute信息
 */
const updateWorkmanshipRoute = (data: WorkmanshipRouteUpdate) => {
    return alovaInstance.Put<WorkmanshipRoute>(
        '/v1/bas/workmanship_route/update',
        data
    );
};

/**
 * @description 删除WorkmanshipRoute
 * @param {number} id WorkmanshipRoute ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeWorkmanshipRoute = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/bas/workmanship_route/delete/${id}`);
};

/**
 * @description 批量删除WorkmanshipRoute
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteWorkmanshipRoute = (ids: number[]) => {
    return alovaInstance.Delete<any>(
        '/v1/bas/workmanship_route/bulk_delete',
        ids
    );
};

/**
 * @description 反审核Order
 * @param {number} id Order ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(
        `/v1/bas/workmanship_route/anti_approval`,
        {
            relation_id: id,
        }
    );
};

// /**
//  * @description 导出WorkmanshipRoute数据
//  * @param {WorkmanshipRouteQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportWorkmanshipRoute = (params?: WorkmanshipRouteQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/bas/workmanship_route/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入WorkmanshipRoute数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importWorkmanshipRoute = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/bas/workmanship_route/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getWorkmanshipRouteMetadata,
    getWorkmanshipRouteList,
    getWorkmanshipRoute,
    createWorkmanshipRoute,
    updateWorkmanshipRoute,
    removeWorkmanshipRoute,
    batchDeleteWorkmanshipRoute,
    antiApproval,
    // exportWorkmanshipRoute,
    // importWorkmanshipRoute,
};
