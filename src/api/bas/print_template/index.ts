import alovaInstance from '@/api';
import type { ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type {
    PrintTemplate,
    PrintTemplateCreate,
    PrintTemplateUpdate,
} from './types';

/**
 * @description 获取PrintTemplate元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getPrintTemplateMetadata = () => {
    return alovaInstance.Get<any>('/v1/bas/print_template/get_metadata');
};

/**
 * @description 获取PrintTemplate列表
 * @param {PrintTemplateQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<PrintTemplate>>} 返回包含PrintTemplate信息的Promise对象
 * @example
 * // 使用示例
 * const printTemplateList = await getPrintTemplateList({ start: 1, limit: 20 });
 */
const getPrintTemplateList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<PrintTemplate>>(
        '/v1/bas/print_template/query',
        params
    );
};

/**
 * @description 获取PrintTemplate详情
 * @param {DetailQuery} params PrintTemplate ID
 * @returns {Promise<PrintTemplate>} 返回PrintTemplate详情信息
 */
const getPrintTemplate = (params: DetailQuery) => {
    return alovaInstance.Get<PrintTemplate>('/v1/bas/print_template/get', {
        params,
    });
};

/**
 * @description 创建PrintTemplate
 * @param {PrintTemplateCreate} data 创建数据
 * @returns {Promise<PrintTemplate>} 返回创建的PrintTemplate信息
 */
const createPrintTemplate = (data: PrintTemplateCreate) => {
    return alovaInstance.Post<PrintTemplate>(
        '/v1/bas/print_template/create',
        data
    );
};

/**
 * @description 更新PrintTemplate
 * @param {PrintTemplateUpdate} data 更新数据
 * @returns {Promise<PrintTemplate>} 返回更新后的PrintTemplate信息
 */
const updatePrintTemplate = (data: PrintTemplateUpdate) => {
    return alovaInstance.Put<PrintTemplate>(
        '/v1/bas/print_template/update',
        data
    );
};

/**
 * @description 删除PrintTemplate
 * @param {number} id PrintTemplate ID
 * @returns {Promise<any>} 返回删除结果
 */
const removePrintTemplate = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/bas/print_template/delete/${id}`);
};

/**
 * @description 批量删除PrintTemplate
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeletePrintTemplate = (ids: number[]) => {
    return alovaInstance.Delete<any>('/bas/print_template/bulk_delete', ids);
};

// /**
//  * @description 导出PrintTemplate数据
//  * @param {PrintTemplateQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportPrintTemplate = (params?: PrintTemplateQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/bas/print_template/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入PrintTemplate数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importPrintTemplate = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/bas/print_template/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getPrintTemplateMetadata,
    getPrintTemplateList,
    getPrintTemplate,
    createPrintTemplate,
    updatePrintTemplate,
    removePrintTemplate,
    bulkDeletePrintTemplate,
    // exportPrintTemplate,
    // importPrintTemplate,
};
