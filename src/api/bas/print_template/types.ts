import type { ModelBase } from '@/types/core'

interface DataSource {
  template_id?: number
  query_sql?: string
  name?: string
  id?: number
  json_data?: string
}

// PrintTemplate 数据接口定义
export interface PrintTemplate extends ModelBase {
  id?: number
  created_at?: string // 创建时间
  updated_at?: string // 更新时间
  created_by?: number // 创建者
  updated_by?: number // 更新者
  name: string // 模板名称
  tag: string // 模板标记
  module?: string // 所属模块
  file_path?: string // 模版文件路径
  valid?: boolean // 生效
  notes?: string // 备注
  data_source: DataSource[] // data_source
}

export interface PrintTemplateCreate extends Omit<PrintTemplate, 'id'> {}

export interface PrintTemplateUpdate extends PrintTemplate {}

/** 打印/预览/设计的请求类型 */
export interface PrintRequest {
  code?: string
  template_id?: number
  ids?: Array<number>
  pages?: Array<unknown>
  print_by?: number
  user_name?: string
  token?: string
  json_data?: string
  is_record?: boolean
}
