import alovaInstance from '../index';
import type { QueryParams } from '@/types/api/queryParams';
/**
 * @description 获取工艺信息列表数据
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<any>} 返回包含工艺信息的Promise对象
 * @example
 * // 使用示例
 * const workmanshipList = await getWorkmanshipList(data);
 */
const getWorkmanshipList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<any>('/v1/bas/workmanship/query', data);
};

export { getWorkmanshipList };
