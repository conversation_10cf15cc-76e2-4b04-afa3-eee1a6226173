import alovaInstance from '@/api'
import type { ResponseListModel } from '@/types/core'
import type {
  DetailQuery,
  QueryParams,
  QueryResponse,
} from '@/types/api/queryParams'

import type { DictType, DictTypeCreate, DictTypeUpdate } from './types'
import { DictDataItem } from '@/views/bas/dict/dict'

/**
 * @description 获取DictType元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getDictTypeInfoMetadata = () => {
  return alovaInstance.Get<any>('/v1/bas/dict_type/get_metadata')
}

/**
 * @description 获取DictType列表
 * @param {DictTypeQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<DictType>>} 返回包含DictType信息的Promise对象
 * @example
 * // 使用示例
 * const dictTypeList = await getDictTypeList({ start: 1, limit: 20 });
 */
const getDictTypeList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<DictType>>(
    '/v1/bas/dict_type/query',
    params
  )
}

/**
 * @description 获取DictType详情
 * @param {number} id DictType ID
 * @returns {Promise<DictType>} 返回DictType详情信息
 */
const getDictTypeInfo = (params: DetailQuery) => {
  return alovaInstance.Get<DictType>('/v1/bas/dict_type/get', { params })
}

/**
 * @description 创建DictType
 * @param {DictTypeCreate} data 创建数据
 * @returns {Promise<DictType>} 返回创建的DictType信息
 */
const createDictType = (data: DictTypeCreate) => {
  return alovaInstance.Post<DictType>('/v1/bas/dict_type/create', data)
}

/**
 * @description 更新DictType
 * @param {DictTypeUpdate} data 更新数据
 * @returns {Promise<DictType>} 返回更新后的DictType信息
 */
const updateDictType = (data: DictTypeUpdate) => {
  return alovaInstance.Put<DictType>('/v1/bas/dict_type/update', data)
}

/**
 * @description 删除DictType
 * @param {number} id DictType ID
 * @returns {Promise<any>} 返回删除结果
 */
const deleteDictType = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/bas/dict_type/delete/${id}`)
}

/**
 * @description 批量删除DictType
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteDictType = (ids: number[]) => {
  return alovaInstance.Delete<any>('/v1/bas/dict_type/bulk_delete', ids)
}

/**
 * @description 获取枚举列表
 * @param {number} id DictType ID
 * @returns {Promise<DictType>} 返回DictType详情信息
 */
const getEnumList = (params: Recordable) => {
  return alovaInstance.Get<DictType>('/v1/bas/dict_data/get_enum_list', {
    params,
  })
}

// /**
//  * @description 导出DictType数据
//  * @param {DictTypeQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportDictType = (params?: DictTypeQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/bas/dict_type/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入DictType数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importDictType = (file: File) => {
//   const formData = new FormData();
//   formData.append('/v1file', file);

//   return alovaInstance.Post<any>('/v1/bas/dict_type/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

/** == 字典数据接口 ======================================================================================================== */

/** 获取字典数据metadata */
const getDictDataMetadata = () => {
  return alovaInstance.Get<Record<string, any>>(
    '/v1/bas/dict_data/get_metadata'
  )
}
/**
 * @description 获取字典数据列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Record<string, any>>} 返回包含字典信息的Promise对象
 * @example
 * // 使用示例
 * const dictList = await getDictDataList(data);
 */
const getDictDataList = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<QueryResponse<DictDataItem>>(
    '/v1/bas/dict_data/query',
    data
  )
}

/**
 * 新增字典数据
 */
const createDictData = (data: Partial<Record<string, any>>) => {
  return alovaInstance.Post<Record<string, any>>(
    '/v1/bas/dict_data/create',
    data
  )
}

/**
 * 批量新增字典数据
 */
const createDictDataBatch = (data: Partial<Record<string, any>>) => {
  return alovaInstance.Post<Record<string, any>>(
    '/v1/bas/dict_data/bulk_create',
    data
  )
}

/**
 * @description 修改字典数据
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Record<string, any>>} 返回包含字典信息的Promise对象
 * @example
 */
const updateDictData = (data: Partial<Record<string, any>>) => {
  return alovaInstance.Put<Record<string, any>>(
    '/v1/bas/dict_data/update',
    data
  )
}

/**
 * @description 删除字典数据
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Record<string, any>>} 返回包含字典信息的Promise对象
 * @example
 */
const deleteDictData = (id: number) => {
  return alovaInstance.Delete<Record<string, any>>(
    `/v1/bas/dict_data/delete/${id}`
  )
}

/**
 * @description 获取字典
 * @param {string} code - 字典信息
 * @returns {Promise<DictDataItem>} 返回包含创建字典信息的Promise对象
 * @example
 */
const getDictByCode = (code: string) => {
  return alovaInstance.Get<DictDataItem>(`/v1/bas/dict_data/get_by_code`, {
    params: { code },
  })
}

/**
 * 获取批量新增，删除，修改的常用语字典
 * @param {Partial<Recordable>} data - 字典
 * @returns {Promise<Recordable>} 返回包含单个字典的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleStakeholder(data);/api/v1/bas/common_phrases/change_common_phrases
 * */

const changeCommonPhrases = (data: Partial<any>) => {
  return alovaInstance.Post(
    '/v1/bas/common_phrases/change_common_phrases',
    data
  )
}

/**
 * @description 获取常用语数据列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Record<string, any>>} 返回包含字典信息的Promise对象
 * @example
 * // 使用示例
 * const dictList = await getDictDataList(data);
 */
const getCommonPhrasesList = (data: Partial<QueryParams>) => {
  return alovaInstance.Post<Record<string, any>>(
    '/v1/bas/common_phrases/query',
    data
  )
}

export {
  getDictTypeInfoMetadata,
  getDictTypeList,
  getDictTypeInfo,
  createDictType,
  updateDictType,
  deleteDictType,
  batchDeleteDictType,
  changeCommonPhrases,
  getCommonPhrasesList,
  // exportDictType,
  // importDictType,
  getDictDataMetadata,
  getDictDataList,
  createDictData,
  createDictDataBatch,
  updateDictData,
  deleteDictData,
  getDictByCode,
  getEnumList,
}
