import type { ModelBase } from '@/types/core';

// DictType 数据接口定义
export interface DictType extends ModelBase {
    id?: number;
    created_at?: string | Date; // 创建时间
    updated_at?: string | Date; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    code?: string; // 字典类型编码
    name?: string; // 字典类型名称
    is_system?: boolean; // 是否系统字典
    valid?: boolean; // 是否生效
    notes?: string; // 备注
    custom_sql?: string; // 自定义查询SQL
    dict_datas: any[]; // dict_datas
}

export interface DictTypeCreate extends Omit<DictType, 'id'> {}

export interface DictTypeUpdate extends DictType {}

// API 响应接口定义
