import alovaInstance from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/**
 * 根据单据类型的编码规则生成编码
 * @param {Partial<Record<string, any>>} params - 编码规则信息
 * @returns {Promise<Record<string, any>>} 返回包含生成编码信息的Promise对象
 * @example
 * // 使用示例
 * const generatedOrderCode = await generateOrderCode({ doc_type: "销售订单" });
 */
const generateOrderCode = (params: {
    doc_type: string;
    classify_code?: string;
}) => {
    return alovaInstance.Get<Record<string, any>>(
        '/v1/sys/code_trace/get_code',
        {
            params,
            cacheFor: 0,
        }
    );
};

export { generateOrderCode };
