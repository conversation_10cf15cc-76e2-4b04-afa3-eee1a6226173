import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type { Config, ConfigCreate, ConfigUpdate } from './types';

/**
 * @description 获取Config元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getConfigMetadata = () => {
    return alovaInstance.Get<any>('/v1/bas/config/get_metadata');
};

/**
 * @description 获取Config列表
 * @param {ConfigQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Config>>} 返回包含Config信息的Promise对象
 * @example
 * // 使用示例
 * const configList = await getConfigList({ start: 1, limit: 20 });
 */
const getConfigList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Config>>(
        '/v1/bas/config/query',
        params
    );
};

/**
 * @description 获取Config详情
 * @param {number} id Config ID
 * @returns {Promise<Config>} 返回Config详情信息
 */
const getConfig = (params: DetailQuery) => {
    return alovaInstance.Get<Config>('/v1/bas/config/get', { params });
};

/**
 * @description 创建Config
 * @param {ConfigCreate} data 创建数据
 * @returns {Promise<Config>} 返回创建的Config信息
 */
const createConfig = (data: ConfigCreate) => {
    return alovaInstance.Post<Config>('/v1/bas/config/create', data);
};

/**
 * @description 更新Config
 * @param {ConfigUpdate} data 更新数据
 * @returns {Promise<Config>} 返回更新后的Config信息
 */
const updateConfig = (data: ConfigUpdate) => {
    return alovaInstance.Put<Config>('/v1/bas/config/update', data);
};

/**
 * @description 删除Config
 * @param {number} id Config ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeConfig = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/bas/config/delete/${id}`);
};

/**
 * @description 批量删除Config
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteConfig = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/bas/config/bulk_delete', ids);
};

// /**
//  * @description 导出Config数据
//  * @param {ConfigQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportConfig = (params?: ConfigQueryParams) => {
//   return alovaInstance.Post<Blob>('/bas/config/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Config数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importConfig = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/bas/config/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getConfigMetadata,
    getConfigList,
    getConfig,
    createConfig,
    updateConfig,
    removeConfig,
    batchDeleteConfig,
    // exportConfig,
    // importConfig,
};
