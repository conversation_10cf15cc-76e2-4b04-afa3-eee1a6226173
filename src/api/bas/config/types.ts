import type { ModelBase } from '@/types/core';

// Config 数据接口定义
export interface Config extends ModelBase {
    id: number;
    created_at: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    code?: string; // 配置编码
    name?: string; // 配置名称
    value?: string; // 参数值
    is_system: boolean; // 是否系统参数
    notes?: string; // 备注
    module_name?: string; // 所属模块名称
    module_group?: string; // 模块组别名称
}

export interface ConfigCreate extends Omit<Config, 'id'> {}

export interface ConfigUpdate extends Config {}

// API 响应接口定义
