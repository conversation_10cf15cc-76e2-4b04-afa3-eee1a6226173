import type { ModelBase } from "@/types/core"

/**
 * 单据类型
 */
export enum DocumentType {
  SALE_ORDER = '销售订单',
  SALE_QUOTATION = '销售报价',
  SALE_CONTRACT = '销售合同',
  SALE_DELIVERY = '销售送货',
  SALE_RETURN = '销售退货',
  PURCHASE_ORDER = '采购订单',
  PURCHASE_RETURN = '采购退货',
  PURCHASE_CONTRACT = '采购合同',
  INBOUND = '入库',
  OUTBOUND = '出库',
  ADJUSTMENT = '调拨',
  RETURN = '退货',
  INVENTORY_LOCATION_INFO = '仓位资料',
  FIN_RECEIVABLE = '应收账单',
  FIN_COLLECTION = '收款单',
  FIN_PAYABLE = '应付账单',
  FIN_PAYMENT = '付款单',
  FIN_ACCOUNTING_ENTRY = '财务分录',
  FIN_VOUCHER = '财务凭证',
  FIN_ANALYSIS = '财务分析',
  FIN_BUDGET = '财务预算',
  FIN_FORECAST = '财务预测',
  INVENTORY_INFO = '仓库资料',
  OTHER = '其他'
}

/**
 * 日期生成规则
 */
export enum CodeGenerationRule {
  NONE = '不使用日期',
  YYMMDD = 'YYMMDD',
  YYYYMMDD = 'YYYYMMDD',
  YYYYMM = 'YYYYMM',
  YYMM = 'YYMM',
  YYYY = 'YYYY',
  YY = 'YY'
}

/**
 * 序号重置频率
 */
export enum CodeResetFrequency {
  DAILY = '每天',
  MONTHLY = '每月',
  YEARLY = '每年',
  NEVER = '从不'
}

// CodeSetting 数据接口定义
export interface CodeSetting extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  doc_type: DocumentType; // 单据类型
  date_rule: CodeGenerationRule; // 日期生成规则
  reset_frequency: CodeResetFrequency; // 序号重置频率
  prefix: string; // 前缀代码
  suffix_length: number; // 后缀序列号长度(3-8位)
  placeholder: boolean; // 占位符
}

export interface CodeSettingCreate extends Omit<CodeSetting, 'id'> {

}

export interface CodeSettingUpdate extends CodeSetting {

}

// API 响应接口定义
