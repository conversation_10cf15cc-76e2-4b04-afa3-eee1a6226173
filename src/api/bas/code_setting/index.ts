import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type {
    CodeSetting,
    CodeSettingCreate,
    CodeSettingUpdate,
} from './types';

/**
 * @description 获取CodeSetting元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getCodeSettingMetadata = () => {
    return alovaInstance.Get<any>('/v1/sys/code_setting/get_metadata');
};

/**
 * @description 获取CodeSetting列表
 * @param {CodeSettingQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<CodeSetting>>} 返回包含CodeSetting信息的Promise对象
 * @example
 * // 使用示例
 * const codeSettingList = await getCodeSettingList({ start: 1, limit: 20 });
 */
const getCodeSettingList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<CodeSetting>>(
        '/v1/sys/code_setting/query',
        params
    );
};

/**
 * @description 获取CodeSetting详情
 * @param {number} id CodeSetting ID
 * @returns {Promise<CodeSetting>} 返回CodeSetting详情信息
 */
const getCodeSetting = (params: DetailQuery) => {
    return alovaInstance.Get<CodeSetting>('/v1/sys/code_setting/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * @description 创建CodeSetting
 * @param {CodeSettingCreate} data 创建数据
 * @returns {Promise<CodeSetting>} 返回创建的CodeSetting信息
 */
const createCodeSetting = (data: CodeSettingCreate) => {
    return alovaInstance.Post<CodeSetting>('/v1/sys/code_setting/create', data);
};

/**
 * @description 更新CodeSetting
 * @param {CodeSettingUpdate} data 更新数据
 * @returns {Promise<CodeSetting>} 返回更新后的CodeSetting信息
 */
const updateCodeSetting = (data: CodeSettingUpdate) => {
    return alovaInstance.Put<CodeSetting>('/v1/sys/code_setting/update', data);
};

/**
 * @description 删除CodeSetting
 * @param {number} id CodeSetting ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeCodeSetting = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/sys/code_setting/delete/${id}`);
};

/**
 * @description 批量删除CodeSetting
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteCodeSetting = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/sys/code_setting/bulk_delete', ids);
};

// /**
//  * @description 导出CodeSetting数据
//  * @param {CodeSettingQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportCodeSetting = (params?: CodeSettingQueryParams) => {
//   return alovaInstance.Post<Blob>('/sys/code_setting/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入CodeSetting数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importCodeSetting = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/sys/code_setting/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

/**
 * @description 切换编码规则状态
 * @param {Partial<boolean>} data - 编码规则状态
 * @returns {Promise<Record<string, any>>}
 */
const switchStatus = (data: Record<string, any>) => {
    return alovaInstance.Put<Record<string, any>>(
        '/v1/sys/code_setting/set_enable_status',
        { params: data }
    );
};

/**
 * 根据单据类型的编码规则生成编码
 * @param {Partial<Record<string, any>>} params - 编码规则信息
 * @returns {Promise<Record<string, any>>} 返回包含生成编码信息的Promise对象
 * @example
 * // 使用示例
 * const generatedOrderCode = await generateOrderCode({ doc_type: "销售订单" });
 */
const generateOrderCode = (params: {
    doc_type: string;
    classify_code?: string;
}) => {
    return alovaInstance.Get<Record<string, any>>(
        '/v1/sys/code_trace/get_code',
        {
            params,
            cacheFor: 0,
        }
    );
};

export {
    getCodeSettingMetadata,
    getCodeSettingList,
    getCodeSetting,
    createCodeSetting,
    updateCodeSetting,
    removeCodeSetting,
    batchDeleteCodeSetting,
    // exportCodeSetting,
    // importCodeSetting,
    switchStatus,
    generateOrderCode,
};
