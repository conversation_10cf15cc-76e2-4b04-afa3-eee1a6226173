import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { payment, paymentCreate, paymentUpdate } from './types';

/**
 * @description 获取payment元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getMetadata = () => {
    return alovaInstance.Get<any>('/v1/bas/payment_terms/get_metadata');
};

/**
 * @description 获取payment列表
 * @param {paymentQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<payment>>} 返回包含payment信息的Promise对象
 * @example
 * // 使用示例
 * const paymentList = await getList({ page: 1, size: 20 });
 */
const getList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<payment>>(
        '/v1/bas/payment_terms/query',
        params
    );
};

/**
 * @description 获取payment详情
 * @param {number} id payment ID
 * @returns {Promise<payment>} 返回payment详情信息
 */
const get = (id?: number) => {
    const url = id
        ? `/v1/bas/payment_terms/get/${id}`
        : '/v1/bas/payment_terms/get';
    return alovaInstance.Get<payment>(url);
};

/**
 * @description 创建payment
 * @param {paymentCreateParams} data 创建数据
 * @returns {Promise<payment>} 返回创建的payment信息
 */
const create = (data: Recordable) => {
    return alovaInstance.Post<payment>('/v1/bas/payment_terms/create', data);
};

/**
 * @description 更新payment
 * @param {paymentUpdateParams} data 更新数据
 * @returns {Promise<payment>} 返回更新后的payment信息
 */
const update = (data: Recordable) => {
    return alovaInstance.Put<payment>('/v1/bas/payment_terms/update', data);
};

/**
 * @description 删除payment
 * @param {number} id payment ID
 * @returns {Promise<any>} 返回删除结果
 */
const remove = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/bas/payment_terms/delete/${id}`);
};

/**
 * @description 批量删除payment
 * @param {number[]} ids payment ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */

const batchDelete = (ids: string[]) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/bas/payment_terms/bulk_delete`,
        ids
    );
};

/**
 * @description 切换付款条款状态
 * @param {Partial<boolean>} data - 付款条款状态
 * @returns {Promise<Recordable>}
 */
const switchStatus = (data: Recordable) => {
    return alovaInstance.Put<Recordable>(
        '/v1/bas/payment_terms/set_enable_status',
        { params: data }
    );
};

// /**
//  * @description 导出payment数据
//  * @param {paymentQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportData = (params?: paymentQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/bas/payment_terms/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入payment数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importData = (file: File) => {
//   const formData = new FormData();
//   formData.append('/v1file', file);

//   return alovaInstance.Post<any>('/v1/bas/payment_terms/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getMetadata,
    getList,
    get,
    create,
    update,
    remove,
    batchDelete,
    switchStatus,
    //   exportData,
    //   importData,
};
