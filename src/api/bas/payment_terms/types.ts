import type { ModelBase } from "@/types/core"

// payment 数据接口定义
export interface payment extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  name: string; // 条款名称
  monthly_settlement: boolean; // 是否月结
  payment_before_shipment: boolean; // 款到发货
  days?: number; // 月结天数
  sign: boolean; // 签收后才付款
  valid: boolean; // 是否生效
  notes?: string; // 备注
}

export interface paymentCreate extends Omit<payment, 'id'> {

}

export interface paymentUpdate extends payment {

}

// API 响应接口定义
