import type { ModelBase } from '@/types/core';

// Currency 数据接口定义
export interface Currency extends ModelBase {
    id: number;
    created_at: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    code?: string; // 货币编码
    name?: string; // 货币名称
    alias?: string; // 货币别名
    symbol?: string; // 货币符号
    exchange_rate: number; // 汇率
    standard_currency: boolean; // 是否为本位币
    valid: boolean; // 是否生效
    notes?: string; // 备注
}

export interface CurrencyCreate extends Omit<Currency, 'id'> {}

export interface CurrencyUpdate extends Omit<Currency, 'id'> {}

// API 响应接口定义
