import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type { Currency, CurrencyCreate, CurrencyUpdate } from './types';

/**
 * @description 获取Currency元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getCurrencyMetadata = () => {
    return alovaInstance.Get<any>('/v1/bas/currency/get_metadata');
};

/**
 * @description 获取Currency列表
 * @param {CurrencyQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Currency>>} 返回包含Currency信息的Promise对象
 * @example
 * // 使用示例
 * const currencyList = await getCurrencyList({ start: 1, limit: 20 });
 */
const getCurrencyList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Currency>>(
        '/v1/bas/currency/query',
        params
    );
};

/**
 * @description 获取Currency详情
 * @param {number} id Currency ID
 * @returns {Promise<Currency>} 返回Currency详情信息
 */
const getCurrency = (params: DetailQuery) => {
    return alovaInstance.Get<Currency>('/v1/bas/currency/get', { params });
};

/**
 * @description 创建Currency
 * @param {CurrencyCreate} data 创建数据
 * @returns {Promise<Currency>} 返回创建的Currency信息
 */
const createCurrency = (data: CurrencyCreate) => {
    return alovaInstance.Post<Currency>('/v1/bas/currency/create', data);
};

/**
 * @description 更新Currency
 * @param {CurrencyUpdate} data 更新数据
 * @returns {Promise<Currency>} 返回更新后的Currency信息
 */
const updateCurrency = (data: CurrencyUpdate) => {
    return alovaInstance.Put<Currency>('/v1/bas/currency/update', data);
};

/**
 * @description 删除Currency
 * @param {number} id Currency ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeCurrency = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/bas/currency/delete/${id}`);
};

/**
 * @description 批量删除Currency
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteCurrency = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/bas/currency/bulk_delete', ids);
};

/**
 * @description 切换币种状态
 * @param {Partial<boolean>} data - 币种状态
 * @returns {Promise<Record<string, any>>}
 */
const switchStatus = (data: { ids: string; status: boolean }) => {
    return alovaInstance.Put<any>('/v1/bas/currency/set_enable_status', {
        params: data,
    });
};

/**
 * @description 设置本位币
 * @param {Partial<Currency>} data - 币种信息
 * @returns {Promise<Record<string, any>>}
 */
const setBaseCurrency = (data: Partial<Currency>) => {
    return alovaInstance.Put<Record<string, any>>(
        '/v1/bas/currency/set_standard_currency',
        { params: data }
    );
};
// /**
//  * @description 导出Currency数据
//  * @param {CurrencyQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportCurrency = (params?: CurrencyQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/bas/currency/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Currency数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importCurrency = (file: File) => {
//   const formData = new FormData();
//   formData.append('/v1file', file);

//   return alovaInstance.Post<any>('/v1/bas/currency/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getCurrencyMetadata,
    getCurrencyList,
    getCurrency,
    createCurrency,
    updateCurrency,
    removeCurrency,
    batchDeleteCurrency,
    switchStatus,
    setBaseCurrency,
    // exportCurrency,
    // importCurrency,
};
