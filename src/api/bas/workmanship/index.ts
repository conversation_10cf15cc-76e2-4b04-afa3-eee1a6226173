import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
    Workmanship,
    WorkmanshipCreate,
    WorkmanshipUpdate,
} from './types';

/**
 * @description 获取Workmanship元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getWorkmanshipMetadata = () => {
    return alovaInstance.Get<any>('/v1/bas/workmanship/get_metadata');
};

/**
 * @description 获取Workmanship列表
 * @param {WorkmanshipQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Workmanship>>} 返回包含Workmanship信息的Promise对象
 * @example
 * // 使用示例
 * const workmanshipList = await getWorkmanshipList({ start: 1, limit: 20 });
 */
const getWorkmanshipList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Workmanship>>(
        '/v1/bas/workmanship/query',
        params
    );
};

/**
 * @description 获取Workmanship详情
 * @param {number} id Workmanship ID
 * @returns {Promise<Workmanship>} 返回Workmanship详情信息
 */
const getWorkmanship = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Workmanship>(`/v1/bas/workmanship/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 创建Workmanship
 * @param {WorkmanshipCreate} data 创建数据
 * @returns {Promise<Workmanship>} 返回创建的Workmanship信息
 */
const createWorkmanship = (data: WorkmanshipCreate) => {
    return alovaInstance.Post<Workmanship>('/v1/bas/workmanship/create', data);
};

/**
 * @description 更新Workmanship
 * @param {WorkmanshipUpdate} data 更新数据
 * @returns {Promise<Workmanship>} 返回更新后的Workmanship信息
 */
const updateWorkmanship = (data: WorkmanshipUpdate) => {
    return alovaInstance.Put<Workmanship>('/v1/bas/workmanship/update', data);
};

/**
 * @description 删除Workmanship
 * @param {number} id Workmanship ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeWorkmanship = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/bas/workmanship/delete/${id}`);
};

/**
 * @description 批量删除Workmanship
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteWorkmanship = (ids: number[]) => {
    return alovaInstance.Post<any>('/v1/bas/workmanship/bulk_delete', ids);
};

// /**
//  * @description 导出Workmanship数据
//  * @param {WorkmanshipQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportWorkmanship = (params?: WorkmanshipQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/bas/workmanship/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Workmanship数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importWorkmanship = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/bas/workmanship/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getWorkmanshipMetadata,
    getWorkmanshipList,
    getWorkmanship,
    createWorkmanship,
    updateWorkmanship,
    removeWorkmanship,
    batchDeleteWorkmanship,
    // exportWorkmanship,
    // importWorkmanship,
};
