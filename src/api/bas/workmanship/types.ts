import type { ModelBase } from "@/types/core"

/**
 * 工序类型
 */
export enum WipType {
  Cut = '切割',
  Drill = '钻孔',
  Edge = '磨边',
  Clean = '清洗',
  Tempered = '钢化',
  Combined = '合片',
  Other = '其他'
}

/**
 * 计价方式
 */
export enum PricingMode {
  Area = '按面积计价',
  Number = '按数量计价',
  Girth = '按周长计价',
  Weight = '按重量计价'
}

// Workmanship 数据接口定义
export interface Workmanship extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  factory_id?: number; // 所属组织
  code?: string; // 工艺编码
  name?: string; // 工艺名称
  alias?: string; // 工艺别名
  seq_no?: number; // 工艺序号
  wip_type: WipType; // 工序类型
  pricing_mode?: PricingMode; // 计价方式
  price?: number; // 单价
  uom_id?: number; // 加工单位
  is_charge: boolean; // 收费工艺
  is_handover: boolean; // 交接否
  is_check: boolean; // 校验否
  is_out: boolean; // 委外否
  is_multiple: boolean; // 多品种并行加工否
  smg_out_flag: boolean; // 半成品出库工艺
  valid: boolean; // 生效
  notes?: string; // 备注
  uom?: DictData; // uom
  factory?: Factory; // factory
}

export interface WorkmanshipCreate extends Omit<Workmanship, 'id'> {

}

export interface WorkmanshipUpdate extends Workmanship {

}

// API 响应接口定义
