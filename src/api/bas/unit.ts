import alovaInstance from '@/api';
import type { ModelBase } from '@/types/core';

/**************************************************
 *接口定义
 ***************************************************/
interface Unit extends ModelBase {
    id?: number;
    name: string;
    code?: string;
    description?: string;
    status?: boolean;
}

// 接口-单位创建
type UnitCreate = Omit<Unit, 'id'>;

//接口-单位更新
interface UnitUpdate extends Unit {
    id: number;
}

/**************************************************
 *方法定义
 ***************************************************/

/**
 * @description 获取单位列表
 * @returns {Promise<Unit[]>} 返回包含单位信息的Promise对象
 */
function getUnitList(params?: any) {
    return alovaInstance.Post<Unit[]>('/v1/bas/unit/query', params);
}

/**
 * @description 创建新单位
 * @param {UnitCreate} data - 单位信息
 * @returns {Promise<Unit>} 返回包含创建单位信息的Promise对象
 */
async function createUnit(data: UnitCreate) {
    return alovaInstance.Post<Unit>('/v1/bas/unit/create', data);
}

/**
 * @description 删除单位
 * @param {number} id - 单位ID
 * @returns {Promise<any>} 返回包含删除结果的Promise对象
 */
async function deleteUnit(id: number) {
    return alovaInstance.Delete<any>('/v1/bas/unit/delete', { id });
}

/**
 * @description 更新单位信息
 * @param {UnitUpdate} data - 单位信息
 * @returns {Promise<Unit>} 返回包含更新单位信息的Promise对象
 */
async function updateUnit(data: UnitUpdate) {
    return alovaInstance.Put<Unit>('/v1/bas/unit/update', data);
}

/**************************************************
 *导出接口和方法
 ***************************************************/

export type { Unit, UnitCreate, UnitUpdate };

export { getUnitList, createUnit, deleteUnit, updateUnit };
