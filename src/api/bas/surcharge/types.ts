import type { ModelBase } from '@/types/core';

// Surcharge 数据接口定义
export interface Surcharge extends ModelBase {
    id?: number;
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    code?: string; // 费用编码
    name?: string; // 费用名称
    price?: number; // 单价
    picture?: string; // 图片
    uom_id?: number; // 单位
    valid?: boolean; // 是否生效
    notes?: string; // 备注
    uom?: any; // uom
}

export interface SurchargeCreate extends Omit<Surcharge, 'id'> {}

export interface SurchargeUpdate extends Surcharge {}

// API 响应接口定义
