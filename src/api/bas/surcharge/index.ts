import alovaInstance from '@/api';
import type { ResponseListModel } from '@/types/core';
import type { DetailQuery, QueryParams } from '@/types/api/queryParams';

import type { Surcharge, SurchargeCreate, SurchargeUpdate } from './types';

/**
 * @description 获取Surcharge元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getSurchargeMetadata = () => {
    return alovaInstance.Get<any>('/v1/bas/surcharge/get_metadata');
};

/**
 * @description 获取Surcharge列表
 * @param {SurchargeQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Surcharge>>} 返回包含Surcharge信息的Promise对象
 * @example
 * // 使用示例
 * const surchargeList = await getSurchargeList({ start: 1, limit: 20 });
 */
const getSurchargeList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Surcharge>>(
        '/v1/bas/surcharge/query',
        params
    );
};

/**
 * @description 获取Surcharge详情
 * @param {number} id Surcharge ID
 * @returns {Promise<Surcharge>} 返回Surcharge详情信息
 */
const getSurcharge = (params: DetailQuery) => {
    return alovaInstance.Get<Surcharge>('/v1/bas/surcharge/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * @description 创建Surcharge
 * @param {SurchargeCreate} data 创建数据
 * @returns {Promise<Surcharge>} 返回创建的Surcharge信息
 */
const createSurcharge = (data: SurchargeCreate) => {
    return alovaInstance.Post<Surcharge>('/v1/bas/surcharge/create', data);
};

/**
 * @description 更新Surcharge
 * @param {SurchargeUpdate} data 更新数据
 * @returns {Promise<Surcharge>} 返回更新后的Surcharge信息
 */
const updateSurcharge = (data: SurchargeUpdate) => {
    return alovaInstance.Put<Surcharge>('/v1/bas/surcharge/update', data);
};

/**
 * @description 删除Surcharge
 * @param {number} id Surcharge ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeSurcharge = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/bas/surcharge/delete/${id}`);
};

/**
 * @description 批量删除Surcharge
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteSurcharge = (ids: number[]) => {
    return alovaInstance.Post<any>('/v1/bas/surcharge/batch-delete', { ids });
};

// /**
//  * @description 导出Surcharge数据
//  * @param {SurchargeQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportSurcharge = (params?: SurchargeQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/bas/surcharge/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Surcharge数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importSurcharge = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/bas/surcharge/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

/**
 * @description 切换附加费状态
 * @param {Partial<boolean>} data - 附加费状态
 * @returns {Promise<any>}
 */
const switchStatus = (data: any) => {
    return alovaInstance.Put<any>('/v1/bas/surcharge/set_enable_status', {
        params: data,
    });
};

export {
    getSurchargeMetadata,
    getSurchargeList,
    getSurcharge,
    createSurcharge,
    updateSurcharge,
    removeSurcharge,
    batchDeleteSurcharge,
    // exportSurcharge,
    // importSurcharge,
    switchStatus,
};
