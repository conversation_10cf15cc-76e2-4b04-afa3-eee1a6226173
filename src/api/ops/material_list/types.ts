import type { ModelBase } from '@/types/core';

/**
 * 管理模式 1.批次管理 2.序列管理
 */
export enum MaterialManagementMode {
    Batch = '1',
    Serial = '2',
}

// MaterialType 数据接口定义
export interface MaterialType extends ModelBase {
    id: number;
    parent_id?: number; // 父类别ID
    tree_path?: string; // 节点路径
    level?: number; // 节点层级
    sort_order?: number; // 排序
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    code?: string; // 类别编码
    name: string; // 类别名称
    base_unit_id?: number; // 基础单位ID
    opt_type?: number; // 优化类型 0.不优化 1.面积优化 2.长度优化
    management_mode?: MaterialManagementMode; // 管理模式 1.批次管理 2.序列管理
    specs?: Specs[]; // specs
    children?: MaterialType[]; // children
    base_unit?: BaseUnit; // base_unit
}

/** 规格数组 */
interface Specs {
    id: number;
}

/** 基础单位数组 */
interface BaseUnit {
    id: number;
    name: string;
}

export interface MaterialTypeCreate extends Omit<MaterialType, 'id'> {}

export interface MaterialTypeUpdate extends MaterialType {}

/** 物料信息 */

/**
 * 计价方式
 */
export enum PricingMode {
    Area = '按面积计价',
    Number = '按数量计价',
    Girth = '按周长计价',
    Weight = '按重量计价',
}

export interface Uom {
    id?: string;
    material_id?: number;
    unit_type?: number;
    unit_id?: number;
    unit_rate?: number | string;
}

// MaterialInfo 数据接口定义
export interface MaterialInfo extends ModelBase {
    id: number;
    created_at: string; // 创建时间
    updated_at?: string; // 更新时间
    created_by?: number; // 创建者
    updated_by?: number; // 更新者
    code?: string; // 物料编码
    name?: string; // 物料名称
    type_id?: number; // 物料类别ID
    base_unit_id?: number; // 基础单位ID
    purchase_unit_id?: number; // 采购单位ID
    stock_unit_id?: number; // 库存单位ID
    opt_type?: number; // 优化类型 0.不优化 1.面积优化 2.长度优化
    management_mode: MaterialManagementMode; // 管理模式 1.批次管理 2.序列管理
    alias?: string; // 物料别名
    place_origin_id?: number; // 产地
    brand_id?: number; // 品牌
    grade_id?: number; // 等级
    color_id?: number; // 颜色
    deep?: number; // 厚度
    material_quality?: string; // 材质
    material_specs_json?: string; // 材质规格
    model?: string; // 型号
    pricing_mode?: PricingMode; // 计价方式
    sale_unit_price?: number; // 销售单价
    is_sale?: boolean; // 是否销售
    supplier_id?: number; // 供应商
    purchase_unit_price?: number; // 最近采购单价
    purchase_advance_days?: number; // 采购提前天数
    store_id?: number; // 所属仓库
    position?: string; // 使用位置
    specs_json?: string; // 规格
    valid: boolean; // 是否生效
    notes?: string; // 备注
    pic?: string;
    inbound_qty?: number; // 入库数量
    outbound_qty?: number; // 出库数量
    stock_qty?: number; // 库存数量
    specs: Specs[]; // specs
    uoms: Uom[]; // 多计量单位
    type?: MaterialType; // type
    base_unit?: BaseUnit; // base_unit
    purchase_unit?: BaseUnit; // purchase_unit
    stock_unit?: BaseUnit; // stock_unit
    place_origin?: BaseUnit; // place_origin
    brand?: BaseUnit; // brand
    grade?: BaseUnit; // grade
    color?: BaseUnit; // color
    supplier?: Supplier; // supplier
    store?: BaseUnit; // store
}

/** 供应商 */
interface Supplier {
    id: number;
    name: string;
}

export interface MaterialInfoCreate extends Omit<MaterialInfo, 'id'> {}

export interface MaterialInfoUpdate extends MaterialInfo {}

// API 响应接口定义

// Variant 数据接口定义
export interface Variant extends ModelBase {
    id: number;
    material_id?: number; // 物料ID
    code?: string; // 变体编码
    specs_json?: string; // 规格
    variant_specs_json?: string; // 变体规格
    inbound_qty?: number; // 入库数量
    outbound_qty?: number; // 出库数量
    stock_qty?: number; // 库存数量
    specs: any[]; // specs
    uoms: any[]; // uoms
}

export interface VariantCreate extends Omit<Variant, 'id'> {}

export interface VariantUpdate extends Variant {}
