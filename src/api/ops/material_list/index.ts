import alovaInstance from '@/api'
import type { ResponseListModel } from '@/types/core'
import type { DetailQuery, QueryParams } from '@/types/api/queryParams'

import type {
  MaterialType,
  MaterialTypeCreate,
  MaterialTypeUpdate,
  MaterialInfo,
  MaterialInfoCreate,
  MaterialInfoUpdate,
  Variant,
  VariantCreate,
  VariantUpdate,
} from './types'

/**
 * @description 获取MaterialType列表
 * @param {MaterialTypeQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<MaterialType>>} 返回包含MaterialType信息的Promise对象
 * @example
 * // 使用示例
 * const materialTypeList = await getMaterialTypeList({ start: 1, limit: 20 });
 */
const getMaterialTypeList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<MaterialType>>(
    '/v1/ops/material_type/query',
    params
  )
}

/**
 * 获取物料类别树
 * @param {Partial<Recordable>} data - 物料类别信息
 * @returns {Promise<Recordable>} 返回包含物料类别信息的Promise对象
 * @example
 * // 使用示例
 * const materialTypeTree = await getMaterialTypeTree(data);
 */
const getMaterialTypeTree = () => {
  return alovaInstance.Get<MaterialType[]>('/v1/ops/material_type/tree', {
    cacheFor: 0,
  })
}

/**
 * @description 获取MaterialType详情
 * @param {number} id MaterialType ID
 * @returns {Promise<MaterialType>} 返回MaterialType详情信息
 */
const getSingleMaterialType = (params: { id: string; max_depth?: number }) => {
  return alovaInstance.Get<Recordable>('/v1/ops/material_type/get', {
    params,
  })
}

/**
 * @description 创建MaterialType
 * @param {MaterialTypeCreate} data 创建数据
 * @returns {Promise<MaterialType>} 返回创建的MaterialType信息
 */
const createMaterialType = (data: MaterialTypeCreate) => {
  return alovaInstance.Post<MaterialType>('/v1/ops/material_type/create', data)
}

/**
 * @description 更新MaterialType
 * @param {MaterialTypeUpdate} data 更新数据
 * @returns {Promise<MaterialType>} 返回更新后的MaterialType信息
 */
const updateMaterialType = (data: MaterialTypeUpdate) => {
  return alovaInstance.Put<MaterialType>('/v1/ops/material_type/update', data)
}

/**
 * @description 删除MaterialType
 * @param {number} id MaterialType ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeMaterialType = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/ops/material_type/delete/${id}`)
}

/**
 * @description 批量删除MaterialType
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteMaterialType = (ids: number[]) => {
  return alovaInstance.Delete<any>('/v1/ops/material_type/batch-delete', ids)
}

// /**
//  * @description 导出MaterialType数据
//  * @param {MaterialTypeQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportMaterialType = (params?: MaterialTypeQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/ops/material_type/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入MaterialType数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importMaterialType = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/ops/material_type/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

/** == 物料信息 =================================================================================== */
/**
 * @description 获取MaterialType元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getMaterialInfoMetadata = () => {
  return alovaInstance.Get<any>('/v1/ops/material/get_metadata')
}

/**
 * @description 获取MaterialInfo列表
 * @param {MaterialInfoQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<MaterialInfo>>} 返回包含MaterialInfo信息的Promise对象
 * @example
 * // 使用示例
 * const materialInfoList = await getMaterialInfoList({ start: 1, limit: 20 });
 */
const getMaterialInfoList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<MaterialInfo>>(
    '/v1/ops/material/query',
    params
  )
}

/**
 * @description 获取MaterialInfo详情
 * @param {number} id MaterialInfo ID
 * @returns {Promise<MaterialInfo>} 返回MaterialInfo详情信息
 */
const getMaterialInfo = (params: DetailQuery) => {
  return alovaInstance.Get<MaterialInfo>('/v1/ops/material/get', {
    params,
    cacheFor: 0,
  })
}

/**
 * @description 创建MaterialInfo
 * @param {MaterialInfoCreate} data 创建数据
 * @returns {Promise<MaterialInfo>} 返回创建的MaterialInfo信息
 */
const createMaterialInfo = (data: MaterialInfoCreate) => {
  return alovaInstance.Post<MaterialInfo>('/v1/ops/material/create', data)
}

/**
 * @description 更新MaterialInfo
 * @param {MaterialInfoUpdate} data 更新数据
 * @returns {Promise<MaterialInfo>} 返回更新后的MaterialInfo信息
 */
const updateMaterialInfo = (data: MaterialInfoUpdate) => {
  return alovaInstance.Put<MaterialInfo>('/v1/ops/material/update', data)
}

/**
 * @description 删除MaterialInfo
 * @param {number} id MaterialInfo ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeMaterialInfo = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/ops/material/delete/${id}`)
}

/**
 * @description 批量删除MaterialInfo
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteMaterialInfo = (ids: number[]) => {
  return alovaInstance.Delete<any>('/v1/ops/material/batch-delete', ids)
}

/**
 * 设置物料信息状态
 * @param {Partial<any>} data - 物料信息
 * @returns {Promise<any>} 返回包含设置物料信息状态的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await setMaterialInfoStatus(data);
 */
const setMaterialInfoStatus = (
  data: Partial<{ ids: string; status: boolean }>
) => {
  return alovaInstance.Put<any>('/v1/ops/material/set_enable_status', {
    params: data,
  })
}

// /**
//  * @description 导出MaterialInfo数据
//  * @param {MaterialInfoQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportMaterialInfo = (params?: MaterialInfoQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/ops/material/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入MaterialInfo数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importMaterialInfo = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/ops/material/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

/**
 * 获取物料信息和变体信息的所有数据
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含物料信息和变体信息的Promise对象
 * @example
 * // 使用示例
 * const materialInfoAndVariantInfo = await getMaterialInfoAndVariantInfo(data);
 */
const getMaterialInfoAndVariantInfo = (data: QueryParams) => {
  return alovaInstance.Post<Recordable>('/v1/ops/material_variant/query', data)
}

/** == 变体 =================================================================================== */
/**
 * @description 获取Variant元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getVariantMetadata = () => {
  return alovaInstance.Get<any>('/v1/ops/variant/get_metadata')
}

/**
 * @description 获取Variant列表
 * @param {VariantQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Variant>>} 返回包含Variant信息的Promise对象
 * @example
 * // 使用示例
 * const variantList = await getVariantList({ start: 1, limit: 20 });
 */
const getVariantList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<Variant>>(
    '/v1/ops/variant/query',
    params
  )
}

/**
 * @description 获取Variant详情
 * @param {number} id Variant ID
 * @returns {Promise<Variant>} 返回Variant详情信息
 */
const getVariant = (params: DetailQuery) => {
  return alovaInstance.Get<Variant>('/v1/ops/variant/get', {
    params,
    cacheFor: 0,
  })
}

/**
 * @description 创建Variant
 * @param {VariantCreate} data 创建数据
 * @returns {Promise<Variant>} 返回创建的Variant信息
 */
const createVariant = (data: VariantCreate) => {
  return alovaInstance.Post<Variant>('/v1/ops/variant/create', data)
}

/**
 * @description 更新Variant
 * @param {VariantUpdate} data 更新数据
 * @returns {Promise<Variant>} 返回更新后的Variant信息
 */
const updateVariant = (data: VariantUpdate) => {
  return alovaInstance.Put<Variant>('/v1/ops/variant/update', data)
}

/**
 * @description 删除Variant
 * @param {number} id Variant ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeVariant = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/ops/variant/delete/${id}`)
}

/**
 * @description 批量删除Variant
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteVariant = (ids: number[]) => {
  return alovaInstance.Delete<any>('/v1/ops/variant/batch-delete', ids)
}

/** 根据物料id获取变体列表 */
const getVariantListByMaterialId = (materialId: number) => {
  return alovaInstance.Get<Variant[]>(
    '/v1/ops/variant/get_variants_by_material_id',
    {
      params: {
        material_id: materialId,
      },
    }
  )
}

// /**
//  * @description 导出Variant数据
//  * @param {VariantQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportVariant = (params?: VariantQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/ops/variant/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Variant数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importVariant = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/ops/variant/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getMaterialTypeList,
  getMaterialTypeTree,
  getSingleMaterialType,
  createMaterialType,
  updateMaterialType,
  removeMaterialType,
  batchDeleteMaterialType,
  // exportMaterialType,
  // importMaterialType,
  getMaterialInfoMetadata,
  getMaterialInfoList,
  getMaterialInfo,
  createMaterialInfo,
  updateMaterialInfo,
  removeMaterialInfo,
  batchDeleteMaterialInfo,
  setMaterialInfoStatus,
  // exportMaterialInfo,
  // importMaterialInfo,
  getMaterialInfoAndVariantInfo,
  getVariantMetadata,
  getVariantList,
  getVariant,
  createVariant,
  updateVariant,
  removeVariant,
  batchDeleteVariant,
  getVariantListByMaterialId,
}
