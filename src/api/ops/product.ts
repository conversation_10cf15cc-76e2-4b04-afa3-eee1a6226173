import alovaInstance from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/** == 产品类别 =================================================================================== */
/**
 * @description 获取产品类别列表数据
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含产品类别信息的Promise对象
 * @example
 * // 使用示例
 * const currencyList = await getProductTypeList(data);
 */
const getProductTypeList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/ops/product_type/query', data);
};

export interface ProductTypeTreeNode {
    tree_path?: string;
    id?: number;
    sort_order?: null;
    updated_at?: string;
    updated_by?: number;
    name?: string;
    component_required?: boolean;
    level?: number;
    parent_id?: null;
    created_at?: string;
    created_by?: number;
    code?: string;
    component_type?: number;
    children?: Array<ProductTypeTreeNode>;
}
/**
 * 获取产品类别树
 * @param {Partial<Recordable>} data - 产品类别信息
 * @returns {Promise<ProductTypeTreeNode[]>} 返回包含产品类别信息的Promise对象
 * @example
 * // 使用示例
 * const materialTypeTree = await getProductTypeTree(data);
 */
const getProductTypeTree = () => {
    return alovaInstance.Get<ProductTypeTreeNode[]>(
        '/v1/ops/product_type/tree'
    );
};

/**
 * @description 创建产品类别数据
 * @param {Partial<Recordable>} data - 产品类别信息
 * @returns {Promise<Recordable>} 返回包含创建产品类别信息的Promise对象
 * @example
 * // 使用示例
 * const newMaterialType = await createProductType(data);
 */
const createProductType = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/ops/product_type/create', data);
};

/**
 * @description 更新产品类别数据
 * @param {Partial<Recordable>} data - 产品类别信息
 * @returns {Promise<Recordable>} 返回包含更新产品类别信息的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialType = await updateProductType(data);
 */
const updateProductType = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/ops/product_type/update', data);
};

/**
 * @description 删除产品类别数据
 * @param {Partial<Recordable>} data - 产品类别信息
 * @returns {Promise<Recordable>} 返回包含删除产品类别信息的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialType = await deleteProductType(data);
 */
const deleteProductType = (id: string) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/ops/product_type/delete/${id}`
    );
};

/**
 * 获取单个产品类别数据
 * @param { id: string } params - 产品类别ID
 * @returns {Promise<Recordable>} 返回包含单个产品类别信息的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialType = await getSingleProductType({ id: '1' });
 */
const getSingleProductType = (params: { id: number }) => {
    return alovaInstance.Get<Recordable>('/v1/ops/product_type/get', {
        params,
    });
};

/**
 * @description 获取ProductType同级节点数据
 * @param {Partial<Recordable>} data - ProductType同级节点信息
 * @returns {Promise<Recordable>} 返回包含ProductType同级节点信息的Promise对象
 * @example
 * // 使用示例
 * const siblingsProductType = await siblingsProductType(data);
 */

const getPageMetaData = () => {
    return alovaInstance.Get<Recordable>('/v1/ops/product/get_metadata');
};

const siblingsProductType = (id: string) => {
    return alovaInstance.Get<Recordable>(`/v1/ops/product_type/siblings/${id}`);
};
/** == 产品信息 =================================================================================== */

/**
 * 获取产品信息列表
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含产品信息的Promise对象
 * @example
 * // 使用示例
 * const materialList = await getProductList(data);
 */
const getProductList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/ops/product/query', data);
};

/**
 * 创建产品信息
 * @param {Partial<Recordable>} data - 产品信息
 * @returns {Promise<Recordable>} 返回包含创建产品信息的Promise对象
 * @example
 * // 使用示例
 * const newMaterialInfo = await createProduct(data);
 */
const createProduct = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/ops/product/create', data);
};

/**
 * 更新产品信息
 * @param {Partial<Recordable>} data - 产品信息
 * @returns {Promise<Recordable>} 返回包含更新产品信息的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialInfo = await updateProduct(data);
 */
const updateProduct = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/ops/product/update', data);
};

/**
 * 删除产品信息
 * @param {Partial<Recordable>} data - 产品信息
 * @returns {Promise<Recordable>} 返回包含删除产品信息的Promise对象
 * @example
 * // 使用示例
 */
const deleteProduct = (id: string) => {
    return alovaInstance.Delete<Recordable>(`/v1/ops/product/delete/${id}`);
};

/**
 * 批量删除产品信息
 * @param {Partial<Recordable>} data - 产品信息
 * @returns {Promise<Recordable>} 返回包含批量删除产品信息的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteProductBatch(data);
 */
const deleteProductBatch = (params: { ids: string[] }) => {
    return alovaInstance.Delete<Recordable>(
        '/v1/ops/product/bulk_delete',
        params.ids
    );
};

/**
 * 获取单个产品信息
 * @param {Partial<Recordable>} data - 产品信息
 * @returns {Promise<Recordable>} 返回包含单个产品信息的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialInfo = await getSingleProduct(data);
 */
const getSingleProduct = (params: { id: Number }) => {
    return alovaInstance.Get<Recordable>('/v1/ops/product/get', {
        params,
        cacheFor: 0,
    });
};

/**
 * @description 切换产品信息状态
 * @param {Partial<boolean>} data - 产品信息状态
 * @returns {Promise<Recordable>}
 */
const switchStatus = (data: Recordable) => {
    return alovaInstance.Put<Recordable>('/v1/ops/product/set_enable_status', {
        params: data,
    });
};

/** == 组件 =================================================================================== */
/**
 * 获取产品组件树
 * @param {Partial<Recordable>} data - 产品组件信息
 * @returns {Promise<Recordable>} 返回包含产品组件信息的Promise对象
 * @example
 * // 使用示例
 * const productComponentTree = await getProductComponentTree(data);
 */
const getProductComponentTree = (params) => {
    return alovaInstance.Get<Recordable>('/v1/ops/product_component/tree', {
        params,
        cacheFor: 0,
    });
};
/**
 * 更新产品组件信息
 * @param {Partial<Recordable>} data - 产品组件信息
 * @returns {Promise<Recordable>} 返回包含更新产品组件信息的Promise对象
 * @example
 * // 使用示例
 * const updateProductComponent = await updateProductComponent(data);
 */
const updateProductComponent = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/ops/product_component/set', data);
};
export {
    getProductTypeList,
    getProductTypeTree,
    createProductType,
    updateProductType,
    deleteProductType,
    getSingleProductType,
    siblingsProductType,
    getProductList,
    createProduct,
    updateProduct,
    deleteProduct,
    deleteProductBatch,
    getSingleProduct,
    switchStatus,
    getProductComponentTree,
    updateProductComponent,
    getPageMetaData,
};
