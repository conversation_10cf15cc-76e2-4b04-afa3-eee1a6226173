import alovaInstance from '../index';
import type { QueryParams } from '@/types/api/queryParams';

/** == BOM =================================================================================== */
/**
 * @description 获取BOM列表数据
 * @param {Partial<QueryParams>} data - 请求信息
 * @returns {Promise<Recordable>} 返回包含BOM信息的Promise对象
 * @example
 * // 使用示例
 * const currencyList = await getBOMList(data);
 */
const getBOMList = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/ops/bom/query', data);
};



/**
 * @description 创建BOM数据
 * @param {Partial<Recordable>} data - BOM信息
 * @returns {Promise<Recordable>} 返回包含创建BOM信息的Promise对象
 * @example
 * // 使用示例
 * const newMaterialType = await createBOM(data);
 */
const createBOM = (data: Partial<Recordable>) => {
    return alovaInstance.Post<Recordable>('/v1/ops/bom/create', data);
};

/**
 * @description 更新BOM数据
 * @param {Partial<Recordable>} data - BOM信息
 * @returns {Promise<Recordable>} 返回包含更新BOM信息的Promise对象
 * @example
 * // 使用示例
 * const updatedMaterialType = await updateBOM(data);
 */
const updateBOM = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/ops/bom/update', data);
};

/**
 * @description 删除BOM数据
 * @param {Partial<Recordable>} data - BOM信息
 * @returns {Promise<Recordable>} 返回包含删除BOM信息的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialType = await deleteBOM(data);
 */
const deleteBOM = (id: string) => {
    return alovaInstance.Delete<Recordable>(
        `/v1/ops/bom/delete/${id}`
    );
};

/**
 * 批量删除BOM
 * @param {Partial<Recordable>} data - BOM
 * @returns {Promise<Recordable>} 返回包含批量删除BOM的Promise对象
 * @example
 * // 使用示例
 * const deletedMaterialInfo = await deleteProductBatch(data);
 */
const deleteBOMBatch = (params: { ids: string[] }) => {
    return alovaInstance.Delete<Recordable>(
        '/v1/ops/bom/bulk_delete',
        params.ids
    );
};

/**
 * 获取单个BOM数据
 * @param { id: string } params - BOMID
 * @returns {Promise<Recordable>} 返回包含单个BOM信息的Promise对象
 * @example
 * // 使用示例
 * const singleMaterialType = await getSingleBOM({ id: '1' });
 */
const getSingleBOM = (params: { id: number }) => {
    return alovaInstance.Get<Recordable>('/v1/ops/bom/get', {
        params,
        cacheFor:0,
    });
};

/**
 * @description 获取BOM同级节点数据
 * @param {Partial<Recordable>} data - BOM同级节点信息
 * @returns {Promise<Recordable>} 返回包含BOM同级节点信息的Promise对象
 * @example
 * // 使用示例
 * const siblingsBOM = await siblingsBOM(data);
 */

const getPageMetaData = () => {
    return alovaInstance.Get<Recordable>('/v1/ops/bom/get_metadata');
};

/**
 * 获取BOM组件树
 * @param {Partial<Recordable>} data - BOM组件信息
 * @returns {Promise<Recordable>} 返回包含BOM组件信息的Promise对象
 * @example
 * // 使用示例
 * const productComponentTree = await getProductComponentTree(data);
 */
const getBOMTree = (params) => {
    return alovaInstance.Get<Recordable>('/v1/ops/bom/bom_tree', {
        params,
        cacheFor:0,
    });
};

export {
    getPageMetaData,
    getSingleBOM,
    deleteBOM,
    updateBOM,
    createBOM,
    getBOMList,
    deleteBOMBatch,
    getBOMTree,
}
