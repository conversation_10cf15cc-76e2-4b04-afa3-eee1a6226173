import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { Proset, ProsetCreate, ProsetUpdate } from './types';

/**
 * @description 获取Proset元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getProsetMetadata = () => {
    return alovaInstance.Get<any>('/v1/sal/pro_set/get_metadata');
};

/**
 * @description 获取Proset列表
 * @param {ProsetQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Proset>>} 返回包含Proset信息的Promise对象
 * @example
 * // 使用示例
 * const pro_setList = await getProsetList({ start: 1, limit: 20 });
 */
const getProsetList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Proset>>(
        '/v1/sal/pro_set/query',
        params
    );
};

/**
 * @description 获取Proset详情
 * @param {number} id Proset ID
 * @returns {Promise<Proset>} 返回Proset详情信息
 */
const getProset = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Proset>(`/v1/sal/pro_set/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 创建Proset
 * @param {ProsetCreate} data 创建数据
 * @returns {Promise<Proset>} 返回创建的Proset信息
 */
const createProset = (data: ProsetCreate) => {
    return alovaInstance.Post<Proset>('/v1/sal/pro_set/create', data);
};

/**
 * @description 更新Proset
 * @param {ProsetUpdate} data 更新数据
 * @returns {Promise<Proset>} 返回更新后的Proset信息
 */
const updateProset = (data: ProsetUpdate) => {
    return alovaInstance.Put<Proset>('/v1/sal/pro_set/update', data);
};

/**
 * @description 更新Proset
 * @param {ProsetUpdate} data 更新数据
 * @returns {Promise<Proset>} 返回更新后的Proset信息
 */
const bulkUpdateProset = (data: ProsetUpdate) => {
    return alovaInstance.Post<Proset>('/v1/sal/pro_set/bulk_update', data);
};

/**
 * @description 删除Proset
 * @param {number} id Proset ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeProset = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/sal/pro_set/delete/${id}`);
};

/**
 * @description 批量删除Proset
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteProset = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/sal/pro_set/bulk_delete', ids);
};

/**
 * @description 反审核Delivery
 * @param {number} id Delivery ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(`/v1/sal/pro_set/anti_approval`, {
        relation_id: id,
    });
};

// /**
//  * @description 导出Proset数据
//  * @param {ProsetQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportProset = (params?: ProsetQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/pro_set/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Proset数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importProset = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/pro_set/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getProsetMetadata,
    getProsetList,
    getProset,
    createProset,
    updateProset,
    removeProset,
    batchDeleteProset,
    antiApproval,
    bulkUpdateProset,
    // exportProset,
    // importProset,
};
