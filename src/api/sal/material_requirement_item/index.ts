import alovaInstance from '@/api'
import type { ModelBase, ResponseListModel } from '@/types/core'
import type { QueryParams } from '@/types/api/queryParams'

import type {
  MaterialRequirementItem,
  MaterialRequirementItemCreate,
  MaterialRequirementItemUpdate,
} from './types'
import { UploadFileResponse } from '@/api/sys/attachment'

/**
 * @description 获取MaterialRequirementItem元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getMaterialRequirementItemMetadata = () => {
  return alovaInstance.Get<any>(
    '/v1/sal/material_requirement_item/get_metadata'
  )
}

/**
 * @description 获取MaterialRequirementItem列表
 * @param {MaterialRequirementItemQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<MaterialRequirementItem>>} 返回包含MaterialRequirementItem信息的Promise对象
 * @example
 * // 使用示例
 * const materialRequirementList = await getMaterialRequirementItemList({ start: 1, limit: 20 });
 */
const getMaterialRequirementItemList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<MaterialRequirementItem>>(
    '/v1/sal/material_requirement_item/query',
    params
  )
}
/**
 * @description 获取MaterialRequirementItem列表
 * @param {MaterialRequirementItemQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<MaterialRequirementItem>>} 返回包含MaterialRequirementItem信息的Promise对象
 * @example
 * // 使用示例
 * const materialRequirementList = await getMaterialRequirementItemList({ start: 1, limit: 20 });
 */
const getOrderMaterialRequirementItemList = (order_id: number) => {
  return alovaInstance.Post<ResponseListModel<MaterialRequirementItem>>(
    `/v1/sal/material_requirement_item/get_order_material_requirement_item_list?order_id=${order_id}`,
    { order_id }
  )
}

/**
 * @description 获取MaterialRequirementItem列表
 * @param {MaterialRequirementItemQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<MaterialRequirementItem>>} 返回包含MaterialRequirementItem信息的Promise对象
 * @example
 * // 使用示例
 * const materialRequirementList = await getMaterialRequirementItemList({ start: 1, limit: 20 });
 */
const getMaterialRequirementItemListById = (order_id: number) => {
  return alovaInstance.Post<ResponseListModel<MaterialRequirementItem>>(
    `/v1/sal/material_requirement_item/get_material_requirement_item_list?order_id=${order_id}`,
    {
      order_id,
    }
  )
}

/**
 * @description 获取MaterialRequirementItem详情
 * @param {number} id MaterialRequirementItem ID
 * @returns {Promise<MaterialRequirementItem>} 返回MaterialRequirementItem详情信息
 */
const getMaterialRequirementItem = (id?: number) => {
  const url = id
    ? `/v1/sal/material_requirement_item/get/${id}`
    : '/v1/sal/material_requirement_item/get'
  return alovaInstance.Get<MaterialRequirementItem>(url)
}

/**
 * @description 创建MaterialRequirementItem
 * @param {MaterialRequirementItemCreate} data 创建数据
 * @returns {Promise<MaterialRequirementItem>} 返回创建的MaterialRequirementItem信息
 */
const createMaterialRequirementItem = (data: MaterialRequirementItemCreate) => {
  return alovaInstance.Post<MaterialRequirementItem>(
    '/v1/sal/material_requirement_item/create',
    data
  )
}

/**
 * @description 保存MaterialRequirementItem
 * @param {MaterialRequirementItemCreate} data 创建数据
 * @returns {Promise<MaterialRequirementItem>} 返回创建的MaterialRequirementItem信息
 */
const saveMaterialRequirementItem = (
  data: MaterialRequirementItemCreate,
  order_id: number
) => {
  return alovaInstance.Post<MaterialRequirementItem>(
    `/v1/sal/material_requirement_item/save_material_requirement_item?order_id=${order_id}`,
    data
  )
}

/**
 * @description 更新MaterialRequirementItem
 * @param {MaterialRequirementItemUpdate} data 更新数据
 * @returns {Promise<MaterialRequirementItem>} 返回更新后的MaterialRequirementItem信息
 */
const updateMaterialRequirementItem = (data: MaterialRequirementItemUpdate) => {
  return alovaInstance.Put<MaterialRequirementItem>(
    '/v1/sal/material_requirement_item/update',
    data
  )
}

/**
 * @description 删除MaterialRequirementItem
 * @param {number} id MaterialRequirementItem ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeMaterialRequirementItem = (id: number) => {
  return alovaInstance.Delete<any>(
    `/v1/sal/material_requirement_item/delete/${id}`
  )
}

/**
 * @description 批量删除MaterialRequirementItem
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteMaterialRequirementItem = (ids: number[]) => {
  return alovaInstance.Delete<any>(
    '/v1/sal/material_requirement_item/bulk_delete',
    ids
  )
}

/**
 * @description 上传物料清单
 * @param {Recordable} data
 * @returns {Promise<any>}
 */
const uploadMaterialRequirementItem = (data: FormData) => {
  return alovaInstance.Post<UploadFileResponse>(
    `/v1/sal/material_requirement_item/upload_material_requirement_item`,
    data
  )
}

/**
 * @description 根据订单明细获取物料需求明细列表
 * @param {Recordable} data
 * @returns {Promise<any>}
 */
const getMaterialRequirementItemsList = (data: Recordable) => {
  return alovaInstance.Post<UploadFileResponse>(
    `/v1/sal/material_requirement_item/get_material_requirement_item_list`,
    data
  )
}

// /**
//  * @description 导出MaterialRequirementItem数据
//  * @param {MaterialRequirementItemQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportMaterialRequirementItem = (params?: MaterialRequirementItemQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/material_requirement_item/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入MaterialRequirementItem数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importMaterialRequirementItem = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/material_requirement_item/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getMaterialRequirementItemMetadata,
  getMaterialRequirementItemList,
  getMaterialRequirementItem,
  createMaterialRequirementItem,
  updateMaterialRequirementItem,
  removeMaterialRequirementItem,
  bulkDeleteMaterialRequirementItem,
  saveMaterialRequirementItem,
  uploadMaterialRequirementItem,
  getMaterialRequirementItemsList,
  getMaterialRequirementItemListById,
  getOrderMaterialRequirementItemList,
  // exportMaterialRequirementItem,
  // importMaterialRequirementItem,
}
