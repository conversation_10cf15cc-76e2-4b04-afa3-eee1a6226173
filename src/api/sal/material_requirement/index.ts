import alovaInstance from '@/api'
import type { ModelBase, ResponseListModel } from '@/types/core'
import type { QueryParams } from '@/types/api/queryParams'

import type {
  MaterialRequirement,
  MaterialRequirementCreate,
  MaterialRequirementUpdate,
} from './types'
import { UploadFileResponse } from '@/api/sys/attachment'

/**
 * @description 获取MaterialRequirement元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getMaterialRequirementMetadata = () => {
  return alovaInstance.Get<any>('/v1/sal/material_requirement/get_metadata')
}

/**
 * @description 获取MaterialRequirement列表
 * @param {MaterialRequirementQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<MaterialRequirement>>} 返回包含MaterialRequirement信息的Promise对象
 * @example
 * // 使用示例
 * const materialRequirementList = await getMaterialRequirementList({ start: 1, limit: 20 });
 */
const getMaterialRequirementList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<MaterialRequirement>>(
    '/v1/sal/material_requirement/query',
    params
  )
}

/**
 * @description 获取MaterialRequirement详情
 * @param {number} id MaterialRequirement ID
 * @returns {Promise<MaterialRequirement>} 返回MaterialRequirement详情信息
 */
const getMaterialRequirement = (id?: number) => {
  const url = id
    ? `/v1/sal/material_requirement/get/${id}`
    : '/v1/sal/material_requirement/get'
  return alovaInstance.Get<MaterialRequirement>(url)
}

/**
 * @description 创建MaterialRequirement
 * @param {MaterialRequirementCreate} data 创建数据
 * @returns {Promise<MaterialRequirement>} 返回创建的MaterialRequirement信息
 */
const createMaterialRequirement = (data: MaterialRequirementCreate) => {
  return alovaInstance.Post<MaterialRequirement>(
    '/v1/sal/material_requirement/create',
    data
  )
}

/**
 * @description 保存MaterialRequirement
 * @param {MaterialRequirementCreate} data 创建数据
 * @returns {Promise<MaterialRequirement>} 返回创建的MaterialRequirement信息
 */
const saveMaterialRequirement = (
  data: MaterialRequirementCreate,
  order_id: number
) => {
  return alovaInstance.Post<MaterialRequirement>(
    `/v1/sal/material_requirement/save_material_requirement?order_id=${order_id}`,
    data
  )
}

/**
 * @description 更新MaterialRequirement
 * @param {MaterialRequirementUpdate} data 更新数据
 * @returns {Promise<MaterialRequirement>} 返回更新后的MaterialRequirement信息
 */
const updateMaterialRequirement = (data: MaterialRequirementUpdate) => {
  return alovaInstance.Put<MaterialRequirement>(
    '/v1/sal/material_requirement/update',
    data
  )
}

/**
 * @description 删除MaterialRequirement
 * @param {number} id MaterialRequirement ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeMaterialRequirement = (id: number) => {
  return alovaInstance.Delete<any>(`/v1/sal/material_requirement/delete/${id}`)
}

/**
 * @description 批量删除MaterialRequirement
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteMaterialRequirement = (ids: number[]) => {
  return alovaInstance.Delete<any>('/sal/material_requirement/bulk_delete', ids)
}

/**
 * @description 上传物料清单
 * @param {Recordable} data
 * @returns {Promise<any>}
 */
const uploadMaterialRequirement = (data: FormData) => {
  return alovaInstance.Post<UploadFileResponse>(
    `/v1/sal/material_requirement/upload_material_requirement`,
    data
  )
}

// /**
//  * @description 导出MaterialRequirement数据
//  * @param {MaterialRequirementQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportMaterialRequirement = (params?: MaterialRequirementQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/material_requirement/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入MaterialRequirement数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importMaterialRequirement = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/material_requirement/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getMaterialRequirementMetadata,
  getMaterialRequirementList,
  getMaterialRequirement,
  createMaterialRequirement,
  updateMaterialRequirement,
  removeMaterialRequirement,
  bulkDeleteMaterialRequirement,
  saveMaterialRequirement,
  uploadMaterialRequirement,
  // exportMaterialRequirement,
  // importMaterialRequirement,
}
