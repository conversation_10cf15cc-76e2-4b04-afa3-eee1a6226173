import type { ModelBase } from "@/types/core"

// MaterialRequirement 数据接口定义
export interface MaterialRequirement extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  order_id?: number; // 订单ID
  notes?: string; // 备注
  items: MaterialRequirementItem[]; // items
  order?: Order; // order
}

export interface MaterialRequirementCreate extends Omit<MaterialRequirement, 'id'> {

}

export interface MaterialRequirementUpdate extends MaterialRequirement {

}

// API 响应接口定义
