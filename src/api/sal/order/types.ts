import type { ModelBase } from "@/types/core"

/**
 * 单据状态
 */
export enum DocStatus {
  DRAFT = '草稿',
  PENDING_REVIEW = '待审核',
  UNDER_REVIEW = '审核中',
  APPROVED = '已审核',
  REJECTED = '已驳回',
  CANCELLED = '已作废'
}

/**
 * 源单类型
 */
export enum DocumentType {
  SALE_ORDER = '销售订单',
  SALE_QUOTATION = '销售报价',
  SALE_CONTRACT = '销售合同',
  SALE_DELIVERY = '销售送货',
  SALE_RETURN = '销售退货',
  PURCHASE_ORDER = '采购订单',
  PURCHASE_RETURN = '采购退货',
  PURCHASE_CONTRACT = '采购合同',
  INBOUND = '入库',
  OUTBOUND = '出库',
  ADJUSTMENT = '调拨',
  RETURN = '退货',
  INVENTORY_LOCATION_INFO = '仓位资料',
  FIN_RECEIVABLE = '应收账单',
  FIN_COLLECTION = '收款单',
  FIN_PAYABLE = '应付账单',
  FIN_PAYMENT = '付款单',
  FIN_ACCOUNTING_ENTRY = '财务分录',
  FIN_VOUCHER = '财务凭证',
  FIN_ANALYSIS = '财务分析',
  FIN_BUDGET = '财务预算',
  FIN_FORECAST = '财务预测',
  INVENTORY_INFO = '仓库资料',
  OTHER = '其他'
}

// Order 数据接口定义
export interface Order extends ModelBase {
  id: number;
  version: number; // 乐观锁版本号
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  doc_status: DocStatus; // 单据状态
  factory_id?: number; // 所属组织
  sale_type_id?: number; // 销售类型
  customer_id: number; // 客户
  delivery_date?: string; // 交货日期
  payment_terms_id?: number; // 付款条款
  pay_mode_id?: number; // 支付方式
  currency_id?: number; // 货币
  exchange_rate?: number; // 汇率
  amount_including_tax?: number; // 总金 (含税)
  amount_not_taxed?: number; // 总金额 (未税)
  transport_id?: number; // 送货方式
  delivery_address?: string; // 送货地址
  pack_mode_id?: number; // 包装方式
  salesman_id?: number; // 业务员
  deposit_amount?: number; // 定金
  workmanship_requirement?: string; // 工艺要求
  quality_requirement?: string; // 质量标准
  base_currency_amount?: number; // 本位币金额
  material_cost?: number; // 材料费
  surcharge?: number; // 附加费
  wip_cost?: number; // 工艺费
  discount_amount?: number; // 折扣金额
  tax_rate_id?: number;
  tax_rate?: number; // 税率
  tax_amount?: number; // 税额
  total_actual_area?: number; // 实际总面积
  total_sale_area?: number; // 结算总面积
  total_weight?: number; // 总重量
  total_girth?: number; // 总周长
  product_qty?: number; // 产品种类数量
  total_qty?: number; // 总数量
  product_codes?: string; // 产品编码汇总
  product_names?: string; // 产品名称汇总
  trademark_id?: number; // 商标
  producing_area?: string; // 原料产地
  quality_level?: string; // 品质级别
  notes?: string; // 备注
  order_no: string; // 订单单号
  org_no?: string; // 源单号
  org_type?: DocumentType; // 源单类型
  cust_order_no?: string; // 客户单号
  order_date?: string; // 订单日期
  urgent_time?: number; // 加急时间
  un_accept_amount?: number; // 未收金额
  accept_amount?: number; // 已收金额
  delivery_amount?: number; // 送货金额
  delivery_qty?: number; // 送货数量
  items: OrderItem[]; // items
  currency?: Currency; // currency
  customer: Customer; // customer
  payment_terms?: PaymentTerms; // payment_terms
  salesman?: Employee; // salesman
  pay_mode?: DictData; // pay_mode
  transport?: DictData; // transport
  pack_mode?: DictData; // pack_mode
  trademark?: DictData; // trademark
  is_complete_delivery: boolean; // 是否已完成送货
}

export interface OrderCreate extends Omit<Order, 'id'> {

}

export interface OrderUpdate extends Order {

}

// API 响应接口定义
