import alovaInstance from '@/api';
import type { ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { Order, OrderCreate, OrderUpdate } from './types';

/**
 * @description 获取Order元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getMetadata = () => {
    return alovaInstance.Get<any>('/v1/sal/order/get_metadata');
};

/**
 * @description 获取Order列表
 * @param {OrderQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Order>>} 返回包含Order信息的Promise对象
 * @example
 * // 使用示例
 * const orderList = await getOrderList({ start: 1, limit: 20 });
 */
const getOrderList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Order>>(
        '/v1/sal/order/query',
        params
    );
};

/**
 * @description 获取Order详情
 * @param {number} id Order ID
 * @returns {Promise<Order>} 返回Order详情信息
 */
const getOrder = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Order>(`/v1/sal/order/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 创建Order
 * @param {OrderCreate} data 创建数据
 * @returns {Promise<Order>} 返回创建的Order信息
 */
const createOrder = (data: OrderCreate) => {
    return alovaInstance.Post<Order>('/v1/sal/order/create', data);
};

/**
 * @description 更新Order
 * @param {OrderUpdate} data 更新数据
 * @returns {Promise<Order>} 返回更新后的Order信息
 */
const updateOrder = (data: OrderUpdate) => {
    return alovaInstance.Put<Order>('/v1/sal/order/update', data);
};

/**
 * @description 删除Order
 * @param {number} id Order ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeOrder = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/sal/order/delete/${id}`);
};

/**
 * @description 批量删除Order
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteOrder = (ids: number[]) => {
    return alovaInstance.Delete<any>(`/v1/sal/order/bulk_delete`, ids);
};

/**
 * @description 获取订单明细
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getDeliveryOrderItems = (id: string) => {
    return alovaInstance.Get<Recordable>(
        `/v1/sal/order/get_delivery_order_items?order_id=${id}`,
        {
            cacheFor: 0,
        }
    );
};

/**
 * @description 获取订单明细
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getUnProducedOrderItems = (id: string) => {
    return alovaInstance.Get<Recordable>(
        `/v1/sal/order/get_un_produced_order_items?order_id=${id}`,
        {
            cacheFor: 0,
        }
    );
};

/**
 * @description get_check_data
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getCheckData = (id: string | number) => {
    return alovaInstance.Get<Recordable>(`/v1/sal/order/get_check_data`, {
        params: {
            order_id: id,
        },
    });
};

/**
 * @description order check
 * @param {Partial<Recordable>} data
 * @returns {Promise<Recordable>}
 */
const orderCheck = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/sal/order/check', data);
};

/**
 * @description order check item
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const orderCheckItem = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>('/v1/sal/order/check_item', data);
};

/**
 * @description get_repeat_order_items
 * @param {Partial<QueryParams>} data
 * @returns {Promise<Recordable>}
 * @example
 */
const getRepeatOrderItems = (data: Partial<QueryParams>) => {
    return alovaInstance.Post<Recordable>(
        '/v1/sal/order/get_repeat_order_items',
        data
    );
};

/**
 * @description set_order_state
 * @param {Partial<Recordable>} data
 * @returns {Promise<Recordable>}
 */
const setOrderState = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>('/v1/sal/order/set_order_state', data);
};

/**
 * @description 反审核Order
 * @param {number} id Order ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(`/v1/sal/order/anti_approval`, {
        relation_id: id,
    });
};

// /**
//  * @description 导出Order数据
//  * @param {OrderQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportOrder = (params?: OrderQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/order/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Order数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importOrder = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/order/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getMetadata,
    getOrderList,
    getOrder,
    createOrder,
    updateOrder,
    removeOrder,
    batchDeleteOrder,
    getDeliveryOrderItems,
    getUnProducedOrderItems,
    getCheckData,
    orderCheck,
    orderCheckItem,
    getRepeatOrderItems,
    setOrderState,
    antiApproval,
    // exportOrder,
    // importOrder,
};
