import type { ModelBase } from "@/types/core"

/**
 * 使用类型
 */
export enum ComponentUseType {
  Workshop = '车间生产',
  Project = '项目工地'
}

// OrderBomStructureItem 数据接口定义
export interface OrderBomStructureItem extends ModelBase {
  id: number;
  order_id?: number; // 订单Id
  order_no?: string; // 订单单号
  order_item_id?: number; // 订单项Id
  bom_id?: number; // BOM ID
  bom_item_id?: number; // BOM项ID
  purpose?: string; // 用途
  material_id?: number; // 物料ID
  material_code?: string; // 物料编码
  material_name?: string; // 物料名称
  structure_id?: number; // 属性结构id
  item_flag?: string; // 片标记
  wip_names?: string; // 工艺路线
  work_content?: string; // 加工要求
  width?: number; // 宽
  height?: number; // 长/高
  deep?: number; // 厚度
  weight?: number; // 重量
  material_quality?: string; // 材质
  color_id?: number; // 颜色
  unit_id?: number; // 单位
  use_qty: number; // 用量
  use_type?: ComponentUseType; // 使用类型
  specs?: string; // 规格型号
  variant_id?: number; // 变体规格ID
  variant_specs?: string; // 变体规格
  pic?: string;
  org_id?: number; // 组件引用ID
  cost_amount?: number; // 成本金额
  sale_amount?: number; // 销售金额
  price?: number; // 单价
  qty: number; // 数量
  delivery_qty?: number; // 已送数量
  arrears_qty?: number; // 送货欠数
  color?: DictData; // color
  base_unit?: DictData; // base_unit
  structure?: DictData; // structure
  order?: Order; // order
  order_item?: OrderItem; // order_item
}

export interface OrderBomStructureItemCreate extends Omit<OrderBomStructureItem, 'id'> {

}

export interface OrderBomStructureItemUpdate extends OrderBomStructureItem {

}

// API 响应接口定义
