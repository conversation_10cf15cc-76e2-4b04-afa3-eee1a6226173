import alovaInstance from '@/api'
import type { ModelBase, ResponseListModel } from '@/types/core'
import type { QueryParams } from '@/types/api/queryParams'

import type {
  OrderBomStructureItem,
  OrderBomStructureItemCreate,
  OrderBomStructureItemUpdate,
} from './types'

/**
 * @description 获取OrderBomStructureItem元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getOrderBomStructureItemMetadata = () => {
  return alovaInstance.Get<any>('/v1/sal/order_bom_structure_item/get_metadata')
}

/**
 * @description 获取OrderBomStructureItem列表
 * @param {OrderBomStructureItemQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<OrderBomStructureItem>>} 返回包含OrderBomStructureItem信息的Promise对象
 * @example
 * // 使用示例
 * const orderBomStructureItemList = await getOrderBomStructureItemList({ start: 1, limit: 20 });
 */
const getOrderBomStructureItemList = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<OrderBomStructureItem>>(
    '/v1/sal/order_bom_structure_item/query',
    params
  )
}

/**
 * @description 获取OrderBomStructureItem详情
 * @param {number} id OrderBomStructureItem ID
 * @returns {Promise<OrderBomStructureItem>} 返回OrderBomStructureItem详情信息
 */
const getOrderBomStructureItem = (id?: number) => {
  const url = id
    ? `/v1/sal/order_bom_structure_item/get/${id}`
    : '/v1/sal/order_bom_structure_item/get'
  return alovaInstance.Get<OrderBomStructureItem>(url)
}

/**
 * @description 创建OrderBomStructureItem
 * @param {OrderBomStructureItemCreate} data 创建数据
 * @returns {Promise<OrderBomStructureItem>} 返回创建的OrderBomStructureItem信息
 */
const createOrderBomStructureItem = (data: OrderBomStructureItemCreate) => {
  return alovaInstance.Post<OrderBomStructureItem>(
    '/v1/sal/order_bom_structure_item/create',
    data
  )
}
/**
 * @description 根据订单ID获取订单送货BOM结构项信息
 * @param {OrderBomStructureItemCreate} data 创建数据
 * @returns {Promise<OrderBomStructureItem>} 返回创建的OrderBomStructureItem信息
 */
const getBomStructureItem = (data: OrderBomStructureItemCreate) => {
  return alovaInstance.Post<OrderBomStructureItem>(
    `/v1/sal/order_bom_structure_item/get_bom_structure_item?order_id=${data.order_id}`,
    data
  )
}

/**
 * @description 更新OrderBomStructureItem
 * @param {OrderBomStructureItemUpdate} data 更新数据
 * @returns {Promise<OrderBomStructureItem>} 返回更新后的OrderBomStructureItem信息
 */
const updateOrderBomStructureItem = (data: OrderBomStructureItemUpdate) => {
  return alovaInstance.Put<OrderBomStructureItem>(
    '/v1/sal/order_bom_structure_item/update',
    data
  )
}

/**
 * @description 删除OrderBomStructureItem
 * @param {number} id OrderBomStructureItem ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeOrderBomStructureItem = (id: number) => {
  return alovaInstance.Delete<any>(
    `/v1/sal/order_bom_structure_item/delete/${id}`
  )
}

/**
 * @description 批量删除OrderBomStructureItem
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDeleteOrderBomStructureItem = (ids: number[]) => {
  return alovaInstance.Delete<any>(
    '/sal/order_bom_structure_item/bulk_delete',
    ids
  )
}

// /**
//  * @description 导出OrderBomStructureItem数据
//  * @param {OrderBomStructureItemQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportOrderBomStructureItem = (params?: OrderBomStructureItemQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/order_bom_structure_item/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入OrderBomStructureItem数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importOrderBomStructureItem = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/order_bom_structure_item/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  getOrderBomStructureItemMetadata,
  getOrderBomStructureItemList,
  getOrderBomStructureItem,
  createOrderBomStructureItem,
  updateOrderBomStructureItem,
  removeOrderBomStructureItem,
  bulkDeleteOrderBomStructureItem,
  getBomStructureItem,
  // exportOrderBomStructureItem,
  // importOrderBomStructureItem,
}
