import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { Contract, ContractCreate, ContractUpdate } from './types';

/**
 * @description 获取Contract元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getContractMetadata = () => {
    return alovaInstance.Get<any>('/v1/sal/contract/get_metadata');
};

/**
 * @description 获取Contract列表
 * @param {ContractQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Contract>>} 返回包含Contract信息的Promise对象
 * @example
 * // 使用示例
 * const contractList = await getContractList({ start: 1, limit: 20 });
 */
const getContractList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Contract>>(
        '/v1/sal/contract/query',
        params
    );
};

/**
 * @description 获取Contract详情
 * @param {number} id Contract ID
 * @returns {Promise<Contract>} 返回Contract详情信息
 */
const getContract = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Contract>(`/v1/sal/contract/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 创建Contract
 * @param {ContractCreate} data 创建数据
 * @returns {Promise<Contract>} 返回创建的Contract信息
 */
const createContract = (data: ContractCreate) => {
    return alovaInstance.Post<Contract>('/v1/sal/contract/create', data);
};

/**
 * @description 更新Contract
 * @param {ContractUpdate} data 更新数据
 * @returns {Promise<Contract>} 返回更新后的Contract信息
 */
const updateContract = (data: ContractUpdate) => {
    return alovaInstance.Put<Contract>('/v1/sal/contract/update', data);
};

/**
 * @description 删除Contract
 * @param {number} id Contract ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeContract = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/sal/contract/delete/${id}`);
};

/**
 * @description 批量删除Contract
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteContract = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/sal/contract/bulk_delete', ids);
};

/**
 * @description 反审核Delivery
 * @param {number} id Delivery ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(`/v1/sal/contract/anti_approval`, {
        relation_id: id,
    });
};

// /**
//  * @description 导出Contract数据
//  * @param {ContractQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportContract = (params?: ContractQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/contract/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Contract数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importContract = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/contract/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getContractMetadata,
    getContractList,
    getContract,
    createContract,
    updateContract,
    removeContract,
    batchDeleteContract,
    antiApproval,
    // exportContract,
    // importContract,
};
