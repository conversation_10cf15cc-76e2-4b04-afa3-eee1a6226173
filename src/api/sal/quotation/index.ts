import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { Quotation, QuotationCreate, QuotationUpdate } from './types';

/**
 * @description 获取Quotation元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getQuotationMetadata = () => {
    return alovaInstance.Get<any>('/v1/sal/quotation/get_metadata');
};

/**
 * @description 获取Quotation列表
 * @param {QuotationQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Quotation>>} 返回包含Quotation信息的Promise对象
 * @example
 * // 使用示例
 * const quotationList = await getQuotationList({ start: 1, limit: 20 });
 */
const getQuotationList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Quotation>>(
        '/v1/sal/quotation/query',
        params
    );
};

/**
 * @description 获取Quotation详情
 * @param {number} id Quotation ID
 * @returns {Promise<Quotation>} 返回Quotation详情信息
 */
const getQuotation = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Quotation>(`/v1/sal/quotation/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 创建Quotation
 * @param {QuotationCreate} data 创建数据
 * @returns {Promise<Quotation>} 返回创建的Quotation信息
 */
const createQuotation = (data: QuotationCreate) => {
    return alovaInstance.Post<Quotation>('/v1/sal/quotation/create', data);
};

/**
 * @description 更新Quotation
 * @param {QuotationUpdate} data 更新数据
 * @returns {Promise<Quotation>} 返回更新后的Quotation信息
 */
const updateQuotation = (data: QuotationUpdate) => {
    return alovaInstance.Put<Quotation>('/v1/sal/quotation/update', data);
};

/**
 * @description 删除Quotation
 * @param {number} id Quotation ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeQuotation = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/sal/quotation/delete/${id}`);
};

/**
 * @description 批量删除Quotation
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteQuotation = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/sal/quotation/bulk_delete', ids);
};

const getBom = (id?: number) => {
    return alovaInstance.Post<ResponseListModel<Quotation>>(
        '/v1/ops/bom/query',
        {
            filters: {
                couple: 'and',
                conditions: [
                    {
                        field: 'product_id',
                        op: 'eq',
                        value: id,
                    },
                    {
                        field: 'is_quotation',
                        op: 'eq',
                        value: true,
                    },
                ],
            },
            offset: 0,
            limit: 1000,
        }
    );
};

/**
 * @description 反审核Quotation
 * @param {number} id Quotation ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(`/v1/sal/quotation/anti_approval`, {
        relation_id: id,
    });
};

// /**
//  * @description 导出Quotation数据
//  * @param {QuotationQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportQuotation = (params?: QuotationQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/quotation/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Quotation数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importQuotation = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/quotation/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getQuotationMetadata,
    getQuotationList,
    getQuotation,
    createQuotation,
    updateQuotation,
    removeQuotation,
    batchDeleteQuotation,
    getBom,
    antiApproval,
    // exportQuotation,
    // importQuotation,
};
