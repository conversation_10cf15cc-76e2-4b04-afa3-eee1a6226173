import type { ModelBase } from "@/types/core"

/**
 * 单据状态
 */
export enum DocStatus {
  DRAFT = '草稿',
  PENDING_REVIEW = '待审核',
  UNDER_REVIEW = '审核中',
  APPROVED = '已审核',
  REJECTED = '已驳回',
  CANCELLED = '已作废'
}

// Quotation 数据接口定义
export interface Quotation extends ModelBase {
  id: number;
  version: number; // 乐观锁版本号
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  doc_status: DocStatus; // 单据状态
  factory_id?: number; // 所属组织
  project_id?: number; // 所属项目
  customer_id?: number; // 所属客户
  quotation_no?: string; // 报价编号
  version_no?: string; // 版本号
  amount_including_tax?: number; // 含税金额
  amount_not_taxed?: number; // 未税金额
  discount_amount?: number; // 折扣金额
  base_currency_amount?: number; // 本位币金额
  tax_rate_id?: number; // 税率ID
  tax_rate?: number; // 税率
  tax_amount?: number; // 税额
  salesman_id?: number; // 业务员
  is_include_tax?: boolean; // 是否含税
  payment_terms_id?: number; // 付款条款
  pay_mode_id?: number; // 付款方式
  currency_id?: number; // 结算币别
  exchange_rate?: number; // 汇率
  notes?: string; // 备注
  items: QuotationItem[]; // items
  currency?: Currency; // currency
  customer?: Customer; // customer
  project?: Project; // project
  factory?: Factory; // factory
  payment_terms?: PaymentTerms; // payment_terms
  salesman?: Employee; // salesman
  pay_mode?: DictData; // pay_mode
}

export interface QuotationCreate extends Omit<Quotation, 'id'> {

}

export interface QuotationUpdate extends Quotation {

}

// API 响应接口定义
