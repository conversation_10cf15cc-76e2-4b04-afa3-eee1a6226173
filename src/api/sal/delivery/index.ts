import alovaInstance from '@/api';
import type { ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { Delivery, DeliveryCreate, DeliveryUpdate } from './types';

/**
 * @description 获取Delivery元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getMetadata = () => {
    return alovaInstance.Get<any>('/v1/sal/delivery/get_metadata');
};

/**
 * @description 获取Delivery列表
 * @param {DeliveryQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Delivery>>} 返回包含Delivery信息的Promise对象
 * @example
 * // 使用示例
 * const deliveryList = await getDeliveryList({ start: 1, limit: 20 });
 */
const getDeliveryList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Delivery>>(
        '/v1/sal/delivery/query',
        params
    );
};

/**
 * @description 获取Delivery详情
 * @param {number} id Delivery ID
 * @returns {Promise<Delivery>} 返回Delivery详情信息
 */
const getDelivery = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Delivery>(`/v1/sal/delivery/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};
/**
 * @description 创建Delivery
 * @param {DeliveryCreate} data 创建数据
 * @returns {Promise<Delivery>} 返回创建的Delivery信息
 */
const createDelivery = (data: DeliveryCreate) => {
    return alovaInstance.Post<Delivery>('/v1/sal/delivery/create', data);
};

/**
 * @description 更新Delivery
 * @param {DeliveryUpdate} data 更新数据
 * @returns {Promise<Delivery>} 返回更新后的Delivery信息
 */
const updateDelivery = (data: DeliveryUpdate) => {
    return alovaInstance.Put<Delivery>('/v1/sal/delivery/update', data);
};

/**
 * @description 删除Delivery
 * @param {number} id Delivery ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeDelivery = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/sal/delivery/delete/${id}`);
};

/**
 * @description 批量删除Delivery
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteDelivery = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/sal/delivery/bulk_delete', ids);
};

/**
 * @description 反审核Delivery
 * @param {number} id Delivery ID
 * @returns {Promise<Recordable>} 返回反审核结果
 */
const antiApproval = (id: number) => {
    return alovaInstance.Post<Recordable>(`/v1/sal/delivery/anti_approval`, {
        relation_id: id,
    });
};

// /**
//  * @description 导出Delivery数据
//  * @param {DeliveryQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportDelivery = (params?: DeliveryQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/delivery/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Delivery数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importDelivery = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/delivery/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getMetadata,
    getDeliveryList,
    getDelivery,
    createDelivery,
    updateDelivery,
    removeDelivery,
    batchDeleteDelivery,
    antiApproval,
    // exportDelivery,
    // importDelivery,
};
