import type { ModelBase } from "@/types/core"

/**
 * 单据状态
 */
export enum DocStatus {
  DRAFT = '草稿',
  PENDING_REVIEW = '待审核',
  UNDER_REVIEW = '审核中',
  APPROVED = '已审核',
  REJECTED = '已驳回',
  CANCELLED = '已作废'
}

// Delivery 数据接口定义
export interface Delivery extends ModelBase {
  id: number;
  version: number; // 乐观锁版本号
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  doc_status: DocStatus; // 单据状态
  delivery_no?: string; // 送货单号
  doc_date?: string; // 单据日期
  delivery_date?: string; // 送货日期
  factory_id?: number; // 所属组织
  project_id?: number; // 所属项目
  contact?: string; // 主联系人
  phone?: string; // 联系电话
  delivery_address?: string; // 送货地址
  delivery_company?: string; // 派送公司
  driver?: string; // 派送人
  driver_phone?: string; // 派送电话
  plate_number?: string; // 车牌号
  is_sign?: boolean; // 签收
  sign_date?: string; // 签收日期
  is_received_payment?: boolean; // 收款
  is_print?: boolean; // 打印
  un_accept_amount?: number; // 未收金额
  accept_amount?: number; // 已收金额
  customer_id: number; // 客户
  payment_terms_id?: number; // 付款条款
  deposit_amount?: number; // 定金
  currency_id?: number; // 货币
  exchange_rate?: number; // 汇率
  material_cost?: number; // 材料费
  surcharge?: number; // 附加费
  wip_cost?: number; // 工艺费
  base_currency_amount?: number; // 本位币金额
  amount_including_tax?: number; // 总金 (含税)
  amount_not_taxed?: number; // 总金额 (未税)
  tax_rate?: number; // 税率
  tax_amount?: number; // 税额
  total_qty?: number; // 总数量
  total_sale_area?: number; // 结算总面积
  total_weight?: number; // 总重量
  total_girth?: number; // 总周长
  notes?: string; // 备注
  factory?: Factory; // factory
  currency?: Currency; // currency
  customer: Customer; // customer
  payment_terms?: PaymentTerms; // payment_terms
  items: DeliveryItem[]; // items
  surcharges: DeliverySurcharge[]; // surcharges
}

export interface DeliveryCreate extends Omit<Delivery, 'id'> {

}

export interface DeliveryUpdate extends Delivery {

}

// API 响应接口定义
