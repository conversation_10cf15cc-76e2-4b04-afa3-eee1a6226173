import type { ModelBase } from "@/types/core"

/**
 * 客户属性
 */
export enum CustomerAttribute {
  Company = '公司',
  Personal = '个人'
}

/**
 * 客户等级
 */
export enum CustomerGrade {
  Intention = '意向客户',
  Transaction = '成交客户',
  Important = '重要客户'
}

// Customer 数据接口定义
export interface Customer extends ModelBase {
  id: number;
  created_at: string; // 创建时间
  updated_at?: string; // 更新时间
  created_by?: number; // 创建者
  updated_by?: number; // 更新者
  factory_id?: number; // 所属组织
  code?: string; // 客户编码
  name?: string; // 客户名称
  attribute: CustomerAttribute; // 客户属性
  type_id?: number; // 客户类型
  source_id?: number; // 客户来源
  grade?: CustomerGrade; // 客户等级
  salesman_id?: number; // 业务员
  address?: string; // 地址
  contact?: string; // 联系人
  phone?: string; // 联系电话
  mailbox?: string; // 邮箱
  fax?: string; // 传真
  opening_bank?: string; // 开户行
  bank_card_no?: string; // 银行卡号
  currency_id?: number; // 结算货币
  exchange_rate?: number; // 汇率
  payment_days?: number; // 账期
  quota?: number; // 信用额度
  valid: boolean; // 是否生效
  notes?: string; // 备注
  order_qty?: number; // 下单数量
  order_amount?: number; // 下单金额
  unpaid_amount?: number; // 未付款金额
  items: CustomerItem[]; // items
  factory?: Factory; // factory
  type?: DictData; // type
  salesman?: Employee; // salesman
  currency?: Currency; // currency
  source?: DictData; // source
}

export interface CustomerCreate extends Omit<Customer, 'id'> {

}

export interface CustomerUpdate extends Customer {

}

// API 响应接口定义
