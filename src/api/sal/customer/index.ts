import alovaInstance from '@/api';
import type { ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type { Customer, CustomerCreate, CustomerUpdate } from './types';

/**
 * @description 获取Customer元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const getMetadata = () => {
    return alovaInstance.Get<any>('/v1/sal/customer/get_metadata');
};

/**
 * @description 获取Customer列表
 * @param {CustomerQueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<Customer>>} 返回包含Customer信息的Promise对象
 * @example
 * // 使用示例
 * const customerList = await getCustomerList({ start: 1, limit: 20 });
 */
const getCustomerList = (params: QueryParams) => {
    return alovaInstance.Post<ResponseListModel<Customer>>(
        '/v1/sal/customer/query',
        params
    );
};

/**
 * @description 获取Customer详情
 * @param {number} id Customer ID
 * @returns {Promise<Customer>} 返回Customer详情信息
 */
const getCustomer = (id: string | number, max_depth = 2) => {
    return alovaInstance.Get<Customer>(`/v1/sal/customer/get`, {
        params: {
            id,
            max_depth,
        },
        cacheFor: 0,
    });
};

/**
 * @description 创建Customer
 * @param {CustomerCreate} data 创建数据
 * @returns {Promise<Customer>} 返回创建的Customer信息
 */
const createCustomer = (data: CustomerCreate) => {
    return alovaInstance.Post<Customer>('/v1/sal/customer/create', data);
};

/**
 * @description 更新Customer
 * @param {CustomerUpdate} data 更新数据
 * @returns {Promise<Customer>} 返回更新后的Customer信息
 */
const updateCustomer = (data: CustomerUpdate) => {
    return alovaInstance.Put<Customer>('/v1/sal/customer/update', data);
};

/**
 * @description 删除Customer
 * @param {number} id Customer ID
 * @returns {Promise<any>} 返回删除结果
 */
const removeCustomer = (id: number) => {
    return alovaInstance.Delete<any>(`/v1/sal/customer/delete/${id}`);
};

/**
 * @description 批量删除Customer
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const batchDeleteCustomer = (ids: number[]) => {
    return alovaInstance.Delete<any>('/v1/sal/customer/bulk_delete', ids);
};

/**
 * @description 更新客户状态
 * @param {Partial<Recordable>} data
 * @returns {Promise<Recordable>}
 */
const setEnableStatus = (data: Partial<Recordable>) => {
    return alovaInstance.Put<Recordable>(
        '/v1/sal/customer/set_enable_status',
        data
    );
};

// /**
//  * @description 导出Customer数据
//  * @param {CustomerQueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const exportCustomer = (params?: CustomerQueryParams) => {
//   return alovaInstance.Post<Blob>('/v1/sal/customer/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入Customer数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const importCustomer = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('/v1/sal/customer/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
    getMetadata,
    getCustomerList,
    getCustomer,
    createCustomer,
    updateCustomer,
    removeCustomer,
    batchDeleteCustomer,
    setEnableStatus,
    // exportCustomer,
    // importCustomer,
};
