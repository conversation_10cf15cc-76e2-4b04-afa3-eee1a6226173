import type { Component } from 'vue'

/**
 * 简化的渲染器函数类型
 */
export interface RendererParams {
  value: any
  row: any
  column: any
  field: string
  config?: any
  [key: string]: any
}

export type RendererFunction = (params: RendererParams) => string | number | null | undefined

/**
 * 内置渲染器类型
 */
export type BuiltinRendererType = 
  | 'status'
  | 'boolean'
  | 'link'
  | 'actions'
  | 'rating'
  | 'composite'
  | 'currency'
  | 'date'
  | 'image'

/**
 * 渲染器配置
 */
export type RendererConfig = 
  | BuiltinRendererType
  | RendererFunction
  | Component

/**
 * 简化的列配置
 */
export interface SimpleColumnConfig {
  /** 字段名 */
  field?: string
  /** 列标题 */
  title?: string
  /** 列宽度 */
  width?: number | string
  /** 最小宽度 */
  minWidth?: number | string
  /** 最大宽度 */
  maxWidth?: number | string
  /** 固定列 */
  fixed?: 'left' | 'right'
  /** 列类型 */
  type?: 'seq' | 'checkbox' | 'radio' | 'expand' | string
  /** 是否可排序 */
  sortable?: boolean
  /** 是否可筛选 */
  filterable?: boolean
  /** 是否可编辑 */
  editable?: boolean
  /** 是否显示 */
  visible?: boolean
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 表头对齐方式 */
  headerAlign?: 'left' | 'center' | 'right'
  /** 表尾对齐方式 */
  footerAlign?: 'left' | 'center' | 'right'
  /** 是否显示表头溢出提示 */
  showHeaderOverflow?: boolean | string
  /** 是否显示内容溢出提示 */
  showOverflow?: boolean | string
  /** 是否显示表尾溢出提示 */
  showFooterOverflow?: boolean | string
  /** 类名 */
  className?: string
  /** 表头类名 */
  headerClassName?: string
  /** 表尾类名 */
  footerClassName?: string
  
  /** 渲染器配置 */
  renderer?: RendererConfig
  /** 渲染器配置参数 */
  rendererConfig?: any
  
  /** VxeTable 原生插槽配置 */
  slots?: Record<string, string>
  
  /** 子列配置 */
  children?: SimpleColumnConfig[]
  
  /** 其他 VxeTable 列配置 */
  [key: string]: any
}

/**
 * 工具栏配置
 */
export interface SimpleToolbarConfig {
  /** 标题 */
  title?: string
  /** 总数 */
  total?: number
  /** 查询参数 */
  queryParams?: {
    offset?: number
    limit?: number
    [key: string]: any
  }
  /** 是否显示刷新按钮 */
  refresh?: boolean
  /** 是否显示导入按钮 */
  import?: boolean
  /** 是否显示导出按钮 */
  export?: boolean
  /** 是否显示打印按钮 */
  print?: boolean
  /** 是否显示缩放按钮 */
  zoom?: boolean
  /** 是否显示自定义按钮 */
  custom?: boolean
  /** 自定义按钮配置 */
  buttons?: Array<{
    code?: string
    name?: string
    type?: string
    icon?: string
    status?: string
    disabled?: boolean
    visible?: boolean
    [key: string]: any
  }>
  /** 其他工具栏配置 */
  [key: string]: any
}

/**
 * 加载配置
 */
export interface SimpleLoadingConfig {
  /** 加载类型 */
  type?: 'spinner' | 'dots' | 'pulse'
  /** 加载文本 */
  text?: string
  /** 大小 */
  size?: 'small' | 'medium' | 'large'
  /** 颜色 */
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  /** 主题 */
  theme?: 'auto' | 'light' | 'dark'
}

/**
 * 简化的 DataGrid 属性
 */
export interface SimpleDataGridProps {
  /** 列配置 */
  columns: SimpleColumnConfig[]
  /** 数据 */
  data: any[]
  /** 是否加载中 */
  loading?: boolean
  /** 高度 */
  height?: number | string
  /** 工具栏配置 */
  toolbarOptions?: SimpleToolbarConfig
  /** 加载配置 */
  loadingConfig?: SimpleLoadingConfig
  /** 事件处理器 */
  gridEvents?: Record<string, Function>
  /** 其他 VxeGrid 配置 */
  gridOptions?: any
}

/**
 * 状态渲染器配置
 */
export interface StatusRendererConfig {
  /** 状态映射 */
  statusMap?: Record<string | number, {
    text: string
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'
    color?: string
    icon?: string
  }>
  /** 默认状态 */
  defaultStatus?: {
    text: string
    type?: string
    color?: string
    icon?: string
  }
  /** 显示变体 */
  variant?: 'badge' | 'dot' | 'text' | 'progress'
  /** 是否显示图标 */
  showIcon?: boolean
}

/**
 * 布尔渲染器配置
 */
export interface BooleanRendererConfig {
  /** 真值文本 */
  trueText?: string
  /** 假值文本 */
  falseText?: string
  /** 真值图标 */
  trueIcon?: string
  /** 假值图标 */
  falseIcon?: string
  /** 真值颜色 */
  trueColor?: string
  /** 假值颜色 */
  falseColor?: string
  /** 显示类型 */
  type?: 'switch' | 'checkbox' | 'text' | 'icon'
}

/**
 * 链接渲染器配置
 */
export interface LinkRendererConfig {
  /** 链接文本字段 */
  textField?: string
  /** 链接地址字段 */
  hrefField?: string
  /** 目标窗口 */
  target?: '_blank' | '_self' | '_parent' | '_top'
  /** 链接类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  /** 是否下划线 */
  underline?: boolean
  /** 点击事件 */
  onClick?: (params: RendererParams) => void
}

/**
 * 操作渲染器配置
 */
export interface ActionsRendererConfig {
  /** 操作按钮配置 */
  actions: Array<{
    text?: string
    icon?: string
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
    size?: 'small' | 'medium' | 'large'
    disabled?: boolean | ((params: RendererParams) => boolean)
    visible?: boolean | ((params: RendererParams) => boolean)
    onClick?: (params: RendererParams) => void
  }>
  /** 最大显示数量 */
  maxVisible?: number
  /** 更多操作文本 */
  moreText?: string
}

/**
 * 评分渲染器配置
 */
export interface RatingRendererConfig {
  /** 最大评分 */
  max?: number
  /** 是否只读 */
  readonly?: boolean
  /** 是否显示文本 */
  showText?: boolean
  /** 图标 */
  icon?: string
  /** 空图标 */
  voidIcon?: string
  /** 颜色 */
  colors?: string[]
}

/**
 * 货币渲染器配置
 */
export interface CurrencyRendererConfig {
  /** 货币类型 */
  currency?: 'CNY' | 'USD' | 'EUR' | 'GBP' | 'JPY' | string
  /** 精度 */
  precision?: number
  /** 是否显示符号 */
  showSymbol?: boolean
  /** 是否显示零值 */
  showZero?: boolean
  /** 本地化 */
  locale?: string
}

/**
 * 日期渲染器配置
 */
export interface DateRendererConfig {
  /** 日期格式 */
  format?: string
  /** 是否相对时间 */
  relative?: boolean
  /** 时区 */
  timezone?: string
}

/**
 * 图片渲染器配置
 */
export interface ImageRendererConfig {
  /** 宽度 */
  width?: number
  /** 高度 */
  height?: number
  /** 适应方式 */
  fit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'
  /** 默认图片 */
  fallback?: string
  /** 是否可预览 */
  preview?: boolean
  /** 圆角 */
  radius?: number
}

/**
 * 复合渲染器配置
 */
export interface CompositeRendererConfig {
  /** 主要内容 */
  main?: {
    field: string
    formatter?: (value: any, row: any) => string
  }
  /** 副内容 */
  subs?: Array<{
    field: string
    template?: string
    condition?: (row: any) => boolean
  }>
  /** 图标配置 */
  icon?: {
    type: 'avatar' | 'icon'
    avatarField?: string
    imageField?: string
    iconField?: string
    size?: number
  }
  /** 操作配置 */
  actions?: Array<{
    icon: string
    tooltip?: string
    variant?: 'default' | 'outline'
    onClick?: (row: any) => void
  }>
  /** 布局方式 */
  layout?: 'horizontal' | 'vertical'
  /** 显示的操作数量 */
  showActionsCount?: number
}
