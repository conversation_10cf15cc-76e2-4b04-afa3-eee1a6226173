import type { BadgeVariants } from '@/components/ui/badge'
import type { ButtonVariants } from '@/components/ui/button'
import type { VxeGridProps } from 'vxe-table'
import type { Ref } from 'vue'
import type { ModelApi } from '@/api/apiService'
import type {
  FilterCondition,
  FilterGroup,
  QueryParams,
} from '@/types/api/queryParams'
import type { PluginHelper } from '../plugins/core/types'
import type { ModernPluginManager as IPluginManager } from '../plugins'

// ============================================================================
// 核心类型定义
// ============================================================================

/**
 * 简化的 VxeGridProps 类型，移除不需要的配置项
 */
type BaseVxeGridProps = Omit<
  VxeGridProps,
  'toolbarConfig' | 'pagerConfig' | 'formConfig' | 'zoomConfig'
>

// ============================================================================
// 事件处理器类型
// ============================================================================

/**
 * 数据网格事件处理器类型
 */
interface GridEvents {
  // VxeGrid 原生事件
  'checkbox-change'?: (params: any) => void
  'checkbox-all'?: (params: any) => void
  'radio-change'?: (params: any) => void
  'current-change'?: (params: any) => void
  'cell-click'?: (params: any) => void
  'cell-dblclick'?: (params: any) => void
  'sort-change'?: (params: any) => void
  'filter-change'?: (params: any) => void
  'page-change'?: (params: any) => void
  'toolbar-button-click'?: (params: any) => void

  // 自定义选择事件
  'has-selection'?: (params: { hasSelection: boolean }) => void
  'selection-change'?: (params: { selection: any[]; count: number }) => void
  'selection-clear'?: (params: { selection: any[] }) => void
  'selection-count-change'?: (params: { count: number }) => void
}

// ============================================================================
// 工具栏配置类型
// ============================================================================

/**
 * 工具栏标题配置
 */
interface ToolbarTitleProps {
  /** 主标题 */
  main: string
  /** 标题图标 */
  icon?: string
  /** 副标题 */
  sub?: string
  /** 徽章配置 */
  badge?:
    | string
    | { text: string; variant?: BadgeVariants['variant']; className?: string }
  /** 自定义类名 */
  className?: string
}

/**
 * 按钮基础配置
 */
interface BaseButtonConfig {
  /** 按钮图标 */
  icon?: string
  /** 按钮文本 */
  text?: string
  /** 权限配置 */
  permission?: string | string[]
  /** 按钮变体 */
  variant?: ButtonVariants['variant'] | 'default'
  /** 自定义类名 */
  className?: string
  /** 按钮可用条件 */
  condition?: boolean | (() => boolean)
}

/**
 * 常规按钮配置
 */
interface RegularButtonConfig extends BaseButtonConfig {
  onClick?: () => void
}

/**
 * 导出格式类型
 */
export type ExportFormat = 'csv' | 'excel' | 'pdf' | 'json'

/**
 * 导出按钮配置
 */
interface ExportButtonConfig extends BaseButtonConfig {
  formats?: ExportFormat[]
  defaultFormat?: ExportFormat
  onClick?: (format: string) => void
}

/**
 * 工具栏按钮配置
 */
interface ToolbarButtonProps {
  /** 创建按钮配置 */
  create: boolean | RegularButtonConfig
  /** 批量删除按钮配置 */
  bulkDelete?: boolean | RegularButtonConfig
  /** 自定义按钮配置 */
  custom?: RegularButtonConfig[]
}

/**
 * 工具栏操作配置
 */
interface ToolbarActionProps {
  /** 刷新按钮 */
  refresh?: boolean | RegularButtonConfig
  /** 全屏按钮 */
  fullScreen?: boolean | RegularButtonConfig
  /** 表格设置按钮 */
  gridSetting?: boolean | RegularButtonConfig
  /** 导出按钮 */
  dataExport?: boolean | ExportButtonConfig
}

// ============================================================================
// 搜索配置类型
// ============================================================================

/**
 * 搜索面板配置项
 */
interface SearchPanelItem {
  /** 显示文本 */
  label: string
  /** 图标 */
  icon?: string
  /** 值 */
  value: string
  /** 点击处理函数 */
  onClick?: (value: string) => void
  /** 是否可收藏 */
  favoritable?: boolean
}

/**
 * 搜索面板配置
 */
interface SearchPanelConfig {
  /** 快速搜索项 */
  quickItems?: SearchPanelItem[]
  /** 推荐搜索项 */
  recommendedItems?: SearchPanelItem[]
  /** 个人收藏项 */
  favoriteItems?: SearchPanelItem[]
  /** 是否显示字段列表 */
  showFieldList?: boolean
  /** 是否显示高级搜索入口 */
  showAdvancedSearch?: boolean
}

/**
 * 工具栏完整配置
 */
interface ToolbarProps {
  /** 工具栏标题 */
  title?: string | ToolbarTitleProps
  /** 工具栏按钮配置 */
  action?: ToolbarButtonProps
  /** 表格操作配置 */
  tableActions?: ToolbarActionProps
  /** 查询参数 */
  queryParams?: QueryParams
  /** 总条数 */
  total?: Ref<number> | number
  /** 搜索面板配置 */
  searchPanel?: SearchPanelConfig
}

// ============================================================================
// Loading 配置类型
// ============================================================================

/**
 * 数据网格 Loading 配置
 */
interface DataGridLoadingConfig {
  /** Loading 动画类型 */
  type?: 'spinner' | 'dots' | 'bars' | 'pulse'
  /** 加载文本 */
  text?: string
  /** 图标大小 */
  size?: 'small' | 'medium' | 'large'
  /** 颜色主题 */
  color?: 'primary' | 'success' | 'warning' | 'error' | 'info' | string
  /** 主题模式 */
  theme?: 'light' | 'dark' | 'auto'
  /** 文本颜色 */
  textColor?: string
  /** 容器背景色 */
  containerBackground?: string
  /** 遮罩层配置 */
  overlay?: {
    /** 遮罩透明度 */
    opacity?: number
    /** 背景颜色 */
    backgroundColor?: string
    /** 是否启用模糊效果 */
    blur?: boolean
  }
  /** 动画配置 */
  animation?: {
    /** 动画速度 */
    speed?: 'slow' | 'normal' | 'fast'
  }
}

// ============================================================================
// Footer 统计配置类型
// ============================================================================

/**
 * Footer 统计计算器类型
 */
type FooterCalculatorType =
  | 'sum'
  | 'avg'
  | 'max'
  | 'min'
  | 'count'
  | 'countNonNull'

/**
 * Footer 自定义计算函数类型
 */
type FooterCalculatorFunction = (values: unknown[], data: unknown[]) => unknown

/**
 * Footer 单列统计配置
 */
interface FooterSummaryConfig {
  /** 计算器类型或自定义计算函数 */
  calculator?: FooterCalculatorType | FooterCalculatorFunction
  /** 显示标签 */
  label?: string
  /** 格式化函数 */
  formatter?: (value: unknown) => string
  /** 条件过滤函数 */
  condition?: (value: unknown, row: unknown) => boolean
  /** 数字精度 */
  precision?: number
  /** 前缀 */
  prefix?: string
  /** 后缀 */
  suffix?: string
  /** 单元格样式 */
  style?: Record<string, unknown>
  /** 是否显示空值 */
  showEmpty?: boolean
  /** 空值显示文本 */
  emptyText?: string
}

/**
 * Footer 配置选项
 */
interface FooterConfig {
  /** 是否启用 footer */
  enabled?: boolean
  /** footer 位置 */
  position?: 'bottom' | 'top'
  /** 各列的统计配置 */
  summaries?: Record<string, FooterSummaryConfig>
  /** footer 行样式 */
  style?: Record<string, unknown>
  /** footer 行 CSS 类名 */
  className?: string
  /** footer 行高度 */
  height?: number
  /** 是否固定 footer */
  sticky?: boolean
  /** 是否显示边框 */
  showBorder?: boolean
  /** 背景色 */
  background?: string
}

// ============================================================================
// 搜索标签配置类型
// ============================================================================

/**
 * 搜索标签数据接口
 */
interface SearchTagData {
  /** 标签唯一标识符 */
  id: string
  /** 标签显示文本 */
  label: string
  /** 是否为高级筛选 */
  isAdvanced: boolean
  /** 底层筛选条件 */
  underlyingFilter: FilterCondition | FilterGroup
  /** 标签类型 */
  type: 'condition' | 'group' | 'quick' | 'custom'
  /** 是否可编辑 */
  editable: boolean
  /** 标签样式变体 */
  variant?: 'default' | 'secondary' | 'outline'
}

/**
 * 搜索建议接口
 */
interface SearchSuggestions {
  field: string
  fieldLabel?: string
  dataType?: string
  operator?: string
  operatorLabel?: string
  searchValue?: string
}

// ============================================================================
// 核心配置类型
// ============================================================================

/**
 * 数据网格选项配置
 */
type GridOptions = BaseVxeGridProps & {
  /** 是否启用选择功能 */
  enableSelection?: boolean | 'checkbox' | 'radio' | 'seq'
  /** 工具栏配置 */
  toolbarOptions?: ToolbarProps
  /** 事件处理器 */
  gridEvents?: GridEvents
  /** Loading 配置 */
  loadingConfig?: DataGridLoadingConfig
  /** Footer 统计配置 */
  footerConfig?: FooterConfig
}

/**
 * 数据网格组件属性
 */
interface DataGridProps extends VxeGridProps {
  /** 模块模型名称 */
  moduleModel?: string
  /** 数据网格选项 */
  gridOptions?: GridOptions
}

// ============================================================================
// useDataGrid 组合函数返回类型
// ============================================================================

/**
 * 选择管理接口
 */
interface SelectionManager {
  /** 获取当前选择的项目 */
  getSelection: () => unknown[]
  /** 获取选择项目数量 */
  getSelectionCount: () => number
  /** 是否有选择项目 */
  hasSelection: () => boolean
  /** 清除所有选择 */
  clearSelection: () => void
  /** 设置选择状态 */
  setSelection: (
    rows: unknown | unknown[],
    checked?: boolean,
    selectionType?: 'checkbox' | 'radio' | 'seq'
  ) => void
  /** 设置全选状态 */
  setAllSelection: (checked?: boolean) => void
}

/**
 * 资源管理接口
 */
interface ResourceManager {
  /** 清理资源 */
  cleanup: () => void
  /** 添加清理任务 */
  addCleanupTask: (task: () => void) => void
  /** 创建定时器 */
  createTimer: (
    callback: () => void,
    delay: number
  ) => ReturnType<typeof setTimeout>
  /** 创建防抖函数 */
  createDebouncedFunction: <T extends (...args: unknown[]) => unknown>(
    key: string,
    fn: T,
    delay: number
  ) => T
}

/**
 * 内部方法接口
 */
interface InternalMethods {
  /** 设置 grid 实例引用 */
  _setGridRef: (ref: unknown) => void
  /** 触发自定义事件 */
  _emitCustomEvent: (eventName: string, payload: unknown) => void
  /** 检查是否有自定义事件监听器 */
  _hasCustomEventListeners: (eventNames: string[]) => boolean
}

/**
 * useDataGrid 组合函数返回类型
 */
interface UseDataGridReturn
  extends SelectionManager,
    ResourceManager,
    InternalMethods {
  /** 模块模型名称 */
  moduleModel: string
  /** 网格选项配置 */
  gridOptions: Ref<GridOptions>
  /** 模型 API 实例 */
  modelApi: Ref<ModelApi | null>
  /** 加载状态 */
  loading: Ref<boolean>

  // 事件管理
  /** 注册事件监听器 */
  on: (events: Record<string, unknown>) => void

  // 数据管理
  /** 处理数据获取 */
  handleDataFetch: (query: QueryParams) => Promise<void>
  /** 刷新数据 */
  refreshData: () => Promise<void>
  /** 设置加载状态 */
  setLoading: (loading: boolean) => void

  // 缓存管理
  /** 清除缓存 */
  clearCache: () => void

  // 插件管理
  /** 获取插件管理器 */
  getPluginManager: () => IPluginManager
  /** 获取列辅助器 */
  getColumnHelper: () => PluginHelper
}

// ============================================================================
// 导出所有类型
// ============================================================================

export type {
  // 核心类型
  BaseVxeGridProps,
  GridOptions,
  GridEvents,
  DataGridProps,
  UseDataGridReturn,

  // 组件接口
  SelectionManager,
  ResourceManager,
  InternalMethods,

  // 工具栏类型
  ToolbarProps,
  ToolbarActionProps,
  ToolbarTitleProps,
  ToolbarButtonProps,
  BaseButtonConfig,
  RegularButtonConfig,
  ExportButtonConfig,

  // 搜索类型
  SearchSuggestions,
  SearchTagData,
  SearchPanelConfig,
  SearchPanelItem,

  // 配置类型
  DataGridLoadingConfig,
  FooterConfig,
  FooterSummaryConfig,
  FooterCalculatorType,
  FooterCalculatorFunction,
}

/**
 * Version information for type system
 */
export const ENHANCED_TYPES_VERSION = '1.0.0'
