# 插件系统简化 - 快速开始指南

## 🚀 5分钟快速上手

### 第一步：创建你的第一个简化插件

```typescript
// src/components/data-grid/plugins/my-first-plugin.ts
import { createSimpleRenderer } from '@/components/data-grid/plugins/simple'

// 🎯 最简单的文本渲染器 - 仅需3行代码！
export const HelloRenderer = createSimpleRenderer('hello', {
  render: (value) => `👋 Hello, ${value}!`
})
```

### 第二步：在数据网格中使用

```typescript
// 在你的页面组件中
const columns = [
  column.text('id', 'ID'),
  column.hello('name', '姓名'),  // 🎉 自动可用！
  column.text('email', '邮箱')
]
```

### 第三步：查看效果

就是这样！你的插件已经可以工作了。数据网格会自动：
- ✅ 注册你的渲染器
- ✅ 创建 `column.hello()` 方法
- ✅ 处理所有底层配置

## 🎨 进阶：添加样式和配置

```typescript
export const StatusRenderer = createSimpleRenderer('status', {
  render: (value, { config }) => {
    const statusMap = config.statusMap || {
      1: { text: '启用', type: 'success' },
      0: { text: '禁用', type: 'danger' }
    }
    
    const status = statusMap[value] || { text: value, type: 'default' }
    return `<span class="status status-${status.type}">${status.text}</span>`
  },
  
  // 默认配置
  defaultConfig: {
    statusMap: {
      1: { text: '启用', type: 'success' },
      0: { text: '禁用', type: 'danger' }
    }
  },
  
  // 自动注入的样式
  styles: `
    .status {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    .status-success { background: #d1fae5; color: #065f46; }
    .status-danger { background: #fee2e2; color: #991b1b; }
    .status-default { background: #f3f4f6; color: #374151; }
  `,
  
  defaultWidth: 100
})
```

## 📋 使用预设模板

```typescript
import { CommonTemplates } from '@/components/data-grid/plugins/simple'

// 🔥 2行代码创建状态渲染器
export const OrderStatus = CommonTemplates.status.create('order-status', {
  statusMap: {
    'pending': { text: '待处理', type: 'warning' },
    'completed': { text: '已完成', type: 'success' },
    'cancelled': { text: '已取消', type: 'danger' }
  }
})

// 🔗 链接渲染器
export const UserProfile = CommonTemplates.link.create('user-profile', {
  href: (row) => `/user/${row.userId}`,
  target: '_blank'
})

// 💰 货币渲染器  
export const OrderAmount = CommonTemplates.currency.create('order-amount', {
  currency: 'CNY',
  precision: 2
})
```

## 🛠️ CLI 工具快速创建

```bash
# 安装CLI工具
npm install -g @datagrid/cli

# 创建简单插件
datagrid create-plugin --name=my-renderer --type=simple

# 从模板创建
datagrid create-plugin --template=status --name=order-status

# 预览效果
datagrid preview ./my-plugin.ts
```

## 📈 渐进式升级路径

### Level 1: 零配置 (入门)
```typescript
// ✨ 最简单 - 适合原型和学习
export const SimpleRenderer = createSimpleRenderer('simple', {
  render: (value) => `<span>${value}</span>`
})
```

### Level 2: 简化配置 (标准)
```typescript
// 🎯 适合大多数业务场景
export const ConfigurableRenderer = defineSimplePlugin({
  name: 'configurable',
  config: {
    prefix: { type: 'string', default: '' },
    suffix: { type: 'string', default: '' }
  },
  render: (value, { config }) => `${config.prefix}${value}${config.suffix}`
})
```

### Level 3: 完整功能 (企业级)
```typescript
// 🚀 完整功能，向后兼容
export const EnterpriseRenderer: PluginDefinition = {
  // 现有的完整配置...
  name: 'enterprise',
  version: '2.0.0',
  requires: ['AdvancedService'],
  setup: async (context) => {
    // 复杂的企业级逻辑...
  }
}
```

## 🔄 现有插件迁移

### 自动迁移工具
```bash
# 分析现有插件复杂度
datagrid analyze ./src/plugins/

# 自动生成简化版本
datagrid simplify ./src/plugins/status-plugin.ts

# 生成迁移报告
datagrid migrate-report ./src/plugins/
```

### 手动迁移示例
```typescript
// 🔴 原有复杂插件 (80+ 行)
export const OldStatusPlugin: PluginDefinition = {
  name: 'status-renderer',
  version: '1.0.0',
  provides: [/* 复杂配置... */],
  columnHelperMethods: [/* 手动配置... */],
  setup: async (context) => {
    // 50+ 行设置代码...
  }
}

// ✅ 迁移后简化版本 (5 行)
export const NewStatusPlugin = createSimpleRenderer('status', {
  render: (value, { config }) => {
    const status = config.statusMap[value] || { text: value, type: 'default' }
    return `<span class="status status-${status.type}">${status.text}</span>`
  }
})

// 🔄 保持功能兼容
export default NewStatusPlugin.toFullPlugin()
```

## 🎯 常见使用场景

### 1. 简单文本格式化
```typescript
// 高亮搜索结果
export const HighlightRenderer = createSimpleRenderer('highlight', {
  render: (value, { config }) => {
    const keyword = config.keyword || ''
    if (!keyword) return value
    
    const regex = new RegExp(`(${keyword})`, 'gi')
    return value.replace(regex, '<mark>$1</mark>')
  }
})

// 用法
column.highlight('title', '标题', { keyword: '搜索关键词' })
```

### 2. 业务状态显示
```typescript
// 订单状态
export const OrderStatusRenderer = CommonTemplates.status.create('order-status', {
  statusMap: {
    'draft': { text: '草稿', type: 'default' },
    'pending': { text: '待审核', type: 'warning' },
    'approved': { text: '已审核', type: 'success' },
    'rejected': { text: '已拒绝', type: 'danger' }
  },
  variant: 'badge'
})
```

### 3. 数据关联显示
```typescript
// 用户头像和姓名
export const UserRenderer = createSimpleRenderer('user', {
  render: (value, { config, row }) => {
    const avatar = row.avatar || '/default-avatar.png'
    const name = value || '未知用户'
    
    return `
      <div class="user-cell">
        <img src="${avatar}" class="user-avatar" />
        <span class="user-name">${name}</span>
      </div>
    `
  },
  
  styles: `
    .user-cell { 
      display: flex; 
      align-items: center; 
      gap: 8px; 
    }
    .user-avatar { 
      width: 24px; 
      height: 24px; 
      border-radius: 50%; 
    }
  `
})
```

### 4. 操作按钮
```typescript
// 操作按钮组
export const ActionsRenderer = createSimpleRenderer('actions', {
  render: (value, { config, row }) => {
    const actions = config.actions || ['edit', 'delete']
    
    return actions.map(action => {
      const onclick = `handleAction('${action}', ${row.id})`
      return `<button class="action-btn action-${action}" onclick="${onclick}">
        ${getActionLabel(action)}
      </button>`
    }).join('')
  },
  
  defaultConfig: {
    actions: ['edit', 'delete']
  }
})
```

## 🧪 测试和调试

### 简化的测试
```typescript
import { createSimpleRenderer } from '@/components/data-grid/plugins/simple'

describe('我的简单插件', () => {
  test('基本渲染功能', () => {
    const plugin = createSimpleRenderer('test', {
      render: (value) => `Test: ${value}`
    })
    
    // 简单的功能测试
    expect(plugin.name).toBe('test')
    expect(plugin.type).toBe('renderer')
  })
  
  test('配置应用正确', () => {
    const plugin = createSimpleRenderer('test', {
      render: (value, { config }) => `${config.prefix}${value}`,
      defaultConfig: { prefix: 'Hello: ' }
    })
    
    // 测试配置功能
    const result = plugin.options.render('World', { 
      config: { prefix: 'Hello: ' } 
    })
    expect(result).toBe('Hello: World')
  })
})
```

### 开发工具
```typescript
// 开发模式下的实时预览
if (process.env.NODE_ENV === 'development') {
  // 自动热重载
  if (import.meta.hot) {
    import.meta.hot.accept(['./my-plugin.ts'], () => {
      console.log('插件已更新，正在重新加载...')
    })
  }
  
  // 性能监控
  console.time('插件加载时间')
  // 插件加载逻辑...
  console.timeEnd('插件加载时间')
}
```

## 📚 学习资源

### 🎥 视频教程
- **5分钟入门**: 从零创建第一个插件
- **进阶配置**: 使用配置和样式
- **模板使用**: 活用预设模板
- **迁移指南**: 升级现有插件

### 📖 文档链接
- **API参考**: `/docs/api/simple-plugins`
- **模板库**: `/docs/templates`
- **最佳实践**: `/docs/best-practices`
- **常见问题**: `/docs/faq`

### 💬 社区支持
- **Discord**: 实时技术讨论
- **GitHub**: 问题反馈和功能请求
- **StackOverflow**: 标签 `datagrid-plugins`

## 🎉 下一步

恭喜！你已经掌握了简化插件系统的基本使用。现在你可以：

1. **🚀 开始创建**: 用5分钟创建你的第一个插件
2. **📖 深入学习**: 探索更多高级功能和模板
3. **🔄 迁移现有插件**: 使用工具简化现有的复杂插件
4. **🤝 参与社区**: 分享你的插件和经验

简化插件系统让插件开发变得前所未有的简单，同时保持了企业级的功能完整性。立即开始你的插件开发之旅吧！