# 插件系统简化策略

## 现状分析

### 当前复杂性来源

1. **接口复杂度高**
   - `PluginDefinition` 包含10+个配置项
   - 需要理解依赖注入概念 (`provides`、`requires`)
   - 复杂的生命周期管理 (`setup`、`teardown`)

2. **学习曲线陡峭**
   - 需要掌握 TypeScript 高级特性
   - 理解服务容器和依赖注入
   - 掌握插件注册和配置系统

3. **样板代码过多**
   - 简单渲染器需要50+行代码
   - 重复的服务注册逻辑
   - 复杂的错误处理和性能监控

### 开发者痛点

1. **入门门槛高**：新手开发者难以快速上手
2. **开发效率低**：简单功能需要复杂配置
3. **维护成本高**：代码冗余，维护困难
4. **学习资源缺乏**：缺少渐进式学习路径

## 分层简化方案

### 🎯 设计目标

- **降低入门门槛**：5分钟内可创建第一个插件
- **渐进式复杂度**：从简单到复杂的平滑过渡
- **保持功能完整**：不牺牲现有功能的强大性
- **向后兼容**：现有插件继续可用

### 📊 三层架构设计

```
层级3: 完整功能插件 (Enterprise)     ← 当前系统
    ↑ 迁移升级
层级2: 简化配置插件 (Standard)      ← 新增
    ↑ 功能扩展  
层级1: 零配置插件 (Simple)          ← 新增
```

## Layer 1: 零配置插件 (Zero-Config)

### 设计理念
- **约定优于配置**：使用智能默认值
- **函数式API**：一个函数解决一切
- **自动注册**：无需手动服务注册

### API 设计

#### 1. 简单渲染器
```typescript
// 原来：50+ 行代码
// 现在：3 行代码
import { createSimpleRenderer } from '@/data-grid/plugins/simple'

export const MyRenderer = createSimpleRenderer('my-renderer', {
  render: (value) => `<span class="my-style">${value}</span>`
})
```

#### 2. 带配置的渲染器
```typescript
import { createSimpleRenderer } from '@/data-grid/plugins/simple'

export const StatusRenderer = createSimpleRenderer('status', {
  render: (value, { config }) => {
    const variant = config.variant || 'default'
    return `<span class="status status-${variant}">${value}</span>`
  },
  defaultConfig: {
    variant: 'default'
  },
  defaultWidth: 100
})
```

#### 3. Vue 组件渲染器
```typescript
import { createVueRenderer } from '@/data-grid/plugins/simple'
import MyComponent from './MyComponent.vue'

export const MyRenderer = createVueRenderer('my-renderer', MyComponent, {
  defaultWidth: 120
})
```

### 实现机制

#### 自动包装函数
```typescript
function createSimpleRenderer(name: string, options: SimpleRendererOptions) {
  return {
    name: `${name}-simple`,
    version: '1.0.0',
    autoGenerated: true,
    
    // 自动生成完整的插件定义
    toFullPlugin(): PluginDefinition {
      return {
        name: this.name,
        version: this.version,
        description: `Auto-generated simple renderer: ${name}`,
        
        columnHelperMethods: [{
          name: name,
          implementation: function(field: string, title: string, config = {}) {
            return this.createPluginColumn(field, title, name, { 
              ...options.defaultConfig, 
              ...config 
            })
          },
          rendererConfig: {
            component: `${name}RendererComponent`,
            defaultWidth: options.defaultWidth || 100
          }
        }],
        
        setup: async (context) => {
          // 自动注册逻辑
          await autoRegisterRenderer(context, name, options)
        }
      }
    }
  }
}
```

## Layer 2: 简化配置插件 (Simple Config)

### 设计理念
- **声明式配置**：使用简化的配置对象
- **智能推导**：自动推导依赖关系
- **预设模板**：常见场景的预设配置

### API 设计

#### 1. 声明式插件定义
```typescript
import { defineSimplePlugin } from '@/data-grid/plugins/simple'

export const StatusPlugin = defineSimplePlugin({
  name: 'status',
  type: 'renderer', // 'renderer' | 'service' | 'extension'
  
  // 简化的渲染器配置
  renderer: {
    component: StatusComponent,
    defaultWidth: 100,
    props: {
      variant: { type: 'string', default: 'default' },
      showIcon: { type: 'boolean', default: true }
    }
  },
  
  // 自动生成 columnHelper 方法
  columnHelper: {
    methodName: 'status',
    description: '状态列渲染器'
  }
})
```

#### 2. 配置驱动的插件
```typescript
import { defineConfigPlugin } from '@/data-grid/plugins/simple'

export const MyPlugin = defineConfigPlugin({
  name: 'my-plugin',
  
  // 配置schema（自动验证）
  config: {
    theme: { type: 'string', default: 'light', enum: ['light', 'dark'] },
    size: { type: 'number', default: 16, min: 12, max: 24 }
  },
  
  // 简化的渲染逻辑
  render: (value, { config, row }) => {
    return `<span style="font-size: ${config.size}px" class="${config.theme}">${value}</span>`
  }
})
```

### 模板系统

#### 预设模板
```typescript
// 状态渲染器模板
export const statusTemplate = createPluginTemplate('status', {
  defaultProps: {
    variant: 'default',
    showIcon: true,
    statusMap: {}
  },
  
  render: (value, { config }) => {
    const status = config.statusMap[value] || { text: value, type: 'default' }
    return `<span class="status status-${status.type}">${status.text}</span>`
  }
})

// 使用模板
export const MyStatus = statusTemplate.create('my-status', {
  statusMap: {
    1: { text: '启用', type: 'success' },
    0: { text: '禁用', type: 'danger' }
  }
})
```

## Layer 3: 完整功能插件 (Full Feature)

### 保持现有系统
- 现有的 `PluginDefinition` 接口保持不变
- 所有企业级功能继续可用
- 高级用户可以直接使用完整功能

### 升级路径
```typescript
// 从简单插件升级到完整插件
const simplePlugin = createSimpleRenderer('my-renderer', options)

// 升级为完整插件
const fullPlugin = simplePlugin.toFullPlugin()

// 进一步自定义
const customPlugin: PluginDefinition = {
  ...fullPlugin,
  
  // 添加依赖注入
  requires: ['ThemeService'],
  
  // 自定义setup逻辑
  setup: async (context) => {
    await fullPlugin.setup(context)
    
    // 额外的自定义逻辑
    const themeService = context.container.resolveService('ThemeService')
    // ...
  }
}
```

## 开发工具增强

### 1. CLI 工具
```bash
# 创建简单插件
npx datagrid-cli create-plugin --type=simple --name=my-renderer

# 从模板创建
npx datagrid-cli create-plugin --template=status --name=my-status

# 升级插件
npx datagrid-cli upgrade-plugin ./my-plugin.ts --to=standard
```

### 2. VS Code 扩展
- **智能提示**：插件配置自动补全
- **模板片段**：常用插件代码片段
- **错误检查**：实时配置验证
- **预览功能**：插件效果实时预览

### 3. 可视化插件编辑器
```typescript
// Web 界面插件配置
const PluginBuilder = () => {
  return (
    <PluginEditor>
      <PluginType value="renderer" />
      <PluginName value="my-status" />
      <ConfigSchema>
        <Field name="variant" type="select" options={['default', 'primary']} />
        <Field name="showIcon" type="boolean" />
      </ConfigSchema>
      <RenderLogic>
        <CodeEditor language="javascript" />
      </RenderLogic>
    </PluginEditor>
  )
}
```

## 智能默认值系统

### 1. 约定驱动配置
```typescript
// 文件名约定：status-renderer.ts
// 自动推导：
// - 插件名：status
// - 类型：renderer
// - 组件名：StatusRenderer
// - 方法名：status()

export default {
  render: (value) => `<span class="status-${value}">${value}</span>`
}
```

### 2. 智能类型推导
```typescript
// 自动推导配置类型
const plugin = defineSimplePlugin({
  config: {
    variant: { type: 'string', default: 'primary' },
    size: { type: 'number', default: 16 }
  },
  
  // TypeScript 自动推导 config 类型
  render: (value, { config }) => {
    // config.variant: string
    // config.size: number
    return `<span style="font-size: ${config.size}px">${value}</span>`
  }
})
```

### 3. 样式自动注入
```typescript
// 自动注入样式
const plugin = createSimpleRenderer('status', {
  render: (value) => `<span class="auto-status">${value}</span>`,
  
  // 自动注入CSS
  styles: `
    .auto-status {
      padding: 4px 8px;
      border-radius: 4px;
      background: var(--color-primary);
      color: white;
    }
  `
})
```

## 错误处理简化

### 1. 友好的错误信息
```typescript
// 原来：Generic error in plugin system
// 现在：具体的错误指导
class SimplePluginError extends Error {
  constructor(pluginName: string, issue: string, solution: string) {
    super(`插件 '${pluginName}' 配置错误: ${issue}

解决方案:
${solution}

文档: https://docs.datagrid.com/plugins/simple#${issue.toLowerCase()}`)
  }
}
```

### 2. 自动错误恢复
```typescript
// 插件加载失败时的降级策略
const loadPluginWithFallback = async (plugin: SimplePlugin) => {
  try {
    return await loadPlugin(plugin)
  } catch (error) {
    console.warn(`插件 ${plugin.name} 加载失败，使用默认渲染器`)
    return createDefaultRenderer(plugin.name)
  }
}
```

## 性能优化

### 1. 懒加载插件
```typescript
// 按需加载插件
const lazyPlugin = createLazyPlugin(() => import('./my-plugin'))

// 自动代码分割
const statusPlugin = defineSimplePlugin({
  name: 'status',
  lazy: true, // 自动懒加载
  render: (value) => `<span>${value}</span>`
})
```

### 2. 插件缓存
```typescript
// 自动缓存插件实例
const cachedPlugin = createCachedPlugin(plugin, {
  ttl: 5 * 60 * 1000, // 5分钟TTL
  maxSize: 100 // 最多缓存100个实例
})
```

## 文档和学习资源

### 1. 交互式教程
```markdown
# 5分钟创建你的第一个插件

## 步骤1：创建简单渲染器
```typescript
import { createSimpleRenderer } from '@/data-grid/plugins/simple'

export const MyRenderer = createSimpleRenderer('my-renderer', {
  render: (value) => `Hello, ${value}!`
})
```

## 步骤2：在数据网格中使用
```typescript
const columns = [
  column.my_renderer('name', '姓名')
]
```

## 步骤3：添加配置
```typescript
export const MyRenderer = createSimpleRenderer('my-renderer', {
  render: (value, { config }) => `${config.prefix}${value}${config.suffix}`,
  defaultConfig: {
    prefix: 'Hello, ',
    suffix: '!'
  }
})
```
```

### 2. 示例库
- **基础示例**：10+ 简单插件示例
- **进阶示例**：复杂业务场景插件
- **最佳实践**：插件开发最佳实践
- **性能优化**：高性能插件开发技巧

## 迁移指南

### 现有插件迁移
```typescript
// 原有复杂插件
export const OldPlugin: PluginDefinition = {
  // 50+ 行配置
}

// 迁移到简化版本
export const NewPlugin = defineSimplePlugin({
  name: 'my-plugin',
  type: 'renderer',
  render: (value) => `<span>${value}</span>`
})

// 保持功能兼容
export default NewPlugin.toFullPlugin()
```

### 渐进式升级
1. **评估现有插件**：自动分析插件复杂度
2. **生成简化版本**：自动生成简化配置
3. **逐步迁移**：分批迁移，保证稳定性
4. **性能对比**：迁移前后性能对比

## 总结

通过三层架构设计，我们可以：

1. **大幅降低入门门槛**：从50行代码减少到3行
2. **提供渐进式学习路径**：简单 → 标准 → 企业级
3. **保持系统完整性**：不牺牲现有功能
4. **改善开发体验**：更好的工具和文档

这种分层设计既满足了新手开发者的简单需求，也保留了高级用户的灵活性，是一个平衡易用性和功能性的最佳方案。