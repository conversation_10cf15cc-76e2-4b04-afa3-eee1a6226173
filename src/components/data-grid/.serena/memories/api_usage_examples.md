# API 使用示例和最佳实践

## 🚀 Layer 1 (Simple) API 使用指南

### 基础 API

#### createSimpleRenderer
```typescript
import { createSimpleRenderer } from '@/components/data-grid/plugins'

// 最简单的渲染器 (3行代码)
export const HelloRenderer = createSimpleRenderer('hello', {
  render: (value) => `<span class="hello">Hello, ${value}!</span>`
})

// 带配置的渲染器
export const HighlightRenderer = createSimpleRenderer('highlight', {
  render: (value, { config }) => `<span class="highlight-${config.type}">${value}</span>`,
  defaultConfig: { type: 'warning' },
  defaultWidth: 120,
  styles: `
    .highlight-warning { background: #fef3c7; color: #92400e; }
    .highlight-error { background: #fee2e2; color: #991b1b; }
  `
})
```

### 预设模板 API

#### createStatusRenderer
```typescript
import { createStatusRenderer } from '@/components/data-grid/plugins'

export const OrderStatus = createStatusRenderer('order-status', {
  statusMap: {
    'pending': { text: '待处理', type: 'warning' },
    'completed': { text: '已完成', type: 'success' },
    'cancelled': { text: '已取消', type: 'danger' }
  },
  variant: 'badge',    // badge | dot | text
  showIcon: true
})
```

#### createCurrencyRenderer
```typescript
import { createCurrencyRenderer } from '@/components/data-grid/plugins'

export const PriceRenderer = createCurrencyRenderer('price', {
  currency: 'CNY',     // CNY | USD | EUR | GBP | JPY
  precision: 2,        // 小数位数
  showSymbol: true,    // 显示货币符号
  showZero: false      // 零值显示方式
})
```

#### createLinkRenderer
```typescript
import { createLinkRenderer } from '@/components/data-grid/plugins'

export const UserLinkRenderer = createLinkRenderer('user-link', {
  href: (row) => `/users/${row.userId}`,  // 函数或字符串
  target: '_self',                        // _self | _blank
  color: 'blue',                          // blue | green | red
  showExternal: false                     // 显示外部链接图标
})
```

#### createDateRenderer
```typescript
import { createDateRenderer } from '@/components/data-grid/plugins'

export const CreatedTimeRenderer = createDateRenderer('created-time', {
  format: 'YYYY-MM-DD',    // 日期格式
  showTime: true,          // 显示时间
  relative: false          // 相对时间显示
})
```

#### createBooleanRenderer
```typescript
import { createBooleanRenderer } from '@/components/data-grid/plugins'

export const EnabledRenderer = createBooleanRenderer('enabled', {
  style: 'switch',         // badge | switch | icon | text
  trueText: '启用',        // true 值显示文本
  falseText: '禁用',       // false 值显示文本
  trueColor: 'success',    // 颜色主题
  falseColor: 'default'
})
```

## 🎯 在 DataGrid 中的使用

### 基础集成
```typescript
// MyTable.vue
<script setup lang="ts">
import { useDataGrid, createPluginManager } from '@/components/data-grid'
import { OrderStatus, PriceRenderer, UserLinkRenderer } from './plugins'

// 创建插件管理器
const pluginManager = createPluginManager({
  simplePlugins: [OrderStatus, PriceRenderer, UserLinkRenderer],
  simpleOptions: {
    autoFix: true,           // 自动修复常见问题
    validateFirst: true,     // 注册前验证
    continueOnError: true    // 出错时继续注册其他插件
  }
})

// 使用 DataGrid
const { gridOptions, getColumnHelper } = useDataGrid('demo/order', {
  toolbarOptions: {
    title: '订单管理'
  }
})

const column = getColumnHelper()

// 配置列 - 自动生成的方法现在可用
gridOptions.value.columns = [
  { field: 'orderNo', title: '订单号' },
  { ...column.order_status('status', '状态') },          // 自动生成
  { ...column.price('totalAmount', '总金额') },          // 自动生成
  { ...column.user_link('customerName', '客户') }        // 自动生成
]
</script>
```

### 使用预设套装
```typescript
import { 
  SimplePluginPresets, 
  registerSimplePlugins,
  getGlobalPluginManagerSync 
} from '@/components/data-grid/plugins'

const pluginManager = getGlobalPluginManagerSync()

// 注册业务套装
await registerSimplePlugins(pluginManager, SimplePluginPresets.business)

// 或注册完整套装
await registerSimplePlugins(pluginManager, SimplePluginPresets.full)

// 或注册自定义组合
await registerSimplePlugins(pluginManager, [
  ...SimplePluginPresets.basic,
  MyCustomRenderer
])
```

## 🏗️ 高级自定义示例

### 条件渲染
```typescript
export const ConditionalRenderer = createSimpleRenderer('conditional', {
  render: (value, { config, row }) => {
    const isImportant = config.condition?.(value, row) || false
    const className = isImportant ? 'important' : 'normal'
    return `<span class="${className}">${value}</span>`
  },
  defaultConfig: {
    condition: null  // (value, row) => boolean
  }
})

// 使用示例
const ImportantAmountRenderer = createSimpleRenderer('important-amount', {
  ...ConditionalRenderer.options,
  defaultConfig: {
    condition: (value, row) => Number(value) > 10000  // 金额>1万时高亮
  }
})
```

### 复合内容渲染
```typescript
export const UserInfoRenderer = createSimpleRenderer('user-info', {
  render: (value, { config, row }) => {
    const avatar = row.avatar ? `<img src="${row.avatar}" class="user-avatar">` : ''
    const name = `<span class="user-name">${row.name || value}</span>`
    const role = row.role ? `<span class="user-role">${row.role}</span>` : ''
    
    return `
      <div class="user-info">
        ${avatar}
        <div class="user-details">
          ${name}
          ${role}
        </div>
      </div>
    `
  },
  styles: `
    .user-info { display: flex; align-items: center; gap: 8px; }
    .user-avatar { width: 24px; height: 24px; border-radius: 50%; }
    .user-details { display: flex; flex-direction: column; }
    .user-name { font-weight: 500; }
    .user-role { font-size: 12px; color: #6b7280; }
  `
})
```

### 数据处理和格式化
```typescript
export const StatisticsRenderer = createSimpleRenderer('statistics', {
  render: (value, { config }) => {
    const num = Number(value) || 0
    const { format = 'number', showChange = false, previousValue } = config
    
    let formattedValue = ''
    let changeIndicator = ''
    
    // 格式化数值
    switch (format) {
      case 'percentage':
        formattedValue = `${(num * 100).toFixed(1)}%`
        break
      case 'thousands':
        formattedValue = (num / 1000).toFixed(1) + 'K'
        break
      default:
        formattedValue = num.toLocaleString()
    }
    
    // 计算变化
    if (showChange && previousValue !== undefined) {
      const change = num - previousValue
      const changePercent = previousValue !== 0 ? (change / previousValue * 100).toFixed(1) : '0'
      const arrow = change > 0 ? '↗' : change < 0 ? '↘' : '→'
      const color = change > 0 ? 'success' : change < 0 ? 'danger' : 'neutral'
      changeIndicator = `<span class="change change-${color}">${arrow} ${changePercent}%</span>`
    }
    
    return `
      <div class="statistics">
        <span class="value">${formattedValue}</span>
        ${changeIndicator}
      </div>
    `
  },
  
  styles: `
    .statistics { display: flex; align-items: center; gap: 8px; }
    .value { font-weight: 600; }
    .change { font-size: 12px; }
    .change-success { color: #10b981; }
    .change-danger { color: #ef4444; }
    .change-neutral { color: #6b7280; }
  `
})
```

## 🔧 插件管理和调试

### 验证插件配置
```typescript
import { validateSimplePlugin } from '@/components/data-grid/plugins'

const validation = validateSimplePlugin(MyRenderer)
if (!validation.valid) {
  console.error('Plugin validation failed:', validation.errors)
}
```

### 兼容性检查
```typescript
import { CompatibilityChecker } from '@/components/data-grid/plugins'

const compatibility = CompatibilityChecker.checkCompatibility(MyRenderer)
if (!compatibility.compatible) {
  console.warn('Compatibility issues:', compatibility.issues)
}

// 生成完整报告
const report = CompatibilityChecker.generateReport([MyRenderer, OtherRenderer])
console.log(report)
```

### 批量注册与错误处理
```typescript
import { AutoRegisterAdapter } from '@/components/data-grid/plugins'

const result = await AutoRegisterAdapter.autoRegister(
  pluginManager,
  [Plugin1, Plugin2, Plugin3],
  {
    enableAutoFix: true,
    validateBeforeRegister: true,
    continueOnError: true
  }
)

console.log(`注册成功: ${result.registered}, 失败: ${result.failed}`)
if (result.warnings.length > 0) {
  console.warn('警告:', result.warnings)
}
```

## 🎨 样式和主题

### 样式最佳实践
```typescript
export const ThemedRenderer = createSimpleRenderer('themed', {
  render: (value, { config }) => {
    const theme = config.theme || 'light'
    return `<span class="themed-content themed-${theme}">${value}</span>`
  },
  
  styles: `
    .themed-content {
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.2s ease;
    }
    
    /* 明亮主题 */
    .themed-light {
      background: #f8fafc;
      color: #1e293b;
      border: 1px solid #e2e8f0;
    }
    
    /* 深色主题 */
    .themed-dark {
      background: #1e293b;
      color: #f8fafc;
      border: 1px solid #475569;
    }
    
    /* 响应式适配 */
    @media (max-width: 768px) {
      .themed-content {
        font-size: 12px;
        padding: 2px 4px;
      }
    }
  `
})
```

### CSS 变量支持
```typescript
export const CSSVariableRenderer = createSimpleRenderer('css-var', {
  render: (value) => `<span class="css-var-content">${value}</span>`,
  
  styles: `
    .css-var-content {
      color: var(--primary-color, #3b82f6);
      background: var(--primary-bg, #dbeafe);
      border: 1px solid var(--primary-border, #93c5fd);
      padding: var(--spacing-sm, 4px) var(--spacing-md, 8px);
      border-radius: var(--border-radius, 4px);
    }
  `
})
```

## 📊 性能优化技巧

### 防抖和节流
```typescript
export const ThrottledRenderer = createSimpleRenderer('throttled', {
  render: (value, { config }) => {
    // 使用简单的缓存避免重复计算
    const cacheKey = `${value}-${JSON.stringify(config)}`
    if (ThrottledRenderer._cache?.has(cacheKey)) {
      return ThrottledRenderer._cache.get(cacheKey)
    }
    
    const result = expensiveRenderOperation(value, config)
    
    // 简单的 LRU 缓存
    if (!ThrottledRenderer._cache) {
      ThrottledRenderer._cache = new Map()
    }
    if (ThrottledRenderer._cache.size > 100) {
      const firstKey = ThrottledRenderer._cache.keys().next().value
      ThrottledRenderer._cache.delete(firstKey)
    }
    ThrottledRenderer._cache.set(cacheKey, result)
    
    return result
  }
})
```

### 大数据量优化
```typescript
export const VirtualizedRenderer = createSimpleRenderer('virtualized', {
  render: (value, { config, rowIndex }) => {
    // 只渲染必要的内容，避免复杂的DOM结构
    if (config.simplified && rowIndex && rowIndex > 100) {
      return `<span class="simplified">${value}</span>`
    }
    
    return `<div class="full-content">${complexRenderLogic(value)}</div>`
  }
})
```

这些 API 使用示例展示了简化插件系统的完整能力，从最基础的 3 行代码到复杂的业务逻辑，都能够优雅地处理。