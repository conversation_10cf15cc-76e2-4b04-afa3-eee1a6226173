# 智能类型系统设计 - 动态插件智能提示方案

## 🎯 问题分析

### 现状
当前的 DataGrid 插件系统支持动态方法注册，运行时可以调用 `column.email()`、`column.phone()` 等方法，但存在以下问题：

1. **类型安全缺失**：虽然有 `[methodName: string]: any` 允许任意调用，但失去了类型检查
2. **智能提示不完整**：IDE 无法提供准确的方法提示和参数类型
3. **开发体验差**：开发者需要查阅文档才知道可用的方法和参数

### 技术挑战
- **静态 vs 动态**：TypeScript 类型系统是静态的，而插件是动态注册的
- **类型分散**：插件分布在不同文件中，类型定义分散
- **编译时限制**：需要在编译时确定类型，但插件是运行时加载的

## 🏗️ 解决方案架构

### 核心思路
通过 **三层类型系统** + **自动类型生成** + **开发工具支持** 的组合方案：

```
📝 编译时类型生成    ←  构建工具扫描插件定义
    ↓
🔧 TypeScript 模块增强  ←  自动合并类型定义
    ↓  
💡 IDE 智能提示      ←  完整的类型信息和文档
```

## 🛠️ 技术实现方案

### 1. 自动类型生成系统

#### 1.1 插件类型提取器
```typescript
// tools/type-generator/plugin-extractor.ts
export interface PluginTypeInfo {
  name: string
  methodName: string
  parameters: TypeParameter[]
  returnType: string
  description?: string
  examples?: string[]
}

export interface TypeParameter {
  name: string
  type: string
  optional: boolean
  description?: string
  default?: any
}

export class PluginTypeExtractor {
  /**
   * 从插件定义中提取类型信息
   */
  extractFromPlugin(plugin: PluginDefinition): PluginTypeInfo[] {
    const methods: PluginTypeInfo[] = []
    
    if (plugin.columnHelperMethods) {
      for (const method of plugin.columnHelperMethods) {
        const typeInfo = this.analyzeMethodSignature(method)
        methods.push({
          name: plugin.name,
          methodName: method.name,
          parameters: typeInfo.parameters,
          returnType: 'EnhancedColumnConfig',
          description: method.description,
          examples: this.generateExamples(method)
        })
      }
    }
    
    return methods
  }

  /**
   * 分析方法签名，提取参数类型信息
   */
  private analyzeMethodSignature(method: ColumnHelperMethodDefinition): {
    parameters: TypeParameter[]
  } {
    // 通过函数字符串分析或装饰器元数据提取类型信息
    const funcStr = method.implementation.toString()
    
    // 解析参数
    const parameters: TypeParameter[] = [
      {
        name: 'field',
        type: 'string',
        optional: false,
        description: '字段名称'
      },
      {
        name: 'title', 
        type: 'string',
        optional: false,
        description: '列标题'
      }
    ]
    
    // 根据渲染器类型添加特定配置参数
    if (method.rendererConfig) {
      parameters.push({
        name: 'config',
        type: this.getConfigTypeName(method.name),
        optional: true,
        description: '渲染器配置选项'
      })
    }
    
    return { parameters }
  }

  /**
   * 根据方法名推导配置类型名称
   */
  private getConfigTypeName(methodName: string): string {
    const typeMap: Record<string, string> = {
      'status': 'StatusColumnConfig',
      'email': 'EmailColumnConfig', 
      'phone': 'PhoneColumnConfig',
      'link': 'LinkColumnConfig',
      'boolean': 'BooleanColumnConfig',
      'currency': 'CurrencyColumnConfig',
      'date': 'DateColumnConfig',
      'image': 'ImageColumnConfig',
      'rating': 'RatingColumnConfig',
      'actions': 'ActionsColumnConfig',
      'composite': 'CompositeColumnConfig'
    }
    
    return typeMap[methodName] || 'Record<string, any>'
  }

  /**
   * 生成使用示例
   */
  private generateExamples(method: ColumnHelperMethodDefinition): string[] {
    const examples: string[] = []
    
    // 基础示例
    examples.push(`column.${method.name}('fieldName', '列标题')`)
    
    // 带配置的示例
    if (method.rendererConfig) {
      const configExample = this.getConfigExample(method.name)
      examples.push(`column.${method.name}('fieldName', '列标题', ${configExample})`)
    }
    
    return examples
  }

  /**
   * 获取配置示例
   */
  private getConfigExample(methodName: string): string {
    const configExamples: Record<string, string> = {
      'status': `{
        statusMap: {
          1: { text: '启用', type: 'success' },
          0: { text: '禁用', type: 'danger' }
        }
      }`,
      'email': `{
        showTypeIcon: true,
        onClick: (row) => console.log('Email clicked:', row.email)
      }`,
      'phone': `{
        showTypeIcon: true,
        format: 'international'
      }`,
      'link': `{
        target: '_blank',
        showExternal: true
      }`
    }
    
    return configExamples[methodName] || '{ /* 配置选项 */ }'
  }
}
```

#### 1.2 类型定义生成器
```typescript
// tools/type-generator/type-generator.ts
export class TypeDefinitionGenerator {
  /**
   * 生成 ColumnHelper 扩展类型定义
   */
  generateColumnHelperTypes(plugins: PluginTypeInfo[]): string {
    const methods = plugins.map(plugin => 
      this.generateMethodDefinition(plugin)
    ).join('\n\n  ')
    
    return `
// Auto-generated ColumnHelper method definitions
// This file is automatically generated. Do not edit manually.

import type { EnhancedColumnConfig } from './core/types'

declare module './core/types' {
  interface PluginHelper {
${methods}
  }
}

// Export individual method types for external use
${this.generateExportTypes(plugins)}
`
  }

  /**
   * 生成单个方法的类型定义
   */
  private generateMethodDefinition(plugin: PluginTypeInfo): string {
    const params = plugin.parameters.map(param => {
      const optional = param.optional ? '?' : ''
      const defaultValue = param.default !== undefined ? ` = ${JSON.stringify(param.default)}` : ''
      return `${param.name}${optional}: ${param.type}${defaultValue}`
    }).join(', ')

    const jsdoc = this.generateJSDoc(plugin)
    
    return `${jsdoc}
    ${plugin.methodName}(${params}): ${plugin.returnType}`
  }

  /**
   * 生成 JSDoc 注释
   */
  private generateJSDoc(plugin: PluginTypeInfo): string {
    let jsdoc = `    /**\n     * ${plugin.description || `${plugin.methodName} 列渲染器`}\n`
    
    // 参数文档
    for (const param of plugin.parameters) {
      jsdoc += `     * @param ${param.name} ${param.description || ''}\n`
    }
    
    // 示例
    if (plugin.examples && plugin.examples.length > 0) {
      jsdoc += `     * @example\n`
      for (const example of plugin.examples) {
        jsdoc += `     * ${example}\n`
      }
    }
    
    jsdoc += `     */`
    
    return jsdoc
  }

  /**
   * 生成导出类型定义
   */
  private generateExportTypes(plugins: PluginTypeInfo[]): string {
    return plugins.map(plugin => {
      const params = plugin.parameters.map(p => p.type).join(', ')
      return `export type ${plugin.methodName}Method = (${params}) => ${plugin.returnType}`
    }).join('\n')
  }
}
```

### 2. 构建时类型生成

#### 2.1 Vite 插件
```typescript
// tools/vite-plugin-column-types.ts
import { Plugin } from 'vite'
import { PluginTypeExtractor } from './type-generator/plugin-extractor'
import { TypeDefinitionGenerator } from './type-generator/type-generator'

export interface ColumnTypesPluginOptions {
  /** 插件扫描目录 */
  pluginDirs: string[]
  /** 输出类型文件路径 */
  outputPath: string
  /** 是否启用热更新 */
  hotReload?: boolean
}

export function columnTypesPlugin(options: ColumnTypesPluginOptions): Plugin {
  const extractor = new PluginTypeExtractor()
  const generator = new TypeDefinitionGenerator()
  
  return {
    name: 'column-types',
    
    configResolved(config) {
      // 在开发模式下启用类型生成
      if (config.command === 'serve') {
        this.generateTypes()
        
        if (options.hotReload) {
          this.setupWatcher()
        }
      }
    },

    buildStart() {
      // 构建时生成类型
      this.generateTypes()
    },

    async generateTypes() {
      try {
        console.log('🔄 正在生成 ColumnHelper 类型定义...')
        
        // 扫描插件文件
        const plugins = await this.scanPlugins()
        
        // 提取类型信息
        const typeInfos = plugins.flatMap(plugin => 
          extractor.extractFromPlugin(plugin)
        )
        
        // 生成类型定义
        const typeDefinition = generator.generateColumnHelperTypes(typeInfos)
        
        // 写入文件
        await fs.writeFile(options.outputPath, typeDefinition, 'utf-8')
        
        console.log(`✅ 类型定义已生成：${options.outputPath}`)
        console.log(`📊 共生成 ${typeInfos.length} 个方法的类型定义`)
      } catch (error) {
        console.error('❌ 类型生成失败:', error)
      }
    },

    async scanPlugins(): Promise<PluginDefinition[]> {
      const plugins: PluginDefinition[] = []
      
      for (const dir of options.pluginDirs) {
        const files = await glob(`${dir}/**/*.{ts,js}`)
        
        for (const file of files) {
          try {
            const module = await import(file)
            
            // 查找插件定义
            for (const [key, value] of Object.entries(module)) {
              if (this.isPluginDefinition(value)) {
                plugins.push(value as PluginDefinition)
              }
            }
          } catch (error) {
            console.warn(`⚠️ 无法解析插件文件 ${file}:`, error)
          }
        }
      }
      
      return plugins
    },

    isPluginDefinition(obj: any): boolean {
      return obj && 
             typeof obj === 'object' && 
             typeof obj.name === 'string' &&
             typeof obj.version === 'string' &&
             Array.isArray(obj.columnHelperMethods)
    },

    setupWatcher() {
      // 监听插件文件变化，实时重新生成类型
      const chokidar = require('chokidar')
      
      const watcher = chokidar.watch(options.pluginDirs, {
        ignored: /node_modules/,
        persistent: true
      })
      
      watcher.on('change', (path: string) => {
        if (path.endsWith('.ts') || path.endsWith('.js')) {
          console.log(`📝 插件文件变化: ${path}`)
          setTimeout(() => this.generateTypes(), 100) // 延迟生成，避免频繁触发
        }
      })
    }
  }
}
```

#### 2.2 构建配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import { columnTypesPlugin } from './tools/vite-plugin-column-types'

export default defineConfig({
  plugins: [
    // 其他插件...
    
    columnTypesPlugin({
      pluginDirs: [
        'src/components/data-grid/plugins/renderers',
        'src/components/data-grid/plugins/custom'
      ],
      outputPath: 'src/components/data-grid/types/auto-generated.d.ts',
      hotReload: true
    })
  ]
})
```

### 3. 模块增强和类型合并

#### 3.1 自动生成的类型文件
```typescript
// src/components/data-grid/types/auto-generated.d.ts
// Auto-generated ColumnHelper method definitions
// This file is automatically generated. Do not edit manually.

import type { EnhancedColumnConfig } from './core/types'
import type { 
  StatusColumnConfig,
  EmailColumnConfig,
  PhoneColumnConfig,
  LinkColumnConfig,
  BooleanColumnConfig,
  CompositeColumnConfig,
  ActionsColumnConfig,
  RatingColumnConfig,
  CurrencyColumnConfig,
  DateColumnConfig,
  ImageColumnConfig
} from './config-types'

declare module './core/types' {
  interface PluginHelper {
    /**
     * 状态列渲染器
     * @param field 字段名称
     * @param title 列标题  
     * @param config 状态渲染器配置
     * @example
     * column.status('status', '状态')
     * column.status('status', '状态', { 
     *   statusMap: { 
     *     1: { text: '启用', type: 'success' },
     *     0: { text: '禁用', type: 'danger' }
     *   }
     * })
     */
    status(field: string, title: string, config?: StatusColumnConfig): EnhancedColumnConfig

    /**
     * 邮箱链接列渲染器
     * @param field 字段名称
     * @param title 列标题
     * @param config 邮箱渲染器配置
     * @example
     * column.email('email', '邮箱')
     * column.email('email', '邮箱', {
     *   showTypeIcon: true,
     *   onClick: (row) => console.log('Email clicked:', row.email)
     * })
     */
    email(field: string, title: string, config?: EmailColumnConfig): EnhancedColumnConfig

    /**
     * 电话链接列渲染器
     * @param field 字段名称
     * @param title 列标题
     * @param config 电话渲染器配置
     * @example
     * column.phone('phone', '电话')
     * column.phone('phone', '电话', {
     *   showTypeIcon: true,
     *   format: 'international'
     * })
     */
    phone(field: string, title: string, config?: PhoneColumnConfig): EnhancedColumnConfig

    /**
     * 链接列渲染器
     * @param field 字段名称
     * @param title 列标题
     * @param config 链接渲染器配置
     * @example
     * column.link('url', '链接')
     * column.link('url', '链接', {
     *   target: '_blank',
     *   showExternal: true
     * })
     */
    link(field: string, title: string, config?: LinkColumnConfig): EnhancedColumnConfig

    /**
     * 布尔值列渲染器
     * @param field 字段名称
     * @param title 列标题
     * @param config 布尔渲染器配置
     * @example
     * column.boolean('enabled', '启用状态')
     * column.boolean('enabled', '启用状态', {
     *   variant: 'switch',
     *   trueText: '是',
     *   falseText: '否'
     * })
     */
    boolean(field: string, title: string, config?: BooleanColumnConfig): EnhancedColumnConfig

    /**
     * 复合列渲染器
     * @param field 字段名称
     * @param title 列标题
     * @param config 复合渲染器配置
     * @example
     * column.composite('user', '用户信息', {
     *   layout: 'horizontal',
     *   items: [
     *     { type: 'avatar', field: 'avatar' },
     *     { type: 'text', field: 'name' }
     *   ]
     * })
     */
    composite(field: string, title: string, config?: CompositeColumnConfig): EnhancedColumnConfig

    /**
     * 操作按钮列渲染器
     * @param title 列标题
     * @param config 操作按钮配置
     * @example
     * column.actions('操作', {
     *   buttons: [
     *     { text: '编辑', action: 'edit', type: 'primary' },
     *     { text: '删除', action: 'delete', type: 'danger' }
     *   ]
     * })
     */
    actions(title: string, config?: ActionsColumnConfig): EnhancedColumnConfig

    /**
     * 评分列渲染器
     * @param field 字段名称
     * @param title 列标题
     * @param config 评分渲染器配置
     * @example
     * column.rating('rating', '评分')
     * column.rating('rating', '评分', {
     *   max: 5,
     *   allowHalf: true,
     *   showText: true
     * })
     */
    rating(field: string, title: string, config?: RatingColumnConfig): EnhancedColumnConfig

    /**
     * 货币列渲染器
     * @param field 字段名称
     * @param title 列标题
     * @param config 货币渲染器配置
     * @example
     * column.currency('amount', '金额')
     * column.currency('amount', '金额', {
     *   currency: 'CNY',
     *   precision: 2
     * })
     */
    currency(field: string, title: string, config?: CurrencyColumnConfig): EnhancedColumnConfig

    /**
     * 日期列渲染器
     * @param field 字段名称
     * @param title 列标题
     * @param config 日期渲染器配置
     * @example
     * column.date('createdAt', '创建时间')
     * column.date('createdAt', '创建时间', {
     *   format: 'YYYY-MM-DD HH:mm:ss',
     *   relative: true
     * })
     */
    date(field: string, title: string, config?: DateColumnConfig): EnhancedColumnConfig

    /**
     * 图片列渲染器
     * @param field 字段名称
     * @param title 列标题
     * @param config 图片渲染器配置
     * @example
     * column.image('avatar', '头像')
     * column.image('avatar', '头像', {
     *   width: 40,
     *   height: 40,
     *   preview: true
     * })
     */
    image(field: string, title: string, config?: ImageColumnConfig): EnhancedColumnConfig
  }
}

// Export individual method types for external use
export type StatusMethod = (field: string, title: string, config?: StatusColumnConfig) => EnhancedColumnConfig
export type EmailMethod = (field: string, title: string, config?: EmailColumnConfig) => EnhancedColumnConfig
export type PhoneMethod = (field: string, title: string, config?: PhoneColumnConfig) => EnhancedColumnConfig
export type LinkMethod = (field: string, title: string, config?: LinkColumnConfig) => EnhancedColumnConfig
export type BooleanMethod = (field: string, title: string, config?: BooleanColumnConfig) => EnhancedColumnConfig
export type CompositeMethod = (field: string, title: string, config?: CompositeColumnConfig) => EnhancedColumnConfig
export type ActionsMethod = (title: string, config?: ActionsColumnConfig) => EnhancedColumnConfig
export type RatingMethod = (field: string, title: string, config?: RatingColumnConfig) => EnhancedColumnConfig
export type CurrencyMethod = (field: string, title: string, config?: CurrencyColumnConfig) => EnhancedColumnConfig
export type DateMethod = (field: string, title: string, config?: DateColumnConfig) => EnhancedColumnConfig
export type ImageMethod = (field: string, title: string, config?: ImageColumnConfig) => EnhancedColumnConfig
```

#### 3.2 配置类型定义
```typescript
// src/components/data-grid/types/config-types.ts
// 各种渲染器的配置类型定义

/**
 * 状态列配置
 */
export interface StatusColumnConfig {
  /** 状态映射表 */
  statusMap?: Record<string | number, { text: string; type: StatusType }>
  /** 显示变体 */
  variant?: 'badge' | 'dot' | 'text'
  /** 是否显示图标 */
  showIcon?: boolean
  /** 默认状态类型 */
  defaultType?: StatusType
}

export type StatusType = 'default' | 'primary' | 'success' | 'warning' | 'danger'

/**
 * 邮箱列配置
 */
export interface EmailColumnConfig {
  /** 是否显示类型图标 */
  showTypeIcon?: boolean
  /** 点击事件处理器 */
  onClick?: (row: any) => void
  /** 邮箱验证 */
  validateEmail?: boolean
  /** 自定义邮箱格式化 */
  formatter?: (email: string) => string
}

/**
 * 电话列配置
 */
export interface PhoneColumnConfig {
  /** 是否显示类型图标 */
  showTypeIcon?: boolean
  /** 电话格式 */
  format?: 'national' | 'international' | 'e164'
  /** 点击事件处理器 */
  onClick?: (row: any) => void
  /** 国家代码 */
  countryCode?: string
}

/**
 * 链接列配置
 */
export interface LinkColumnConfig {
  /** 链接目标 */
  target?: '_blank' | '_self' | '_parent' | '_top'
  /** 是否显示外部链接图标 */
  showExternal?: boolean
  /** 自定义URL生成函数 */
  href?: string | ((row: any) => string)
  /** 点击事件处理器 */
  onClick?: (row: any) => void
}

/**
 * 布尔列配置
 */
export interface BooleanColumnConfig {
  /** 显示变体 */
  variant?: 'badge' | 'switch' | 'icon' | 'text'
  /** 真值文本 */
  trueText?: string
  /** 假值文本 */
  falseText?: string
  /** 真值颜色 */
  trueColor?: string
  /** 假值颜色 */
  falseColor?: string
  /** 是否可交互 */
  interactive?: boolean
  /** 值变化回调 */
  onChange?: (value: boolean, row: any) => void
}

/**
 * 复合列配置
 */
export interface CompositeColumnConfig {
  /** 布局方向 */
  layout?: 'horizontal' | 'vertical'
  /** 子项配置 */
  items: SubContentItem[]
  /** 间距 */
  gap?: number
  /** 对齐方式 */
  align?: 'start' | 'center' | 'end'
}

export interface SubContentItem {
  /** 内容类型 */
  type: 'text' | 'image' | 'icon' | 'badge' | 'link'
  /** 数据字段 */
  field?: string
  /** 静态内容 */
  content?: string
  /** 样式配置 */
  style?: Record<string, any>
  /** 点击事件 */
  onClick?: (row: any) => void
}

/**
 * 操作按钮列配置
 */
export interface ActionsColumnConfig {
  /** 按钮配置 */
  buttons: ActionButton[]
  /** 按钮布局 */
  layout?: 'horizontal' | 'dropdown'
  /** 最大显示按钮数 */
  maxVisible?: number
  /** 更多按钮文本 */
  moreText?: string
}

export interface ActionButton {
  /** 按钮文本 */
  text: string
  /** 操作类型 */
  action: string
  /** 按钮类型 */
  type?: 'default' | 'primary' | 'success' | 'warning' | 'danger'
  /** 按钮图标 */
  icon?: string
  /** 是否显示 */
  visible?: boolean | ((row: any) => boolean)
  /** 是否禁用 */
  disabled?: boolean | ((row: any) => boolean)
  /** 点击事件 */
  onClick?: (row: any, action: string) => void
}

/**
 * 评分列配置
 */
export interface RatingColumnConfig {
  /** 最大评分 */
  max?: number
  /** 是否允许半分 */
  allowHalf?: boolean
  /** 是否显示文本 */
  showText?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 评分变化回调 */
  onChange?: (value: number, row: any) => void
  /** 自定义图标 */
  icon?: string
  /** 自定义颜色 */
  color?: string
}

/**
 * 货币列配置
 */
export interface CurrencyColumnConfig {
  /** 货币代码 */
  currency?: 'CNY' | 'USD' | 'EUR' | 'JPY' | string
  /** 小数位数 */
  precision?: number
  /** 是否显示货币符号 */
  showSymbol?: boolean
  /** 是否显示千分位分隔符 */
  showThousandsSeparator?: boolean
  /** 负数显示方式 */
  negativeFormat?: 'minus' | 'parentheses' | 'red'
}

/**
 * 日期列配置
 */
export interface DateColumnConfig {
  /** 日期格式 */
  format?: string
  /** 是否显示相对时间 */
  relative?: boolean
  /** 时区 */
  timezone?: string
  /** 语言 */
  locale?: string
  /** 是否显示时间 */
  showTime?: boolean
}

/**
 * 图片列配置
 */
export interface ImageColumnConfig {
  /** 图片宽度 */
  width?: number
  /** 图片高度 */
  height?: number
  /** 是否启用预览 */
  preview?: boolean
  /** 默认图片 */
  fallback?: string
  /** 图片样式 */
  style?: 'circle' | 'rounded' | 'square'
  /** 懒加载 */
  lazy?: boolean
}
```

### 4. 简化插件中的类型支持

#### 4.1 增强简化API的类型支持
```typescript
// src/components/data-grid/plugins/simple/types.ts
import type { EnhancedColumnConfig } from '../core/types'

/**
 * 简化插件的类型定义接口
 */
export interface SimplePluginTyping {
  /** 方法名称 */
  methodName: string
  /** 参数类型定义 */
  parameters: SimpleParameterDef[]
  /** 配置类型名称 */
  configType?: string
  /** 方法描述 */
  description?: string
  /** 使用示例 */
  examples?: string[]
}

export interface SimpleParameterDef {
  name: string
  type: string
  optional?: boolean
  description?: string
  default?: any
}

/**
 * 增强的简化插件创建函数，支持类型元数据
 */
export function createSimpleRenderer<TConfig = Record<string, any>>(
  name: string,
  options: SimpleRendererOptions<TConfig> & {
    /** 类型元数据，用于自动生成智能提示 */
    typing?: SimplePluginTyping
  }
): SimplePlugin<TConfig> {
  const plugin = new SimplePlugin(name, 'renderer', options)
  
  // 如果提供了类型信息，注册到类型系统
  if (options.typing) {
    registerPluginTyping(name, options.typing)
  }
  
  return plugin
}

/**
 * 类型信息注册表
 */
const pluginTypings = new Map<string, SimplePluginTyping>()

export function registerPluginTyping(pluginName: string, typing: SimplePluginTyping) {
  pluginTypings.set(pluginName, typing)
  
  // 触发类型重新生成
  if (process.env.NODE_ENV === 'development') {
    notifyTypeSystemUpdate(pluginName, typing)
  }
}

/**
 * 通知类型系统更新
 */
function notifyTypeSystemUpdate(pluginName: string, typing: SimplePluginTyping) {
  // 发送消息给开发工具，触发类型重新生成
  if (typeof window !== 'undefined') {
    window.postMessage({
      type: 'PLUGIN_TYPE_UPDATE',
      pluginName,
      typing
    }, '*')
  }
}

/**
 * 获取所有注册的类型信息
 */
export function getAllPluginTypings(): Map<string, SimplePluginTyping> {
  return new Map(pluginTypings)
}
```

#### 4.2 使用示例：带类型信息的简化插件
```typescript
// src/components/data-grid/plugins/simple/examples/enhanced-status.ts
import { createSimpleRenderer } from '../api'
import type { SimplePluginTyping } from '../types'

// 定义配置类型
export interface EnhancedStatusConfig {
  statusMap?: Record<string | number, { text: string; type: StatusType }>
  variant?: 'badge' | 'dot' | 'text'
  showIcon?: boolean
  defaultType?: StatusType
}

type StatusType = 'default' | 'primary' | 'success' | 'warning' | 'danger'

// 类型元数据定义
const statusTyping: SimplePluginTyping = {
  methodName: 'enhancedStatus',
  parameters: [
    {
      name: 'field',
      type: 'string',
      description: '数据字段名称'
    },
    {
      name: 'title', 
      type: 'string',
      description: '列标题'
    },
    {
      name: 'config',
      type: 'EnhancedStatusConfig',
      optional: true,
      description: '状态渲染器配置选项'
    }
  ],
  configType: 'EnhancedStatusConfig',
  description: '增强状态列渲染器，支持多种显示变体和自定义状态映射',
  examples: [
    "column.enhancedStatus('status', '状态')",
    `column.enhancedStatus('status', '状态', {
  statusMap: {
    1: { text: '启用', type: 'success' },
    0: { text: '禁用', type: 'danger' }
  },
  variant: 'badge',
  showIcon: true
})`
  ]
}

// 创建带类型信息的简化插件
export const EnhancedStatusRenderer = createSimpleRenderer<EnhancedStatusConfig>('enhancedStatus', {
  render: (value, { config }) => {
    const statusMap = config.statusMap || {}
    const status = statusMap[value] || { 
      text: String(value), 
      type: config.defaultType || 'default' 
    }
    
    const iconHtml = config.showIcon 
      ? `<i class="status-icon status-icon-${status.type}"></i>` 
      : ''
    
    return `
      <span class="status-${config.variant || 'badge'} status-${status.type}">
        ${iconHtml}
        ${status.text}
      </span>
    `
  },
  
  defaultConfig: {
    statusMap: {},
    variant: 'badge',
    showIcon: true,
    defaultType: 'default'
  },
  
  styles: `
    .status-badge {
      display: inline-flex;
      align-items: center;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      gap: 4px;
    }
    .status-default { background: #f3f4f6; color: #374151; }
    .status-success { background: #d1fae5; color: #065f46; }
    .status-warning { background: #fef3c7; color: #92400e; }
    .status-danger { background: #fee2e2; color: #991b1b; }
  `,
  
  defaultWidth: 120,
  
  // 提供类型元数据
  typing: statusTyping
})
```

### 5. 开发工具支持

#### 5.1 VS Code 扩展增强
```typescript
// tools/vscode-extension/src/type-provider.ts
export class ColumnHelperTypeProvider {
  /**
   * 提供智能提示
   */
  provideCompletionItems(
    document: vscode.TextDocument,
    position: vscode.Position
  ): vscode.CompletionItem[] {
    const lineText = document.lineAt(position).text
    const prefix = lineText.substring(0, position.character)
    
    // 检测 column. 调用
    if (prefix.includes('column.')) {
      return this.getColumnMethodCompletions()
    }
    
    return []
  }

  /**
   * 获取列方法的自动补全项
   */
  private getColumnMethodCompletions(): vscode.CompletionItem[] {
    const completions: vscode.CompletionItem[] = []
    
    // 从生成的类型文件中读取方法定义
    const typings = this.loadGeneratedTypings()
    
    for (const [methodName, typing] of typings) {
      const completion = new vscode.CompletionItem(
        methodName,
        vscode.CompletionItemKind.Method
      )
      
      // 设置详细信息
      completion.detail = typing.description
      completion.documentation = new vscode.MarkdownString(
        this.generateDocumentation(typing)
      )
      
      // 设置代码片段
      completion.insertText = new vscode.SnippetString(
        this.generateSnippet(typing)
      )
      
      completions.push(completion)
    }
    
    return completions
  }

  /**
   * 生成方法文档
   */
  private generateDocumentation(typing: SimplePluginTyping): string {
    let doc = `### ${typing.methodName}\n\n`
    doc += `${typing.description}\n\n`
    
    // 参数说明
    if (typing.parameters.length > 0) {
      doc += `**参数:**\n`
      for (const param of typing.parameters) {
        const optional = param.optional ? '?' : ''
        doc += `- \`${param.name}${optional}: ${param.type}\` - ${param.description}\n`
      }
      doc += '\n'
    }
    
    // 使用示例
    if (typing.examples && typing.examples.length > 0) {
      doc += `**示例:**\n`
      for (const example of typing.examples) {
        doc += `\`\`\`typescript\n${example}\n\`\`\`\n`
      }
    }
    
    return doc
  }

  /**
   * 生成代码片段
   */
  private generateSnippet(typing: SimplePluginTyping): string {
    const params = typing.parameters.map((param, index) => {
      if (param.optional && index >= 2) {
        // 可选参数使用占位符
        return `\${${index + 1}:${param.name}}`
      } else {
        // 必填参数
        return `\${${index + 1}:${param.name}}`
      }
    }).join(', ')
    
    return `${typing.methodName}(${params})`
  }

  /**
   * 加载生成的类型定义
   */
  private loadGeneratedTypings(): Map<string, SimplePluginTyping> {
    // 从自动生成的类型文件中加载类型信息
    // 实际实现需要解析 TypeScript 文件或从缓存中读取
    return new Map()
  }
}
```

#### 5.2 实时类型检查
```typescript
// tools/type-checker/real-time-checker.ts
export class RealTimeTypeChecker {
  private watcher: chokidar.FSWatcher
  private typeCache = new Map<string, SimplePluginTyping>()
  
  constructor(private projectRoot: string) {
    this.setupWatcher()
  }

  /**
   * 设置文件监听
   */
  private setupWatcher() {
    this.watcher = chokidar.watch([
      `${this.projectRoot}/src/**/*.vue`,
      `${this.projectRoot}/src/**/*.ts`
    ], {
      ignored: /node_modules/,
      persistent: true
    })

    this.watcher.on('change', (filePath) => {
      this.checkFile(filePath)
    })
  }

  /**
   * 检查文件中的 column 方法调用
   */
  private async checkFile(filePath: string) {
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      const errors = this.validateColumnCalls(content, filePath)
      
      if (errors.length > 0) {
        this.reportErrors(filePath, errors)
      }
    } catch (error) {
      console.warn(`无法检查文件 ${filePath}:`, error)
    }
  }

  /**
   * 验证 column 方法调用
   */
  private validateColumnCalls(content: string, filePath: string): TypeCheckError[] {
    const errors: TypeCheckError[] = []
    
    // 使用正则表达式查找 column.xxx() 调用
    const columnCallRegex = /column\.(\w+)\s*\((.*?)\)/g
    let match
    
    while ((match = columnCallRegex.exec(content)) !== null) {
      const methodName = match[1]
      const argsStr = match[2]
      
      // 检查方法是否存在
      const typing = this.typeCache.get(methodName)
      if (!typing) {
        errors.push({
          type: 'unknown-method',
          message: `未知的列方法: ${methodName}`,
          line: this.getLineNumber(content, match.index),
          column: match.index,
          suggestion: this.suggestSimilarMethods(methodName)
        })
        continue
      }
      
      // 检查参数
      const paramErrors = this.validateParameters(argsStr, typing, filePath)
      errors.push(...paramErrors)
    }
    
    return errors
  }

  /**
   * 验证参数
   */
  private validateParameters(
    argsStr: string, 
    typing: SimplePluginTyping,
    filePath: string
  ): TypeCheckError[] {
    const errors: TypeCheckError[] = []
    
    // 解析参数（简化版本，实际需要更复杂的解析）
    const args = argsStr.split(',').map(arg => arg.trim())
    
    // 检查必填参数
    const requiredParams = typing.parameters.filter(p => !p.optional)
    if (args.length < requiredParams.length) {
      errors.push({
        type: 'missing-parameter',
        message: `缺少必填参数，期望 ${requiredParams.length} 个，得到 ${args.length} 个`,
        line: 0,
        column: 0,
        suggestion: `正确用法: ${typing.methodName}(${typing.parameters.map(p => p.name).join(', ')})`
      })
    }
    
    return errors
  }

  /**
   * 报告错误
   */
  private reportErrors(filePath: string, errors: TypeCheckError[]) {
    console.log(`\n❌ 类型检查错误 - ${filePath}:`)
    
    for (const error of errors) {
      console.log(`  ${error.line}:${error.column} - ${error.message}`)
      if (error.suggestion) {
        console.log(`    💡 建议: ${error.suggestion}`)
      }
    }
  }

  /**
   * 建议相似方法
   */
  private suggestSimilarMethods(methodName: string): string | undefined {
    const availableMethods = Array.from(this.typeCache.keys())
    
    // 简单的相似度匹配
    for (const method of availableMethods) {
      if (this.calculateSimilarity(methodName, method) > 0.6) {
        return `你是否想使用 '${method}' ?`
      }
    }
    
    return `可用的方法: ${availableMethods.slice(0, 5).join(', ')}`
  }

  /**
   * 计算字符串相似度
   */
  private calculateSimilarity(str1: string, str2: string): number {
    // Levenshtein 距离算法的简化版本
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => 
      Array(str1.length + 1).fill(null)
    )
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // 删除
          matrix[j - 1][i] + 1,     // 插入
          matrix[j - 1][i - 1] + indicator // 替换
        )
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  private getLineNumber(content: string, index: number): number {
    return content.substring(0, index).split('\n').length
  }
}

interface TypeCheckError {
  type: 'unknown-method' | 'missing-parameter' | 'invalid-type'
  message: string
  line: number
  column: number
  suggestion?: string
}
```

## 🎯 总结

通过这套智能类型系统设计，我们实现了：

### ✅ 完整的类型安全
- 编译时类型检查
- 运行时错误防护
- 参数类型验证

### ✅ 优秀的开发体验
- 智能方法提示
- 参数类型提示
- 实时错误检查
- 代码片段支持

### ✅ 动态扩展能力
- 插件注册时自动更新类型
- 热重载支持
- 渐进式类型增强

### ✅ 工具链支持
- VS Code 扩展
- 构建时类型生成
- 实时类型检查
- 错误建议和修复

这样，开发者在使用 `column.email()`、`column.phone()` 等方法时，将获得完整的智能提示支持，包括方法名称、参数类型、配置选项和使用示例。