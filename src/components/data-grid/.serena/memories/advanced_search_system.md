# Data-Grid 高级搜索系统

## 搜索系统概述

Data-Grid 的搜索系统是一个**智能化、可视化的查询构建引擎**，支持复杂的查询条件组合、实时搜索建议、搜索收藏等高级功能。系统采用**QueryBuilder 模式**设计，提供了类似 SQL WHERE 子句的灵活查询能力。

## 核心架构

### 组件架构图
```
DGToolbarSearch (主容器)
├── DGSearchInput (搜索输入框)
├── DGSearchColumns (字段选择器) 
├── DGSearchPanel (搜索面板)
├── DGSearchTag (搜索标签)
├── DGAdvancedSearchDialog (高级搜索对话框)
├── FieldSuggestions (字段建议)
└── QueryBuilder (查询构建器)
```

### 数据流架构
```
用户输入 → 搜索建议 → 条件构建 → 查询验证 → API 调用 → 结果展示
    ↓              ↓            ↓           ↓          ↓         ↓
SearchInput → Suggestions → QueryBuilder → Validator → DataGrid → UI
```

## 核心功能模块

### 1. QueryBuilder - 查询构建器

#### 功能特性
- **嵌套查询支持**: 支持 AND/OR 逻辑组合
- **条件管理**: 增删改查查询条件
- **序列化支持**: 查询条件持久化存储
- **类型安全**: TypeScript 完整类型支持

#### 核心方法
```typescript
class QueryBuilder {
  // 确保根查询组存在
  static ensureRootGroup(queryParams: QueryParams): FilterGroup
  
  // 添加查询条件
  static addCondition(queryParams: QueryParams, condition: FilterCondition): void
  
  // 添加查询组
  static addGroup(queryParams: QueryParams, conditions: FilterCondition[], couple: 'and' | 'or'): void
  
  // 替换查询条件
  static replaceById(queryParams: QueryParams, targetId: string, replacement: FilterCondition | FilterGroup): boolean
  
  // 删除查询条件
  static removeById(queryParams: QueryParams, targetId: string): boolean
  
  // 清空所有条件
  static clear(queryParams: QueryParams): void
  
  // 统计条件数量
  static getConditionCount(queryParams: QueryParams): number
}
```

#### 查询结构设计
```typescript
interface FilterGroup {
  id: string
  couple: 'and' | 'or'
  conditions: (FilterCondition | FilterGroup)[]
}

interface FilterCondition {
  id: string
  field: string
  operator: string
  value: any
  dataType?: string
}

interface QueryParams {
  conditions?: FilterGroup
  orderBy?: OrderByClause[]
  pagination?: PaginationParams
}
```

### 2. 搜索组合式函数

#### useSearchTags - 搜索标签管理
```typescript
export function useSearchTags(
  queryParams: Ref<QueryParams>,
  latestColumns: Ref<any[]>
) {
  // 从查询参数生成搜索标签
  const generateSearchTagsFromQueryParams = (params: QueryParams): SearchTagData[]
  
  // 获取字段显示标签
  const getFieldLabel = (fieldName: string): string
  
  // 创建搜索标签
  const createSearchTag = (
    id: string,
    label: string,
    underlyingFilter: FilterCondition | FilterGroup,
    isAdvanced = false,
    type: 'condition' | 'group' = 'condition'
  ): SearchTagData
}
```

#### useSearchOperations - 搜索操作处理
```typescript
export function useSearchOperations(
  dataGridInstance: any,
  showSuggestions: Ref<boolean>,
  currentInputText: Ref<string>,
  suggestionActiveIndex: Ref<number>,
  updateSearchTags: () => void
) {
  // 创建过滤条件
  const createFilterCondition = (
    suggestion: DGSearchSuggestions,
    searchValue: string
  ): FilterCondition
  
  // 添加过滤条件到查询参数
  const addFilterToQueryParams = async (filter: FilterCondition)
  
  // 从查询参数中移除过滤条件
  const removeFilterFromQueryParams = async (
    tagId: string,
    searchTags: Ref<any[]>
  )
  
  // 选择搜索建议
  const selectSuggestion = async (
    suggestion: DGSearchSuggestions,
    searchValue: string
  )
}
```

#### useSearchFavorites - 搜索收藏
```typescript
export function useSearchFavorites(dataSource: string) {
  // 获取收藏列表
  function getFavorites(): SearchFavorite[]
  
  // 添加收藏
  function addFavorite(name: string, tags: SearchTagData[]): void
  
  // 删除收藏
  function removeFavorite(id: string): void
  
  // 应用收藏
  function applyFavorite(id: string): SearchTagData[] | undefined
}

interface SearchFavorite {
  id: string
  name: string
  tags: SearchTagData[]
  createdAt: number
}
```

#### useSearchKeyboard - 键盘导航
```typescript
export function useSearchKeyboard(
  suggestions: Ref<DGSearchSuggestions[]>,
  showSuggestions: Ref<boolean>
) {
  // 处理键盘事件
  const handleKeydown = (event: KeyboardEvent)
  
  // 重置建议状态
  const resetSuggestionState = ()
  
  // 滚动到激活项
  const scrollToActiveItem = ()
}
```

### 3. 搜索核心服务

#### SearchCoreService - 搜索核心服务
```typescript
export class SearchCoreService {
  // 操作符标签映射
  private static readonly OPERATOR_ICONS = {
    '=': 'mdi:equal',
    '!=': 'mdi:not-equal',
    '>': 'mdi:greater-than',
    '<': 'mdi:less-than',
    '>=': 'mdi:greater-than-or-equal',
    '<=': 'mdi:less-than-or-equal',
    'like': 'mdi:contain',
    'in': 'mdi:format-list-bulleted',
    'between': 'mdi:between'
  }
  
  // 获取操作符标签
  static getOperatorLabel(operator: string): string
  
  // 获取操作符图标
  static getOperatorIcon(operator: string): string
  
  // 高亮匹配文本
  static highlightMatch(value?: string, searchText?: string): string
}
```

## 高级功能特性

### 1. 智能搜索建议

#### 建议生成逻辑
- **字段建议**: 基于列配置的字段建议
- **值建议**: 基于历史数据的值建议
- **操作符建议**: 基于字段类型的操作符建议
- **组合建议**: 复合查询条件建议

#### 性能优化
- **防抖处理**: 避免频繁的建议请求
- **缓存机制**: 建议结果本地缓存
- **增量更新**: 基于输入变化的增量建议

### 2. 搜索收藏系统

#### 存储机制
- **本地存储**: localStorage 持久化存储
- **数据源隔离**: 按数据源分别存储收藏
- **自动清理**: 过期收藏自动清理

#### 功能特性
- **快速应用**: 一键应用收藏的搜索条件
- **收藏管理**: 增删改查收藏条件
- **智能命名**: 基于条件内容的智能命名

### 3. 高级搜索对话框

#### 可视化查询构建
- **拖拽支持**: 拖拽调整条件顺序
- **嵌套分组**: 可视化的条件分组
- **实时预览**: 查询结果实时预览
- **语法高亮**: SQL 风格的语法高亮

#### 批量操作
- **批量删除**: 批量删除查询条件
- **批量编辑**: 批量修改查询条件
- **条件复制**: 查询条件的复制粘贴

### 4. 搜索性能优化

#### 查询优化
- **查询缓存**: 查询结果缓存机制
- **去重优化**: 重复查询自动去重
- **分页优化**: 智能分页加载

#### 用户体验优化
- **输入防抖**: 避免频繁触发搜索
- **加载状态**: 搜索过程状态指示
- **错误提示**: 友好的错误提示信息

## 集成和扩展

### 1. DataGrid 集成

#### 数据绑定
```typescript
// 搜索条件与 DataGrid 的双向绑定
const searchParams = ref<QueryParams>({})

// 监听搜索条件变化
watch(searchParams, (newParams) => {
  dataGridInstance.value?.updateQuery(newParams)
}, { deep: true })
```

#### 事件通信
```typescript
// 搜索事件
dataGridInstance.value?.on('search:changed', (params: QueryParams) => {
  // 处理搜索条件变化
})

// 重置事件
dataGridInstance.value?.on('search:reset', () => {
  // 重置搜索条件
  QueryBuilder.clear(searchParams.value)
})
```

### 2. 自定义扩展

#### 自定义操作符
```typescript
// 注册自定义操作符
SearchCoreService.registerOperator('custom_op', {
  label: '自定义操作',
  icon: 'mdi:custom',
  validator: (value: any) => boolean,
  formatter: (value: any) => string
})
```

#### 自定义字段类型
```typescript
// 注册自定义字段类型
SearchCoreService.registerFieldType('custom_type', {
  operators: ['=', '!=', 'custom_op'],
  validator: (value: any) => boolean,
  formatter: (value: any) => string
})
```

### 3. 插件化支持

#### 搜索插件接口
```typescript
interface SearchPlugin {
  name: string
  version: string
  
  // 扩展搜索建议
  getSuggestions?(context: SearchContext): Promise<SearchSuggestion[]>
  
  // 扩展查询处理
  processQuery?(query: QueryParams): QueryParams
  
  // 扩展搜索结果
  processResults?(results: any[]): any[]
}
```

## 最佳实践

### 1. 性能优化建议
- 合理使用搜索防抖，避免频繁请求
- 利用查询缓存，减少重复计算
- 对复杂查询进行分页处理

### 2. 用户体验建议
- 提供清晰的搜索状态反馈
- 使用智能默认值简化操作
- 提供键盘快捷键支持

### 3. 扩展开发建议
- 遵循搜索插件接口规范
- 提供完整的类型定义
- 编写单元测试确保质量

### 4. 配置建议
- 根据数据特点配置合适的操作符
- 设置合理的搜索建议数量
- 配置适当的缓存策略