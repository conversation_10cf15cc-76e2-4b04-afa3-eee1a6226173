<template>
  <div :class="containerClasses" @click="handleClick">
    <Icon v-if="icon" :icon="icon" class="h-4 w-4" />
    <span>{{ displayText }}</span>
  </div>
</template>

<script setup lang="ts">
// 显式设置组件名称，防止Vue自动生成引起的响应式问题
defineOptions({
  name: 'BooleanRenderer'
})

import { Icon } from '@iconify/vue'
import type { BooleanColumnConfig } from '../../types'
import { useBooleanRenderer } from './index'

interface Props {
  value?: any
  row: any
  config: BooleanColumnConfig
  field: string
}

const props = defineProps<Props>()

// 使用 composable API
const { displayText, icon, containerClasses, handleClick } = useBooleanRenderer(props)
</script>