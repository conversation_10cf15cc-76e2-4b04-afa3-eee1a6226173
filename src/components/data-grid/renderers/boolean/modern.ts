import { computed } from 'vue'
import {
  definePlugin,
  defineRenderer,
  defineConfigSchema,
} from '../../core/DevUtils'
import type { InferConfigType } from '../../core/injection'
import type { RowData } from '../../core/types'
import BooleanRenderer from './renderer.vue'


/**
 * 布尔渲染器配置Schema
 */
export const BooleanConfigSchema = defineConfigSchema({
  variant: {
    type: 'string',
    description: '显示样式变体',
    enum: ['default', 'success-danger', 'primary-secondary', 'badge', 'switch'],
    default: 'default',
  },
  showIcon: {
    type: 'boolean',
    description: '是否显示图标',
    default: true,
  },
  customLabels: {
    type: 'object',
    description: '自定义标签',
    properties: {
      true: { type: 'string', default: '是' },
      false: { type: 'string', default: '否' },
    },
  },
  customIcons: {
    type: 'object',
    description: '自定义图标',
    properties: {
      true: { type: 'string', default: 'check' },
      false: { type: 'string', default: 'x' },
    },
  },
  interactive: {
    type: 'boolean',
    description: '是否可交互（开关模式）',
    default: false,
  },
  width: {
    type: 'number',
    description: '列宽度',
    default: 100,
  },
})

/**
 * 布尔配置类型（自动推导）
 */
export type BooleanConfig = InferConfigType<typeof BooleanConfigSchema>

// Import common types from core
// Note: RowData is defined locally above

/**
 * 布尔列配置 (从 core/types.ts 迁移)
 */
export interface BooleanColumnConfig {
  /** 显示样式变体 */
  variant?:
    | 'default'
    | 'success-danger'
    | 'primary-secondary'
    | 'badge'
    | 'switch'
  /** 是否显示图标 */
  showIcon?: boolean
  /** 自定义标签 */
  customLabels?: {
    true: string
    false: string
  }
  /** 自定义图标 */
  customIcons?: {
    true: string
    false: string
  }
  /** 点击事件处理器 */
  onClick?: (row: RowData, value: boolean, event: Event) => void
  /** 列宽 */
  width?: number
  /** 是否固定 */
  fixed?: 'left' | 'right'
  /** 允许扩展属性 */
  [key: string]: unknown
}

/**
 * 布尔渲染器定义（新的声明式API）
 */
export const BooleanRendererDefinition = defineRenderer({
  name: 'boolean',
  component: BooleanRenderer,
  description: '布尔值渲染器，支持开关、徽章、图标等多种显示方式',
  defaultWidth: 100,
  defaultConfig: {
    variant: 'default',
    showIcon: true,
    customLabels: {
      true: '是',
      false: '否',
    },
    customIcons: {
      true: 'check',
      false: 'x',
    },
    interactive: false,
  },
  props: [
    {
      name: 'value',
      type: 'boolean',
      required: true,
      description: '布尔值',
    },
    {
      name: 'row',
      type: 'object',
      required: true,
      description: '行数据',
    },
    {
      name: 'config',
      type: 'object',
      description: '渲染器配置',
    },
    {
      name: 'onChange',
      type: 'function',
      description: '值变更回调函数',
    },
  ],
  validator: (config: BooleanConfig) => {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证变体
    const validVariants = [
      'default',
      'success-danger',
      'primary-secondary',
      'badge',
      'switch',
    ]
    if (config.variant && !validVariants.includes(config.variant)) {
      warnings.push(
        `Unknown variant '${config.variant}', will fall back to 'default'`
      )
    }

    // 验证自定义标签
    if (config.customLabels) {
      if (typeof config.customLabels.true !== 'string') {
        errors.push('customLabels.true must be a string')
      }
      if (typeof config.customLabels.false !== 'string') {
        errors.push('customLabels.false must be a string')
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  },
})

/**
 * 布尔插件（新的声明式定义）
 */
export const ModernBooleanPlugin = definePlugin({
  name: 'boolean-modern',
  version: '2.0.0',
  description: '现代化布尔值渲染器插件，支持多种显示变体和交互模式',

  provides: [
    {
      token: 'BooleanRenderer',
      provider: {
        value: BooleanRendererDefinition,
        singleton: true,
      },
    },
  ],

  // 注册 ColumnHelper 方法
  columnHelperMethods: [
    {
      name: 'boolean',
      implementation: function(
        field: string,
        title: string,
        config: BooleanColumnConfig
      ) {
        return (this as any).createPluginColumn(field, title, 'boolean', config)
      },
      rendererConfig: {
        component: 'BooleanRenderer',
        defaultWidth: 100,
      },
      description: '布尔列',
      priority: 3,
    },
  ],

  setup: async (context) => {
    // 获取渲染器注册表
    const rendererRegistry =
      context.container.resolveService('RendererRegistry')
    if (!rendererRegistry) {
      throw new Error('RendererRegistry service not found')
    }

    // 注册布尔渲染器及其变体
    rendererRegistry.set('boolean', BooleanRendererDefinition)

    // 注册变体渲染器
    const variants = [
      { name: 'boolean-badge', variant: 'badge' },
      { name: 'boolean-switch', variant: 'switch' },
      { name: 'boolean-icon', variant: 'default', showIcon: true },
      { name: 'boolean-text', variant: 'default', showIcon: false },
    ]

    variants.forEach(({ name, variant, showIcon }) => {
      const variantDefinition = {
        ...BooleanRendererDefinition,
        name,
        defaultConfig: {
          ...BooleanRendererDefinition.defaultConfig,
          variant,
          showIcon:
            showIcon !== undefined
              ? showIcon
              : BooleanRendererDefinition.defaultConfig.showIcon,
        },
      }
      rendererRegistry.set(name, variantDefinition)
    })

    // 注册Vue组件
    context.utils.registerComponent('BooleanRenderer', BooleanRenderer)

    // 注册列助手方法
    context.container.registerExtensionPoint(
      'column.helper.boolean',
      (extensionContext, field, title, config) => {
        return {
          field,
          title,
          width: config?.width || 100,
          plugin: 'boolean',
          pluginConfig: config,
        }
      }
    )

    context.utils.logger.info(
      'Modern boolean plugin initialized with variants:',
      variants.map((v) => v.name)
    )
  },

  teardown: async (context) => {
    // 清理注册的组件和服务
    const rendererRegistry =
      context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.delete('boolean')
      rendererRegistry.delete('boolean-badge')
      rendererRegistry.delete('boolean-switch')
      rendererRegistry.delete('boolean-icon')
      rendererRegistry.delete('boolean-text')
    }

    context.utils.logger.info('Modern boolean plugin cleaned up')
  },
})

/**
 * 布尔渲染器组合式API - 支持Vue组件使用
 */
export function useBooleanRenderer(props?: any) {
  // 如果传入了props，返回响应式的组件API
  if (props) {
    // 获取真实配置
    const config = computed(() => ({
      ...BooleanRendererDefinition.defaultConfig,
      ...props.config,
    }))

    // 获取布尔值
    const value = computed(() => {
      if (props.field) {
        return props.row?.[props.field]
      }
      return props.value
    })

    // 获取布尔值（规范化）
    const booleanValue = computed(() => {
      const val = value.value
      // 处理各种形式的布尔值
      if (typeof val === 'boolean') return val
      if (typeof val === 'string') {
        return (
          val.toLowerCase() === 'true' ||
          val === '1' ||
          val === 'yes' ||
          val === '是'
        )
      }
      if (typeof val === 'number') return val > 0
      return Boolean(val)
    })

    // 获取显示文本
    const displayText = computed(() => {
      const isTrue = booleanValue.value
      const labels =
        config.value.customLabels ||
        BooleanRendererDefinition.defaultConfig.customLabels
      return isTrue ? labels.true : labels.false
    })

    // 获取图标
    const icon = computed(() => {
      if (!config.value.showIcon) return null

      const isTrue = booleanValue.value
      const icons =
        config.value.customIcons ||
        BooleanRendererDefinition.defaultConfig.customIcons
      return isTrue ? icons.true : icons.false
    })

    // 容器样式类
    const containerClasses = computed(() => {
      const classes = ['boolean-renderer']
      const isTrue = booleanValue.value
      const variant = config.value.variant

      // 添加变体样式
      switch (variant) {
        case 'success-danger':
          classes.push(isTrue ? 'text-green-600' : 'text-red-600')
          break
        case 'primary-secondary':
          classes.push(isTrue ? 'text-blue-600' : 'text-gray-500')
          break
        case 'badge':
          classes.push(
            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
            isTrue ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          )
          break
        case 'switch':
          classes.push('cursor-pointer select-none')
          break
        default:
          classes.push('flex items-center gap-1')
      }

      // 添加交互样式
      if (config.value.interactive) {
        classes.push('cursor-pointer hover:opacity-80 transition-opacity')
      }

      return classes.join(' ')
    })

    // 点击处理
    const handleClick = () => {
      if (!config.value.interactive) return

      const newValue = !booleanValue.value

      if (props.onChange) {
        props.onChange(newValue, props.row, props.field)
      }
    }

    return {
      displayText,
      icon,
      containerClasses,
      handleClick,
      config,
      value: booleanValue,
    }
  }

  // 如果没有传入props，返回列创建工具API
  return {
    /**
     * 创建布尔列配置
     */
    createBooleanColumn: (
      field: string,
      title: string,
      config?: Partial<BooleanConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 100,
        plugin: 'boolean',
        pluginConfig: {
          ...BooleanRendererDefinition.defaultConfig,
          ...config,
        },
      }
    },

    /**
     * 创建开关列
     */
    createSwitchColumn: (
      field: string,
      title: string,
      config?: Partial<BooleanConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 80,
        plugin: 'boolean-switch',
        pluginConfig: {
          ...BooleanRendererDefinition.defaultConfig,
          variant: 'switch',
          interactive: true,
          ...config,
        },
      }
    },

    /**
     * 创建徽章列
     */
    createBadgeColumn: (
      field: string,
      title: string,
      config?: Partial<BooleanConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 100,
        plugin: 'boolean-badge',
        pluginConfig: {
          ...BooleanRendererDefinition.defaultConfig,
          variant: 'badge',
          ...config,
        },
      }
    },

    /**
     * 创建图标列
     */
    createIconColumn: (
      field: string,
      title: string,
      config?: Partial<BooleanConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 80,
        plugin: 'boolean-icon',
        pluginConfig: {
          ...BooleanRendererDefinition.defaultConfig,
          variant: 'default',
          showIcon: true,
          ...config,
        },
      }
    },

    /**
     * 创建文本列
     */
    createTextColumn: (
      field: string,
      title: string,
      config?: Partial<BooleanConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 100,
        plugin: 'boolean-text',
        pluginConfig: {
          ...BooleanRendererDefinition.defaultConfig,
          variant: 'default',
          showIcon: false,
          ...config,
        },
      }
    },

    /**
     * 验证布尔配置
     */
    validateConfig: BooleanRendererDefinition.validator,

    /**
     * 获取默认配置
     */
    getDefaultConfig: () => ({ ...BooleanRendererDefinition.defaultConfig }),
  }
}

/**
 * 预定义布尔配置
 */
export const CommonBooleanConfigs = {
  /**
   * 启用/禁用开关
   */
  enabledDisabled: {
    variant: 'switch' as const,
    customLabels: {
      true: '启用',
      false: '禁用',
    },
    customIcons: {
      true: 'check-circle',
      false: 'x-circle',
    },
  },

  /**
   * 是/否徽章
   */
  yesNoBadge: {
    variant: 'badge' as const,
    customLabels: {
      true: '是',
      false: '否',
    },
  },

  /**
   * 成功/失败状态
   */
  successFailure: {
    variant: 'success-danger' as const,
    customLabels: {
      true: '成功',
      false: '失败',
    },
    customIcons: {
      true: 'check',
      false: 'x',
    },
  },

  /**
   * 激活/未激活
   */
  activeInactive: {
    variant: 'primary-secondary' as const,
    customLabels: {
      true: '激活',
      false: '未激活',
    },
    customIcons: {
      true: 'play',
      false: 'pause',
    },
  },

  /**
   * 显示/隐藏
   */
  visibleHidden: {
    variant: 'default' as const,
    customLabels: {
      true: '显示',
      false: '隐藏',
    },
    customIcons: {
      true: 'eye',
      false: 'eye-off',
    },
  },
}

/**
 * 布尔值处理工具
 */
export class BooleanValueProcessor {
  /**
   * 标准化布尔值
   */
  static normalize(value: any): boolean {
    if (typeof value === 'boolean') return value
    if (typeof value === 'string') {
      const lower = value.toLowerCase()
      return (
        lower === 'true' ||
        lower === '1' ||
        lower === 'yes' ||
        lower === 'on' ||
        lower === '是'
      )
    }
    if (typeof value === 'number') return value !== 0
    return Boolean(value)
  }

  /**
   * 格式化布尔值显示
   */
  static format(value: any, config: BooleanConfig): string {
    const boolValue = this.normalize(value)
    const labels = config.customLabels || { true: '是', false: '否' }
    return boolValue ? labels.true : labels.false
  }

  /**
   * 获取布尔值对应的图标
   */
  static getIcon(value: any, config: BooleanConfig): string {
    const boolValue = this.normalize(value)
    const icons = config.customIcons || { true: 'check', false: 'x' }
    return boolValue ? icons.true : icons.false
  }

  /**
   * 获取布尔值对应的CSS类
   */
  static getClasses(value: any, config: BooleanConfig): string[] {
    const boolValue = this.normalize(value)
    const variant = config.variant || 'default'

    const baseClasses = ['boolean-value']

    switch (variant) {
      case 'success-danger':
        baseClasses.push(boolValue ? 'text-success' : 'text-danger')
        break
      case 'primary-secondary':
        baseClasses.push(boolValue ? 'text-primary' : 'text-secondary')
        break
      case 'badge':
        baseClasses.push(
          'badge',
          boolValue ? 'badge-success' : 'badge-secondary'
        )
        break
      case 'switch':
        baseClasses.push('switch', boolValue ? 'switch-on' : 'switch-off')
        break
      default:
        baseClasses.push(boolValue ? 'boolean-true' : 'boolean-false')
    }

    return baseClasses
  }
}

// Export the Vue component
export { BooleanRenderer }
