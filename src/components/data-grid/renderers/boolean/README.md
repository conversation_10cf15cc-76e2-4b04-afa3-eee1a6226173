# Boolean Renderer Plugin

Boolean 渲染器插件用于在数据网格中显示布尔值，支持多种视觉样式和自定义配置。

## 基本用法

```typescript
import { BooleanPlugin } from './boolean'

// 注册插件
pluginManager.register(new BooleanPlugin())

// 在列配置中使用
const columns = [
  {
    field: 'isActive',
    title: '状态',
    renderer: 'boolean',
    config: {
      variant: 'default',
      showIcon: true,
    }
  }
]
```

## 配置选项

### BooleanColumnConfig

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `variant` | `'default' \| 'success-danger' \| 'primary-secondary' \| 'badge' \| 'switch'` | `'default'` | 显示样式变体 |
| `showIcon` | `boolean` | `false` | 是否显示图标 |
| `customLabels` | `{ true: string, false: string }` | `{ true: '是', false: '否' }` | 自定义文本标签 |
| `customIcons` | `{ true: string, false: string }` | `{ true: 'mdi:check-circle', false: 'mdi:close-circle' }` | 自定义图标 |
| `onClick` | `(row: any, value: boolean, event: Event) => void` | - | 点击事件处理器 |

## 样式变体

### default
基础样式，简单的文本颜色区分：
- `true`: 绿色文本
- `false`: 红色文本

### success-danger
成功/危险样式，带背景色和边框：
- `true`: 绿色背景 + 绿色边框
- `false`: 红色背景 + 红色边框

### primary-secondary
主要/次要样式：
- `true`: 蓝色背景 + 蓝色边框
- `false`: 灰色背景 + 灰色边框

### badge
徽章样式，实心背景：
- `true`: 绿色徽章
- `false`: 红色徽章

### switch
开关样式（待实现）

## 使用示例

### 基础布尔值显示

```typescript
{
  field: 'isPublished',
  title: '已发布',
  renderer: 'boolean',
  config: {
    variant: 'default',
    showIcon: true,
  }
}
```

### 自定义标签和图标

```typescript
{
  field: 'isOnline',
  title: '在线状态',
  renderer: 'boolean',
  config: {
    variant: 'success-danger',
    showIcon: true,
    customLabels: {
      true: '在线',
      false: '离线'
    },
    customIcons: {
      true: 'mdi:wifi',
      false: 'mdi:wifi-off'
    }
  }
}
```

### 徽章样式

```typescript
{
  field: 'isVip',
  title: 'VIP状态',
  renderer: 'boolean-badge',
  config: {
    showIcon: true,
    customLabels: {
      true: 'VIP',
      false: '普通'
    }
  }
}
```

### 带点击事件

```typescript
{
  field: 'isEnabled',
  title: '启用状态',
  renderer: 'boolean',
  config: {
    variant: 'switch',
    showIcon: false,
    onClick: (row, value, event) => {
      console.log(`切换行 ${row.id} 的状态: ${value}`)
      // 这里可以调用API更新状态
    }
  }
}
```

## 数据类型支持

该渲染器支持多种数据类型的布尔值转换：

- `boolean`: 直接使用
- `string`: `'true'`, `'1'`, `'yes'` (不区分大小写) 转为 `true`，其他为 `false`
- `number`: 非 0 值转为 `true`，0 转为 `false`
- 其他类型: 转为 `false`

## 注册的渲染器类型

- `boolean`: 基础布尔渲染器
- `boolean-switch`: 开关样式的布尔渲染器
- `boolean-badge`: 徽章样式的布尔渲染器

## 样式说明

渲染器使用 Tailwind CSS 类进行样式化，确保与项目整体样式保持一致。各种变体使用不同的颜色主题：

- 绿色系：表示积极/成功状态
- 红色系：表示消极/失败状态
- 蓝色系：表示主要/活跃状态
- 灰色系：表示次要/非活跃状态