import { computed } from 'vue'
import {
  definePlugin,
  defineRenderer,
  defineConfigSchema,
} from '../../core/DevUtils'
import type { InferConfigType } from '../../core/injection'
import type { RowData } from '../../core/types'
import LinkRenderer from './renderer.vue'


/**
 * 链接渲染器配置Schema
 */
export const LinkConfigSchema = defineConfigSchema({
  type: {
    type: 'string',
    description: '链接类型',
    enum: ['url', 'mail', 'phone', 'custom'],
    default: 'url',
  },
  href: {
    type: 'string',
    description: '链接地址模板或固定链接',
  },
  target: {
    type: 'string',
    description: '打开方式',
    enum: ['_blank', '_self', '_parent', '_top'],
    default: '_blank',
  },
  showExternalIcon: {
    type: 'boolean',
    description: '是否显示外链图标',
    default: true,
  },
  showTypeIcon: {
    type: 'boolean',
    description: '是否显示类型图标',
    default: true,
  },
  truncate: {
    type: 'boolean',
    description: '是否截断长链接',
    default: true,
  },
  maxLength: {
    type: 'number',
    description: '最大显示长度',
    default: 30,
  },
  interactive: {
    type: 'boolean',
    description: '是否可交互',
    default: true,
  },
  width: {
    type: 'number',
    description: '列宽度',
    default: 150,
  },
})

/**
 * 链接配置类型（自动推导）
 */
export type LinkConfig = InferConfigType<typeof LinkConfigSchema>

// Import common types from core
// Note: RowData is defined locally above

/**
 * 链接列配置 (从 core/types.ts 迁移)
 */
export interface LinkColumnConfig {
  /** 链接类型 */
  type?: 'url' | 'mail' | 'phone'
  /** 链接地址生成函数 */
  href?: string | ((row: RowData) => string)
  /** 打开方式 */
  target?: '_blank' | '_self' | '_parent' | '_top'
  /** 是否显示外链图标 */
  showExternalIcon?: boolean
  /** 是否显示类型图标 */
  showTypeIcon?: boolean
  /** 点击事件 */
  onClick?: (row: RowData, event: Event) => void
  /** 列宽 */
  width?: number
  /** 允许扩展属性 */
  [key: string]: unknown
}

/**
 * 链接渲染器定义（新的声明式API）
 */
export const LinkRendererDefinition = defineRenderer({
  name: 'link',
  component: LinkRenderer,
  description: '链接渲染器，支持URL、邮箱、电话等多种链接类型',
  defaultWidth: 150,
  defaultConfig: {
    type: 'url',
    target: '_blank',
    showExternalIcon: true,
    showTypeIcon: true,
    truncate: true,
    maxLength: 30,
    interactive: true,
  },
  props: [
    {
      name: 'value',
      type: 'string',
      required: true,
      description: '链接文本或URL',
    },
    {
      name: 'row',
      type: 'object',
      required: true,
      description: '行数据',
    },
    {
      name: 'config',
      type: 'object',
      description: '渲染器配置',
    },
    {
      name: 'onClick',
      type: 'function',
      description: '点击事件回调',
    },
  ],
  validator: (config: LinkConfig) => {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证链接类型
    const validTypes = ['url', 'mail', 'phone', 'custom']
    if (config.type && !validTypes.includes(config.type)) {
      errors.push(`Invalid link type: ${config.type}`)
    }

    // 验证target
    const validTargets = ['_blank', '_self', '_parent', '_top']
    if (config.target && !validTargets.includes(config.target)) {
      warnings.push(
        `Unknown target '${config.target}', will fall back to '_blank'`
      )
    }

    // 验证href模板
    if (config.href && typeof config.href !== 'string') {
      errors.push('href must be a string template')
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  },
})

/**
 * 链接插件（新的声明式定义）
 */
export const ModernLinkPlugin = definePlugin({
  name: 'link-modern',
  version: '2.0.0',
  description: '现代化链接渲染器插件，支持多种链接类型和智能识别',

  provides: [
    {
      token: 'LinkRenderer',
      provider: {
        value: LinkRendererDefinition,
        singleton: true,
      },
    },
  ],

  // 注册 ColumnHelper 方法
  columnHelperMethods: [
    {
      name: 'link',
      implementation: function(
        field: string,
        title: string,
        config: LinkColumnConfig = {}
      ) {
        return (this as any).createPluginColumn(field, title, 'link', config)
      },
      rendererConfig: {
        component: 'LinkRenderer',
        defaultWidth: 150,
      },
      description: '链接列',
      priority: 4,
    },
    {
      name: 'email',
      implementation: function(
        field: string,
        title: string,
        config: Omit<LinkColumnConfig, 'type'> = {}
      ) {
        return (this as any).link(field, title, {
          ...config,
          type: 'mail',
          showTypeIcon: (config.showTypeIcon as boolean) ?? true,
        })
      },
      rendererConfig: {
        component: 'LinkRenderer',
        defaultWidth: 150,
      },
      description: '邮箱链接列',
      priority: 7,
    },
    {
      name: 'phone',
      implementation: function(
        field: string,
        title: string,
        config: Omit<LinkColumnConfig, 'type'> = {}
      ) {
        return (this as any).link(field, title, {
          ...config,
          type: 'phone',
          showTypeIcon: (config.showTypeIcon as boolean) ?? true,
        })
      },
      rendererConfig: {
        component: 'LinkRenderer',
        defaultWidth: 150,
      },
      description: '电话链接列',
      priority: 8,
    },
  ],

  setup: async (context) => {
    // 获取渲染器注册表
    const rendererRegistry =
      context.container.resolveService('RendererRegistry')
    if (!rendererRegistry) {
      throw new Error('RendererRegistry service not found')
    }

    // 注册链接渲染器及其变体
    rendererRegistry.set('link', LinkRendererDefinition)

    // 注册变体渲染器
    const variants = [
      { name: 'link-url', type: 'url' },
      { name: 'link-email', type: 'mail' },
      { name: 'link-phone', type: 'phone' },
      { name: 'link-external', type: 'url', target: '_blank' },
      { name: 'link-internal', type: 'url', target: '_self' },
    ]

    variants.forEach(({ name, type, target }) => {
      const variantDefinition = {
        ...LinkRendererDefinition,
        name,
        defaultConfig: {
          ...LinkRendererDefinition.defaultConfig,
          type,
          target: target || LinkRendererDefinition.defaultConfig.target,
        },
      }
      rendererRegistry.set(name, variantDefinition)
    })

    // 注册Vue组件
    context.utils.registerComponent('LinkRenderer', LinkRenderer)

    // 注册列助手方法
    context.container.registerExtensionPoint(
      'column.helper.link',
      (
        _extensionContext: any,
        field: string,
        title: string,
        config?: Partial<LinkConfig>
      ) => {
        return {
          field,
          title,
          width: config?.width || 150,
          plugin: 'link',
          pluginConfig: config,
        }
      }
    )

    context.utils.logger.info(
      'Modern link plugin initialized with variants:',
      variants.map((v) => v.name)
    )
  },

  teardown: async (context) => {
    // 清理注册的组件和服务
    const rendererRegistry =
      context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.delete('link')
      rendererRegistry.delete('link-url')
      rendererRegistry.delete('link-email')
      rendererRegistry.delete('link-phone')
      rendererRegistry.delete('link-external')
      rendererRegistry.delete('link-internal')
    }

    context.utils.logger.info('Modern link plugin cleaned up')
  },
})

/**
 * 链接渲染器组合式API - 支持Vue组件使用
 */
export function useLinkRenderer(props?: any) {
  // 如果传入了props，返回响应式的组件API
  if (props) {
    // 获取真实配置
    const config = computed(() => ({
      ...LinkRendererDefinition.defaultConfig,
      ...props.config,
    }))

    // 获取链接值
    const value = computed(() => {
      return props.field ? props.row?.[props.field] : props.value
    })

    // 自动检测链接类型（如果未明确指定）
    const detectedType = computed(() => {
      const currentValue = value.value
      if (!currentValue) return config.value.type

      if (config.value.type === 'auto') {
        return LinkTypeDetector.detectType(currentValue)
      }

      return config.value.type
    })

    // 获取显示文本
    const displayValue = computed(() => {
      const currentValue = value.value
      if (!currentValue) return ''

      return LinkTextProcessor.getDisplayText(
        String(currentValue),
        config.value
      )
    })

    // 获取类型图标
    const typeIcon = computed(() => {
      if (!config.value.showTypeIcon) return null
      return LinkTextProcessor.getTypeIcon(detectedType.value)
    })

    // 生成链接URL
    const linkUrl = computed(() => {
      const currentValue = value.value
      if (!currentValue) return '#'

      return LinkUrlGenerator.generateUrl(
        String(currentValue),
        detectedType.value,
        config.value.href,
        props.row
      )
    })

    // 是否显示外部图标
    const showExternalIcon = computed(() => config.value.showExternalIcon)
    const shouldShowExternalIcon = computed(() => {
      return (
        showExternalIcon.value &&
        ['url'].includes(detectedType.value) &&
        config.value.target === '_blank'
      )
    })

    // 链接样式类
    const linkClasses = computed(() => {
      const classes = []

      if (!config.value.interactive) {
        classes.push('pointer-events-none')
      }

      return classes.join(' ')
    })

    // 工具提示文本
    const tooltipText = computed(() => {
      const currentValue = value.value
      if (!currentValue) return ''

      if (
        config.value.truncate &&
        String(currentValue).length > (config.value.maxLength || 30)
      ) {
        return String(currentValue)
      }

      return ''
    })

    // 无障碍标签
    const ariaLabel = computed(() => {
      const currentValue = value.value
      if (!currentValue) return ''

      const typeText =
        {
          url: '链接',
          mail: '邮箱',
          phone: '电话',
          custom: '自定义链接',
        }[detectedType.value] || '链接'

      return `${typeText}: ${currentValue}`
    })

    // 点击处理
    const handleClick = () => {
      if (!config.value.interactive) return

      const url = linkUrl.value
      if (!url || url === '#') return

      if (props.onClick) {
        props.onClick(url, props.row, props.value)
      } else {
        // 默认处理：打开链接
        if (detectedType.value === 'mail' || detectedType.value === 'phone') {
          window.location.href = url
        } else {
          window.open(url, config.value.target)
        }
      }
    }

    return {
      displayValue,
      typeIcon,
      linkUrl,
      showExternalIcon,
      shouldShowExternalIcon,
      linkClasses,
      tooltipText,
      ariaLabel,
      handleClick,
      config,
      value,
      detectedType,
    }
  }

  // 如果没有传入props，返回列创建工具API
  return {
    /**
     * 创建链接列配置
     */
    createLinkColumn: (
      field: string,
      title: string,
      config?: Partial<LinkConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 150,
        plugin: 'link',
        pluginConfig: {
          ...LinkRendererDefinition.defaultConfig,
          ...config,
        },
      }
    },

    /**
     * 创建URL链接列
     */
    createUrlColumn: (
      field: string,
      title: string,
      config?: Partial<LinkConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 150,
        plugin: 'link-url',
        pluginConfig: {
          ...LinkRendererDefinition.defaultConfig,
          type: 'url',
          ...config,
        },
      }
    },

    /**
     * 创建邮箱链接列
     */
    createEmailColumn: (
      field: string,
      title: string,
      config?: Partial<LinkConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 200,
        plugin: 'link-email',
        pluginConfig: {
          ...LinkRendererDefinition.defaultConfig,
          type: 'mail',
          showTypeIcon: true,
          ...config,
        },
      }
    },

    /**
     * 创建电话链接列
     */
    createPhoneColumn: (
      field: string,
      title: string,
      config?: Partial<LinkConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 140,
        plugin: 'link-phone',
        pluginConfig: {
          ...LinkRendererDefinition.defaultConfig,
          type: 'phone',
          showTypeIcon: true,
          ...config,
        },
      }
    },

    /**
     * 创建外部链接列
     */
    createExternalColumn: (
      field: string,
      title: string,
      config?: Partial<LinkConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 150,
        plugin: 'link-external',
        pluginConfig: {
          ...LinkRendererDefinition.defaultConfig,
          type: 'url',
          target: '_blank',
          showExternalIcon: true,
          ...config,
        },
      }
    },

    /**
     * 验证链接配置
     */
    validateConfig: LinkRendererDefinition.validator,

    /**
     * 获取默认配置
     */
    getDefaultConfig: () => ({ ...LinkRendererDefinition.defaultConfig }),
  }
}

/**
 * 链接类型检测器
 */
export class LinkTypeDetector {
  /**
   * 检测链接类型
   */
  static detectType(value: string): 'url' | 'mail' | 'phone' | 'unknown' {
    if (!value || typeof value !== 'string') return 'unknown'

    const trimmed = value.trim()

    // 邮箱检测
    if (this.isEmail(trimmed)) return 'mail'

    // 电话检测
    if (this.isPhone(trimmed)) return 'phone'

    // URL检测
    if (this.isUrl(trimmed)) return 'url'

    return 'unknown'
  }

  /**
   * 检测是否为邮箱
   */
  static isEmail(value: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(value)
  }

  /**
   * 检测是否为电话
   */
  static isPhone(value: string): boolean {
    // 支持多种电话格式
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,20}$/
    return phoneRegex.test(value.replace(/\s/g, ''))
  }

  /**
   * 检测是否为URL
   */
  static isUrl(value: string): boolean {
    try {
      new URL(value)
      return true
    } catch {
      // 检测没有协议的URL
      const urlRegex =
        /^(www\.)?[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
      return urlRegex.test(value)
    }
  }
}

/**
 * 链接URL生成器
 */
export class LinkUrlGenerator {
  /**
   * 生成链接URL
   */
  static generateUrl(
    value: string,
    type: string,
    hrefTemplate?: string,
    row?: RowData
  ): string {
    if (hrefTemplate) {
      // 使用模板生成URL
      return this.processTemplate(hrefTemplate, { value, row })
    }

    switch (type) {
      case 'mail':
        return `mailto:${value}`
      case 'phone':
        return `tel:${value.replace(/\s/g, '')}`
      case 'url':
        return this.normalizeUrl(value)
      default:
        return value
    }
  }

  /**
   * 标准化URL
   */
  static normalizeUrl(url: string): string {
    if (!url) return '#'

    // 如果已经有协议，直接返回
    if (/^https?:\/\//.test(url)) return url

    // 如果是相对路径，直接返回
    if (url.startsWith('/')) return url

    // 其他情况加上http://
    return `http://${url}`
  }

  /**
   * 处理URL模板
   */
  static processTemplate(
    template: string,
    context: { value: string; row?: RowData }
  ): string {
    return template.replace(/\{([^}]+)\}/g, (match, key) => {
      if (key === 'value') return context.value
      if (key.startsWith('row.') && context.row) {
        const rowKey = key.substring(4)
        return String(context.row[rowKey] || '')
      }
      return match
    })
  }
}

/**
 * 链接文本处理器
 */
export class LinkTextProcessor {
  /**
   * 截断链接文本
   */
  static truncate(text: string, maxLength: number = 30): string {
    if (!text || text.length <= maxLength) return text

    // 对于URL，优先保留域名部分
    if (text.startsWith('http')) {
      try {
        const url = new URL(text)
        const domain = url.hostname
        if (domain.length <= maxLength) {
          return domain + (url.pathname !== '/' ? '/...' : '')
        }
      } catch {
        // 如果URL解析失败，使用普通截断
      }
    }

    return text.substring(0, maxLength - 3) + '...'
  }

  /**
   * 获取显示文本
   */
  static getDisplayText(value: string, config: LinkConfig): string {
    if (!value) return ''

    let displayText = value

    // 如果需要截断
    if (config.truncate && config.maxLength) {
      displayText = this.truncate(value, config.maxLength)
    }

    return displayText
  }

  /**
   * 获取类型图标
   */
  static getTypeIcon(type: string): string {
    switch (type) {
      case 'mail':
        return 'mail'
      case 'phone':
        return 'phone'
      case 'url':
        return 'link'
      default:
        return 'external-link'
    }
  }
}

/**
 * 预定义链接配置
 */
export const CommonLinkConfigs = {
  /**
   * 标准URL链接
   */
  standardUrl: {
    type: 'url' as const,
    target: '_blank',
    showExternalIcon: true,
    truncate: true,
    maxLength: 30,
  },

  /**
   * 邮箱链接
   */
  email: {
    type: 'mail' as const,
    showTypeIcon: true,
    showExternalIcon: false,
    truncate: true,
    maxLength: 35,
  },

  /**
   * 电话链接
   */
  phone: {
    type: 'phone' as const,
    showTypeIcon: true,
    showExternalIcon: false,
    truncate: false,
  },

  /**
   * 内部链接
   */
  internal: {
    type: 'url' as const,
    target: '_self',
    showExternalIcon: false,
    truncate: true,
    maxLength: 25,
  },

  /**
   * 下载链接
   */
  download: {
    type: 'url' as const,
    target: '_blank',
    showExternalIcon: true,
    showTypeIcon: true,
    truncate: true,
    maxLength: 20,
  },
}

// Export the Vue component
export { LinkRenderer }
