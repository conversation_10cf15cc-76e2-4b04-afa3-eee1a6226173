<template>
  <Button
    variant="link"
    :class="linkClasses"
    @click="handleClick"
    @keydown.enter="handleClick"
    @keydown.space.prevent="handleClick"
    :title="tooltipText"
    :aria-label="ariaLabel"
    class="h-auto p-0 gap-0 text-left justify-start no-underline hover:underline transition-colors duration-200 truncate"
  >
    <Icon v-if="typeIcon" :icon="typeIcon" class="mr-1 inline-block" />{{
      displayValue
    }}
    <Icon
      v-if="showExternalIcon && shouldShowExternalIcon"
      icon="mdi:open-in-new"
      class="ml-1 inline-block text-xs"
    />
  </Button>
</template>

<script setup lang="ts">
// 显式设置组件名称，防止Vue自动生成引起的响应式问题
defineOptions({
  name: 'LinkRenderer'
})

import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import type { LinkColumnConfig } from '../../types'
import { useLinkRenderer } from './index'

interface Props {
  value?: any
  row: any
  config: LinkColumnConfig
}

const props = defineProps<Props>()

// 使用 composable API
const {
  displayValue,
  typeIcon,
  showExternalIcon,
  shouldShowExternalIcon,
  linkClasses,
  tooltipText,
  ariaLabel,
  handleClick,
} = useLinkRenderer(props)
</script>