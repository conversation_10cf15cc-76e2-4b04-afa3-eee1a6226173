<template>
  <Badge :class="badgeClasses">
    <Icon v-if="status.icon" :icon="status.icon" class="mr-1 inline-block" />
    {{ status.text }}
  </Badge>
</template>

<script setup lang="ts">
// 显式设置组件名称，防止Vue自动生成引起的响应式问题
defineOptions({
  name: 'StatusRenderer',
})

import { Badge } from '@/components/ui/badge'
import { Icon } from '@iconify/vue'
import type { StatusColumnConfig } from '../../types'
import { useStatusRenderer } from './index'
import { onMounted } from 'vue'

interface Props {
  value?: any
  row: any
  config?: StatusColumnConfig
  field?: string
}

const props = defineProps<Props>()

// 使用 composable API
const { status, badgeClasses } = useStatusRenderer({
  ...props,
  field: props.field || '',
})

onMounted(() => {
  console.log('props', props.value)
  // console.log('status', status.value)
})
</script>
