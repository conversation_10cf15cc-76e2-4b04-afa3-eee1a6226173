<template>
  <div class="flex items-center justify-center gap-1">
    <!-- 直接显示的操作按钮 -->
    <Button
      v-for="action in directActions"
      :key="action.key || action.text || action.icon"
      :variant="action.variant || 'outline'"
      :size="action.size || 'sm'"
      :title="action.tooltip"
      class="p-1 gap-0"
      @click="handleActionClick(action)"
    >
      <Icon v-if="action.icon" :icon="action.icon" class="w-4 h-4" />
      <span v-if="action.text">{{ action.text }}</span>
    </Button>

    <!-- 更多操作下拉菜单 -->
    <DropdownMenu v-if="hasMoreActions">
      <DropdownMenuTrigger as-child>
        <Button variant="outline" size="sm" class="p-1 gap-0" title="更多操作">
          <Icon icon="mdi:dots-horizontal" class="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          v-for="action in dropdownActions"
          :key="action.key || action.text || action.icon"
          @select="handleActionClick(action)"
        >
          <Icon v-if="action.icon" :icon="action.icon" class="w-4 h-4 mr-2" />
          <span>{{ action.text || action.tooltip || '操作' }}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface ActionItem {
  /** 唯一标识 */
  key?: string
  /** 按钮文本 */
  text?: string
  /** 图标 */
  icon?: string
  /** 提示文本 */
  tooltip?: string
  /** 按钮变体 */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  /** 按钮大小 */
  size?: 'default' | 'sm' | 'lg' | 'icon'
  /** 点击处理函数 */
  onClick?: (rowData: any) => void
  /** 是否可见 */
  visible?: boolean | ((rowData: any) => boolean)
}

interface Props {
  /** 当前行数据 */
  rowData: any
  /** 操作配置列表 */
  actions?: ActionItem[]
  /** 最大直接显示的操作数量 */
  maxDirectActions?: number
}

const props = withDefaults(defineProps<Props>(), {
  actions: () => [],
  maxDirectActions: 2,
})

// 过滤可见的操作
const visibleActions = computed(() => {
  return props.actions.filter(action => {
    if (action.visible === undefined) return true
    if (typeof action.visible === 'boolean') return action.visible
    if (typeof action.visible === 'function') return action.visible(props.rowData)
    return true
  })
})

// 直接显示的操作
const directActions = computed(() => {
  return visibleActions.value.slice(0, props.maxDirectActions)
})

// 下拉菜单中的操作
const dropdownActions = computed(() => {
  return visibleActions.value.slice(props.maxDirectActions)
})

// 是否有更多操作
const hasMoreActions = computed(() => {
  return dropdownActions.value.length > 0
})

// 处理操作点击
const handleActionClick = (action: ActionItem) => {
  if (action.onClick) {
    action.onClick(props.rowData)
  }
}
</script>
