# Date Renderer Plugin

日期时间渲染器插件，用于格式化和显示日期时间值。

## 功能特性

- ✅ 多种日期格式支持
- ✅ 时间显示控制
- ✅ 相对时间显示
- ✅ 本地化支持
- ✅ 时区处理
- ✅ TypeScript 完整支持

## 基础用法

```typescript
import { useDateRenderer } from '@/components/data-grid/plugins'

// 在列配置中使用
const columns = [
  { field: 'createdAt', title: '创建时间', ...column.date('createdAt', '创建时间') }
]
```

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| format | string | 'YYYY-MM-DD' | 日期格式 |
| showTime | boolean | false | 显示时间 |
| relative | boolean | false | 相对时间显示 |
| locale | string | 'zh-CN' | 本地化设置 |
| timezone | string | 'Asia/Shanghai' | 时区设置 |

## 日期格式

| 格式 | 示例 | 说明 |
|------|------|------|
| YYYY-MM-DD | 2024-01-30 | 年-月-日 |
| YYYY/MM/DD | 2024/01/30 | 年/月/日 |
| MM-DD | 01-30 | 月-日 |
| YYYY-MM-DD HH:mm | 2024-01-30 14:30 | 日期+时间 |
| YYYY-MM-DD HH:mm:ss | 2024-01-30 14:30:45 | 完整时间 |

## 高级用法

```typescript
// 显示完整日期时间
{ 
  ...column.date('updatedAt', '更新时间', {
    format: 'YYYY-MM-DD HH:mm:ss',
    showTime: true,
    timezone: 'Asia/Shanghai'
  })
}

// 相对时间显示
{ 
  ...column.date('lastSeen', '最后活跃', {
    relative: true,
    locale: 'zh-CN'
  })
}
```

## 相对时间示例

| 时间差 | 显示效果 |
|-------|---------|
| 1分钟前 | 1分钟前 |
| 1小时前 | 1小时前 |
| 1天前 | 1天前 |
| 1周前 | 1周前 |
| 1个月前 | 1个月前 |

## 本地化支持

支持多种语言的日期时间显示：
- 中文 (zh-CN)
- 英文 (en-US)
- 日文 (ja-JP)
- 韩文 (ko-KR)