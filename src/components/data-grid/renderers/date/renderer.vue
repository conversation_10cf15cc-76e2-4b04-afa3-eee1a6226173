<template>
  <span :class="dateClass" :title="tooltipText">
    {{ isValid ? formattedValue : (realConfig.emptyText || '--') }}
  </span>
</template>

<script setup lang="ts">
import { useDateRenderer } from './modern'

interface Props {
  value: string | number | Date
  config?: any
  configId?: string
  row?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
})

const { formattedValue, tooltipText, dateClass, isValid, realConfig } = useDateRenderer(props)
</script>

<style scoped>
.date-value {
  @apply font-mono;
}

.date-value.today {
  @apply font-semibold text-blue-600 bg-blue-50 px-1 rounded;
}

.date-value.relative {
  @apply text-gray-600;
}

.date-value:hover {
  @apply bg-gray-50 px-1 rounded transition-colors;
}
</style>