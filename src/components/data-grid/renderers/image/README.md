# Image Renderer Plugin

图片渲染器插件，用于在数据网格中显示图片。

## 功能特性

- ✅ 图片URL自动处理
- ✅ 多种尺寸预设
- ✅ 占位符和错误处理
- ✅ 懒加载支持
- ✅ 预览功能
- ✅ TypeScript 完整支持

## 基础用法

```typescript
import { useImageRenderer } from '@/components/data-grid/plugins'

// 在列配置中使用
const columns = [
  { field: 'avatar', title: '头像', ...column.image('avatar', '头像') }
]
```

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| size | string | 'medium' | 图片尺寸 |
| shape | string | 'square' | 图片形状 |
| lazy | boolean | true | 懒加载 |
| preview | boolean | false | 点击预览 |
| placeholder | string | - | 占位符图片 |
| fallback | string | - | 错误时显示 |

## 尺寸预设

| 尺寸 | 像素 | 适用场景 |
|------|------|---------|
| small | 24x24 | 小图标 |
| medium | 48x48 | 头像 |
| large | 96x96 | 大头像 |
| thumbnail | 64x64 | 缩略图 |

## 形状选项

| 形状 | 说明 |
|------|------|
| square | 正方形 |
| circle | 圆形 |
| rounded | 圆角矩形 |

## 高级用法

```typescript
// 圆形头像
{ 
  ...column.image('userAvatar', '用户头像', {
    size: 'medium',
    shape: 'circle',
    lazy: true,
    preview: true,
    placeholder: '/default-avatar.png'
  })
}

// 产品缩略图
{ 
  ...column.image('productImage', '产品图片', {
    size: 'thumbnail',
    shape: 'rounded',
    preview: true,
    fallback: '/no-image.png'
  })
}
```

## URL处理

支持多种URL格式：
- 绝对URL: `https://example.com/image.jpg`
- 相对URL: `/images/product.jpg`
- Base64: `data:image/jpeg;base64,...`
- 对象URL: `{url: '...', alt: '...'}`

## 错误处理

1. **图片加载失败**: 显示fallback图片或默认占位符
2. **无效URL**: 显示placeholder或空状态
3. **网络错误**: 提供重试机制

## 性能优化

- **懒加载**: 只在图片进入视口时加载
- **尺寸优化**: 根据显示尺寸请求合适的图片
- **缓存控制**: 浏览器缓存优化
- **占位符**: 快速显示占位内容