<template>
  <div :class="containerClass" @click="handlePreview">
    <!-- 加载状态 -->
    <div v-if="loading && !error" class="flex items-center justify-center bg-gray-100">
      <div class="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
    </div>
    
    <!-- 图片显示 -->
    <img
      v-else-if="isValidImage && !error"
      :src="optimizedUrl"
      :alt="imageData?.alt || ''"
      :title="imageData?.title || imageData?.alt || ''"
      :style="imageStyle"
      class="object-cover"
      @load="handleLoad"
      @error="handleError"
      :loading="realConfig.lazy ? 'lazy' : 'eager'"
    />
    
    <!-- 错误状态 -->
    <img
      v-else
      :src="fallbackUrl"
      :alt="imageData?.alt || '加载失败'"
      :style="imageStyle"
      class="object-cover opacity-60"
    />
  </div>
</template>

<script setup lang="ts">
import { useImageRenderer } from './modern'

interface Props {
  value: string | { url: string; alt?: string; title?: string }
  config?: any
  configId?: string
  row?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
})

const {
  imageData,
  optimizedUrl,
  fallbackUrl,
  imageStyle,
  containerClass,
  isValidImage,
  loading,
  error,
  handleLoad,
  handleError,
  handlePreview,
  realConfig,
} = useImageRenderer(props)
</script>

<style scoped>
.image-container {
  @apply inline-block overflow-hidden border border-gray-200;
}

.image-container:hover {
  @apply border-gray-300;
}

.image-container img {
  @apply transition-all duration-200;
}
</style>