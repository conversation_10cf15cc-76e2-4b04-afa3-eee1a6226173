import { computed } from 'vue'
import {
  definePlugin,
  defineRenderer,
  defineConfigSchema,
} from '../../core/DevUtils'
import type { InferConfigType } from '../../core/injection'
import { getRealConfig } from '../../core/ConfigStore'
import CurrencyRenderer from './renderer.vue'

/**
 * 货币配置Schema
 */
export const CurrencyConfigSchema = defineConfigSchema({
  symbol: {
    type: 'string',
    description: '货币符号',
    default: '¥',
  },
  precision: {
    type: 'number',
    description: '小数位数',
    default: 2,
    minimum: 0,
    maximum: 4,
  },
  showSymbol: {
    type: 'boolean',
    description: '是否显示货币符号',
    default: true,
  },
  symbolPosition: {
    type: 'string',
    description: '符号位置',
    enum: ['before', 'after'],
    default: 'before',
  },
  thousandsSeparator: {
    type: 'boolean',
    description: '是否显示千分位分隔符',
    default: true,
  },
  locale: {
    type: 'string',
    description: '本地化设置',
    default: 'zh-CN',
  },
  colorCoding: {
    type: 'boolean',
    description: '是否启用颜色编码(负数红色)',
    default: true,
  },
  zeroDisplay: {
    type: 'string',
    description: '零值显示方式',
    enum: ['normal', 'dash', 'empty'],
    default: 'normal',
  },
})

/**
 * 货币配置类型
 */
export type CurrencyConfig = InferConfigType<typeof CurrencyConfigSchema>

/**
 * 货币列配置
 */
export interface CurrencyColumnConfig extends CurrencyConfig {
  /** 列宽 */
  width?: number
  /** 是否固定 */
  fixed?: 'left' | 'right'
  /** 允许扩展属性 */
  [key: string]: unknown
}

/**
 * 货币渲染器定义
 */
export const CurrencyRendererDefinition = defineRenderer({
  name: 'currency',
  component: CurrencyRenderer,
  description: '货币渲染器，支持多种货币格式和本地化',
  defaultWidth: 120,
  defaultConfig: {
    symbol: '¥',
    precision: 2,
    showSymbol: true,
    symbolPosition: 'before',
    thousandsSeparator: true,
    locale: 'zh-CN',
    colorCoding: true,
    zeroDisplay: 'normal',
  },
  props: [
    {
      name: 'value',
      type: ['number', 'string'],
      required: true,
      description: '货币值',
    },
    {
      name: 'config',
      type: 'object',
      required: true,
      description: '货币配置',
    },
    {
      name: 'row',
      type: 'object',
      description: '行数据',
    },
  ],
  validator: (config: CurrencyConfig) => {
    const errors: string[] = []
    const warnings: string[] = []

    if (config.precision < 0 || config.precision > 4) {
      errors.push('precision must be between 0 and 4')
    }

    if (!['before', 'after'].includes(config.symbolPosition)) {
      warnings.push(
        `Unknown symbolPosition '${config.symbolPosition}', will fall back to 'before'`
      )
    }

    if (!['normal', 'dash', 'empty'].includes(config.zeroDisplay)) {
      warnings.push(
        `Unknown zeroDisplay '${config.zeroDisplay}', will fall back to 'normal'`
      )
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  },
})

/**
 * 货币值处理器
 */
export class CurrencyValueProcessor {
  /**
   * 格式化货币值
   */
  static format(value: number | string, config: CurrencyConfig): string {
    const numValue = typeof value === 'string' ? parseFloat(value) : value

    if (isNaN(numValue)) {
      return '--'
    }

    // 处理零值显示
    if (numValue === 0) {
      switch (config.zeroDisplay) {
        case 'dash':
          return '--'
        case 'empty':
          return ''
        default:
          break
      }
    }

    // 格式化数字
    let formatted = numValue.toFixed(config.precision)

    // 添加千分位分隔符
    if (config.thousandsSeparator) {
      const parts = formatted.split('.')
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      formatted = parts.join('.')
    }

    // 添加货币符号
    if (config.showSymbol) {
      if (config.symbolPosition === 'before') {
        formatted = `${config.symbol}${formatted}`
      } else {
        formatted = `${formatted}${config.symbol}`
      }
    }

    return formatted
  }

  /**
   * 解析货币字符串为数值
   */
  static parse(value: string, config: CurrencyConfig): number {
    if (!value || typeof value !== 'string') {
      return 0
    }

    // 移除货币符号和千分位分隔符
    let cleaned = value.replace(new RegExp(`\\${config.symbol}`, 'g'), '')
    cleaned = cleaned.replace(/,/g, '')
    cleaned = cleaned.trim()

    return parseFloat(cleaned) || 0
  }

  /**
   * 验证货币值
   */
  static validate(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return true // 空值允许
    }

    const numValue = typeof value === 'string' ? parseFloat(value) : value
    return !isNaN(numValue) && isFinite(numValue)
  }
}

/**
 * 货币格式化工具
 */
export class CurrencyFormatter {
  private config: CurrencyConfig

  constructor(config: CurrencyConfig) {
    this.config = config
  }

  /**
   * 格式化为显示文本
   */
  format(value: number | string): string {
    return CurrencyValueProcessor.format(value, this.config)
  }

  /**
   * 格式化为本地化货币
   */
  formatLocalized(value: number | string): string {
    const numValue = typeof value === 'string' ? parseFloat(value) : value

    if (isNaN(numValue)) {
      return '--'
    }

    try {
      return new Intl.NumberFormat(this.config.locale, {
        style: 'currency',
        currency: this.getCurrencyCode(),
        minimumFractionDigits: this.config.precision,
        maximumFractionDigits: this.config.precision,
      }).format(numValue)
    } catch (error) {
      // 回退到简单格式化
      return this.format(value)
    }
  }

  /**
   * 获取货币代码
   */
  private getCurrencyCode(): string {
    const symbolToCurrency: Record<string, string> = {
      '¥': 'CNY',
      '$': 'USD',
      '€': 'EUR',
      '£': 'GBP',
      '₹': 'INR',
      '¢': 'USD',
    }

    return symbolToCurrency[this.config.symbol] || 'CNY'
  }
}

/**
 * 货币渲染器组合式API
 */
export function useCurrencyRenderer(props?: any) {
  if (props) {
    // 获取真实配置
    const realConfig = computed(() => {
      if (props.configId) {
        const storedConfig = getRealConfig(props.configId)
        if (storedConfig) {
          return storedConfig
        }
      }
      return {
        ...CurrencyRendererDefinition.defaultConfig,
        ...props.config,
      }
    })

    // 格式化后的值
    const formattedValue = computed(() => {
      return CurrencyValueProcessor.format(props.value, realConfig.value)
    })

    // 数值样式类
    const valueClass = computed(() => {
      const classes = ['currency-value']
      
      if (realConfig.value.colorCoding) {
        const numValue = typeof props.value === 'string' ? parseFloat(props.value) : props.value
        if (numValue < 0) {
          classes.push('negative')
        } else if (numValue > 0) {
          classes.push('positive')
        }
      }

      return classes.join(' ')
    })

    // 是否有效值
    const isValid = computed(() => {
      return CurrencyValueProcessor.validate(props.value)
    })

    return {
      formattedValue,
      valueClass,
      isValid,
      realConfig,
    }
  }

  // 工具函数API
  return {
    /**
     * 创建货币列配置
     */
    createCurrencyColumn: (
      field: string,
      title: string,
      symbol: string,
      config?: Partial<CurrencyConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 120,
        plugin: 'currency',
        pluginConfig: {
          ...CurrencyRendererDefinition.defaultConfig,
          symbol,
          ...config,
        },
      }
    },

    /**
     * 验证配置
     */
    validateConfig: CurrencyRendererDefinition.validator,

    /**
     * 获取默认配置
     */
    getDefaultConfig: () => ({ ...CurrencyRendererDefinition.defaultConfig }),

    /**
     * 创建格式化器
     */
    createFormatter: (config: CurrencyConfig) => new CurrencyFormatter(config),
  }
}

/**
 * 预定义货币配置
 */
export const CommonCurrencyConfigs = {
  /**
   * 人民币配置
   */
  CNY: {
    symbol: '¥',
    precision: 2,
    locale: 'zh-CN',
    thousandsSeparator: true,
  } as CurrencyConfig,

  /**
   * 美元配置
   */
  USD: {
    symbol: '$',
    precision: 2,
    locale: 'en-US',
    thousandsSeparator: true,
  } as CurrencyConfig,

  /**
   * 欧元配置
   */
  EUR: {
    symbol: '€',
    precision: 2,
    locale: 'de-DE',
    thousandsSeparator: true,
    symbolPosition: 'after',
  } as CurrencyConfig,

  /**
   * 英镑配置
   */
  GBP: {
    symbol: '£',
    precision: 2,
    locale: 'en-GB',
    thousandsSeparator: true,
  } as CurrencyConfig,

  /**
   * 日元配置(无小数位)
   */
  JPY: {
    symbol: '¥',
    precision: 0,
    locale: 'ja-JP',
    thousandsSeparator: true,
  } as CurrencyConfig,
}

/**
 * 货币插件
 */
export const ModernCurrencyPlugin = definePlugin({
  name: 'currency-modern',
  version: '2.0.0',
  description: '现代化货币渲染器插件，支持多种货币格式和本地化',

  provides: [
    {
      token: 'CurrencyRenderer',
      provider: {
        value: CurrencyRendererDefinition,
        singleton: true,
      },
    },
  ],

  // 注册 ColumnHelper 方法
  columnHelperMethods: [
    {
      name: 'currency',
      implementation: function (
        field: string,
        title: string,
        symbol: string,
        config: CurrencyColumnConfig = {}
      ) {
        return (this as any).createPluginColumn(field, title, 'currency', {
          ...config,
          symbol,
        })
      },
      rendererConfig: {
        component: 'CurrencyRenderer',
        defaultWidth: 120,
      },
      description: '货币列',
      priority: 5,
    },
  ],

  setup: async (context) => {
    // 获取渲染器注册表
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    if (!rendererRegistry) {
      throw new Error('RendererRegistry service not found')
    }

    // 注册货币渲染器
    rendererRegistry.set('currency', CurrencyRendererDefinition)

    // 注册预定义货币变体
    Object.entries(CommonCurrencyConfigs).forEach(([name, config]) => {
      const variantName = `currency-${name.toLowerCase()}`
      const variantDefinition = {
        ...CurrencyRendererDefinition,
        name: variantName,
        defaultConfig: {
          ...CurrencyRendererDefinition.defaultConfig,
          ...config,
        },
      }
      rendererRegistry.set(variantName, variantDefinition)
    })

    // 注册Vue组件
    context.utils.registerComponent('CurrencyRenderer', CurrencyRenderer)

    context.utils.logger.info(
      'Modern currency plugin initialized with variants:',
      Object.keys(CommonCurrencyConfigs).map(name => `currency-${name.toLowerCase()}`)
    )
  },

  teardown: async (context) => {
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.delete('currency')
      Object.keys(CommonCurrencyConfigs).forEach(name => {
        rendererRegistry.delete(`currency-${name.toLowerCase()}`)
      })
    }

    context.utils.logger.info('Modern currency plugin cleaned up')
  },
})

// 重新导出兼容性类型
export const CurrencyPlugin = ModernCurrencyPlugin
export { CurrencyRenderer }
