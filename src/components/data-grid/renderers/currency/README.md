# Currency Renderer Plugin

货币渲染器插件，用于格式化和显示货币值。

## 功能特性

- ✅ 多货币支持 (CNY, USD, EUR, JPY等)
- ✅ 自定义精度控制
- ✅ 货币符号显示/隐藏
- ✅ 零值处理选项
- ✅ 本地化格式支持
- ✅ TypeScript 完整支持

## 基础用法

```typescript
import { useCurrencyRenderer } from '@/components/data-grid/plugins'

// 在列配置中使用
const columns = [
  { field: 'price', title: '价格', ...column.currency('price', '价格') }
]
```

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| currency | string | 'CNY' | 货币类型 |
| precision | number | 2 | 小数位数 |
| showSymbol | boolean | true | 显示货币符号 |
| showZero | boolean | false | 显示零值 |
| locale | string | 'zh-CN' | 本地化设置 |

## 高级用法

```typescript
// 自定义配置
{ 
  ...column.currency('amount', '金额', {
    currency: 'USD',
    precision: 2,
    showSymbol: true,
    showZero: false,
    locale: 'en-US'
  })
}
```

## 支持的货币

- CNY (人民币)
- USD (美元)
- EUR (欧元)
- JPY (日元)
- GBP (英镑)
- 更多货币类型...

## 示例效果

| 原始值 | 显示效果 |
|-------|---------|
| 1234.56 | ¥1,234.56 |
| 0 | - (showZero=false) |
| null | - |
| 999999.99 | ¥999,999.99 |