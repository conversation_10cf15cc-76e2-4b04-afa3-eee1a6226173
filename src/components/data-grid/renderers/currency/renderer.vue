<template>
  <span :class="valueClass" :title="formattedValue">
    {{ isValid ? formattedValue : '--' }}
  </span>
</template>

<script setup lang="ts">
import { useCurrencyRenderer } from './modern'

interface Props {
  value: number | string
  config?: any
  configId?: string
  row?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
})

const { formattedValue, valueClass, isValid } = useCurrencyRenderer(props)
</script>

<style scoped>
.currency-value {
  @apply font-mono text-right;
}

.currency-value.negative {
  @apply text-red-600;
}

.currency-value.positive {
  @apply text-green-600;
}

.currency-value:hover {
  @apply bg-gray-50 px-1 rounded;
}
</style>