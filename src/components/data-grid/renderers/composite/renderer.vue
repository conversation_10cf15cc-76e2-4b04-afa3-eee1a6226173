<template>
  <TooltipProvider>
    <div
      ref="containerRef"
      :class="containerClasses"
      @mouseenter="isHovered = true"
      @mouseleave="handleMouseLeave"
    >
      <!-- 图标区域 -->
      <div v-if="iconConfig" class="flex-shrink-0">
        <!-- 头像 -->
        <Avatar
          v-if="iconConfig.type === 'avatar'"
          :class="iconConfig.sizeClass"
        >
          <AvatarImage
            v-if="avatarInfo?.image"
            :src="avatarInfo.image"
            :alt="avatarInfo.name"
          />
          <AvatarFallback>{{ avatarInfo?.initials }}</AvatarFallback>
        </Avatar>

        <!-- 图片 -->
        <img
          v-else-if="iconConfig.type === 'image' && row[iconConfig.imageField]"
          :src="row[iconConfig.imageField]"
          :alt="mainContent"
          :class="cn('object-cover rounded', iconConfig.sizeClass)"
        />

        <!-- 图标 -->
        <Icon
          v-else-if="iconConfig.type === 'icon' && iconConfig.iconName"
          :icon="iconConfig.iconName"
          :class="cn('text-muted-foreground', iconConfig.sizeClass)"
        />
      </div>

      <!-- 内容区域 -->
      <div class="flex-1 min-w-0">
        <!-- 主要内容 -->
        <div
          :class="
            cn('truncate font-medium text-foreground', config.main?.className)
          "
          :style="config.main?.style"
        >
          {{ mainContent }}
        </div>

        <!-- 子内容区域 -->
        <div
          v-if="subContents.items.length > 0"
          :class="[
            'text-sm text-muted-foreground mt-0.5',
            subContents.layout === 'vertical'
              ? 'flex flex-col space-y-0.5'
              : 'flex items-center',
          ]"
        >
          <template
            v-for="(sub, index) in subContents.items"
            :key="`sub-${index}-${sub.field}`"
          >
            <span :class="cn('truncate overflow-hidden', sub.className)">
              {{ sub.content }}
            </span>
            <span
              v-if="
                subContents.layout === 'horizontal' &&
                index < subContents.items.length - 1
              "
              class="text-muted-foreground/60 mx-1"
            >
              {{ subContents.separator }}
            </span>
          </template>
        </div>
      </div>

      <!-- 操作按钮区域 -->
      <div v-if="actions.shouldRender" :class="actionsClasses">
        <!-- 直接显示的操作按钮 -->
        <template
          v-for="action in actions.direct"
          :key="`action-${action.key || action.icon}`"
        >
          <Tooltip>
            <TooltipTrigger as-child>
              <Button
                :variant="action.variant || 'outline'"
                :size="action.size || 'sm'"
                class="h-6 w-6 p-0 hover:scale-110 transition-transform"
                @click.stop="handleActionClick(action)"
              >
                <Icon :icon="action.icon" class="h-3 w-3" />
                <span class="sr-only">{{ action.tooltip || action.text }}</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent v-if="action.tooltip">
              {{ action.tooltip }}
            </TooltipContent>
          </Tooltip>
        </template>

        <!-- 更多操作下拉菜单 -->
        <DropdownMenu
          v-if="actions.dropdown.length > 0"
          @update:open="handleDropdownToggle"
        >
          <DropdownMenuTrigger as-child>
            <Button
              variant="ghost"
              size="sm"
              class="h-6 w-6 p-0 hover:scale-110 transition-transform"
            >
              <Icon icon="mdi:dots-horizontal" class="h-3 w-3" />
              <span class="sr-only">更多操作</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-40">
            <DropdownMenuItem
              v-for="action in actions.dropdown"
              :key="`dropdown-${action.key || action.icon}`"
              @click.stop="handleActionClick(action)"
              class="cursor-pointer"
            >
              <Icon
                v-if="action.icon"
                :icon="action.icon"
                class="mr-2 h-4 w-4"
              />
              {{ action.text || action.tooltip }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  </TooltipProvider>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { useCompositeRenderer, type CompositeConfig } from './modern'

interface Props {
  value?: any
  row: any
  config?: CompositeConfig
}

const props = defineProps<Props>()

// 悬停状态
const isHovered = ref(false)
const isDropdownOpen = ref(false)
const containerRef = ref<HTMLElement>()

// 使用 composable 获取渲染逻辑
const {
  config,
  mainContent,
  subContents,
  iconConfig,
  avatarInfo,
  actions,
  handleActionClick,
} = useCompositeRenderer(props)

// 样式类
const containerClasses = computed(() => [
  'flex items-center gap-3 p-2 relative',
  actions.value.enableHover && 'transition-colors hover:bg-muted/50',
])

const actionsClasses = computed(() => [
  'absolute right-1 top-1/2 -translate-y-1/2 flex items-center space-x-0.5 z-10',
  'bg-background/95 backdrop-blur-sm rounded-lg px-1.5 py-1 shadow-lg border border-border/50',
  'hover:shadow-xl hover:scale-105 transition-all duration-300 ease-out',
  actions.value.enableHover &&
    !isHovered.value &&
    'opacity-0 translate-x-3 pointer-events-none scale-95',
  actions.value.enableHover &&
    isHovered.value &&
    'opacity-100 translate-x-0 pointer-events-auto',
  !actions.value.enableHover && 'opacity-100 pointer-events-auto',
])

// 事件处理
const handleMouseLeave = () => {
  setTimeout(() => {
    if (!isDropdownOpen.value) {
      isHovered.value = false
    }
  }, 150)
}

const handleDropdownToggle = (open: boolean) => {
  isDropdownOpen.value = open
  if (open) {
    isHovered.value = true
  } else {
    setTimeout(() => {
      isHovered.value = false
    }, 100)
  }
}
</script>
