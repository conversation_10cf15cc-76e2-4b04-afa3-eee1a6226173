import { definePlugin, defineRenderer } from '../../core/DevUtils'
import { computed } from 'vue'
import type { RowData } from '../../core/types'
import CompositeRenderer from './renderer.vue'

// 常量定义
const DEFAULT_MAIN_FIELD = 'name'
const DEFAULT_ICON_SIZE = 32 // 对应 w-8 h-8 (8 * 4px = 32px)
const TAILWIND_UNIT_SIZE = 4 // Tailwind CSS 中 1 个单位 = 4px

/**
 * 获取 Tailwind CSS 尺寸类名
 */
const getTailwindSizeClass = (size: number): string => {
  const units = Math.floor(size / TAILWIND_UNIT_SIZE)
  return `w-${units} h-${units}`
}

/**
 * 模板替换工具函数
 */
const replaceTemplate = (
  template: string,
  value: any,
  row: RowData
): string => {
  return template.replace(/\$\{(\w+)\}/g, (match, key) => {
    if (key === 'value') return String(value || '')
    return String(row[key] || match)
  })
}

/**
 * 子内容项配置 - 简化版
 */
export type SubContentItem = {
  field: string
  condition?: (row: RowData) => boolean
  className?: string
} & (
  | {
      formatter: (value: any, row: RowData) => string
      template?: never
    }
  | {
      formatter?: never
      template: string
    }
  | {
      formatter?: never
      template?: never
    }
)

/**
 * 图标配置基础接口
 */
interface BaseIconConfig {
  size?: number
}

/**
 * 图标配置 - 联合类型定义
 */
export type IconConfig =
  | (BaseIconConfig & {
      type: 'icon'
      iconName: `mdi:${string}` | `lucide:${string}`
    })
  | (BaseIconConfig & {
      type: 'image'
      imageField: string
    })
  | (BaseIconConfig & {
      type: 'avatar'
      avatarField: string
      nameField?: string
    })

/**
 * 按钮变体类型
 */
export type ButtonVariant =
  | 'default'
  | 'destructive'
  | 'outline'
  | 'secondary'
  | 'ghost'

/**
 * 按钮尺寸类型
 */
export type ButtonSize = 'sm' | 'default' | 'lg'

/**
 * 按钮颜色主题
 */
export type ButtonColor =
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'info'

/**
 * 操作按钮配置
 */
export interface ActionButton {
  /** 可选的唯一标识符 */
  key?: string
  /** 图标名称 */
  icon: `mdi:${string}` | `lucide:${string}`
  /** 按钮文本 */
  text?: string
  /** 提示文本 */
  tooltip?: string
  /** 按钮变体 */
  variant?: ButtonVariant
  /** 按钮尺寸 */
  size?: ButtonSize
  /** 颜色主题 */
  color?: ButtonColor
  /** 显示条件 */
  condition?: (row: RowData) => boolean
  /** 确认消息 */
  confirmMessage?: string | ((row: RowData) => string)
  /** 点击事件处理器 */
  onClick: (row: RowData) => void | Promise<void>
}

/**
 * 主内容配置
 */
export interface MainContentConfig {
  field?: string
  formatter?: (value: any, row: RowData) => string
  className?: string
  style?: Record<string, any>
}

/**
 * 子内容配置
 */
export interface SubContentConfig {
  items: SubContentItem[]
  layout?: 'vertical' | 'horizontal'
  separator?: string
}

/**
 * 复合渲染器配置 - 模块化定义
 */
export interface CompositeConfig {
  /** 主内容配置 */
  main?: MainContentConfig
  /** 子内容配置 */
  subs?: SubContentConfig
  /** 图标配置 */
  icon?: IconConfig
  /** 操作按钮配置 */
  actions?: ActionButton[]
  /** 显示操作按钮数量，超出的自动收入下拉菜单 */
  showActionsCount?: number
  /** 是否启用 hover 显示操作 */
  enableHover?: boolean
  /** 基础宽度（仅用于渲染器默认配置） */
  width?: number
}

/**
 * 创建复合列配置的工厂函数
 */
export function createCompositeConfig(
  config: Partial<CompositeConfig>
): CompositeConfig {
  return {
    ...DEFAULT_CONFIG,
    ...config,
    subs: config.subs
      ? {
          ...DEFAULT_CONFIG.subs,
          ...config.subs,
        }
      : DEFAULT_CONFIG.subs,
  }
}

/**
 * 创建复合列配置的工厂函数
 */
export function createCompositeColumnConfig(
  config: Partial<CompositeColumnConfig>
): CompositeColumnConfig {
  const baseConfig = createCompositeConfig(config)
  return {
    ...baseConfig,
    ...config,
  }
}

// 默认配置 - 类型安全的默认值
const DEFAULT_CONFIG: Required<
  Pick<CompositeConfig, 'showActionsCount' | 'enableHover'>
> &
  Pick<CompositeConfig, 'subs'> = {
  showActionsCount: 2,
  enableHover: true,
  subs: { items: [], layout: 'vertical', separator: ' | ' },
}

/**
 * 复合渲染器定义
 */
export const CompositeRendererDefinition = defineRenderer({
  name: 'composite',
  component: CompositeRenderer,
  description: '复合渲染器 - 支持图标、主内容、子内容和操作按钮的灵活组合',
  defaultWidth: 200,
  defaultConfig: DEFAULT_CONFIG,
  props: [
    { name: 'value', type: 'any', description: '单元格值' },
    { name: 'row', type: 'object', required: true, description: '行数据' },
    {
      name: 'config',
      type: 'object',
      required: true,
      description: '复合渲染器配置',
    },
  ],
})

/**
 * 复合插件
 */
export const CompositePlugin = definePlugin({
  name: 'composite',
  version: '2.0.0',
  description: '复合渲染器插件 - 支持图标、内容和操作的组合显示',
  provides: [
    {
      token: 'CompositeRenderer',
      provider: { value: CompositeRendererDefinition, singleton: true },
    },
  ],
  // 注册 PluginHelper 方法
  columnHelperMethods: [
    {
      name: 'composite',
      implementation: function (
        field: string,
        title: string,
        config: CompositeColumnConfig
      ) {
        return (this as any).createPluginColumn(
          field,
          title,
          'composite',
          config
        )
      },
      rendererConfig: {
        component: 'CompositeRenderer',
        defaultWidth: 200,
      },
      description: '复合渲染器 - 支持图标、内容和操作的组合显示',
      priority: 1,
    },
  ],
  setup: async (context) => {
    const rendererRegistry =
      context.container.resolveService('RendererRegistry')
    if (!rendererRegistry) throw new Error('RendererRegistry service not found')

    // 只注册核心渲染器
    rendererRegistry.set('composite', CompositeRendererDefinition)

    context.utils.registerComponent('CompositeRenderer', CompositeRenderer)
    context.utils.logger.info('[CompositePlugin] 插件初始化完成')
  },
  teardown: async (context) => {
    const rendererRegistry =
      context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.delete('composite')
    }
    context.utils.logger.info('[CompositePlugin] 插件清理完成')
  },
})

/**
 * 复合渲染器 Props 类型
 */
interface CompositeRendererProps {
  value: any
  row: RowData
  config: CompositeConfig
}

/**
 * 复合渲染器组合式API
 * 提供复合组件的响应式数据处理和事件处理逻辑
 */
export function useCompositeRenderer(props: CompositeRendererProps) {
  // 合并默认配置
  const config = computed(() => ({
    ...DEFAULT_CONFIG,
    ...props.config,
  }))

  // 主内容处理逻辑
  const mainContent = computed(() => {
    const mainConfig = config.value.main
    if (!mainConfig) return String(props.value || '')

    const field = mainConfig.field || DEFAULT_MAIN_FIELD
    const value = props.row[field] || props.value

    if (mainConfig.formatter) {
      try {
        return mainConfig.formatter(value, props.row)
      } catch (error) {
        console.warn('Formatter error:', error)
        return String(value || '')
      }
    }

    return String(value || '')
  })

  // 子内容处理逻辑
  const subContents = computed(() => {
    const subs = config.value.subs
    if (!subs?.items?.length) {
      return { items: [], layout: 'vertical' as const, separator: ' | ' }
    }

    const processedItems = subs.items
      .filter((item) => {
        if (!item.condition) return true
        try {
          return item.condition(props.row)
        } catch {
          return false
        }
      })
      .map((item) => {
        const value = props.row[item.field]
        let displayValue = value

        if (item.formatter) {
          try {
            displayValue = item.formatter(value, props.row)
          } catch (error) {
            console.warn('Sub formatter error:', error)
          }
        } else if (item.template) {
          displayValue = replaceTemplate(item.template, value, props.row)
        }

        return {
          ...item,
          content: String(displayValue || ''),
          value: displayValue,
        }
      })

    return {
      items: processedItems,
      layout: subs.layout || ('vertical' as const),
      separator: subs.separator || ' | ',
    }
  })

  // 图标配置处理
  const iconConfig = computed(() => {
    const icon = config.value.icon
    if (!icon) return null

    const size = icon.size || DEFAULT_ICON_SIZE
    return {
      ...icon,
      size,
      sizeClass: getTailwindSizeClass(size),
    }
  })

  // 头像信息处理
  const avatarInfo = computed(() => {
    const icon = iconConfig.value
    if (!icon || icon.type !== 'avatar') return null

    const name = String(props.row[icon.nameField || DEFAULT_MAIN_FIELD] || '')
    const image = props.row[icon.avatarField]
    const initials = name
      .split(' ')
      .map((word: string) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)

    return { name, image, initials }
  })

  // 操作按钮处理逻辑
  const actions = computed(() => {
    const allActions = config.value.actions || []
    const showCount = config.value.showActionsCount || 2

    // 过滤满足条件的按钮
    const filteredActions = allActions.filter((action) => {
      if (!action.condition) return true
      try {
        return action.condition(props.row)
      } catch (error) {
        console.warn('Action condition evaluation failed:', error)
        return false
      }
    })

    return {
      direct: filteredActions.slice(0, showCount),
      dropdown: filteredActions.slice(showCount),
      enableHover: config.value.enableHover !== false,
      shouldRender: filteredActions.length > 0,
    }
  })

  // 操作按钮点击处理
  const handleActionClick = async (action: ActionButton) => {
    try {
      // 处理确认消息
      if (action.confirmMessage) {
        const message =
          typeof action.confirmMessage === 'function'
            ? action.confirmMessage(props.row)
            : action.confirmMessage

        if (!confirm(message)) {
          return // 用户取消操作
        }
      }

      if (action.onClick && typeof action.onClick === 'function') {
        await action.onClick(props.row)
      }
    } catch (error) {
      console.error('Action execution failed:', error)
    }
  }

  return {
    config,
    mainContent,
    subContents,
    iconConfig,
    avatarInfo,
    actions,
    handleActionClick,
  }
}

/**
 * 列级别专用配置
 */
export interface ColumnSpecificConfig {
  /** 交互式配置 */
  interactive?: {
    hover?: boolean
    clickable?: boolean
    selectable?: boolean
  }
  /** 列宽 */
  width?: number
  /** 是否固定 */
  fixed?: 'left' | 'right'
  /** 允许扩展属性 */
  [key: string]: unknown
}

/**
 * 复合列配置 - 继承渲染器配置并扩展列级别属性
 */
export interface CompositeColumnConfig
  extends CompositeConfig,
    ColumnSpecificConfig {}
// TYPES_FILE_END

// 导出组件
export { CompositeRenderer }
