# Composite Renderer Plugin

复合列渲染器插件，用于在数据网格中创建具有多种内容组合的复杂列显示。

## 目录结构

```
composite/
├── index.ts          # 插件入口文件，提供 composable API 和插件类
├── renderer.vue      # Vue 组件，处理渲染逻辑
└── README.md         # 插件文档（当前文件）
```

## 功能特性

- 🎨 **多内容组合**: 支持主内容、子内容、图标和操作按钮的组合显示
- 🖼️ **图标支持**: 支持普通图标、图片和头像三种图标类型
- 🎭 **布局模式**: 支持水平和垂直两种布局方式
- 🎯 **操作按钮**: 支持直接显示的操作按钮和下拉菜单更多操作
- 🖱️ **悬停效果**: 智能的悬停显示/隐藏操作按钮
- 📱 **响应式**: 完全响应式设计，支持动态配置
- 🎛️ **高度可配置**: 支持条件显示、自定义样式和格式化函数

## 基本用法

### 1. 安装插件

```typescript
import { CompositePlugin } from '@/components/data-grid/plugins/renderers/composite'
import { PluginManager } from '@/components/data-grid/plugins'

// 注册插件
const manager = new PluginManager()
manager.register(new CompositePlugin())
```

### 2. 使用 composable API

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { useCompositeRenderer } from '@/components/data-grid/plugins/renderers/composite'

const isHovered = ref(false)
const isDropdownOpen = ref(false)

const {
  mainContent,
  subContents,
  iconConfig,
  visibleActions
} = useCompositeRenderer(
  {
    value: 'John Doe',
    row: { name: 'John Doe', email: '<EMAIL>' },
    config: {
      main: { field: 'name' },
      subs: [{ field: 'email' }]
    }
  },
  { isHovered, isDropdownOpen }
)
</script>
```

### 3. 直接使用组件

```vue
<template>
  <CompositeRenderer
    :value="'John Doe'"
    :row="{ name: 'John Doe', email: '<EMAIL>' }"
    :config="compositeConfig"
  />
</template>

<script setup lang="ts">
import { CompositeRenderer } from '@/components/data-grid/plugins/renderers/composite'
import type { CompositeColumnConfig } from '@/components/data-grid/plugins/renderers/composite'

const compositeConfig: CompositeColumnConfig = {
  main: { field: 'name' },
  subs: [{ field: 'email' }],
  icon: { type: 'avatar', avatarField: 'name' },
  actions: [
    { icon: 'edit', tooltip: '编辑', onClick: (row) => console.log('编辑', row) }
  ]
}
</script>
```

## 配置选项

### CompositeColumnConfig

```typescript
interface CompositeColumnConfig {
  // 主要内容配置
  main?: {
    field?: string                    // 数据字段名
    formatter?: (value: any, row: any) => string  // 格式化函数
    className?: string                // CSS 类名
    style?: Record<string, any>       // 内联样式
  }

  // 子内容配置
  subs?: Array<{
    field: string                     // 数据字段名
    formatter?: (value: any, row: any) => string  // 格式化函数
    template?: string                 // 模板字符串，支持 ${value} 占位符
    condition?: (row: any) => boolean // 显示条件
    className?: string                // CSS 类名
    style?: Record<string, any>       // 内联样式
  }>

  // 图标配置
  icon?: {
    type: 'icon' | 'image' | 'avatar' // 图标类型
    size?: number                     // 图标大小（像素）
    position?: 'left' | 'right'       // 图标位置
    
    // 普通图标
    iconName?: string                 // 图标名称
    
    // 图片类型
    imageField?: string               // 图片 URL 字段
    
    // 头像类型
    avatarField?: string              // 头像文本字段
    imageField?: string               // 头像图片字段
  }

  // 操作按钮配置
  actions?: Array<{
    icon: string                      // 按钮图标
    text?: string                     // 按钮文本
    tooltip?: string                  // 提示文本
    variant?: 'outline' | 'default' | 'ghost'  // 按钮样式
    size?: 'sm' | 'md' | 'lg'         // 按钮大小
    condition?: (row: any) => boolean // 显示条件
    onClick: (row: any) => void       // 点击处理函数
  }>

  // 布局配置
  layout?: 'horizontal' | 'vertical'  // 布局方向
  showActionsCount?: number           // 直接显示的操作按钮数量（默认2）
  enableHover?: boolean               // 是否启用悬停效果（默认true）
  
  // 交互配置
  interactive?: {
    hover?: boolean                   // 悬停效果
  }
}
```

## 使用示例

### 示例 1: 用户信息卡片

```typescript
const userConfig: CompositeColumnConfig = {
  main: {
    field: 'name',
    formatter: (value, row) => `${value} (${row.title})`
  },
  subs: [
    { 
      field: 'email',
      template: '📧 ${value}'
    },
    { 
      field: 'phone',
      template: '📱 ${value}',
      condition: (row) => !!row.phone
    }
  ],
  icon: {
    type: 'avatar',
    avatarField: 'name',
    imageField: 'avatar',
    size: 40
  },
  actions: [
    {
      icon: 'mdi:account-edit',
      tooltip: '编辑用户',
      onClick: (row) => editUser(row.id)
    },
    {
      icon: 'mdi:email',
      tooltip: '发送邮件',
      onClick: (row) => sendEmail(row.email)
    },
    {
      icon: 'mdi:delete',
      tooltip: '删除用户',
      variant: 'outline',
      onClick: (row) => deleteUser(row.id)
    }
  ],
  layout: 'horizontal',
  showActionsCount: 2
}
```

### 示例 2: 产品信息显示

```typescript
const productConfig: CompositeColumnConfig = {
  main: {
    field: 'name',
    className: 'font-semibold text-gray-900'
  },
  subs: [
    { 
      field: 'sku',
      template: 'SKU: ${value}',
      className: 'text-gray-600'
    },
    { 
      field: 'price',
      formatter: (value) => `¥${value.toFixed(2)}`,
      className: 'text-green-600 font-medium'
    },
    {
      field: 'stock',
      formatter: (value) => `库存: ${value}`,
      condition: (row) => row.stock < 10,
      className: 'text-red-500'
    }
  ],
  icon: {
    type: 'image',
    imageField: 'thumbnail',
    size: 48
  },
  actions: [
    {
      icon: 'mdi:eye',
      tooltip: '查看详情',
      onClick: (row) => viewProduct(row.id)
    },
    {
      icon: 'mdi:cart-plus',
      tooltip: '添加到购物车',
      onClick: (row) => addToCart(row.id)
    }
  ]
}
```

### 示例 3: 订单状态显示

```typescript
const orderConfig: CompositeColumnConfig = {
  main: {
    field: 'orderNumber',
    formatter: (value) => `订单 #${value}`
  },
  subs: [
    { 
      field: 'customerName',
      template: '客户: ${value}'
    },
    { 
      field: 'amount',
      formatter: (value) => `金额: ¥${value.toFixed(2)}`
    },
    {
      field: 'statusText',
      className: 'px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800'
    }
  ],
  icon: {
    type: 'icon',
    iconName: 'mdi:package',
    size: 32
  },
  actions: [
    {
      icon: 'mdi:eye',
      tooltip: '查看订单',
      onClick: (row) => viewOrder(row.id)
    },
    {
      icon: 'mdi:truck',
      tooltip: '跟踪物流',
      condition: (row) => row.status === 'shipped',
      onClick: (row) => trackShipment(row.id)
    },
    {
      icon: 'mdi:cancel',
      tooltip: '取消订单',
      condition: (row) => row.status === 'pending',
      onClick: (row) => cancelOrder(row.id)
    }
  ],
  enableHover: true,
  showActionsCount: 1
}
```

## API 参考

### useCompositeRenderer

composable 函数，提供复合列渲染所需的响应式数据和计算属性。

```typescript
function useCompositeRenderer(
  props: {
    value?: any
    row: any
    config?: CompositeColumnConfig
    configId?: string
  },
  refs: {
    isHovered: Ref<boolean>
    isDropdownOpen: Ref<boolean>
  }
): {
  realConfig: ComputedRef<CompositeColumnConfig>
  getNestedValue: (obj: any, path: string) => any
  visibleActions: ComputedRef<any[]>
  enableHover: ComputedRef<boolean>
  shouldRenderActions: ComputedRef<boolean>
  mainContent: ComputedRef<string>
  subContents: ComputedRef<any[]>
  iconConfig: ComputedRef<any>
  avatarInfo: ComputedRef<any>
  directActions: ComputedRef<any[]>
  dropdownActions: ComputedRef<any[]>
  layoutClasses: ComputedRef<string>
}
```

### CompositePlugin

插件类，用于在插件管理器中注册复合列渲染器。

```typescript
class CompositePlugin implements Plugin {
  name: string = 'composite'
  version: string = '1.0.0'
  description: string
  
  install(manager: PluginManager): void
  uninstall(): void
}
```

### CompositeRenderer

Vue 组件，处理复合列的实际渲染。

```typescript
interface Props {
  value?: any
  row: any
  config?: CompositeColumnConfig
  configId?: string
}
```

## 样式定制

### CSS 类名

复合列渲染器使用以下 CSS 类名，可以通过自定义样式进行定制：

```css
/* 容器类名 */
.composite-column        /* 主容器 */
.composite-content       /* 内容区域 */
.composite-actions-wrapper /* 操作按钮容器 */

/* 内容类名 */
.composite-main          /* 主要内容 */
.composite-sub           /* 子内容 */

/* 图标类名 */
.composite-icon          /* 图标容器 */
.composite-icon-left     /* 左侧图标 */
.composite-icon-right    /* 右侧图标 */

/* 操作按钮类名 */
.composite-actions       /* 操作按钮组 */
.composite-action-btn    /* 单个操作按钮 */
.composite-more-actions  /* 更多操作容器 */
```

### Tailwind CSS 定制

组件大量使用 Tailwind CSS 类名，可以通过修改配置来定制样式：

```typescript
// 在配置中使用自定义类名
const config: CompositeColumnConfig = {
  main: {
    className: 'text-lg font-bold text-blue-600'
  },
  subs: [
    {
      field: 'description',
      className: 'text-sm text-gray-500 italic'
    }
  ]
}
```

## 性能优化

### 动态渲染

组件支持动态渲染优化，只在需要时渲染操作按钮：

```typescript
const config: CompositeColumnConfig = {
  enableHover: true,  // 启用悬停效果
  // 操作按钮只在悬停时渲染，提高性能
}
```

### 条件渲染

使用条件函数避免不必要的渲染：

```typescript
const config: CompositeColumnConfig = {
  subs: [
    {
      field: 'sensitiveData',
      condition: (row) => row.hasPermission,  // 只在有权限时显示
    }
  ],
  actions: [
    {
      icon: 'edit',
      condition: (row) => row.editable,  // 只在可编辑时显示编辑按钮
      onClick: (row) => editRow(row)
    }
  ]
}
```

## 故障排除

### 常见问题

1. **操作按钮不显示**
   - 检查 `actions` 配置是否正确
   - 确认 `condition` 函数返回 `true`
   - 验证 `enableHover` 设置

2. **图标不显示**
   - 确认图标字体已正确加载
   - 检查 `iconName` 是否正确
   - 验证图片 URL 是否有效

3. **样式异常**
   - 确认 Tailwind CSS 已正确配置
   - 检查自定义 `className` 是否冲突
   - 验证容器高度设置

### 调试技巧

1. **开启开发者工具**: 在浏览器中检查组件渲染结果
2. **使用 Vue DevTools**: 查看组件属性和计算属性
3. **控制台日志**: 在配置函数中添加 `console.log` 进行调试

```typescript
const config: CompositeColumnConfig = {
  main: {
    formatter: (value, row) => {
      console.log('Main formatter:', { value, row })  // 调试日志
      return value
    }
  }
}
```

## 最佳实践

1. **性能优化**: 使用条件渲染和懒加载
2. **可访问性**: 为操作按钮提供 `tooltip` 和 `aria-label`
3. **国际化**: 使用格式化函数支持多语言
4. **响应式设计**: 考虑不同屏幕尺寸下的显示效果
5. **错误处理**: 在格式化函数中添加错误处理逻辑

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的复合列渲染功能
- 提供 composable API
- 支持操作按钮和悬停效果

## 许可证

MIT License

---

<!-- TYPES.TS FILE CONTENT - TO BE EXTRACTED -->
/**
 * Type definitions for Composite column renderer
 */

// Import common types from core
import type { RowData } from '../../core/types'

/**
 * 复合列配置
 */
export interface CompositeColumnConfig {
  /** 主要内容配置 */
  main: {
    field?: string
    formatter?: (value: unknown, row: RowData) => string
    style?: Record<string, unknown>
    className?: string
  }
  /** 子内容配置 */
  subs?: {
    /** 子内容项 */
    items?: Array<{
      field: string
      template?: string
      formatter?: (value: unknown, row: RowData) => string
      style?: Record<string, unknown>
      className?: string
      condition?: (row: RowData) => boolean
    }>
    /** 子内容布局方式 */
    layout?: 'horizontal' | 'vertical'
    /** 水平布局时的分隔符 */
    separator?: string
  }
  /** 图标配置 */
  icon?: {
    type: 'icon' | 'image' | 'avatar'
    iconName?: string
    imageField?: string
    avatarField?: string
    size?: number
    position?: 'left' | 'right' | 'top' | 'bottom'
  }
  /** 操作按钮 */
  actions?: Array<{
    icon: string
    text?: string
    tooltip?: string
    variant?:
      | 'default'
      | 'destructive'
      | 'outline'
      | 'secondary'
      | 'ghost'
      | 'link'
    size?: 'default' | 'sm' | 'lg' | 'icon'
    condition?: (row: RowData) => boolean
    onClick: (row: RowData) => void
  }>
  /** 显示操作按钮数量，超出的自动收入下拉菜单 */
  showActionsCount?: number
  /** 是否启用 hover 显示操作 */
  enableHover?: boolean
  /** 交互式配置 */
  interactive?: {
    hover?: boolean
    clickable?: boolean
    selectable?: boolean
  }
  /** 列宽 */
  width?: number
  /** 是否固定 */
  fixed?: 'left' | 'right'
  /** 允许扩展属性 */
  [key: string]: unknown
}
<!-- END TYPES.TS FILE CONTENT -->