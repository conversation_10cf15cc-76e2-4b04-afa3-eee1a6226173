<template>
  <div class="flex items-center justify-center gap-1">
    <!-- 直接显示的操作按钮 -->
    <Button
      v-for="action in directActions"
      :key="action.text || action.icon || action.tooltip"
      :variant="getButtonVariant(action)"
      :size="getButtonSize(action)"
      :title="action.tooltip"
      class="p-1 gap-0"
      @click="handleActionClick(action)"
    >
      <Icon v-if="action.icon" :icon="action.icon" class="w-4 h-4" />
      <span v-if="action.text">{{ action.text }}</span>
    </Button>

    <!-- 更多操作下拉菜单 -->
    <DropdownMenu v-if="hasMoreActions">
      <DropdownMenuTrigger as-child>
        <Button variant="outline" size="sm" class="p-1 gap-0" title="更多操作">
          <Icon icon="mdi:dots-horizontal" class="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          v-for="action in dropdownActions"
          :key="action.text || action.icon || action.tooltip"
          @select="handleActionClick(action)"
        >
          <Icon v-if="action.icon" :icon="action.icon" class="w-4 h-4 mr-2" />
          <span>{{ action.text || action.tooltip || '操作' }}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
// 显式设置组件名称，防止Vue自动生成引起的响应式问题
defineOptions({
  name: 'ActionsRenderer'
})

import { Icon } from '@iconify/vue'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import type { ActionsColumnConfig } from '../../types'
import { useActionsRenderer } from './index'

interface Props {
  value?: any
  row: any
  config?: ActionsColumnConfig
  configId?: string
}

const props = defineProps<Props>()

// 使用 composable API
const {
  visibleActions,
  directActions,
  dropdownActions,
  hasMoreActions,
  getButtonVariant,
  getButtonSize,
  handleActionClick,
} = useActionsRenderer(props)
</script>