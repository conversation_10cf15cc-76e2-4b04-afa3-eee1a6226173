<template>
  <div class="rating-display flex items-center gap-1" :class="containerClasses">
    <!-- 评分图标 -->
    <span class="flex items-center">
      <Icon
        v-for="(item, index) in ratingItems"
        :key="index"
        :icon="item.icon"
        :style="{ color: item.color }"
        :class="iconClasses"
        @click="handleRatingClick(index + 1)"
        @mouseenter="handleRatingHover(index + 1)"
        @mouseleave="handleRatingLeave"
      />
    </span>
    
    <!-- 数值显示 -->
    <span v-if="showValue" class="text-xs text-gray-600 ml-1">
      {{ formattedValue }}
    </span>
  </div>
</template>

<script setup lang="ts">
// 显式设置组件名称，防止Vue自动生成引起的响应式问题
defineOptions({
  name: 'RatingRenderer'
})

import { Icon } from '@iconify/vue'
import type { RatingColumnConfig } from '../../types'
import { useRatingRenderer } from './index'

interface Props {
  value?: number | string
  row: any
  config?: RatingColumnConfig
}

const props = defineProps<Props>()

// 使用 composable API
const {
  score,
  maxRating,
  showValue,
  formattedValue,
  ratingItems,
  containerClasses,
  iconClasses,
  handleRatingClick,
  handleRatingHover,
  handleRatingLeave,
} = useRatingRenderer(props)
</script>

<style scoped>
.rating-display .rating-icon {
  transition: all 0.2s ease-in-out;
}

.rating-display .rating-icon:hover {
  transform: scale(1.1);
}

.rating-display.interactive .rating-icon {
  cursor: pointer;
}

.rating-display.interactive .rating-icon:hover {
  filter: brightness(1.2);
}
</style>