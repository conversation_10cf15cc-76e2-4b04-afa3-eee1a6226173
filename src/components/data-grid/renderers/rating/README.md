# Rating Renderer Plugin

评分列渲染器插件，用于在数据网格中显示交互式星级评分，支持多种显示样式和自定义配置。

## 目录结构

```
rating/
├── index.ts          # 插件入口文件，提供 composable API 和插件类
├── renderer.vue      # Vue 组件，处理渲染逻辑
└── README.md         # 插件文档（当前文件）
```

## 功能特性

- ⭐ **多种样式**: 支持星星、爱心、拇指、圆点四种显示样式
- 🎨 **自定义外观**: 支持自定义颜色、大小、图标和格式化
- 🖱️ **交互功能**: 支持点击评分、悬停预览（可选）
- 🌟 **半分支持**: 支持半星评分显示和交互
- 🎯 **灵活配置**: 支持只读模式、最大评分、数值显示等
- 🔧 **事件回调**: 支持评分变更回调函数
- 📱 **响应式**: 完全响应式设计，支持动态配置
- 🎪 **多变体**: 内置四种渲染器变体，可轻松切换

## 基本用法

### 1. 安装插件

```typescript
import { RatingPlugin } from '@/components/data-grid/plugins/renderers/rating'
import { PluginManager } from '@/components/data-grid/plugins'

// 注册插件
const manager = new PluginManager()
manager.register(new RatingPlugin())
```

### 2. 使用 composable API

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { useRatingRenderer } from '@/components/data-grid/plugins/renderers/rating'

const {
  score,
  maxRating,
  ratingItems,
  handleRatingClick,
  handleRatingHover,
  handleRatingLeave
} = useRatingRenderer({
  value: 4.5,
  row: { id: 1, rating: 4.5, name: 'Product A' },
  config: {
    max: 5,
    allowHalf: true,
    readonly: false,
    color: '#f59e0b'
  }
})
</script>
```

### 3. 直接使用组件

```vue
<template>
  <RatingRenderer
    :value="4.5"
    :row="{ id: 1, rating: 4.5, name: 'Product A' }"
    :config="ratingConfig"
  />
</template>

<script setup lang="ts">
import { RatingRenderer } from '@/components/data-grid/plugins/renderers/rating'
import type { RatingColumnConfig } from '@/components/data-grid/plugins/renderers/rating'

const ratingConfig: RatingColumnConfig = {
  max: 5,
  color: '#f59e0b',
  emptyColor: '#d9d9d9',
  size: 'md',
  variant: 'star',
  allowHalf: true,
  readonly: false,
  showValue: true,
  onChange: (value, row) => {
    console.log('Rating changed:', value, row)
  }
}
</script>
```

### 4. 在数据网格中使用

```vue
<script setup lang="ts">
import { useColumnHelper } from '@/components/data-grid/plugins'

const { rating } = useColumnHelper()

const columns = [
  // 基本评分列
  rating('rating', '评分'),
  
  // 自定义配置的评分列
  rating('userRating', '用户评分', {
    max: 10,
    color: '#10b981',
    size: 'lg',
    showValue: true,
    valueFormatter: (value, max) => `${value}/${max} 分`
  }),
  
  // 爱心样式评分列
  rating('satisfaction', '满意度', {
    variant: 'heart',
    max: 5,
    color: '#ef4444',
    readonly: false,
    onChange: (value, row) => {
      // 处理评分变更
      updateSatisfaction(row.id, value)
    }
  }),
  
  // 拇指评分列
  rating('recommendation', '推荐度', {
    variant: 'thumb',
    max: 3,
    allowHalf: false,
    showValue: false
  })
]
</script>
```

## 配置选项

### RatingColumnConfig

```typescript
interface RatingColumnConfig {
  // 最大评分（默认5）
  max?: number
  
  // 评分颜色（默认#f59e0b）
  color?: string
  
  // 空星颜色（默认#d9d9d9）
  emptyColor?: string
  
  // 是否显示数值（默认true）
  showValue?: boolean
  
  // 数值格式化函数
  valueFormatter?: (value: number, max: number) => string
  
  // 星星大小（默认'md'）
  size?: number | 'sm' | 'md' | 'lg'
  
  // 显示样式
  variant?: 'star' | 'heart' | 'thumb' | 'dot'
  
  // 是否启用半星（默认true）
  allowHalf?: boolean
  
  // 是否只读（默认true）
  readonly?: boolean
  
  // 评分变更回调
  onChange?: (value: number, row: any) => void
  
  // 自定义图标
  icons?: {
    full?: string       // 满图标
    half?: string       // 半图标
    empty?: string      // 空图标
  }
  
  // 列宽
  width?: number
}
```

### 尺寸选项

支持预设尺寸和自定义像素值：

- `'sm'` 或 `12`: 小尺寸 (12px)
- `'md'` 或 `16`: 中等尺寸 (16px) - 默认
- `'lg'` 或 `20`: 大尺寸 (20px)
- 自定义数字: 任意像素值

### 变体样式

内置四种显示变体：

- `'star'`: 星星样式 (默认)
- `'heart'`: 爱心样式
- `'thumb'`: 拇指样式
- `'dot'`: 圆点样式

## 渲染器变体

### 1. 标准评分 (rating)

默认的星星评分样式。

```typescript
rating('rating', '评分', {
  max: 5,
  color: '#f59e0b',
  allowHalf: true
})
```

### 2. 星星评分 (rating-star)

明确指定星星样式的评分。

```typescript
// 通过插件变体使用
const column = {
  field: 'rating',
  title: '评分',
  plugin: 'rating',
  pluginConfig: {
    variant: 'star',
    max: 5,
    color: '#f59e0b'
  }
}
```

### 3. 爱心评分 (rating-heart)

使用爱心图标的评分样式。

```typescript
const column = {
  field: 'satisfaction',
  title: '满意度',
  plugin: 'rating',
  pluginConfig: {
    variant: 'heart',
    max: 5,
    color: '#ef4444'
  }
}
```

### 4. 拇指评分 (rating-thumb)

使用拇指图标的评分样式。

```typescript
const column = {
  field: 'recommendation',
  title: '推荐度',
  plugin: 'rating',
  pluginConfig: {
    variant: 'thumb',
    max: 3,
    allowHalf: false
  }
}
```

### 5. 圆点评分 (rating-dot)

使用圆点图标的评分样式。

```typescript
const column = {
  field: 'level',
  title: '等级',
  plugin: 'rating',
  pluginConfig: {
    variant: 'dot',
    max: 5,
    color: '#6366f1'
  }
}
```

## 使用示例

### 示例 1: 产品评分

```typescript
const productRatingConfig: RatingColumnConfig = {
  max: 5,
  color: '#f59e0b',
  emptyColor: '#e5e7eb',
  size: 'md',
  variant: 'star',
  allowHalf: true,
  readonly: true,
  showValue: true,
  valueFormatter: (value, max) => `${value.toFixed(1)}/${max}`,
  width: 150
}

// 在表格列配置中使用
const columns = [
  rating('productRating', '产品评分', productRatingConfig)
]
```

### 示例 2: 交互式用户评分

```typescript
const userRatingConfig: RatingColumnConfig = {
  max: 5,
  color: '#10b981',
  size: 'lg',
  variant: 'star',
  allowHalf: true,
  readonly: false,
  showValue: true,
  onChange: async (value, row) => {
    try {
      await updateUserRating(row.id, value)
      // 显示成功提示
      showSuccessMessage(`评分已更新为 ${value} 星`)
    } catch (error) {
      // 处理错误
      showErrorMessage('评分更新失败')
    }
  }
}
```

### 示例 3: 服务满意度调查

```typescript
const satisfactionConfig: RatingColumnConfig = {
  max: 5,
  color: '#ef4444',
  emptyColor: '#fca5a5',
  size: 18,
  variant: 'heart',
  allowHalf: false,
  readonly: false,
  showValue: true,
  valueFormatter: (value, max) => {
    const levels = ['很不满意', '不满意', '一般', '满意', '很满意']
    return levels[value - 1] || '未评分'
  },
  onChange: (value, row) => {
    // 实时保存满意度评分
    saveSatisfactionRating(row.serviceId, value)
  }
}
```

### 示例 4: 技能等级评估

```typescript
const skillLevelConfig: RatingColumnConfig = {
  max: 10,
  color: '#6366f1',
  emptyColor: '#c7d2fe',
  size: 'sm',
  variant: 'dot',
  allowHalf: false,
  readonly: true,
  showValue: true,
  valueFormatter: (value, max) => {
    if (value <= 3) return `初级 (${value}/${max})`
    if (value <= 6) return `中级 (${value}/${max})`
    if (value <= 8) return `高级 (${value}/${max})`
    return `专家 (${value}/${max})`
  }
}
```

### 示例 5: 自定义图标评分

```typescript
const customIconConfig: RatingColumnConfig = {
  max: 5,
  color: '#8b5cf6',
  size: 'md',
  variant: 'star', // 基础变体，会被自定义图标覆盖
  icons: {
    full: 'mdi:diamond',
    half: 'mdi:diamond-outline',
    empty: 'mdi:diamond-outline'
  },
  allowHalf: true,
  readonly: false,
  showValue: true,
  onChange: (value, row) => {
    console.log('钻石评级变更:', value)
  }
}
```

### 示例 6: 多场景评分对比

```typescript
const columns = [
  // 产品质量评分
  rating('qualityRating', '质量', {
    variant: 'star',
    color: '#f59e0b',
    max: 5,
    readonly: true
  }),
  
  // 服务态度评分  
  rating('serviceRating', '服务', {
    variant: 'heart',
    color: '#ef4444',
    max: 5,
    readonly: true
  }),
  
  // 推荐指数
  rating('recommendIndex', '推荐', {
    variant: 'thumb',
    color: '#10b981',
    max: 3,
    allowHalf: false,
    readonly: true
  }),
  
  // 综合评级
  rating('overallRating', '综合', {
    variant: 'dot',
    color: '#6366f1',
    max: 5,
    readonly: true,
    size: 'lg'
  })
]
```

## API 参考

### useRatingRenderer

composable 函数，提供评分列渲染所需的响应式数据和计算属性。

```typescript
function useRatingRenderer(props: {
  value?: number | string
  row: any
  config?: RatingColumnConfig
}): {
  config: ComputedRef<RatingColumnConfig>
  score: ComputedRef<number>
  maxRating: ComputedRef<number>
  showValue: ComputedRef<boolean>
  formattedValue: ComputedRef<string>
  ratingItems: ComputedRef<Array<{
    icon: string
    color: string
    index: number
  }>>
  containerClasses: ComputedRef<string>
  iconClasses: ComputedRef<string>
  handleRatingClick: (value: number) => void
  handleRatingHover: (value: number) => void
  handleRatingLeave: () => void
}
```

#### 参数说明

- `value`: 当前评分值，支持数字或字符串
- `row`: 当前行的完整数据对象
- `config`: 评分列配置对象

#### 返回值

- `config`: 合并默认值后的完整配置
- `score`: 计算后的评分值（范围限制在 0 到 max 之间）
- `maxRating`: 最大评分值
- `showValue`: 是否显示数值
- `formattedValue`: 格式化后的数值显示
- `ratingItems`: 评分图标项目列表，包含图标、颜色和索引
- `containerClasses`: 容器 CSS 类名
- `iconClasses`: 图标 CSS 类名
- `handleRatingClick`: 处理评分点击事件
- `handleRatingHover`: 处理悬停事件
- `handleRatingLeave`: 处理离开悬停事件

### RatingPlugin

插件类，用于在插件管理器中注册评分列渲染器。

```typescript
class RatingPlugin implements Plugin {
  name: string = 'rating'
  version: string = '1.0.0'
  description: string = 'Rating column plugin for displaying interactive star ratings with multiple variants'
  
  install(manager: PluginManager): void
  uninstall(): void
}
```

#### 注册的渲染器

插件会注册以下渲染器：

- `rating`: 标准评分渲染器
- `rating-star`: 星星样式评分渲染器
- `rating-heart`: 爱心样式评分渲染器
- `rating-thumb`: 拇指样式评分渲染器
- `rating-dot`: 圆点样式评分渲染器

### RatingRenderer

Vue 组件，处理评分列的实际渲染。

```typescript
interface Props {
  value?: number | string
  row: any
  config?: RatingColumnConfig
}
```

#### 组件特性

- 使用 `@iconify/vue` 进行图标渲染
- 支持鼠标交互（点击、悬停）
- 响应式样式和布局
- 自动处理半分显示

## 样式定制

### CSS 类名

评分列渲染器使用以下 CSS 类名，可以通过自定义样式进行定制：

```css
/* 容器类名 */
.rating-display         /* 评分显示容器 */
.rating-display.interactive  /* 交互式评分容器 */

/* 图标类名 */
.rating-icon            /* 评分图标基础类 */
.rating-icon.w-3.h-3    /* 小尺寸图标 (12px) */
.rating-icon.w-4.h-4    /* 中等尺寸图标 (16px) */
.rating-icon.w-5.h-5    /* 大尺寸图标 (20px) */
```

### Tailwind CSS 定制

组件使用 Tailwind CSS 类名进行样式设置：

```vue
<template>
  <div class="rating-display flex items-center gap-1">
    <span class="flex items-center">
      <Icon
        v-for="(item, index) in ratingItems"
        :key="index"
        :icon="item.icon"
        :style="{ color: item.color }"
        :class="iconClasses"
      />
    </span>
    <span v-if="showValue" class="text-xs text-gray-600 ml-1">
      {{ formattedValue }}
    </span>
  </div>
</template>
```

### 自定义样式示例

```css
/* 自定义评分图标样式 */
.rating-icon {
  transition: all 0.2s ease;
  cursor: pointer;
}

.rating-display.interactive .rating-icon:hover {
  transform: scale(1.1);
}

/* 自定义评分容器样式 */
.rating-display {
  padding: 4px 8px;
  border-radius: 6px;
  background-color: #f9fafb;
}

/* 自定义数值显示样式 */
.rating-display .text-xs {
  font-weight: 500;
  color: #6b7280;
}
```

## 性能优化

### 响应式计算

组件使用 Vue 3 的 `computed` 来确保只在必要时重新计算：

```typescript
const ratingItems = computed(() => {
  const icons = getIcons()
  const currentScore = hoverValue.value ?? score.value
  // 只有当评分值或配置改变时才重新计算
  // ...
})
```

### 事件处理优化

合理使用事件处理函数，避免不必要的渲染：

```typescript
const handleRatingClick = (value: number) => {
  if (config.value.readonly) return  // 只读模式直接返回
  
  const newValue = value === score.value ? 0 : value
  config.value.onChange?.(newValue, props.row)
}
```

### 条件渲染

使用 `v-if` 进行条件渲染，避免不必要的 DOM 元素：

```vue
<template>
  <div class="rating-display">
    <!-- 只在显示数值时渲染 -->
    <span v-if="showValue" class="text-xs text-gray-600 ml-1">
      {{ formattedValue }}
    </span>
  </div>
</template>
```

## 故障排除

### 常见问题

1. **评分不显示或显示异常**
   - 检查 `value` 是否为有效数字
   - 确认 `max` 配置是否正确
   - 验证 `config` 对象是否正确传递

2. **图标不显示**
   - 确认 `@iconify/vue` 组件已正确安装
   - 检查图标名称是否正确（如 `mdi:star`）
   - 验证自定义 `icons` 配置是否正确

3. **点击不响应**
   - 检查 `readonly` 是否设置为 `false`
   - 确认 `onChange` 回调函数是否正确设置
   - 验证事件绑定是否正确

4. **样式异常**
   - 确认 Tailwind CSS 已正确配置
   - 检查是否有样式冲突
   - 验证 `size` 配置是否正确

5. **半分不显示**
   - 检查 `allowHalf` 是否设置为 `true`
   - 确认评分值是否包含小数部分
   - 验证图标配置中是否包含 `half` 图标

### 调试技巧

1. **使用 Vue DevTools**: 查看组件的 props 和计算属性

2. **控制台调试**: 在配置中添加调试日志

```typescript
const ratingConfig: RatingColumnConfig = {
  max: 5,
  onChange: (value, row) => {
    console.log('评分变更:', { value, row, timestamp: Date.now() })
    // 实际处理逻辑
  }
}
```

3. **检查计算属性**: 监听计算属性的变化

```typescript
// 在组件中调试
import { watchEffect } from 'vue'

watchEffect(() => {
  console.log('当前评分项目:', ratingItems.value)
  console.log('格式化值:', formattedValue.value)
})
```

4. **验证事件处理**: 确认事件处理函数被正确调用

```typescript
const handleRatingClick = (value: number) => {
  console.log('点击评分:', value, '当前配置:', config.value)
  if (config.value.readonly) {
    console.log('只读模式，忽略点击')
    return
  }
  // 处理逻辑...
}
```

## 扩展开发

### 自定义变体

可以通过扩展插件来添加新的评分变体：

```typescript
export class ExtendedRatingPlugin extends RatingPlugin {
  install(manager: PluginManager): void {
    super.install(manager)  // 安装基础变体
    
    const registry = manager.getRendererRegistry()
    
    // 添加自定义变体
    registry.register('rating-emoji', {
      name: 'rating-emoji',
      component: 'RatingRenderer',
      render: (params: any) => {
        const emojiConfig = {
          ...params.config,
          variant: 'star',  // 使用基础变体
          icons: {
            full: '😍',
            half: '😊', 
            empty: '😐'
          }
        }
        return `__COMPONENT__:RatingRenderer:${JSON.stringify({
          value: params.value,
          row: params.row,
          config: emojiConfig,
        })}`
      },
    })
  }
}
```

### 自定义格式化函数

创建可复用的格式化函数：

```typescript
// 评分格式化工具
export const ratingFormatters = {
  // 百分比格式
  percentage: (value: number, max: number) => 
    `${Math.round((value / max) * 100)}%`,
  
  // 等级格式
  level: (value: number, max: number) => {
    const ratio = value / max
    if (ratio >= 0.9) return '优秀'
    if (ratio >= 0.7) return '良好'
    if (ratio >= 0.6) return '合格'
    return '不合格'
  },
  
  // 详细描述格式
  detailed: (value: number, max: number) => 
    `${value.toFixed(1)} 分 (共 ${max} 分)`,
    
  // 星级格式
  starLevel: (value: number) => `${value} 星级`
}

// 使用示例
const config: RatingColumnConfig = {
  valueFormatter: ratingFormatters.percentage
}
```

### 主题支持

添加主题支持：

```typescript
// 主题配置
export const ratingThemes = {
  default: {
    color: '#f59e0b',
    emptyColor: '#d9d9d9'
  },
  success: {
    color: '#10b981',
    emptyColor: '#d1fae5'
  },
  danger: {
    color: '#ef4444',
    emptyColor: '#fee2e2'
  },
  primary: {
    color: '#3b82f6',
    emptyColor: '#dbeafe'
  }
}

// 使用主题
const config: RatingColumnConfig = {
  ...ratingThemes.success,
  max: 5,
  variant: 'star'
}
```

## 最佳实践

1. **合理设置最大值**: 根据业务场景选择合适的最大评分值（通常 5 或 10）
2. **提供视觉反馈**: 在交互式评分中提供清晰的悬停和点击反馈
3. **错误处理**: 在 `onChange` 回调中添加适当的错误处理逻辑
4. **性能考虑**: 对于大型表格，考虑使用只读模式以提升性能
5. **用户体验**: 为不同的业务场景选择合适的变体和配置
6. **可访问性**: 确保键盘用户也能操作评分组件
7. **国际化**: 考虑数值格式化的本地化需求
8. **数据验证**: 在处理评分数据时进行适当的范围验证

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的星级评分渲染
- 提供 composable API
- 支持四种变体（star、heart、thumb、dot）
- 支持交互式评分和只读模式
- 支持半分显示和自定义图标
- 提供完整的配置选项和事件回调

## 许可证

MIT License
