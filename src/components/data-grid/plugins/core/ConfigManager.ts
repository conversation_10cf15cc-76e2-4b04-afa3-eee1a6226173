import { reactive, computed, watch } from 'vue'
import type { ConfigSchema, ValidationResult, InferConfigType } from './injection'

/**
 * 配置管理器
 * 提供统一的配置管理、验证和持久化功能
 */
export class ConfigManager {
  private configs = reactive(new Map<string, any>())
  private schemas = new Map<string, ConfigSchema>()
  private validators = new Map<string, (config: any) => ValidationResult>()
  private watchers = new Map<string, Set<(config: any) => void>>()

  /**
   * 注册配置Schema
   */
  registerSchema<T extends ConfigSchema>(
    key: string, 
    schema: T, 
    validator?: (config: InferConfigType<T>) => ValidationResult
  ): void {
    this.schemas.set(key, schema)
    
    if (validator) {
      this.validators.set(key, validator)
    } else {
      // 基于Schema生成默认验证器
      this.validators.set(key, this.createSchemaValidator(schema))
    }

    // 设置默认配置
    const defaultConfig = this.generateDefaultConfig(schema)
    if (!this.configs.has(key)) {
      this.configs.set(key, defaultConfig)
    }
  }

  /**
   * 获取配置
   */
  getConfig<T = any>(key: string): T | undefined {
    return this.configs.get(key)
  }

  /**
   * 设置配置
   */
  setConfig(key: string, config: any): ValidationResult {
    const validator = this.validators.get(key)
    
    if (validator) {
      const result = validator(config)
      if (!result.valid) {
        return result
      }
    }

    const oldConfig = this.configs.get(key)
    this.configs.set(key, { ...config })
    
    // 触发监听器
    const listeners = this.watchers.get(key)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(config)
        } catch (error) {
          console.error(`Error in config watcher for '${key}':`, error)
        }
      })
    }

    return { valid: true }
  }

  /**
   * 更新配置（部分更新）
   */
  updateConfig(key: string, partialConfig: any): ValidationResult {
    const currentConfig = this.configs.get(key) || {}
    const mergedConfig = { ...currentConfig, ...partialConfig }
    return this.setConfig(key, mergedConfig)
  }

  /**
   * 验证配置
   */
  validateConfig(key: string, config?: any): ValidationResult {
    const validator = this.validators.get(key)
    if (!validator) {
      return { valid: false, errors: [`No validator found for config '${key}'`] }
    }

    const configToValidate = config || this.configs.get(key)
    if (!configToValidate) {
      return { valid: false, errors: [`No config found for '${key}'`] }
    }

    return validator(configToValidate)
  }

  /**
   * 监听配置变化
   */
  watchConfig(key: string, callback: (config: any) => void): () => void {
    if (!this.watchers.has(key)) {
      this.watchers.set(key, new Set())
    }
    
    this.watchers.get(key)!.add(callback)
    
    // 返回取消监听函数
    return () => {
      this.watchers.get(key)?.delete(callback)
    }
  }

  /**
   * 获取响应式配置
   */
  getReactiveConfig<T = any>(key: string) {
    return computed(() => this.configs.get(key) as T)
  }

  /**
   * 重置配置为默认值
   */
  resetConfig(key: string): void {
    const schema = this.schemas.get(key)
    if (schema) {
      const defaultConfig = this.generateDefaultConfig(schema)
      this.configs.set(key, defaultConfig)
    }
  }

  /**
   * 获取所有配置
   */
  getAllConfigs(): Record<string, any> {
    const result: Record<string, any> = {}
    this.configs.forEach((config, key) => {
      result[key] = config
    })
    return result
  }

  /**
   * 清除所有配置
   */
  clearConfigs(): void {
    this.configs.clear()
  }

  /**
   * 基于Schema生成默认配置
   */
  private generateDefaultConfig(schema: ConfigSchema): any {
    const config: any = {}
    
    Object.entries(schema).forEach(([key, definition]) => {
      if (definition.default !== undefined) {
        config[key] = definition.default
      } else {
        // 根据类型生成默认值
        switch (definition.type) {
          case 'string':
            config[key] = ''
            break
          case 'number':
            config[key] = 0
            break
          case 'boolean':
            config[key] = false
            break
          case 'array':
            config[key] = []
            break
          case 'object':
            config[key] = definition.properties 
              ? this.generateDefaultConfig(definition.properties)
              : {}
            break
          default:
            config[key] = null
        }
      }
    })
    
    return config
  }

  /**
   * 基于Schema创建验证器
   */
  private createSchemaValidator(schema: ConfigSchema): (config: any) => ValidationResult {
    return (config: any): ValidationResult => {
      const errors: string[] = []
      const warnings: string[] = []

      Object.entries(schema).forEach(([key, definition]) => {
        const value = config[key]

        // 检查必需属性
        if (definition.required && (value === undefined || value === null)) {
          errors.push(`Property '${key}' is required`)
          return
        }

        // 跳过未定义的可选属性
        if (value === undefined || value === null) {
          return
        }

        // 类型验证
        const expectedType = definition.type
        const actualType = Array.isArray(value) ? 'array' : typeof value

        if (actualType !== expectedType) {
          errors.push(`Property '${key}' should be of type '${expectedType}', got '${actualType}'`)
          return
        }

        // 枚举验证
        if (definition.enum && !definition.enum.includes(value)) {
          errors.push(`Property '${key}' should be one of [${definition.enum.join(', ')}], got '${value}'`)
        }

        // 对象属性验证
        if (expectedType === 'object' && definition.properties) {
          const nestedValidator = this.createSchemaValidator(definition.properties)
          const nestedResult = nestedValidator(value)
          if (!nestedResult.valid) {
            errors.push(...(nestedResult.errors?.map(err => `${key}.${err}`) || []))
            warnings.push(...(nestedResult.warnings?.map(warn => `${key}.${warn}`) || []))
          }
        }

        // 数组项验证
        if (expectedType === 'array' && definition.items) {
          value.forEach((item: any, index: number) => {
            const itemSchema: ConfigSchema = { item: { type: 'object', ...definition.items! } }
            const itemValidator = this.createSchemaValidator(itemSchema)
            const itemResult = itemValidator({ item })
            if (!itemResult.valid) {
              errors.push(...(itemResult.errors?.map(err => `${key}[${index}].${err}`) || []))
            }
          })
        }
      })

      return {
        valid: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined
      }
    }
  }
}

/**
 * 全局配置管理器实例
 */
export const globalConfigManager = new ConfigManager()

/**
 * 配置管理器工具函数
 */
export const ConfigUtils = {
  /**
   * 深度合并配置
   */
  mergeConfigs<T = any>(target: T, source: Partial<T>): T {
    const result = { ...target }
    
    Object.entries(source).forEach(([key, value]) => {
      if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
        // 递归合并对象
        result[key as keyof T] = this.mergeConfigs(
          result[key as keyof T] || {} as any,
          value
        )
      } else {
        // 直接赋值
        result[key as keyof T] = value as any
      }
    })
    
    return result
  },

  /**
   * 配置差异比较
   */
  diffConfigs(oldConfig: any, newConfig: any): {
    added: string[]
    removed: string[]
    modified: string[]
    unchanged: string[]
  } {
    const added: string[] = []
    const removed: string[] = []
    const modified: string[] = []
    const unchanged: string[] = []

    const allKeys = new Set([
      ...Object.keys(oldConfig || {}),
      ...Object.keys(newConfig || {})
    ])

    allKeys.forEach(key => {
      const hasOld = key in (oldConfig || {})
      const hasNew = key in (newConfig || {})

      if (!hasOld && hasNew) {
        added.push(key)
      } else if (hasOld && !hasNew) {
        removed.push(key)
      } else if (hasOld && hasNew) {
        if (JSON.stringify(oldConfig[key]) !== JSON.stringify(newConfig[key])) {
          modified.push(key)
        } else {
          unchanged.push(key)
        }
      }
    })

    return { added, removed, modified, unchanged }
  },

  /**
   * 配置克隆
   */
  cloneConfig<T>(config: T): T {
    return JSON.parse(JSON.stringify(config))
  }
}