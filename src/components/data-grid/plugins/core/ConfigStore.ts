/**
 * Configuration Store for Data Grid Plugins
 * Replaces global window pollution with proper encapsulated state management
 */

import type { RowData } from './types'

/**
 * Configuration item interface
 */
export interface ConfigItem {
  /** Configuration data */
  config: Record<string, unknown>
  /** Creation timestamp for cleanup */
  createdAt: number
  /** Last accessed timestamp for LRU cleanup */
  lastAccessed: number
}

/**
 * Configuration store with memory management and cleanup
 */
export class ConfigStore {
  private static instance: ConfigStore | null = null
  private configs = new Map<string, ConfigItem>()
  private readonly maxSize = 1000
  private readonly ttlMs = 5 * 60 * 1000 // 5 minutes TTL
  private readonly batchSize = 50 // Process cleanup in batches
  private cleanupTimer: ReturnType<typeof setInterval> | null = null
  private accessCount = 0
  private hitCount = 0

  private constructor() {
    this.startCleanupTimer()
  }

  /**
   * Singleton instance getter
   */
  static getInstance(): ConfigStore {
    if (!ConfigStore.instance) {
      ConfigStore.instance = new ConfigStore()
    }
    return ConfigStore.instance
  }

  /**
   * Store configuration with enhanced security validation and sanitization
   */
  store(config: Record<string, unknown>): string {
    // Enhanced input validation
    if (!config || typeof config !== 'object') {
      throw new Error('[ConfigStore] Configuration must be a valid object')
    }

    if (Array.isArray(config)) {
      throw new Error('[ConfigStore] Configuration cannot be an array')
    }

    // Check for circular references
    try {
      JSON.stringify(config)
    } catch (error) {
      throw new Error(
        '[ConfigStore] Configuration contains circular references'
      )
    }

    // Security validation: prevent storing potentially dangerous objects
    if (this.hasUnsafeProperties(config)) {
      throw new Error(
        '[ConfigStore] Configuration contains unsafe properties or patterns'
      )
    }

    // Size validation to prevent memory exhaustion
    const configString = JSON.stringify(config)
    const configSize = new Blob([configString]).size
    const maxConfigSize = 1024 * 1024 // 1MB limit per config

    if (configSize > maxConfigSize) {
      throw new Error(
        `[ConfigStore] Configuration size (${configSize} bytes) exceeds limit (${maxConfigSize} bytes)`
      )
    }

    const configId = this.generateConfigId()
    const now = Date.now()

    // Deep clone with security sanitization
    const sanitizedConfig = this.sanitizeAndClone(config)

    this.configs.set(configId, {
      config: sanitizedConfig,
      createdAt: now,
      lastAccessed: now,
    })

    // Clean up if we exceed max size
    if (this.configs.size > this.maxSize) {
      this.cleanupOldConfigs()
    }

    if (import.meta.env.DEV) {
      console.debug(`[ConfigStore] Stored config ${configId} (${configSize} bytes)`)
    }

    return configId
  }

  /**
   * Retrieve configuration by ID with cache metrics
   */
  get(configId: string): Record<string, unknown> | null {
    this.accessCount++

    const item = this.configs.get(configId)
    if (!item) {
      return null
    }

    this.hitCount++
    // Update last accessed time
    item.lastAccessed = Date.now()
    return item.config
  }

  /**
   * Check if configuration exists
   */
  has(configId: string): boolean {
    return this.configs.has(configId)
  }

  /**
   * Remove specific configuration
   */
  remove(configId: string): boolean {
    return this.configs.delete(configId)
  }

  /**
   * Clear all configurations and reset metrics
   */
  clear(): void {
    this.configs.clear()
    this.accessCount = 0
    this.hitCount = 0
  }

  /**
   * Get current store size
   */
  size(): number {
    return this.configs.size
  }

  /**
   * Get cache performance metrics
   */
  getMetrics(): {
    size: number
    hitRate: number
    accessCount: number
    hitCount: number
  } {
    return {
      size: this.configs.size,
      hitRate: this.accessCount > 0 ? this.hitCount / this.accessCount : 0,
      accessCount: this.accessCount,
      hitCount: this.hitCount,
    }
  }

  /**
   * Prefetch configurations (warm cache)
   */
  prefetch(
    configs: Array<{ id: string; config: Record<string, unknown> }>
  ): void {
    configs.forEach(({ id, config }) => {
      if (!this.configs.has(id)) {
        this.store(config)
      }
    })
  }

  /**
   * Generate cryptographically secure configuration ID with entropy validation
   */
  private generateConfigId(): string {
    try {
      // Use crypto.getRandomValues if available for better security
      if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
        const array = new Uint8Array(16)
        crypto.getRandomValues(array)
        const randomHex = Array.from(array, (byte) =>
          byte.toString(16).padStart(2, '0')
        ).join('')
        const timestamp = Date.now().toString(36)
        const counter = (this.configs.size % 10000).toString(36)
        return `config_${timestamp}_${randomHex}_${counter}`
      }
    } catch (error) {
      console.warn(
        '[ConfigStore] Crypto API not available, falling back to Math.random'
      )
    }

    // Fallback to enhanced Math.random with multiple entropy sources
    const timestamp = Date.now().toString(36)
    const random1 = Math.random().toString(36).substring(2, 11)
    const random2 = Math.random().toString(36).substring(2, 11)
    const performanceNow =
      typeof performance !== 'undefined' ? performance.now().toString(36) : ''
    const counter = (this.configs.size % 10000).toString(36)

    return `config_${timestamp}_${random1}${random2}_${performanceNow}_${counter}`
  }

  /**
   * Enhanced security check for unsafe properties and patterns
   */
  private hasUnsafeProperties(
    obj: Record<string, unknown>,
    depth = 0
  ): boolean {
    // Prevent deep recursion that could cause stack overflow
    if (depth > 10) {
      console.warn(
        '[ConfigStore] Maximum object depth exceeded, potential security risk'
      )
      return true
    }

    // Extended list of unsafe keys and patterns
    const unsafeKeys = [
      '__proto__',
      'constructor',
      'prototype',
      'eval',
      'Function',
      'setTimeout',
      'setInterval',
      'setImmediate',
      'process',
      'global',
      'window',
      'document',
      'location',
      'localStorage',
      'sessionStorage',
      'XMLHttpRequest',
      'fetch',
      'import',
      'require',
      'module',
      'exports',
    ]

    // Legitimate event handler properties that should be allowed
    const legitimateEventHandlers = [
      'onClick',
      'onEdit',
      'onDelete',
      'onView',
      'onSubmit',
      'onCancel',
      'onSave',
      'onUpdate',
      'onChange',
      'onSelect',
      'onToggle',
      'onApprove',
      'onReject',
      'onEnable',
      'onDisable',
      'onCopy',
      'onExport',
      'onImport',
      'onRefresh',
      'onSearch',
      'onFilter',
      'onSort',
      'onAdd',
      'onRemove',
    ]

    // Check for unsafe patterns in key names (exclude legitimate event handlers)
    const unsafePatterns = [
      /^on[A-Z].*(Error|Exception|Load|Unload|Before|After)$/, // Potentially dangerous event patterns
      /javascript:/i, // JavaScript protocol
      /data:/i, // Data URLs that could contain scripts
      /script/i, // Script-related keys
      /iframe/i, // Iframe-related keys
      /eval/i, // Eval-related keys
    ]

    for (const key in obj) {
      // Check against unsafe keys
      if (unsafeKeys.includes(key)) {
        console.warn(`[ConfigStore] Unsafe property detected: ${key}`)
        return true
      }

      // Allow legitimate event handlers, but check patterns for others
      const isLegitimateHandler = legitimateEventHandlers.includes(key)
      if (
        !isLegitimateHandler &&
        unsafePatterns.some((pattern) => pattern.test(key))
      ) {
        console.warn(`[ConfigStore] Unsafe key pattern detected: ${key}`)
        return true
      }

      const value = obj[key]

      // Check for potentially dangerous values
      if (typeof value === 'string') {
        // Check for script injection attempts
        if (/(<script|javascript:|data:.*script|on\w+\s*=)/i.test(value)) {
          console.warn(
            `[ConfigStore] Potentially malicious string value detected: ${key}`
          )
          return true
        }
      }

      // Allow functions for legitimate event handlers, but not for other properties
      if (typeof value === 'function') {
        if (
          !isLegitimateHandler &&
          !key.startsWith('render') &&
          key !== 'validator' &&
          key !== 'formatter'
        ) {
          console.warn(
            `[ConfigStore] Unauthorized function detected in config: ${key}`
          )
          return true
        }
      }

      // Recursively check nested objects
      if (
        typeof value === 'object' &&
        value !== null &&
        !Array.isArray(value)
      ) {
        if (
          this.hasUnsafeProperties(value as Record<string, unknown>, depth + 1)
        ) {
          return true
        }
      }
    }

    return false
  }

  /**
   * Sanitize and deep clone object with security filtering
   */
  private sanitizeAndClone(obj: Record<string, unknown>, depth = 0): any {
    if (depth > 20) {
      throw new Error(
        '[ConfigStore] Maximum object depth exceeded during sanitization'
      )
    }

    if (obj === null || typeof obj !== 'object') {
      return this.sanitizeValue(obj)
    }

    if (obj instanceof Date) {
      return new Date(obj.getTime())
    }

    if (obj instanceof RegExp) {
      return new RegExp(obj.source, obj.flags)
    }

    if (Array.isArray(obj)) {
      return obj.map((item, index) => {
        if (index > 10000) {
          throw new Error('[ConfigStore] Array size limit exceeded')
        }
        return typeof item === 'object' && item !== null
          ? this.sanitizeAndClone(item as Record<string, unknown>, depth + 1)
          : this.sanitizeValue(item)
      })
    }

    const cloned: Record<string, unknown> = {}
    let keyCount = 0

    for (const key in obj) {
      if (keyCount > 1000) {
        throw new Error('[ConfigStore] Object key limit exceeded')
      }

      if (obj.hasOwnProperty(key)) {
        // Skip unsafe keys during cloning
        if (
          key.startsWith('__') ||
          key === 'constructor' ||
          key === 'prototype'
        ) {
          continue
        }

        const value = obj[key]

        // Allow legitimate event handler functions, but skip other functions and undefined values
        if (typeof value === 'function') {
          // Check if this is a legitimate event handler
          const legitimateEventHandlers = [
            'onClick',
            'onEdit',
            'onDelete',
            'onView',
            'onSubmit',
            'onCancel',
            'onSave',
            'onUpdate',
            'onChange',
            'onSelect',
            'onToggle',
            'onApprove',
            'onReject',
            'onEnable',
            'onDisable',
            'onCopy',
            'onExport',
            'onImport',
            'onRefresh',
            'onSearch',
            'onFilter',
            'onSort',
            'onAdd',
            'onRemove',
            'formatter',
            'condition',
            'disabled',
            'confirm'
          ]
          
          if (legitimateEventHandlers.includes(key)) {
            // Keep the function for legitimate event handlers
            cloned[key] = value
            keyCount++
            continue
          } else {
            // Skip other functions
            continue
          }
        }

        // Skip undefined values
        if (value === undefined) {
          continue
        }

        cloned[key] =
          typeof value === 'object' && value !== null
            ? this.sanitizeAndClone(value as Record<string, unknown>, depth + 1)
            : this.sanitizeValue(value)

        keyCount++
      }
    }

    return cloned
  }

  /**
   * Sanitize primitive values
   */
  private sanitizeValue(value: unknown): any {
    if (typeof value === 'string') {
      // Remove potentially dangerous script patterns
      return value
        .replace(/<script[^>]*>.*?<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .replace(/data:.*script/gi, '')
    }

    if (typeof value === 'number' && !isFinite(value)) {
      return 0 // Replace NaN and Infinity with 0
    }

    return value
  }

  /**
   * Start cleanup timer for expired configurations
   */
  private startCleanupTimer(): void {
    // Run cleanup every 1 minute instead of every TTL period
    this.cleanupTimer = setInterval(
      () => {
        this.cleanupExpiredConfigs()
      },
      Math.min(this.ttlMs, 60000)
    )
  }

  /**
   * Clean up expired configurations based on TTL
   * Optimized with batch processing for better performance
   */
  private cleanupExpiredConfigs(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    // Collect expired keys in batches to avoid blocking
    for (const [key, item] of this.configs.entries()) {
      if (now - item.lastAccessed > this.ttlMs) {
        expiredKeys.push(key)
        // Process in batches to prevent blocking
        if (expiredKeys.length >= this.batchSize) {
          break
        }
      }
    }

    // Remove expired configurations
    expiredKeys.forEach((key) => this.configs.delete(key))

    if (import.meta.env.DEV && expiredKeys.length > 0) {
      console.log(
        `[ConfigStore] Cleaned up ${expiredKeys.length} expired configurations`
      )
    }
  }

  /**
   * Clean up oldest configurations using LRU strategy
   */
  private cleanupOldConfigs(): void {
    const entries = Array.from(this.configs.entries())
    entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)

    // Remove oldest 20% of configs
    const removeCount = Math.floor(entries.length * 0.2)
    const removedIds: string[] = []

    for (let i = 0; i < removeCount; i++) {
      const [id] = entries[i]
      this.configs.delete(id)
      removedIds.push(id)
    }

    if (import.meta.env.DEV && removedIds.length > 0) {
      console.log(`[ConfigStore] Cleaned up ${removeCount} old configurations via LRU`)
    }
  }

  /**
   * Destroy the store and cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    this.configs.clear()
    ConfigStore.instance = null
  }
}

/**
 * Get the global configuration store instance
 */
export function getConfigStore(): ConfigStore {
  return ConfigStore.getInstance()
}

/**
 * Helper function to safely extract renderer parameters
 * Replaces the global window approach with proper state management
 */
export function extractSafeParams(
  params: {
    value: unknown
    row: RowData
    column?: { field?: string }
    rowIndex?: number
  },
  config: Record<string, unknown>,
  field?: string
): {
  value: unknown
  row: RowData
  column?: { field?: string }
  rowIndex?: number
  config: Record<string, unknown>
  configId: string
  field?: string
} {
  // Input validation
  if (!params || typeof params !== 'object') {
    throw new Error('[ConfigStore] Invalid params object')
  }

  if (!params.row || typeof params.row !== 'object') {
    throw new Error('[ConfigStore] Invalid row data')
  }

  if (!config || typeof config !== 'object') {
    throw new Error('[ConfigStore] Invalid config object')
  }

  // Validate field if provided
  if (field !== undefined && typeof field !== 'string') {
    throw new Error('[ConfigStore] Field must be a string if provided')
  }

  // Sanitize rowIndex
  const sanitizedRowIndex =
    typeof params.rowIndex === 'number' && !isNaN(params.rowIndex)
      ? params.rowIndex
      : undefined

  const store = getConfigStore()
  const configId = store.store(config)

  // Return the original config with functions preserved, not the serialized version
  const safeConfig = config

  return {
    value: params.value,
    row: params.row,
    column: params.column ? { field: params.column.field } : undefined,
    rowIndex: sanitizedRowIndex,
    config: safeConfig,
    configId,
    ...(field && { field }),
  }
}

/**
 * Helper function to get real configuration with functions preserved
 */
export function getRealConfig(
  configId?: string
): Record<string, unknown> | null {
  if (!configId) {
    return null
  }

  const store = getConfigStore()
  return store.get(configId)
}
