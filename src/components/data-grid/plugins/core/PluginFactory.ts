import type { Component } from 'vue'
import type { 
  PluginDefinition, 
  RendererDefinition, 
  ConfigSchema,
  ValidationResult,
  InferConfigType,
  PropDefinition
} from './injection'
import { ModernPluginManager } from './ModernPluginManager'
import { ConfigManager } from './ConfigManager'

/**
 * 插件工厂类
 * 提供便捷的插件创建和管理功能
 */
export class PluginFactory {
  private static instance: PluginFactory | null = null
  private configManager: ConfigManager
  private templates: Map<string, PluginTemplate> = new Map()

  private constructor() {
    this.configManager = new ConfigManager()
    this.initializeBuiltinTemplates()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): PluginFactory {
    if (!PluginFactory.instance) {
      PluginFactory.instance = new PluginFactory()
    }
    return PluginFactory.instance
  }

  /**
   * 创建Modern插件管理器
   */
  createModernManager(options: ModernManagerOptions = {}): ModernPluginManager {
    const manager = new ModernPluginManager({
      debug: options.debug ?? import.meta.env.DEV
    })

    // 应用预设配置
    if (options.preset) {
      this.applyPreset(manager, options.preset)
    }

    // 注册自定义插件
    if (options.plugins) {
      options.plugins.forEach(plugin => {
        manager.registerPlugin(plugin).catch(error => {
          if (options.debug) {
            console.error(`Failed to register plugin '${plugin.name}':`, error)
          }
        })
      })
    }

    return manager
  }

  /**
   * 创建渲染器插件
   */
  createRenderer(options: RendererCreationOptions): Promise<PluginDefinition> {
    return new Promise((resolve, reject) => {
      try {
        // 验证必需参数
        this.validateRendererOptions(options)

        // 创建配置Schema
        const configSchema = options.configSchema || this.createDefaultConfigSchema()
        
        // 创建渲染器定义
        const rendererDefinition: RendererDefinition = {
          name: options.name,
          component: options.component,
          description: options.description,
          defaultConfig: options.defaultConfig || {},
          defaultWidth: options.defaultWidth || 120,
          props: options.props || [],
          validator: options.validator || this.createSchemaValidator(configSchema)
        }

        // 创建插件定义
        const pluginDefinition: PluginDefinition = {
          name: `${options.name}-plugin`,
          version: options.version || '1.0.0',
          description: options.description || `Plugin for ${options.name} renderer`,
          
          setup: async (context) => {
            // 注册渲染器
            const rendererRegistry = context.container.resolveService('RendererRegistry') as Map<string, any> | null
            if (rendererRegistry) {
              rendererRegistry.set(options.name, rendererDefinition)
            }

            // 注册Vue组件
            if (typeof options.component !== 'string') {
              context.utils.registerComponent(
                options.componentName || `${options.name}Renderer`,
                options.component
              )
            }

            // 注册配置Schema
            this.configManager.registerSchema(
              options.name,
              configSchema,
              rendererDefinition.validator
            )

            context.utils.logger.info(`Renderer plugin '${options.name}' initialized`)
          },

          teardown: async (context) => {
            // 清理渲染器注册
            const rendererRegistry = context.container.resolveService('RendererRegistry') as Map<string, any> | null
            if (rendererRegistry) {
              rendererRegistry.delete(options.name)
            }
            
            context.utils.logger.info(`Renderer plugin '${options.name}' cleaned up`)
          }
        }

        resolve(pluginDefinition)
        
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 从模板创建插件
   */
  createFromTemplate(templateName: string, options: TemplateOptions): Promise<PluginDefinition> {
    return new Promise((resolve, reject) => {
      const template = this.templates.get(templateName)
      if (!template) {
        reject(new Error(`Template '${templateName}' not found`))
        return
      }

      try {
        const pluginDefinition = template.generate(options)
        resolve(pluginDefinition)
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 注册插件模板
   */
  registerTemplate(name: string, template: PluginTemplate): void {
    this.templates.set(name, template)
  }

  /**
   * 获取可用模板列表
   */
  getAvailableTemplates(): string[] {
    return Array.from(this.templates.keys())
  }

  /**
   * 验证插件定义
   */
  validatePlugin(plugin: PluginDefinition): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // 基本验证
    if (!plugin.name) {
      errors.push('Plugin name is required')
    } else if (!/^[a-z0-9-]+$/.test(plugin.name)) {
      errors.push('Plugin name must be lowercase with hyphens only')
    }

    if (!plugin.version) {
      errors.push('Plugin version is required')
    } else if (!/^\d+\.\d+\.\d+/.test(plugin.version)) {
      warnings.push('Plugin version should follow semantic versioning (x.y.z)')
    }

    // 功能验证
    if (!plugin.setup && !plugin.provides?.length && !plugin.extensions?.length) {
      warnings.push('Plugin provides no functionality (no setup, services, or extensions)')
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined
    }
  }

  /**
   * 创建插件预设
   */
  createPreset(name: string, config: PresetConfig): PluginPreset {
    return {
      name,
      description: config.description,
      plugins: config.plugins,
      configuration: config.configuration,
      dependencies: config.dependencies || [],
      version: config.version || '1.0.0'
    }
  }

  /**
   * 应用预设配置
   */
  private applyPreset(manager: ModernPluginManager, preset: PluginPreset): void {
    // 注册预设中的插件
    preset.plugins.forEach(plugin => {
      manager.registerPlugin(plugin).catch(error => {
        console.error(`Failed to register preset plugin '${plugin.name}':`, error)
      })
    })

    // 应用配置
    if (preset.configuration) {
      Object.entries(preset.configuration).forEach(([key, config]) => {
        this.configManager.setConfig(key, config)
      })
    }
  }

  /**
   * 验证渲染器选项
   */
  private validateRendererOptions(options: RendererCreationOptions): void {
    if (!options.name) {
      throw new Error('Renderer name is required')
    }
    
    if (!options.component) {
      throw new Error('Renderer component is required')
    }

    if (!/^[a-zA-Z][a-zA-Z0-9]*$/.test(options.name)) {
      throw new Error('Renderer name must be a valid identifier')
    }
  }

  /**
   * 创建默认配置Schema
   */
  private createDefaultConfigSchema(): ConfigSchema {
    return {
      width: {
        type: 'number',
        default: 120,
        description: 'Column width in pixels'
      },
      enabled: {
        type: 'boolean',
        default: true,
        description: 'Whether the renderer is enabled'
      }
    }
  }

  /**
   * 创建Schema验证器
   */
  private createSchemaValidator(schema: ConfigSchema): (config: any) => ValidationResult {
    return (config: any): ValidationResult => {
      const errors: string[] = []
      
      Object.entries(schema).forEach(([key, definition]) => {
        const value = config[key]
        
        if (definition.required && value === undefined) {
          errors.push(`Property '${key}' is required`)
        }
        
        if (value !== undefined) {
          const expectedType = definition.type
          const actualType = Array.isArray(value) ? 'array' : typeof value
          
          if (actualType !== expectedType) {
            errors.push(`Property '${key}' should be of type '${expectedType}', got '${actualType}'`)
          }
        }
      })

      return {
        valid: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined
      }
    }
  }

  /**
   * 初始化内置模板 (保留为扩展点，但移除默认模板以避免冗余)
   */
  private initializeBuiltinTemplates(): void {
    // 模板系统保留为扩展点，但默认不添加任何内置模板
    // 现有的 Modern 插件渲染器已提供所需功能
    if (import.meta.env.DEV) {
      console.log('[PluginFactory] Templates system initialized (no built-in templates)')
    }
  }
}

// 类型定义
export interface ModernManagerOptions {
  debug?: boolean
  preset?: PluginPreset
  plugins?: PluginDefinition[]
}

export interface RendererCreationOptions {
  name: string
  component: Component | string
  componentName?: string
  version?: string
  description?: string
  defaultConfig?: Record<string, any>
  defaultWidth?: number
  props?: PropDefinition[]
  configSchema?: ConfigSchema
  validator?: (config: any) => ValidationResult
}

export interface PluginTemplate {
  name: string
  description: string
  generate: (options: TemplateOptions) => PluginDefinition
}

export interface TemplateOptions extends Record<string, any> {
  name: string
  version?: string
  description?: string
}

export interface PresetConfig {
  description?: string
  plugins: PluginDefinition[]
  configuration?: Record<string, any>
  dependencies?: string[]
  version?: string
}

export interface PluginPreset {
  name: string
  description?: string
  plugins: PluginDefinition[]
  configuration?: Record<string, any>
  dependencies: string[]
  version: string
}

/**
 * 导出工厂实例
 */
export const pluginFactory = PluginFactory.getInstance()

/**
 * 便捷函数：创建Modern管理器
 * @deprecated Use createPluginManager() from main index instead
 */
export function createModernPluginManager(options?: ModernManagerOptions): ModernPluginManager {
  return pluginFactory.createModernManager(options)
}

/**
 * 便捷函数：创建渲染器插件
 */
export function createRendererPlugin(options: RendererCreationOptions): Promise<PluginDefinition> {
  return pluginFactory.createRenderer(options)
}

/**
 * 便捷函数：从模板创建插件
 */
export function createPluginFromTemplate(templateName: string, options: TemplateOptions): Promise<PluginDefinition> {
  return pluginFactory.createFromTemplate(templateName, options)
}