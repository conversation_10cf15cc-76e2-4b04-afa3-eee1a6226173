import type { App, Component } from 'vue'
import { markRaw } from 'vue'
import type {
  PluginDefinition,
  PluginContainer,
  ServiceDefinition,
  ExtensionHandler,
} from './injection'
import { InjectionContainer } from './InjectionContainer'
import { PluginHelper } from './PluginHelper'

/**
 * Modern plugin manager configuration
 */
export interface ModernPluginManagerConfig {
  debug?: boolean
}

/**
 * Modern Plugin Manager
 * Pure modern plugin architecture implementation
 */
export class ModernPluginManager implements PluginContainer {
  private container: InjectionContainer
  private config: ModernPluginManagerConfig
  private columnHelper: PluginHelper

  constructor(config: ModernPluginManagerConfig = {}) {
    this.config = config
    this.container = new InjectionContainer()
    this.columnHelper = new PluginHelper(this)

    if (config.debug && import.meta.env.DEV) {
      console.log('[ModernPluginManager] Initialized')
    }
  }

  /**
   * Register service
   */
  registerService<T>(definition: ServiceDefinition<T>): void {
    this.container.registerService(definition)
  }

  /**
   * Resolve service
   */
  resolveService<T>(
    token: string | symbol | import('vue').InjectionKey<T>
  ): T | undefined {
    return this.container.resolveService(token)
  }

  /**
   * Register plugin asynchronously
   */
  async registerPlugin(definition: PluginDefinition): Promise<void> {
    await this.container.registerPlugin(definition)

    // 注册 ColumnHelper 方法
    if (definition.columnHelperMethods) {
      for (const methodDef of definition.columnHelperMethods) {
        this.columnHelper.registerMethod(methodDef)
      }
    }
  }

  /**
   * Register plugin synchronously
   * 同步注册插件，确保ColumnHelper方法立即可用
   */
  registerPluginSync(definition: PluginDefinition): void {
    this.container.registerPluginSync(definition)

    // 注册 ColumnHelper 方法
    if (definition.columnHelperMethods) {
      for (const methodDef of definition.columnHelperMethods) {
        this.columnHelper.registerMethod(methodDef)
        if (this.config.debug) {
          console.log(`✅ Registered ColumnHelper method: ${methodDef.name}`)
        }
      }
    }
  }

  /**
   * Register multiple plugins
   */
  async registerPlugins(definitions: PluginDefinition[]): Promise<void> {
    return this.container.registerPlugins(definitions)
  }

  /**
   * Register extension point
   */
  registerExtensionPoint(name: string, handler: ExtensionHandler): void {
    this.container.registerExtensionPoint(name, handler)
  }

  /**
   * Get extension point
   */
  getExtensionPoint(name: string): ExtensionHandler[] {
    return this.container.getExtensionPoint(name)
  }

  /**
   * Unregister plugin
   */
  async unregisterPlugin(name: string): Promise<void> {
    // 获取插件信息以注销其方法
    const pluginInfo = this.container.getPluginInfo(name)
    if (pluginInfo?.columnHelperMethods) {
      for (const methodDef of pluginInfo.columnHelperMethods) {
        this.columnHelper.unregisterMethod(methodDef.name)
      }
    }

    return this.container.unregisterPlugin(name)
  }

  /**
   * Get registered plugins
   */
  getRegisteredPlugins(): string[] {
    return this.container.getRegisteredPlugins()
  }

  /**
   * Get plugin info
   */
  getPluginInfo(name: string): PluginDefinition | undefined {
    return this.container.getPluginInfo(name)
  }

  /**
   * Set Vue app instance
   */
  setApp(app: App): void {
    this.container.setApp(app)
  }

  /**
   * Dispose container
   */
  async dispose(): Promise<void> {
    return this.container.dispose()
  }

  /**
   * Get current configuration
   */
  getConfig(): ModernPluginManagerConfig {
    return { ...this.config }
  }

  /**
   * Enable debug mode
   */
  setDebug(enabled: boolean): void {
    this.config.debug = enabled
  }

  /**
   * Get column helper
   */
  getColumnHelper(): PluginHelper {
    return this.columnHelper
  }

  /**
   * Get component from modern registry
   */
  getComponent(componentName: string): Component | undefined {
    const registry = this.container.resolveService('ComponentRegistry') as Map<
      string,
      Component
    >
    if (registry) {
      const component = registry.get(componentName)
      if (component) {
        return markRaw(component)
      }
    }
    return undefined
  }

  /**
   * Register Vue component
   */
  registerComponent(name: string, component: Component): void {
    const registry = this.container.resolveService('ComponentRegistry') as Map<
      string,
      Component
    >
    if (registry) {
      // Use markRaw to prevent component from being made reactive
      registry.set(name, markRaw(component))
    }
  }

  /**
   * Get renderer registry
   */
  getRendererRegistry(): Map<string, any> {
    return (
      (this.container.resolveService('RendererRegistry') as Map<string, any>) ||
      new Map()
    )
  }
}
