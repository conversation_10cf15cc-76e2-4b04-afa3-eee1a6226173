import type {
  PluginHelper as <PERSON>luginHelper,
  EnhancedColumnConfig,
  RendererConfig as IRendererConfig,
  RowData,
  RendererParams,
  CustomColumnConfig,
} from './types'
import type { ModernPluginManager } from './ModernPluginManager'
import type { ColumnHelperMethodDefinition } from './injection'

import { createStatusMap, CommonStatusMaps } from '../renderers/status/modern'

/**
 * 渲染器配置接口
 */
interface RendererConfig {
  component: string
  defaultWidth: number
}

/**
 * 插件助手实现
 * 提供声明式的列配置 API，使用新的插件系统
 */
export class PluginHelper implements IPluginHelper {
  private registeredMethods = new Map<string, ColumnHelperMethodDefinition>()
  private rendererConfigs = new Map<
    string,
    { component: string; defaultWidth: number }
  >()

  constructor(private manager: ModernPluginManager) {
    // 构造函数现在只存储插件管理器引用
    // 所有方法将通过插件动态注册
  }

  /**
   * 等待插件方法注册完成
   * @param methods 要等待的方法名数组
   * @param timeout 超时时间（毫秒）
   */
  async waitForMethods(methods: string[], timeout = 5000): Promise<boolean> {
    const startTime = Date.now()

    while (Date.now() - startTime < timeout) {
      const missing = methods.filter(
        (method) => !this.registeredMethods.has(method)
      )
      if (missing.length === 0) {
        if (import.meta.env.DEV) {
          console.log(
            `[PluginHelper] All methods registered: ${methods.join(', ')}`
          )
        }
        return true
      }

      if (import.meta.env.DEV) {
        console.log(`[PluginHelper] Waiting for methods: ${missing.join(', ')}`)
      }

      await new Promise((resolve) => setTimeout(resolve, 50))
    }

    const missing = methods.filter(
      (method) => !this.registeredMethods.has(method)
    )
    if (import.meta.env.DEV) {
      console.warn(
        `[PluginHelper] Timeout waiting for methods: ${missing.join(', ')}`
      )
    }
    return false
  }

  /**
   * 注册插件方法
   */
  registerMethod(definition: ColumnHelperMethodDefinition): void {
    if (this.registeredMethods.has(definition.name)) {
      if (import.meta.env.DEV) {
        console.warn(
          `[PluginHelper] Method '${definition.name}' already exists, overriding...`
        )
      }
    }

    // 注册方法
    this.registeredMethods.set(definition.name, definition)

    // 注册对应的渲染器配置
    this.rendererConfigs.set(definition.name, definition.rendererConfig)

    // 动态添加方法到实例
    ;(this as any)[definition.name] = definition.implementation.bind(this)

    if (import.meta.env.DEV) {
      console.log(`[PluginHelper] Registered method: ${definition.name}`)
    }
  }

  /**
   * 取消注册方法
   */
  unregisterMethod(name: string): void {
    if (this.registeredMethods.has(name)) {
      this.registeredMethods.delete(name)
      this.rendererConfigs.delete(name)
      delete (this as any)[name]

      if (import.meta.env.DEV) {
        console.log(`[PluginHelper] Unregistered method: ${name}`)
      }
    }
  }

  /**
   * 获取已注册的方法列表
   */
  getRegisteredMethods(): string[] {
    return Array.from(this.registeredMethods.keys())
  }

  /**
   * 获取方法定义
   */
  getMethodDefinition(name: string): ColumnHelperMethodDefinition | undefined {
    return this.registeredMethods.get(name)
  }

  /**
   * 提取渲染器参数
   */
  private extractRendererParams(
    params: {
      value: unknown
      row: RowData
      column?: { field?: string }
      rowIndex?: number
    },
    config: Record<string, unknown>,
    field?: string
  ): RendererParams {
    return {
      value: params.value,
      row: params.row,
      column: params.column,
      rowIndex: params.rowIndex || 0,
      field: field || params.column?.field,
      config,
      configId: '', // 新架构不需要configId，保留为空字符串以兼容接口
    }
  }

  /**
   * 创建通用的插件列配置
   */
  private createPluginColumn<T extends Record<string, unknown>>(
    field: string | undefined,
    title: string,
    pluginName: string,
    config: T,
    customWidth?: number
  ): EnhancedColumnConfig {
    if (!title || typeof title !== 'string') {
      throw new Error(`Invalid title: ${title}`)
    }

    const rendererConfig = this.rendererConfigs.get(pluginName)
    if (!rendererConfig) {
      throw new Error(
        `Renderer configuration not found for plugin: ${pluginName}`
      )
    }

    const { component: componentName, defaultWidth } = rendererConfig
    const width =
      customWidth ||
      (typeof config.width === 'number' ? config.width : defaultWidth)

    return {
      ...(field && { field }),
      title,
      width,
      fixed: config.fixed as 'left' | 'right' | undefined,
      plugin: pluginName,
      pluginConfig: config,
      slots: {
        default: {
          name: `${pluginName}-renderer`,
          render: (params: {
            value: unknown
            row: RowData
            column?: { field?: string }
            rowIndex?: number
          }) => {
            const safeParams = this.extractRendererParams(params, config, field)
            return `__COMPONENT__:${componentName}:${JSON.stringify(safeParams)}`
          },
        },
      },
    }
  }

  /**
   * 创建自定义列配置
   * 现在与其他方法保持一致的实现方式
   */
  custom(
    field: string,
    title: string,
    renderer: ((params: RendererParams) => unknown) | string,
    config: CustomColumnConfig = {}
  ): EnhancedColumnConfig {
    if (!title || typeof title !== 'string') {
      throw new Error(`Invalid title: ${title}`)
    }

    const width = config.width || 150

    // 如果是函数渲染器，直接使用
    if (typeof renderer === 'function') {
      return {
        field,
        title,
        width,
        fixed: config.fixed as 'left' | 'right' | undefined,
        plugin: 'custom',
        pluginConfig: config,
        slots: {
          default: {
            name: 'custom-renderer',
            render: renderer,
          },
        },
      }
    }

    // 如果是字符串，假设是组件名称
    if (typeof renderer === 'string') {
      return {
        field,
        title,
        width,
        fixed: config.fixed as 'left' | 'right' | undefined,
        plugin: 'custom',
        pluginConfig: config,
        slots: {
          default: {
            name: 'custom-component-renderer',
            render: (params: {
              value: unknown
              row: RowData
              column?: { field?: string }
              rowIndex?: number
            }) => {
              const safeParams = this.extractRendererParams(
                params,
                config,
                field
              )
              return `__COMPONENT__:${renderer}:${JSON.stringify(safeParams)}`
            },
          },
        },
      }
    }

    throw new Error(
      `Invalid renderer type: ${typeof renderer}. Expected function or string.`
    )
  }

  /**
   * 动态注册新的渲染器类型
   * 支持运行时扩展
   */
  registerRenderer(name: string, renderer: IRendererConfig): void {
    if (this.rendererConfigs.has(name)) {
      if (import.meta.env.DEV) {
        console.warn(
          `[PluginHelper] Renderer '${name}' already exists, overriding...`
        )
      }
    }

    // Safe modification with proper typing
    this.rendererConfigs.set(name, {
      component: renderer.component,
      defaultWidth: renderer.defaultWidth,
    })
  }

  /**
   * 获取所有已注册的渲染器类型
   */
  getAvailableRenderers(): string[] {
    return Array.from(this.rendererConfigs.keys())
  }

  /**
   * 清理缓存（接口兼容性）
   */
  clearCache(): void {
    // 新的动态注册架构不需要复杂的缓存管理
    // 保留此方法仅为接口兼容性
    if (import.meta.env.DEV) {
      console.log('[PluginHelper] Cache cleared (no-op in new architecture)')
    }
  }

  /**
   * Get common status mappings
   */
  static readonly StatusMaps = CommonStatusMaps

  /**
   * Create custom status mapping
   */
  static createStatusMap() {
    return createStatusMap()
  }
}

/**
 * 创建插件助手实例（Vue组合式API）
 * 推荐在Vue组件中使用
 */
export function usePluginHelper(manager: ModernPluginManager): PluginHelper {
  return new PluginHelper(manager)
}

/**
 * 获取插件助手实例（非响应式）
 * 在非Vue环境或工具函数中使用
 */
export function getPluginHelper(manager: ModernPluginManager): PluginHelper {
  return new PluginHelper(manager)
}

// 导出常用配置
export const StatusMaps = PluginHelper.StatusMaps
export { createStatusMap }
