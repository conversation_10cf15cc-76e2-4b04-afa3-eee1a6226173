import {
  reactive,
  inject,
  markRaw,
  type InjectionKey,
  type Component,
} from 'vue'
import type {
  PluginDefinition,
  RendererDefinition,
  ServiceDefinition,
  ServiceProvider,
  ExtensionPoint,
  ExtensionHandler,
  ConfigSchema,
  PluginDevUtils,
  InferConfigType,
  ValidationResult,
  PropDefinition,
} from './injection'
import { InjectionContainer } from './InjectionContainer'

/**
 * 插件开发工具实现
 */
export class PluginDevelopmentUtils implements PluginDevUtils {
  /**
   * 定义插件
   */
  definePlugin(definition: Omit<PluginDefinition, 'meta'>): PluginDefinition {
    return {
      ...definition,
      meta: {
        isDevelopment: import.meta.env.DEV,
        createdAt: new Date().toISOString(),
        framework: 'data-grid-v2',
      },
    }
  }

  /**
   * 定义渲染器
   */
  defineRenderer(definition: RendererDefinition): RendererDefinition {
    // 验证渲染器定义
    this.validateRendererDefinition(definition)

    return {
      ...definition,
      // 添加默认属性
      defaultConfig: definition.defaultConfig || {},
      defaultWidth: definition.defaultWidth || 120,
      props: definition.props || [],
    }
  }

  /**
   * 定义服务
   */
  defineService<T>(
    token: string | symbol | InjectionKey<T>,
    provider: ServiceProvider<T>
  ): ServiceDefinition<T> {
    return {
      token,
      provider,
      scope: provider.singleton !== false ? 'singleton' : 'transient',
    }
  }

  /**
   * 定义扩展点
   */
  defineExtension(
    name: string,
    handler: ExtensionHandler,
    config?: Record<string, any>
  ): ExtensionPoint {
    return {
      name,
      handler,
      config: config || {},
      priority: 0,
    }
  }

  /**
   * 创建配置schema
   */
  defineConfigSchema<T extends ConfigSchema>(schema: T): T {
    // 验证schema结构
    this.validateConfigSchema(schema)
    return schema
  }

  /**
   * 使用渲染器配置（Vue组合式API）
   */
  useRendererConfig<T>(): T {
    // 这个函数需要在Vue组件内部使用
    const configToken = Symbol('RendererConfig') as InjectionKey<T>
    const config = inject(configToken)

    if (!config) {
      throw new Error(
        'useRendererConfig must be called within a renderer component context'
      )
    }

    return config
  }

  /**
   * 使用主题（Vue组合式API）
   */
  useTheme(): any {
    const themeToken = Symbol('Theme') as InjectionKey<any>
    return inject(themeToken, () => ({}))
  }

  /**
   * 验证渲染器定义
   */
  private validateRendererDefinition(definition: RendererDefinition): void {
    if (!definition.name) {
      throw new Error('Renderer definition must have a name')
    }

    if (!definition.component) {
      throw new Error('Renderer definition must have a component')
    }

    // 验证属性定义
    if (definition.props) {
      definition.props.forEach((prop) => {
        this.validatePropDefinition(prop)
      })
    }
  }

  /**
   * 验证属性定义
   */
  private validatePropDefinition(prop: PropDefinition): void {
    if (!prop.name) {
      throw new Error('Property definition must have a name')
    }

    const validTypes = [
      'string',
      'number',
      'boolean',
      'object',
      'array',
      'function',
      'any',
    ]

    // 支持单个类型或类型数组
    if (Array.isArray(prop.type)) {
      // 验证数组中的每个类型都是有效的
      for (const type of prop.type) {
        if (!validTypes.includes(type)) {
          throw new Error(`Invalid property type: ${type}`)
        }
      }
    } else {
      // 验证单个类型
      if (!validTypes.includes(prop.type)) {
        throw new Error(`Invalid property type: ${prop.type}`)
      }
    }
  }

  /**
   * 验证配置schema
   */
  private validateConfigSchema(schema: ConfigSchema): void {
    Object.entries(schema).forEach(([key, definition]) => {
      if (!definition.type) {
        throw new Error(`Schema property '${key}' must have a type`)
      }

      const validTypes = ['string', 'number', 'boolean', 'object', 'array']
      if (!validTypes.includes(definition.type)) {
        throw new Error(`Invalid schema type for '${key}': ${definition.type}`)
      }

      // 递归验证嵌套对象
      if (definition.type === 'object' && definition.properties) {
        this.validateConfigSchema(definition.properties)
      }
    })
  }
}

/**
 * 全局开发工具实例
 */
export const devUtils = new PluginDevelopmentUtils()

/**
 * 便利函数导出
 */
export const definePlugin = devUtils.definePlugin.bind(devUtils)
export const defineRenderer = devUtils.defineRenderer.bind(devUtils)
export const defineService = devUtils.defineService.bind(devUtils)
export const defineExtension = devUtils.defineExtension.bind(devUtils)
export const defineConfigSchema = devUtils.defineConfigSchema.bind(devUtils)

/**
 * 配置验证器工厂
 */
export class ConfigValidator {
  /**
   * 根据schema创建验证函数
   */
  static createValidator<T extends ConfigSchema>(schema: T) {
    return (config: any): ValidationResult => {
      const errors: string[] = []
      const warnings: string[] = []

      // 验证配置对象
      this.validateConfigObject(config, schema, '', errors, warnings)

      return {
        valid: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined,
        warnings: warnings.length > 0 ? warnings : undefined,
      }
    }
  }

  /**
   * 验证配置对象
   */
  private static validateConfigObject(
    config: any,
    schema: ConfigSchema,
    path: string,
    errors: string[],
    warnings: string[]
  ): void {
    Object.entries(schema).forEach(([key, definition]) => {
      const currentPath = path ? `${path}.${key}` : key
      const value = config[key]

      // 检查必需字段
      if (definition.required && (value === undefined || value === null)) {
        errors.push(`Required field '${currentPath}' is missing`)
        return
      }

      // 如果值存在，验证类型
      if (value !== undefined && value !== null) {
        this.validateFieldType(value, definition, currentPath, errors, warnings)
      }
    })
  }

  /**
   * 验证字段类型
   */
  private static validateFieldType(
    value: any,
    definition: any,
    path: string,
    errors: string[],
    warnings: string[]
  ): void {
    const expectedType = definition.type
    const actualType = typeof value

    switch (expectedType) {
      case 'string':
        if (actualType !== 'string') {
          errors.push(`Field '${path}' should be string, got ${actualType}`)
        }
        break

      case 'number':
        if (actualType !== 'number' || isNaN(value)) {
          errors.push(`Field '${path}' should be number, got ${actualType}`)
        }
        break

      case 'boolean':
        if (actualType !== 'boolean') {
          errors.push(`Field '${path}' should be boolean, got ${actualType}`)
        }
        break

      case 'object':
        if (actualType !== 'object' || Array.isArray(value)) {
          errors.push(`Field '${path}' should be object, got ${actualType}`)
        } else if (definition.properties) {
          // 递归验证嵌套对象
          this.validateConfigObject(
            value,
            definition.properties,
            path,
            errors,
            warnings
          )
        }
        break

      case 'array':
        if (!Array.isArray(value)) {
          errors.push(`Field '${path}' should be array, got ${actualType}`)
        } else if (definition.items) {
          // 验证数组项
          value.forEach((item, index) => {
            this.validateFieldType(
              item,
              definition.items,
              `${path}[${index}]`,
              errors,
              warnings
            )
          })
        }
        break
    }

    // 验证枚举值
    if (definition.enum && !definition.enum.includes(value)) {
      errors.push(
        `Field '${path}' should be one of [${definition.enum.join(', ')}], got '${value}'`
      )
    }
  }
}

/**
 * 渲染器工厂
 */
export class RendererFactory {
  private renderers = new Map<string, RendererDefinition>()
  private components = new Map<string, Component>()

  /**
   * 注册渲染器
   */
  register(definition: RendererDefinition): void {
    const validated = defineRenderer(definition)
    this.renderers.set(validated.name, validated)

    // 如果是字符串组件名，需要从组件注册表中解析
    if (typeof validated.component === 'string') {
      const component = this.components.get(validated.component)
      if (!component) {
        console.warn(
          `Component '${validated.component}' not found for renderer '${validated.name}'`
        )
      }
    }
  }

  /**
   * 注册组件
   */
  registerComponent(name: string, component: Component): void {
    // Use markRaw to prevent component from being made reactive
    this.components.set(name, markRaw(component))
  }

  /**
   * 创建渲染器实例
   */
  create(type: string, props: any): Component | null {
    const definition = this.renderers.get(type)
    if (!definition) {
      console.warn(`Renderer '${type}' not found`)
      return null
    }

    let component: Component
    if (typeof definition.component === 'string') {
      const comp = this.components.get(definition.component)
      if (!comp) {
        console.error(`Component '${definition.component}' not found`)
        return null
      }
      component = comp
    } else {
      component = definition.component
    }

    // 合并默认配置
    const mergedProps = {
      ...definition.defaultConfig,
      ...props,
    }

    // 确保组件被 markRaw 处理，防止被意外地做成响应式对象
    return component ? markRaw(component) : null
  }

  /**
   * 获取可用渲染器列表
   */
  getAvailableRenderers(): string[] {
    return Array.from(this.renderers.keys())
  }

  /**
   * 获取渲染器定义
   */
  getRendererDefinition(name: string): RendererDefinition | undefined {
    return this.renderers.get(name)
  }
}

/**
 * 通用渲染器属性定义，减少重复代码
 */
export const COMMON_RENDERER_PROPS = [
  {
    name: 'value',
    type: 'string',
    required: true,
    description: '渲染值',
  },
  {
    name: 'row',
    type: 'object',
    required: true,
    description: '行数据',
  },
  {
    name: 'config',
    type: 'object',
    description: '渲染器配置',
  },
  {
    name: 'field',
    type: 'string',
    required: true,
    description: '字段名',
  },
] as const

/**
 * 创建带有通用属性的渲染器定义
 */
export function createRendererDefinition(
  definition: Omit<RendererDefinition, 'props'> & {
    props?: PropDefinition[]
  }
): RendererDefinition {
  return defineRenderer({
    ...definition,
    props: [...COMMON_RENDERER_PROPS, ...(definition.props || [])],
  })
}

/**
 * 插件开发助手类
 */
export class PluginDevHelper {
  /**
   * 创建测试环境
   */
  static createTestEnvironment() {
    return {
      container: new InjectionContainer(),
      mockServices: new Map(),
      mockComponents: new Map(),
    }
  }

  /**
   * 模拟服务
   */
  static mockService<T>(
    token: string | symbol | InjectionKey<T>,
    implementation: T
  ) {
    return defineService(token, { value: implementation })
  }

  /**
   * 创建配置模拟
   */
  static createConfigMock<T extends object>(defaultConfig: T): T {
    return reactive(defaultConfig) as T
  }

  /**
   * 验证插件定义
   */
  static validatePlugin(definition: PluginDefinition): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // 基础验证
    if (!definition.name) {
      errors.push('Plugin must have a name')
    }

    if (!definition.version) {
      errors.push('Plugin must have a version')
    }

    // 版本格式验证
    if (definition.version && !/^\d+\.\d+\.\d+/.test(definition.version)) {
      warnings.push('Plugin version should follow semantic versioning (x.y.z)')
    }

    // 依赖验证
    if (definition.requires && definition.requires.length > 10) {
      warnings.push(
        'Plugin has many dependencies, consider reducing complexity'
      )
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  }

  /**
   * Create Modern plugin templates with enhanced features
   */
  static createModernPluginTemplate(
    name: string,
    options: {
      type: 'basic' | 'renderer' | 'service' | 'composite'
      withPerformanceMonitoring?: boolean
      withConfigValidation?: boolean
      withErrorHandling?: boolean
      withTesting?: boolean
    }
  ): string {
    const baseTemplate = this.generatePluginTemplate(name, options.type as any)

    // Add Modern enhancements
    let enhancedTemplate = baseTemplate

    if (options.withPerformanceMonitoring) {
      enhancedTemplate = this.addPerformanceMonitoring(enhancedTemplate, name)
    }

    if (options.withConfigValidation) {
      enhancedTemplate = this.addConfigValidation(enhancedTemplate, name)
    }

    if (options.withErrorHandling) {
      enhancedTemplate = this.addErrorHandling(enhancedTemplate, name)
    }

    if (options.withTesting) {
      enhancedTemplate = this.addTestingSupport(enhancedTemplate, name)
    }

    return enhancedTemplate
  }

  /**
   * Add performance monitoring to plugin template
   */
  private static addPerformanceMonitoring(
    template: string,
    pluginName: string
  ): string {
    const performanceImport = `import { pluginPerformanceMonitor } from '@/components/data-grid/plugins/utils/performanceMonitor'\n`
    const performanceSetup = `
    // Performance monitoring setup
    const performanceMetricId = pluginPerformanceMonitor.startTiming('${pluginName}', 'initialization')
    
    // Monitor plugin operations
    context.utils.measure = (operationName: string, fn: () => any) => {
      return pluginPerformanceMonitor.measure('${pluginName}', operationName, fn)
    }
    
    // End initialization timing
    pluginPerformanceMonitor.endTiming('${pluginName}', performanceMetricId)`

    return template
      .replace(/^import/, performanceImport + 'import')
      .replace(
        /(setup: async \(context\) => {\s*)/,
        `$1${performanceSetup}\n    `
      )
  }

  /**
   * Add config validation to plugin template
   */
  private static addConfigValidation(
    template: string,
    pluginName: string
  ): string {
    const configImport = `import { ConfigValidator, defineConfigSchema } from '@/components/data-grid/plugins/core/DevUtils'\n`
    const configSchema = `
// Define configuration schema
const ${pluginName}ConfigSchema = defineConfigSchema({
  enabled: { type: 'boolean', required: true, default: true },
  options: { 
    type: 'object', 
    properties: {
      // Add your config properties here
    }
  }
})

// Create validator
const validateConfig = ConfigValidator.createValidator(${pluginName}ConfigSchema)`

    const configValidation = `
    // Validate plugin configuration
    const config = context.config || {}
    const validation = validateConfig(config)
    
    if (!validation.valid) {
      throw new Error(\`Invalid plugin configuration: \${validation.errors?.join(', ')}\`)
    }
    
    if (validation.warnings?.length) {
      context.utils.logger.warn('Configuration warnings:', validation.warnings)
    }`

    return template
      .replace(/^import/, configImport + 'import')
      .replace(
        /export const \w+Plugin = definePlugin\({/,
        `${configSchema}\n\nexport const ${pluginName}Plugin = definePlugin({`
      )
      .replace(
        /(setup: async \(context\) => {\s*)/,
        `$1${configValidation}\n    `
      )
  }

  /**
   * Add error handling to plugin template
   */
  private static addErrorHandling(
    template: string,
    pluginName: string
  ): string {
    const errorHandlingImport = `import { createErrorHandler } from '@/components/data-grid/plugins/utils/errorHandler'\n`
    const errorHandling = `
    // Setup error handling
    const errorHandler = createErrorHandler('${pluginName}')
    
    // Wrap plugin operations with error handling
    context.utils.safeExecute = async (operation: () => Promise<any>) => {
      try {
        return await operation()
      } catch (error) {
        errorHandler.handleError(error, { context: 'plugin-operation' })
        throw error
      }
    }`

    return template
      .replace(/^import/, errorHandlingImport + 'import')
      .replace(/(setup: async \(context\) => {\s*)/, `$1${errorHandling}\n    `)
  }

  /**
   * Add testing support to plugin template
   */
  private static addTestingSupport(
    template: string,
    pluginName: string
  ): string {
    const testingTemplate = `
// Test utilities for ${pluginName} plugin
export const ${pluginName}TestUtils = {
  createTestContext: () => {
    return {
      // Mock test context
      container: new Map(),
      utils: {
        logger: { info: console.log, warn: console.warn, error: console.error },
        measure: (name: string, fn: () => any) => fn(),
        safeExecute: async (fn: () => Promise<any>) => fn()
      }
    }
  },
  
  createMockConfig: () => ({
    enabled: true,
    options: {}
  }),
  
  runBasicTests: async () => {
    const context = ${pluginName}TestUtils.createTestContext()
    
    // Test plugin initialization
    try {
      await ${pluginName}Plugin.setup?.(context as any)
      console.log('✅ Plugin initialization test passed')
      return true
    } catch (error) {
      console.error('❌ Plugin initialization test failed:', error)
      return false
    }
  }
}`

    return template + testingTemplate
  }

  /**
   * 生成插件模板
   */
  static generatePluginTemplate(
    name: string,
    type: 'basic' | 'renderer' | 'service'
  ): string {
    switch (type) {
      case 'basic':
        return this.generateBasicPluginTemplate(name)
      case 'renderer':
        return this.generateRendererPluginTemplate(name)
      case 'service':
        return this.generateServicePluginTemplate(name)
      default:
        throw new Error(`Unknown template type: ${type}`)
    }
  }

  private static generateBasicPluginTemplate(name: string): string {
    return `import { definePlugin } from '@/components/data-grid/plugins/core/DevUtils'

export const ${name}Plugin = definePlugin({
  name: '${name.toLowerCase()}',
  version: '1.0.0',
  description: '${name} plugin description',
  
  setup: async (context) => {
    // Plugin initialization logic
    context.utils.logger.info('${name} plugin initialized')
  },
  
  teardown: async (context) => {
    // Plugin cleanup logic
    context.utils.logger.info('${name} plugin cleaned up')
  }
})
`
  }

  private static generateRendererPluginTemplate(name: string): string {
    return `import { definePlugin, defineRenderer } from '@/components/data-grid/plugins/core/DevUtils'
import ${name}Renderer from './${name}Renderer.vue'

export const ${name}RendererDefinition = defineRenderer({
  name: '${name.toLowerCase()}',
  component: ${name}Renderer,
  defaultWidth: 120,
  defaultConfig: {
    // Default configuration
  }
})

export const ${name}Plugin = definePlugin({
  name: '${name.toLowerCase()}-renderer',
  version: '1.0.0',
  description: '${name} renderer plugin',
  
  setup: async (context) => {
    // Register renderer
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.set('${name.toLowerCase()}', ${name}RendererDefinition)
    }
    
    // Register component
    context.utils.registerComponent('${name}Renderer', ${name}Renderer)
    
    context.utils.logger.info('${name} renderer plugin initialized')
  }
})
`
  }

  private static generateServicePluginTemplate(name: string): string {
    return `import { definePlugin, defineService } from '@/components/data-grid/plugins/core/DevUtils'

export interface ${name}Service {
  // Define service interface
}

export class ${name}ServiceImpl implements ${name}Service {
  // Implement service
}

export const ${name}Plugin = definePlugin({
  name: '${name.toLowerCase()}-service',
  version: '1.0.0',
  description: '${name} service plugin',
  
  provides: [
    defineService('${name}Service', {
      factory: () => new ${name}ServiceImpl(),
      singleton: true
    })
  ],
  
  setup: async (context) => {
    context.utils.logger.info('${name} service plugin initialized')
  }
})
`
  }
}
