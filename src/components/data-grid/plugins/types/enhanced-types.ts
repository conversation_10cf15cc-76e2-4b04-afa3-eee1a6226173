/**
 * Enhanced TypeScript types for improved type safety and developer experience
 * Provides strict typing, branded types, and utility types for the data grid plugin system
 */

import type { Component } from 'vue'

/**
 * Branded types for better type safety and preventing mixing of different ID types
 */
export type PluginName = string & { readonly __brand: 'PluginName' }
export type ConfigId = string & { readonly __brand: 'ConfigId' }
export type RendererName = string & { readonly __brand: 'RendererName' }
export type ComponentName = string & { readonly __brand: 'ComponentName' }

/**
 * Branded type constructors with validation
 */
export const PluginName = {
  create: (name: string): PluginName => {
    if (!/^[a-z][a-z0-9-]*$/.test(name)) {
      throw new Error(`Invalid plugin name: ${name}. Must be lowercase kebab-case.`)
    }
    return name as PluginName
  },
  
  validate: (name: string): name is PluginName => {
    return /^[a-z][a-z0-9-]*$/.test(name)
  }
}

export const ConfigId = {
  create: (id: string): ConfigId => {
    if (!/^config_[a-z0-9_]+$/.test(id)) {
      throw new Error(`Invalid config ID: ${id}. Must start with 'config_'.`)
    }
    return id as ConfigId
  },
  
  validate: (id: string): id is ConfigId => {
    return /^config_[a-z0-9_]+$/.test(id)
  }
}

export const RendererName = {
  create: (name: string): RendererName => {
    if (!/^[a-z][a-z0-9-]*$/.test(name)) {
      throw new Error(`Invalid renderer name: ${name}. Must be lowercase kebab-case.`)
    }
    return name as RendererName
  },
  
  validate: (name: string): name is RendererName => {
    return /^[a-z][a-z0-9-]*$/.test(name)
  }
}

/**
 * Strict semantic version type with validation
 */
export type SemanticVersion = string & { readonly __brand: 'SemanticVersion' }

export const SemanticVersion = {
  create: (version: string): SemanticVersion => {
    if (!/^\d+\.\d+\.\d+(?:-[a-zA-Z0-9-]+)?(?:\+[a-zA-Z0-9-]+)?$/.test(version)) {
      throw new Error(`Invalid semantic version: ${version}. Must follow semver format.`)
    }
    return version as SemanticVersion
  },
  
  validate: (version: string): version is SemanticVersion => {
    return /^\d+\.\d+\.\d+(?:-[a-zA-Z0-9-]+)?(?:\+[a-zA-Z0-9-]+)?$/.test(version)
  },
  
  compare: (a: SemanticVersion, b: SemanticVersion): -1 | 0 | 1 => {
    const [aMajor, aMinor, aPatch] = a.split('.').map(Number)
    const [bMajor, bMinor, bPatch] = b.split('.').map(Number)
    
    if (aMajor !== bMajor) return aMajor < bMajor ? -1 : 1
    if (aMinor !== bMinor) return aMinor < bMinor ? -1 : 1
    if (aPatch !== bPatch) return aPatch < bPatch ? -1 : 1
    return 0
  }
}

/**
 * Utility types for better type inference and safety
 */

/**
 * Makes all properties of T deeply readonly
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/**
 * Makes specific keys K of type T required
 */
export type RequireKeys<T, K extends keyof T> = T & Required<Pick<T, K>>

/**
 * Makes specific keys K of type T optional
 */
export type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/**
 * Extracts all string literal types from a union
 */
export type StringLiteralUnion<T> = T extends string ? T : never

/**
 * Creates a type that excludes null and undefined
 */
export type NonNullable<T> = T extends null | undefined ? never : T

/**
 * Ensures type T has at least one property
 */
export type NonEmpty<T> = T extends Record<string, never> ? never : T

/**
 * Enhanced error types with strict categorization
 */
export const enum ErrorSeverityLevel {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4
}

export const enum ErrorCategory {
  VALIDATION = 'validation',
  RENDER = 'render',
  CONFIG = 'config',
  PERMISSION = 'permission',
  NETWORK = 'network',
  SECURITY = 'security',
  PERFORMANCE = 'performance',
  MEMORY = 'memory',
  UNKNOWN = 'unknown'
}

/**
 * Enhanced renderer parameter types with strict typing
 */
export interface StrictRendererParams<TValue = unknown, TRow extends Record<string, unknown> = Record<string, unknown>> {
  readonly value: TValue
  readonly row: DeepReadonly<TRow>
  readonly column?: DeepReadonly<{ field?: string }>
  readonly rowIndex?: number
  readonly config: DeepReadonly<Record<string, unknown>>
  readonly configId: ConfigId
  readonly field?: string
}

/**
 * Type-safe renderer function with generic constraints
 */
export type TypedRenderer<TValue = unknown, TRow extends Record<string, unknown> = Record<string, unknown>> = 
  (params: StrictRendererParams<TValue, TRow>) => string

/**
 * Plugin configuration with strict validation
 */
export interface StrictPluginConfig {
  readonly name: PluginName
  readonly version: SemanticVersion
  readonly description?: string
  readonly dependencies?: readonly PluginName[]
  readonly peerDependencies?: readonly PluginName[]
  readonly metadata?: DeepReadonly<Record<string, unknown>>
}

/**
 * Enhanced plugin interface with strict typing
 */
// Deprecated - use PluginDefinition from injection.ts instead
export interface TypedPlugin extends StrictPluginConfig {
  readonly install: (manager: any) => void | Promise<void>
  readonly uninstall?: () => void | Promise<void>
  readonly onLoad?: () => void | Promise<void>
  readonly onUnload?: () => void | Promise<void>
}

/**
 * Type-safe plugin manager interface
 * @deprecated Use ModernPluginManager from core/ModernPluginManager.ts instead
 */
export interface PluginManager {
  register(plugin: TypedPlugin): Promise<void>
  unregister(name: PluginName): Promise<void>
  getPlugin(name: PluginName): TypedPlugin | undefined
  getInstalledPlugins(): readonly TypedPlugin[]
  hasPlugin(name: PluginName): boolean
  getRendererRegistry(): RendererRegistry
  getComponentRegistry(): ComponentRegistry
}

/**
 * Enhanced renderer registry with type safety
 */
export interface RendererRegistry {
  register<TValue = unknown, TRow extends Record<string, unknown> = Record<string, unknown>>(
    name: RendererName,
    renderer: TypedRenderer<TValue, TRow>
  ): void
  
  get<TValue = unknown, TRow extends Record<string, unknown> = Record<string, unknown>>(
    name: RendererName
  ): TypedRenderer<TValue, TRow> | undefined
  
  has(name: RendererName): boolean
  unregister(name: RendererName): boolean
  getAll(): ReadonlyMap<RendererName, TypedRenderer>
}

/**
 * Enhanced component registry with type safety
 */
export interface ComponentRegistry {
  register(name: ComponentName, component: Component): void
  get(name: ComponentName): Component | undefined
  has(name: ComponentName): boolean
  unregister(name: ComponentName): boolean
  getAll(): ReadonlyMap<ComponentName, Component>
}

/**
 * Column configuration with enhanced type safety
 */
export interface TypedColumnConfig<TValue = unknown, TRow extends Record<string, unknown> = Record<string, unknown>> {
  readonly field?: string
  readonly title: string
  readonly width?: number
  readonly fixed?: 'left' | 'right'
  readonly renderer?: RendererName
  readonly component?: ComponentName
  readonly config?: DeepReadonly<Record<string, unknown>>
  readonly validator?: (value: TValue, row: TRow) => boolean | string
}

/**
 * Performance metrics with strict typing
 */
export interface StrictPerformanceMetrics {
  readonly pluginName: PluginName
  readonly operationName: string
  readonly startTime: number
  readonly endTime?: number
  readonly duration?: number
  readonly memoryUsage?: number
  readonly metadata?: DeepReadonly<Record<string, unknown>>
}

/**
 * Error context with enhanced type information
 */
export interface StrictErrorContext {
  readonly pluginName?: PluginName
  readonly rendererName?: RendererName
  readonly componentName?: ComponentName
  readonly configId?: ConfigId
  readonly operationName?: string
  readonly metadata?: DeepReadonly<Record<string, unknown>>
}

/**
 * Enhanced error interface with strict typing
 */
export interface StrictPluginError extends Error {
  readonly category: ErrorCategory
  readonly severity: ErrorSeverityLevel
  readonly context: StrictErrorContext
  readonly timestamp: number
  readonly fingerprint: string
  readonly recoverable: boolean
  readonly securityImplications?: readonly string[]
}

/**
 * Type guards for runtime type checking
 */
export const TypeGuards = {
  isPluginName: (value: unknown): value is PluginName => {
    return typeof value === 'string' && PluginName.validate(value)
  },
  
  isConfigId: (value: unknown): value is ConfigId => {
    return typeof value === 'string' && ConfigId.validate(value)
  },
  
  isSemanticVersion: (value: unknown): value is SemanticVersion => {
    return typeof value === 'string' && SemanticVersion.validate(value)
  },
  
  isStrictPluginError: (error: unknown): error is StrictPluginError => {
    return error instanceof Error &&
           'category' in error &&
           'severity' in error &&
           'context' in error &&
           'timestamp' in error &&
           'fingerprint' in error &&
           'recoverable' in error
  },
  
  isTypedPlugin: (plugin: unknown): plugin is TypedPlugin => {
    if (!plugin || typeof plugin !== 'object') return false
    const p = plugin as any
    return TypeGuards.isPluginName(p.name) &&
           TypeGuards.isSemanticVersion(p.version) &&
           typeof p.install === 'function'
  }
} as const

/**
 * Utility functions for type-safe operations
 */
export const TypeUtils = {
  /**
   * Safely cast a value to a specific type with runtime validation
   */
  safeCast: <T>(value: unknown, guard: (value: unknown) => value is T): T => {
    if (!guard(value)) {
      throw new TypeError(`Invalid type cast: expected specific type, got ${typeof value}`)
    }
    return value
  },
  
  /**
   * Create a readonly proxy for deep immutability
   */
  deepFreeze: <T extends object>(obj: T): DeepReadonly<T> => {
    Object.freeze(obj)
    Object.values(obj).forEach(value => {
      if (typeof value === 'object' && value !== null) {
        TypeUtils.deepFreeze(value)
      }
    })
    return obj as DeepReadonly<T>
  },
  
  /**
   * Check if an object has all required properties
   */
  hasRequiredProps: <T extends object, K extends keyof T>(
    obj: T, 
    props: K[]
  ): obj is RequireKeys<T, K> => {
    return props.every(prop => prop in obj && obj[prop] !== undefined)
  },
  
  /**
   * Create a type-safe object with only specific keys
   */
  pick: <T extends object, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
    const result = {} as Pick<T, K>
    keys.forEach(key => {
      if (key in obj) {
        result[key] = obj[key]
      }
    })
    return result
  },
  
  /**
   * Create a type-safe object excluding specific keys
   */
  omit: <T extends object, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
    const result = { ...obj } as any
    keys.forEach(key => {
      delete result[key]
    })
    return result as Omit<T, K>
  }
} as const

/**
 * Type-safe configuration builder
 */
export class ConfigBuilder<T extends Record<string, unknown> = Record<string, unknown>> {
  private config: Partial<T> = {}
  
  set<K extends keyof T>(key: K, value: T[K]): ConfigBuilder<T> {
    this.config[key] = value
    return this
  }
  
  setIf<K extends keyof T>(condition: boolean, key: K, value: T[K]): ConfigBuilder<T> {
    if (condition) {
      this.config[key] = value
    }
    return this
  }
  
  merge(other: Partial<T>): ConfigBuilder<T> {
    Object.assign(this.config, other)
    return this
  }
  
  build(requiredKeys?: (keyof T)[]): T {
    if (requiredKeys && !TypeUtils.hasRequiredProps(this.config, requiredKeys)) {
      const missing = requiredKeys.filter(key => !(key in this.config))
      throw new Error(`Missing required configuration keys: ${missing.join(', ')}`)
    }
    return TypeUtils.deepFreeze({ ...this.config }) as T
  }
  
  validate(validator: (config: Partial<T>) => boolean | string): ConfigBuilder<T> {
    const result = validator(this.config)
    if (typeof result === 'string') {
      throw new Error(`Configuration validation failed: ${result}`)
    }
    if (!result) {
      throw new Error('Configuration validation failed')
    }
    return this
  }
}

/**
 * Factory functions for creating type-safe instances
 */
export const Factories = {
  createPlugin: (config: StrictPluginConfig & { 
    install: (manager: any) => void | Promise<void>
    uninstall?: () => void | Promise<void>
  }): TypedPlugin => {
    return TypeUtils.deepFreeze(config) as TypedPlugin
  },
  
  createRenderer: <TValue = unknown, TRow extends Record<string, unknown> = Record<string, unknown>>(
    name: string,
    renderer: TypedRenderer<TValue, TRow>
  ): { name: RendererName; renderer: TypedRenderer<TValue, TRow> } => {
    return {
      name: RendererName.create(name),
      renderer
    }
  },
  
  createColumn: <TValue = unknown, TRow extends Record<string, unknown> = Record<string, unknown>>(
    config: TypedColumnConfig<TValue, TRow>
  ): DeepReadonly<TypedColumnConfig<TValue, TRow>> => {
    return TypeUtils.deepFreeze(config)
  },
  
  createConfigBuilder: <T extends Record<string, unknown>>(): ConfigBuilder<T> => {
    return new ConfigBuilder<T>()
  }
} as const