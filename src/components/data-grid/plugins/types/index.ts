/**
 * Enhanced TypeScript types and utilities for the data grid plugin system
 * Provides comprehensive type safety, runtime validation, and developer experience improvements
 */

// Core enhanced types
export * from './enhanced-types'

// Development utilities and decorators
export * from './dev-utils'

// Runtime validation and schema validation
export * from './runtime-validation'

/**
 * Re-export commonly used types for convenience
 */
export type {
  PluginName,
  ConfigId,
  SemanticVersion,
  RendererName,
  ComponentName,
  ErrorCategory,
  ErrorSeverityLevel,
  TypedPlugin,
  StrictRendererParams,
  TypedRenderer,
  StrictPluginConfig,
  DeepReadonly,
  RequireKeys,
  PartialKeys,
  NonNullable,
  NonEmpty,
} from './enhanced-types'

export {
  TypeGuards,
  TypeUtils,
  Factories,
  ConfigBuilder,
} from './enhanced-types'

export {
  plugin,
  validateParams,
  monitor,
  handleErrors,
  deprecated,
  ConfigValidator,
  PluginBuilder,
  DevUtils,
  TypedEventEmitter,
} from './dev-utils'

export {
  RuntimeValidator,
  Schemas,
  Validators,
  Assertions,
  SafeConversions,
  DevValidation,
} from './runtime-validation'

/**
 * Version information for type system
 */
export const ENHANCED_TYPES_VERSION = '1.0.0'

// Plugin configuration types re-export
export type { CompositeColumnConfig } from '../renderers/composite/modern'
export type { StatusColumnConfig } from '../renderers/status/modern'
export type { BooleanColumnConfig } from '../renderers/boolean/modern'
export type { ActionsColumnConfig } from '../renderers/actions/modern'
export type { LinkColumnConfig } from '../renderers/link/modern'
export type { RatingColumnConfig } from '../renderers/rating/modern'

/**
 * Feature flags for type system capabilities
 */
export const FEATURES = {
  BRANDED_TYPES: true,
  RUNTIME_VALIDATION: true,
  DEVELOPMENT_UTILITIES: true,
  SCHEMA_VALIDATION: true,
  TYPE_GUARDS: true,
  DECORATORS: true,
  EVENT_EMITTER: true,
} as const
