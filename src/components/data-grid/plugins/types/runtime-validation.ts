/**
 * Runtime type validation utilities for enhanced type safety
 * Provides comprehensive runtime type checking and validation functions
 */

import type {
  PluginName,
  ConfigId,
  SemanticVersion,
  RendererName,
  ComponentName,
  ErrorCategory,
  ErrorSeverityLevel,
  TypedPlugin,
  StrictRendererParams,
  DeepReadonly
} from './enhanced-types'

/**
 * Runtime schema validation with detailed error reporting
 */
export interface ValidationResult {
  readonly valid: boolean
  readonly errors: readonly ValidationError[]
  readonly warnings: readonly ValidationWarning[]
}

export interface ValidationError {
  readonly path: string
  readonly message: string
  readonly code: string
  readonly value?: unknown
}

export interface ValidationWarning {
  readonly path: string
  readonly message: string
  readonly suggestion?: string
}

/**
 * Schema definition for runtime validation
 */
export interface SchemaDefinition {
  readonly type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function' | 'any'
  readonly required?: boolean
  readonly nullable?: boolean
  readonly default?: unknown
  readonly validator?: (value: unknown) => boolean | string
  readonly items?: SchemaDefinition // for arrays
  readonly properties?: Record<string, SchemaDefinition> // for objects
  readonly additionalProperties?: boolean | SchemaDefinition
  readonly minLength?: number
  readonly maxLength?: number
  readonly min?: number
  readonly max?: number
  readonly pattern?: RegExp
  readonly enum?: readonly unknown[]
  readonly description?: string
}

/**
 * Comprehensive runtime validator
 */
export class RuntimeValidator {
  private readonly path: string[] = []
  private readonly errors: ValidationError[] = []
  private readonly warnings: ValidationWarning[] = []
  
  constructor(private readonly strictMode = true) {}
  
  /**
   * Validate a value against a schema
   */
  validate(value: unknown, schema: SchemaDefinition, path = ''): ValidationResult {
    this.path.length = 0
    this.errors.length = 0
    this.warnings.length = 0
    
    if (path) {
      this.path.push(path)
    }
    
    this.validateValue(value, schema)
    
    return {
      valid: this.errors.length === 0,
      errors: [...this.errors],
      warnings: [...this.warnings]
    }
  }
  
  private validateValue(value: unknown, schema: SchemaDefinition): void {
    const currentPath = this.path.join('.')
    
    // Handle null/undefined
    if (value === null || value === undefined) {
      if (schema.required && !schema.nullable) {
        this.addError(currentPath, `Required field is missing`, 'REQUIRED_FIELD_MISSING', value)
        return
      }
      if (value === null && !schema.nullable) {
        this.addError(currentPath, `Field cannot be null`, 'NULL_NOT_ALLOWED', value)
        return
      }
      if (value === undefined && schema.default !== undefined) {
        value = schema.default
      }
      if (value === null || value === undefined) {
        return // Valid null/undefined
      }
    }
    
    // Type checking
    const actualType = this.getType(value)
    if (schema.type !== 'any' && actualType !== schema.type) {
      this.addError(
        currentPath,
        `Expected type '${schema.type}', got '${actualType}'`,
        'TYPE_MISMATCH',
        value
      )
      return
    }
    
    // Type-specific validation
    switch (schema.type) {
      case 'string':
        this.validateString(value as string, schema, currentPath)
        break
      case 'number':
        this.validateNumber(value as number, schema, currentPath)
        break
      case 'array':
        this.validateArray(value as unknown[], schema, currentPath)
        break
      case 'object':
        this.validateObject(value as Record<string, unknown>, schema, currentPath)
        break
    }
    
    // Custom validator
    if (schema.validator) {
      const result = schema.validator(value)
      if (typeof result === 'string') {
        this.addError(currentPath, result, 'CUSTOM_VALIDATION_FAILED', value)
      } else if (!result) {
        this.addError(currentPath, 'Custom validation failed', 'CUSTOM_VALIDATION_FAILED', value)
      }
    }
    
    // Enum validation
    if (schema.enum && !schema.enum.includes(value)) {
      this.addError(
        currentPath,
        `Value must be one of: ${schema.enum.join(', ')}`,
        'ENUM_VALIDATION_FAILED',
        value
      )
    }
  }
  
  private validateString(value: string, schema: SchemaDefinition, path: string): void {
    if (schema.minLength !== undefined && value.length < schema.minLength) {
      this.addError(path, `String too short (min: ${schema.minLength})`, 'STRING_TOO_SHORT', value)
    }
    
    if (schema.maxLength !== undefined && value.length > schema.maxLength) {
      this.addError(path, `String too long (max: ${schema.maxLength})`, 'STRING_TOO_LONG', value)
    }
    
    if (schema.pattern && !schema.pattern.test(value)) {
      this.addError(path, `String does not match pattern`, 'PATTERN_MISMATCH', value)
    }
  }
  
  private validateNumber(value: number, schema: SchemaDefinition, path: string): void {
    if (!isFinite(value)) {
      this.addError(path, 'Number must be finite', 'NUMBER_NOT_FINITE', value)
      return
    }
    
    if (schema.min !== undefined && value < schema.min) {
      this.addError(path, `Number too small (min: ${schema.min})`, 'NUMBER_TOO_SMALL', value)
    }
    
    if (schema.max !== undefined && value > schema.max) {
      this.addError(path, `Number too large (max: ${schema.max})`, 'NUMBER_TOO_LARGE', value)
    }
  }
  
  private validateArray(value: unknown[], schema: SchemaDefinition, path: string): void {
    if (schema.minLength !== undefined && value.length < schema.minLength) {
      this.addError(path, `Array too short (min: ${schema.minLength})`, 'ARRAY_TOO_SHORT', value)
    }
    
    if (schema.maxLength !== undefined && value.length > schema.maxLength) {
      this.addError(path, `Array too long (max: ${schema.maxLength})`, 'ARRAY_TOO_LONG', value)
    }
    
    if (schema.items) {
      value.forEach((item, index) => {
        this.path.push(`[${index}]`)
        this.validateValue(item, schema.items!)
        this.path.pop()
      })
    }
  }
  
  private validateObject(value: Record<string, unknown>, schema: SchemaDefinition, path: string): void {
    if (schema.properties) {
      // Validate defined properties
      for (const [key, propSchema] of Object.entries(schema.properties)) {
        this.path.push(key)
        this.validateValue(value[key], propSchema)
        this.path.pop()
      }
      
      // Check for additional properties
      if (schema.additionalProperties === false) {
        const allowedKeys = Object.keys(schema.properties)
        const extraKeys = Object.keys(value).filter(key => !allowedKeys.includes(key))
        
        if (extraKeys.length > 0) {
          if (this.strictMode) {
            extraKeys.forEach(key => {
              this.addError(`${path}.${key}`, 'Additional property not allowed', 'ADDITIONAL_PROPERTY', value[key])
            })
          } else {
            this.addWarning(
              path, 
              `Additional properties found: ${extraKeys.join(', ')}`,
              'Consider removing or defining these properties in the schema'
            )
          }
        }
      } else if (typeof schema.additionalProperties === 'object') {
        // Validate additional properties against schema
        const allowedKeys = Object.keys(schema.properties)
        Object.keys(value).forEach(key => {
          if (!allowedKeys.includes(key)) {
            this.path.push(key)
            this.validateValue(value[key], schema.additionalProperties as SchemaDefinition)
            this.path.pop()
          }
        })
      }
    }
  }
  
  private getType(value: unknown): string {
    if (value === null) return 'null'
    if (Array.isArray(value)) return 'array'
    return typeof value
  }
  
  private addError(path: string, message: string, code: string, value?: unknown): void {
    this.errors.push({ path, message, code, value })
  }
  
  private addWarning(path: string, message: string, suggestion?: string): void {
    this.warnings.push({ path, message, suggestion })
  }
}

/**
 * Predefined schemas for common plugin types
 */
export const Schemas = {
  pluginName: {
    type: 'string' as const,
    pattern: /^[a-z][a-z0-9-]*$/,
    description: 'Plugin name must be lowercase kebab-case'
  },
  
  semanticVersion: {
    type: 'string' as const,
    pattern: /^\d+\.\d+\.\d+(?:-[a-zA-Z0-9-]+)?(?:\+[a-zA-Z0-9-]+)?$/,
    description: 'Must follow semantic versioning format (e.g., 1.0.0)'
  },
  
  configId: {
    type: 'string' as const,
    pattern: /^config_[a-z0-9_]+$/,
    description: 'Config ID must start with config_ prefix'
  },
  
  plugin: {
    type: 'object' as const,
    required: true,
    properties: {
      name: { ...Schemas.pluginName, required: true },
      version: { ...Schemas.semanticVersion, required: true },
      description: { type: 'string' as const },
      dependencies: {
        type: 'array' as const,
        items: Schemas.pluginName
      },
      install: { type: 'function' as const, required: true },
      uninstall: { type: 'function' as const }
    },
    additionalProperties: false
  } as SchemaDefinition,
  
  rendererParams: {
    type: 'object' as const,
    required: true,
    properties: {
      value: { type: 'any' as const, required: true },
      row: { type: 'object' as const, required: true },
      column: {
        type: 'object' as const,
        properties: {
          field: { type: 'string' as const }
        }
      },
      rowIndex: { type: 'number' as const, min: 0 },
      config: { type: 'object' as const, required: true },
      configId: { ...Schemas.configId, required: true },
      field: { type: 'string' as const }
    },
    additionalProperties: false
  } as SchemaDefinition,
  
  columnConfig: {
    type: 'object' as const,
    properties: {
      field: { type: 'string' as const },
      title: { type: 'string' as const, required: true },
      width: { type: 'number' as const, min: 1 },
      fixed: { type: 'string' as const, enum: ['left', 'right'] },
      renderer: Schemas.pluginName,
      component: { type: 'string' as const },
      config: { type: 'object' as const }
    },
    additionalProperties: true
  } as SchemaDefinition
}

/**
 * Type-safe validation functions
 */
export const Validators = {
  /**
   * Validate plugin instance
   */
  validatePlugin: (plugin: unknown): ValidationResult => {
    const validator = new RuntimeValidator(true)
    return validator.validate(plugin, Schemas.plugin)
  },
  
  /**
   * Validate renderer parameters
   */
  validateRendererParams: (params: unknown): ValidationResult => {
    const validator = new RuntimeValidator(true)
    return validator.validate(params, Schemas.rendererParams)
  },
  
  /**
   * Validate column configuration
   */
  validateColumnConfig: (config: unknown): ValidationResult => {
    const validator = new RuntimeValidator(false) // Allow additional properties
    return validator.validate(config, Schemas.columnConfig)
  },
  
  /**
   * Create a custom validator with schema
   */
  createValidator: (schema: SchemaDefinition, strictMode = true) => {
    const validator = new RuntimeValidator(strictMode)
    return (value: unknown) => validator.validate(value, schema)
  },
  
  /**
   * Validate and throw on error
   */
  validateThrow: (value: unknown, schema: SchemaDefinition, strictMode = true): void => {
    const validator = new RuntimeValidator(strictMode)
    const result = validator.validate(value, schema)
    
    if (!result.valid) {
      const errorMessages = result.errors.map(err => `${err.path}: ${err.message}`).join('\n')
      throw new Error(`Validation failed:\n${errorMessages}`)
    }
  }
} as const

/**
 * Type assertion functions with runtime validation
 */
export const Assertions = {
  /**
   * Assert that a value is a valid plugin
   */
  assertPlugin: (value: unknown): asserts value is TypedPlugin => {
    const result = Validators.validatePlugin(value)
    if (!result.valid) {
      const errors = result.errors.map(e => e.message).join(', ')
      throw new TypeError(`Invalid plugin: ${errors}`)
    }
  },
  
  /**
   * Assert that a value is valid renderer parameters
   */
  assertRendererParams: (value: unknown): asserts value is StrictRendererParams => {
    const result = Validators.validateRendererParams(value)
    if (!result.valid) {
      const errors = result.errors.map(e => e.message).join(', ')
      throw new TypeError(`Invalid renderer parameters: ${errors}`)
    }
  },
  
  /**
   * Assert that a string is a valid plugin name
   */
  assertPluginName: (value: unknown): asserts value is PluginName => {
    if (typeof value !== 'string' || !/^[a-z][a-z0-9-]*$/.test(value)) {
      throw new TypeError(`Invalid plugin name: ${value}. Must be lowercase kebab-case.`)
    }
  },
  
  /**
   * Assert that a string is a valid semantic version
   */
  assertSemanticVersion: (value: unknown): asserts value is SemanticVersion => {
    if (typeof value !== 'string' || !/^\d+\.\d+\.\d+(?:-[a-zA-Z0-9-]+)?(?:\+[a-zA-Z0-9-]+)?$/.test(value)) {
      throw new TypeError(`Invalid semantic version: ${value}. Must follow semver format.`)
    }
  },
  
  /**
   * Assert that a string is a valid config ID
   */
  assertConfigId: (value: unknown): asserts value is ConfigId => {
    if (typeof value !== 'string' || !/^config_[a-z0-9_]+$/.test(value)) {
      throw new TypeError(`Invalid config ID: ${value}. Must start with 'config_'.`)
    }
  }
} as const

/**
 * Safe type conversion utilities
 */
export const SafeConversions = {
  /**
   * Safely convert to plugin name with validation
   */
  toPluginName: (value: unknown): PluginName | null => {
    try {
      Assertions.assertPluginName(value)
      return value
    } catch {
      return null
    }
  },
  
  /**
   * Safely convert to semantic version with validation
   */
  toSemanticVersion: (value: unknown): SemanticVersion | null => {
    try {
      Assertions.assertSemanticVersion(value)
      return value
    } catch {
      return null
    }
  },
  
  /**
   * Safely convert to config ID with validation
   */
  toConfigId: (value: unknown): ConfigId | null => {
    try {
      Assertions.assertConfigId(value)
      return value
    } catch {
      return null
    }
  },
  
  /**
   * Safely parse and validate JSON
   */
  parseJSON: <T>(json: string, schema?: SchemaDefinition): T | null => {
    try {
      const parsed = JSON.parse(json)
      if (schema) {
        const validator = new RuntimeValidator(true)
        const result = validator.validate(parsed, schema)
        if (!result.valid) {
          return null
        }
      }
      return parsed as T
    } catch {
      return null
    }
  }
} as const

/**
 * Development-time validation helpers
 */
export const DevValidation = {
  /**
   * Wrap a function with parameter validation
   */
  withValidation: <TArgs extends readonly unknown[], TReturn>(
    fn: (...args: TArgs) => TReturn,
    schemas: { readonly [K in keyof TArgs]: SchemaDefinition }
  ) => {
    return (...args: TArgs): TReturn => {
      if (import.meta.env.DEV) {
        args.forEach((arg, index) => {
          const schema = schemas[index]
          if (schema) {
            const validator = new RuntimeValidator(true)
            const result = validator.validate(arg, schema)
            if (!result.valid) {
              const errors = result.errors.map(e => e.message).join(', ')
              throw new TypeError(`Parameter ${index} validation failed: ${errors}`)
            }
          }
        })
      }
      return fn(...args)
    }
  },
  
  /**
   * Log validation results in development
   */
  logValidation: (result: ValidationResult, label?: string): void => {
    if (!import.meta.env.DEV) return
    
    const prefix = label ? `[Validation:${label}]` : '[Validation]'
    
    if (result.valid) {
      console.debug(`${prefix} ✅ Valid`)
    } else {
      console.error(`${prefix} ❌ Invalid:`)
      result.errors.forEach(error => {
        console.error(`  ${error.path}: ${error.message}`)
      })
    }
    
    if (result.warnings.length > 0) {
      console.warn(`${prefix} ⚠️ Warnings:`)
      result.warnings.forEach(warning => {
        console.warn(`  ${warning.path}: ${warning.message}`)
        if (warning.suggestion) {
          console.warn(`    Suggestion: ${warning.suggestion}`)
        }
      })
    }
  }
} as const