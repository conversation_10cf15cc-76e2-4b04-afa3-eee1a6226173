/**
 * Type-safe decorators and utilities for enhanced developer experience
 * Provides runtime type checking, validation, and developer-friendly error messages
 */

import { 
  TypeGuards,
  PluginName,
  SemanticVersion,
  ErrorCategory,
  ErrorSeverityLevel,
  StrictPluginError,
  TypedPlugin,
  DeepReadonly
} from './enhanced-types'

/**
 * Plugin metadata decorator for type safety and validation
 */
export function plugin(config: {
  name: string
  version: string
  description?: string
  dependencies?: string[]
}) {
  return function <T extends new (...args: any[]) => any>(constructor: T) {
    // Validate at class decoration time
    const pluginName = PluginName.create(config.name)
    const version = SemanticVersion.create(config.version)
    const dependencies = config.dependencies?.map(dep => PluginName.create(dep)) || []
    
    // Add metadata to the constructor
    Object.defineProperty(constructor, 'pluginMetadata', {
      value: Object.freeze({
        name: pluginName,
        version,
        description: config.description,
        dependencies
      }),
      writable: false,
      enumerable: true,
      configurable: false
    })
    
    // Ensure the class has required methods
    const prototype = constructor.prototype
    if (typeof prototype.install !== 'function') {
      throw new Error(`Plugin class ${constructor.name} must implement install() method`)
    }
    
    return constructor
  }
}

/**
 * Method decorator for validating plugin method parameters
 */
export function validateParams<T extends (...args: any[]) => any>(
  validators: {
    [K in keyof Parameters<T>]?: (value: Parameters<T>[K]) => boolean | string
  }
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function (...args: Parameters<T>): ReturnType<T> {
      // Validate each parameter
      Object.entries(validators).forEach(([index, validator]) => {
        const paramIndex = parseInt(index)
        const value = args[paramIndex]
        const result = validator!(value)
        
        if (typeof result === 'string') {
          throw new Error(`Parameter ${paramIndex} validation failed: ${result}`)
        }
        if (!result) {
          throw new Error(`Parameter ${paramIndex} validation failed`)
        }
      })
      
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * Performance monitoring decorator with type safety
 */
export function monitor(operationName?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const opName = operationName || propertyKey
    const className = target.constructor.name
    
    if (originalMethod.constructor.name === 'AsyncFunction') {
      descriptor.value = async function (...args: any[]) {
        const start = performance.now()
        try {
          const result = await originalMethod.apply(this, args)
          const duration = performance.now() - start
          
          if (import.meta.env.DEV) {
            console.debug(`[Monitor] ${className}.${opName} completed in ${duration.toFixed(2)}ms`)
          }
          
          return result
        } catch (error) {
          const duration = performance.now() - start
          console.error(`[Monitor] ${className}.${opName} failed after ${duration.toFixed(2)}ms:`, error)
          throw error
        }
      }
    } else {
      descriptor.value = function (...args: any[]) {
        const start = performance.now()
        try {
          const result = originalMethod.apply(this, args)
          const duration = performance.now() - start
          
          if (import.meta.env.DEV) {
            console.debug(`[Monitor] ${className}.${opName} completed in ${duration.toFixed(2)}ms`)
          }
          
          return result
        } catch (error) {
          const duration = performance.now() - start
          console.error(`[Monitor] ${className}.${opName} failed after ${duration.toFixed(2)}ms:`, error)
          throw error
        }
      }
    }
    
    return descriptor
  }
}

/**
 * Type-safe error handler decorator
 */
export function handleErrors(options: {
  category?: ErrorCategory
  severity?: ErrorSeverityLevel
  fallback?: any
  rethrow?: boolean
} = {}) {
  const { 
    category = ErrorCategory.UNKNOWN, 
    severity = ErrorSeverityLevel.MEDIUM,
    fallback = undefined,
    rethrow = true
  } = options
  
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const className = target.constructor.name
    
    descriptor.value = function (...args: any[]) {
      try {
        return originalMethod.apply(this, args)
      } catch (error) {
        const pluginError: StrictPluginError = Object.assign(
          error instanceof Error ? error : new Error(String(error)),
          {
            category,
            severity,
            context: {
              pluginName: (this.constructor as any).pluginMetadata?.name,
              operationName: propertyKey,
              metadata: { className, args: args.length }
            },
            timestamp: Date.now(),
            fingerprint: `${className}.${propertyKey}`,
            recoverable: severity < ErrorSeverityLevel.CRITICAL,
            securityImplications: category === ErrorCategory.SECURITY ? ['Security error in plugin operation'] : undefined
          }
        )
        
        if (import.meta.env.DEV) {
          console.error(`[Error] ${className}.${propertyKey}:`, pluginError)
        }
        
        if (rethrow) {
          throw pluginError
        }
        
        return fallback
      }
    }
    
    return descriptor
  }
}

/**
 * Deprecated method decorator with migration hints
 */
export function deprecated(options: {
  since?: string
  replacement?: string
  removeIn?: string
  reason?: string
}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const className = target.constructor.name
    
    descriptor.value = function (...args: any[]) {
      let warning = `⚠️  DEPRECATED: ${className}.${propertyKey} is deprecated`
      
      if (options.since) warning += ` since v${options.since}`
      if (options.removeIn) warning += ` and will be removed in v${options.removeIn}`
      if (options.replacement) warning += `. Use ${options.replacement} instead`
      if (options.reason) warning += `. Reason: ${options.reason}`
      
      console.warn(warning)
      
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * Type-safe configuration validator
 */
export class ConfigValidator<T extends Record<string, unknown>> {
  private rules: Map<keyof T, Array<(value: any) => boolean | string>> = new Map()
  
  addRule<K extends keyof T>(
    key: K,
    rule: (value: T[K]) => boolean | string,
    message?: string
  ): this {
    if (!this.rules.has(key)) {
      this.rules.set(key, [])
    }
    
    const wrappedRule = (value: any) => {
      const result = rule(value)
      if (typeof result === 'string') return result
      return result || (message || `Validation failed for ${String(key)}`)
    }
    
    this.rules.get(key)!.push(wrappedRule)
    return this
  }
  
  required<K extends keyof T>(key: K, message?: string): this {
    return this.addRule(
      key,
      (value) => value !== undefined && value !== null && value !== '',
      message || `${String(key)} is required`
    )
  }
  
  string<K extends keyof T>(key: K, options: {
    minLength?: number
    maxLength?: number
    pattern?: RegExp
  } = {}): this {
    return this.addRule(key, (value) => {
      if (typeof value !== 'string') return `${String(key)} must be a string`
      if (options.minLength && value.length < options.minLength) {
        return `${String(key)} must be at least ${options.minLength} characters`
      }
      if (options.maxLength && value.length > options.maxLength) {
        return `${String(key)} must be at most ${options.maxLength} characters`
      }
      if (options.pattern && !options.pattern.test(value)) {
        return `${String(key)} does not match required pattern`
      }
      return true
    })
  }
  
  number<K extends keyof T>(key: K, options: {
    min?: number
    max?: number
    integer?: boolean
  } = {}): this {
    return this.addRule(key, (value) => {
      const num = Number(value)
      if (isNaN(num)) return `${String(key)} must be a number`
      if (options.integer && !Number.isInteger(num)) {
        return `${String(key)} must be an integer`
      }
      if (options.min !== undefined && num < options.min) {
        return `${String(key)} must be at least ${options.min}`
      }
      if (options.max !== undefined && num > options.max) {
        return `${String(key)} must be at most ${options.max}`
      }
      return true
    })
  }
  
  validate(config: Partial<T>): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    for (const [key, rules] of this.rules.entries()) {
      const value = config[key]
      
      for (const rule of rules) {
        const result = rule(value)
        if (typeof result === 'string') {
          errors.push(result)
        }
      }
    }
    
    return { valid: errors.length === 0, errors }
  }
  
  validateThrow(config: Partial<T>): void {
    const { valid, errors } = this.validate(config)
    if (!valid) {
      throw new Error(`Configuration validation failed:\n${errors.join('\n')}`)
    }
  }
}

/**
 * Type-safe plugin builder with fluent API
 */
export class PluginBuilder {
  private config: Partial<{
    name: string
    version: string
    description: string
    dependencies: string[]
    install: (manager: any) => void | Promise<void>
    uninstall: () => void | Promise<void>
  }> = {}
  
  name(name: string): this {
    this.config.name = name
    return this
  }
  
  version(version: string): this {
    this.config.version = version
    return this
  }
  
  description(description: string): this {
    this.config.description = description
    return this
  }
  
  dependencies(...deps: string[]): this {
    this.config.dependencies = deps
    return this
  }
  
  install(fn: (manager: any) => void | Promise<void>): this {
    this.config.install = fn
    return this
  }
  
  uninstall(fn: () => void | Promise<void>): this {
    this.config.uninstall = fn
    return this
  }
  
  build(): TypedPlugin {
    if (!this.config.name || !this.config.version || !this.config.install) {
      throw new Error('Plugin must have name, version, and install function')
    }
    
    const plugin: TypedPlugin = {
      name: PluginName.create(this.config.name),
      version: SemanticVersion.create(this.config.version),
      description: this.config.description,
      install: this.config.install,
      uninstall: this.config.uninstall
    }
    
    return Object.freeze(plugin)
  }
}

/**
 * Development utilities for better debugging experience
 */
export const DevUtils = {
  /**
   * Create a type-safe plugin with validation
   */
  createPlugin: () => new PluginBuilder(),
  
  /**
   * Create a configuration validator
   */
  createValidator: <T extends Record<string, unknown>>() => new ConfigValidator<T>(),
  
  /**
   * Log type information in development
   */
  logType: <T>(value: T, label?: string): T => {
    if (import.meta.env.DEV) {
      const type = typeof value
      const constructor = (value as any)?.constructor?.name
      const info = constructor && constructor !== 'Object' ? `${type} (${constructor})` : type
      console.debug(`[Type] ${label || 'Value'}:`, info, value)
    }
    return value
  },
  
  /**
   * Assert type at runtime with helpful error messages
   */
  assertType: <T>(value: unknown, guard: (value: unknown) => value is T, message?: string): T => {
    if (!guard(value)) {
      const actualType = value?.constructor?.name || typeof value
      throw new TypeError(message || `Type assertion failed: expected specific type, got ${actualType}`)
    }
    return value
  },
  
  /**
   * Create a type-safe proxy for debugging property access
   */
  createDebugProxy: <T extends object>(obj: T, label?: string): T => {
    if (!import.meta.env.DEV) return obj
    
    return new Proxy(obj, {
      get(target, prop, receiver) {
        const value = Reflect.get(target, prop, receiver)
        console.debug(`[Access] ${label || 'Object'}.${String(prop)} ->`, typeof value)
        return value
      },
      set(target, prop, value, receiver) {
        console.debug(`[Set] ${label || 'Object'}.${String(prop)} <-`, typeof value)
        return Reflect.set(target, prop, value, receiver)
      }
    })
  }
} as const

/**
 * Type-safe event emitter for plugin communication
 */
export class TypedEventEmitter<TEvents extends Record<string, any[]>> {
  private listeners = new Map<keyof TEvents, Array<(...args: any[]) => void>>()
  
  on<K extends keyof TEvents>(event: K, listener: (...args: TEvents[K]) => void): this {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)!.push(listener)
    return this
  }
  
  off<K extends keyof TEvents>(event: K, listener: (...args: TEvents[K]) => void): this {
    const listeners = this.listeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index >= 0) {
        listeners.splice(index, 1)
      }
    }
    return this
  }
  
  emit<K extends keyof TEvents>(event: K, ...args: TEvents[K]): boolean {
    const listeners = this.listeners.get(event)
    if (!listeners || listeners.length === 0) {
      return false
    }
    
    listeners.forEach(listener => {
      try {
        listener(...args)
      } catch (error) {
        console.error(`[EventEmitter] Error in listener for ${String(event)}:`, error)
      }
    })
    
    return true
  }
  
  removeAllListeners<K extends keyof TEvents>(event?: K): this {
    if (event) {
      this.listeners.delete(event)
    } else {
      this.listeners.clear()
    }
    return this
  }
  
  listenerCount<K extends keyof TEvents>(event: K): number {
    return this.listeners.get(event)?.length || 0
  }
}