/**
 * 插件升级器
 * 升级现有插件到新格式或标准
 */

export interface UpgradeOptions {
  targetVersion?: string
  backupOriginal?: boolean
  dryRun?: boolean
  force?: boolean
  outputDir?: string
}

export interface UpgradeResult {
  success: boolean
  upgradedFiles: string[]
  skippedFiles: string[]
  errors: string[]
  warnings: string[]
  backupLocation?: string
}

export interface MigrationRule {
  name: string
  description: string
  fromVersion: string
  toVersion: string
  pattern: RegExp
  replacement: string | ((match: string, ...groups: string[]) => string)
  validate?: (content: string) => boolean
}

export class PluginUpgrader {
  private migrationRules: MigrationRule[] = []

  constructor() {
    this.initializeMigrationRules()
  }

  /**
   * 升级插件
   */
  async upgrade(args: string[]): Promise<void> {
    const options = this.parseUpgradeArgs(args)
    
    if (options.pluginPath) {
      await this.upgradeSinglePlugin(options.pluginPath, options)
    } else if (options.projectPath) {
      await this.upgradeProject(options.projectPath, options)
    } else {
      throw new Error('Must specify --plugin or --project path')
    }
  }

  /**
   * 升级单个插件
   */
  async upgradeSinglePlugin(pluginPath: string, options: UpgradeOptions = {}): Promise<UpgradeResult> {
    const result: UpgradeResult = {
      success: true,
      upgradedFiles: [],
      skippedFiles: [],
      errors: [],
      warnings: []
    }

    try {
      // 读取插件文件
      const originalContent = await this.readFile(pluginPath)
      
      // 检查是否需要升级
      const needsUpgrade = this.checkNeedsUpgrade(originalContent)
      if (!needsUpgrade && !options.force) {
        result.skippedFiles.push(pluginPath)
        console.log(`✓ ${pluginPath} is already up to date`)
        return result
      }

      // 备份原文件
      if (options.backupOriginal !== false) {
        const backupPath = await this.createBackup(pluginPath, originalContent)
        result.backupLocation = backupPath
      }

      // 执行升级
      const upgradedContent = await this.performUpgrade(originalContent, options)
      
      // 验证升级结果
      const validationResult = await this.validateUpgrade(originalContent, upgradedContent)
      if (!validationResult.valid) {
        result.errors.push(...validationResult.errors)
        result.warnings.push(...validationResult.warnings)
        result.success = false
        return result
      }

      // 写入升级后的内容
      if (!options.dryRun) {
        const outputPath = options.outputDir 
          ? `${options.outputDir}/${this.getFileName(pluginPath)}`
          : pluginPath
        
        await this.writeFile(outputPath, upgradedContent)
        result.upgradedFiles.push(outputPath)
        console.log(`✅ Upgraded: ${pluginPath}`)
      } else {
        console.log(`🔍 Dry run - would upgrade: ${pluginPath}`)
        console.log('--- Upgraded content preview ---')
        console.log(upgradedContent.substring(0, 500) + '...')
      }

    } catch (error) {
      result.errors.push(`Failed to upgrade ${pluginPath}: ${error}`)
      result.success = false
    }

    return result
  }

  /**
   * 升级项目中的所有插件
   */
  async upgradeProject(projectPath: string, options: UpgradeOptions = {}): Promise<UpgradeResult> {
    const result: UpgradeResult = {
      success: true,
      upgradedFiles: [],
      skippedFiles: [],
      errors: [],
      warnings: []
    }

    try {
      // 扫描项目中的插件文件
      const pluginFiles = await this.scanPluginFiles(projectPath)
      
      console.log(`Found ${pluginFiles.length} plugin files to check`)
      
      for (const file of pluginFiles) {
        const fileResult = await this.upgradeSinglePlugin(file, options)
        
        // 合并结果
        result.upgradedFiles.push(...fileResult.upgradedFiles)
        result.skippedFiles.push(...fileResult.skippedFiles)
        result.errors.push(...fileResult.errors)
        result.warnings.push(...fileResult.warnings)
        
        if (!fileResult.success) {
          result.success = false
        }
      }
      
      // 输出汇总报告
      this.outputUpgradeSummary(result)
      
    } catch (error) {
      result.errors.push(`Project upgrade failed: ${error}`)
      result.success = false
    }

    return result
  }

  /**
   * 检查是否需要升级
   */
  private checkNeedsUpgrade(content: string): boolean {
    // 检查是否包含旧版本的模式
    const oldPatterns = [
      /import.*from.*'@\/plugins\/old'/,  // 旧导入路径
      /createOldRenderer/,                 // 旧创建函数
      /\.deprecatedMethod\(/,              // 废弃方法
      /pluginVersion:\s*['"]1\./,          // 版本 1.x
      /Vue\.extend/,                       // Vue 2 语法
      /this\.\$createElement/,             // Vue 2 渲染函数
    ]

    return oldPatterns.some(pattern => pattern.test(content))
  }

  /**
   * 执行升级
   */
  private async performUpgrade(content: string, _options: UpgradeOptions): Promise<string> {
    let upgradedContent = content

    // 应用所有迁移规则
    for (const rule of this.migrationRules) {
      if (rule.validate && !rule.validate(upgradedContent)) {
        continue
      }

      if (typeof rule.replacement === 'string') {
        upgradedContent = upgradedContent.replace(rule.pattern, rule.replacement)
      } else {
        upgradedContent = upgradedContent.replace(rule.pattern, rule.replacement)
      }
    }

    // 执行特殊升级逻辑
    upgradedContent = await this.upgradeImports(upgradedContent)
    upgradedContent = await this.upgradePluginStructure(upgradedContent)
    upgradedContent = await this.upgradeVue3Compatibility(upgradedContent)
    upgradedContent = await this.upgradeTypeScript(upgradedContent)

    return upgradedContent
  }

  /**
   * 升级导入语句
   */
  private async upgradeImports(content: string): Promise<string> {
    const importMappings = {
      // 旧路径 -> 新路径
      '@/plugins/old': '@/components/data-grid/plugins',
      '@/plugins/renderers': '@/components/data-grid/plugins',
      '@/utils/pluginHelper': '@/components/data-grid/plugins',
    }

    let upgradedContent = content

    Object.entries(importMappings).forEach(([oldPath, newPath]) => {
      const pattern = new RegExp(`from\\s*['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g')
      upgradedContent = upgradedContent.replace(pattern, `from '${newPath}'`)
    })

    return upgradedContent
  }

  /**
   * 升级插件结构
   */
  private async upgradePluginStructure(content: string): Promise<string> {
    let upgradedContent = content

    // 转换旧的插件创建方式
    upgradedContent = upgradedContent.replace(
      /createOldRenderer\(\s*['"]([^'"]+)['"]\s*,\s*({[^}]+})\s*\)/g,
      'createSimpleRenderer(\'$1\', $2)'
    )

    // 转换旧的配置结构
    upgradedContent = upgradedContent.replace(
      /pluginConfig:\s*{([^}]+)}/g,
      'options: {$1}'
    )

    // 转换旧的渲染函数签名
    upgradedContent = upgradedContent.replace(
      /render:\s*\(([^,]+),\s*([^,]+),\s*([^)]+)\)\s*=>/g,
      'render: ($1, { config: $2, row: $3 }) =>'
    )

    return upgradedContent
  }

  /**
   * 升级 Vue 3 兼容性
   */
  private async upgradeVue3Compatibility(content: string): Promise<string> {
    let upgradedContent = content

    // 替换 Vue 2 的 createElement
    upgradedContent = upgradedContent.replace(
      /this\.\$createElement/g,
      'h'
    )

    // 替换 Vue.extend
    upgradedContent = upgradedContent.replace(
      /Vue\.extend\s*\(/g,
      'defineComponent('
    )

    // 添加必要的导入
    if (/\bh\b/.test(upgradedContent) && !/import.*\bh\b.*from.*vue/.test(upgradedContent)) {
      upgradedContent = `import { h } from 'vue'\n${upgradedContent}`
    }

    if (/defineComponent/.test(upgradedContent) && !/import.*defineComponent.*from.*vue/.test(upgradedContent)) {
      upgradedContent = upgradedContent.replace(
        /(import\s*{[^}]+)}\s*from\s*['"]vue['"]/,
        '$1, defineComponent} from \'vue\''
      )
    }

    return upgradedContent
  }

  /**
   * 升级 TypeScript 支持
   */
  private async upgradeTypeScript(content: string): Promise<string> {
    let upgradedContent = content

    // 添加类型导入
    const typeImports = []
    
    if (/createSimpleRenderer/.test(upgradedContent)) {
      typeImports.push('SimpleRendererOptions', 'RenderContext')
    }
    
    if (/createStatusRenderer/.test(upgradedContent)) {
      typeImports.push('StatusRendererOptions')
    }

    if (typeImports.length > 0) {
      const importLine = `import type { ${typeImports.join(', ')} } from '@/components/data-grid/plugins'\n`
      
      // 检查是否已经有类型导入
      if (!/import\s+type\s*{.*}\s*from.*plugins/.test(upgradedContent)) {
        upgradedContent = importLine + upgradedContent
      }
    }

    // 添加函数类型注解
    upgradedContent = upgradedContent.replace(
      /export\s+const\s+(\w+)\s*=\s*createSimpleRenderer/g,
      'export const $1: SimplePlugin = createSimpleRenderer'
    )

    return upgradedContent
  }

  /**
   * 验证升级结果
   */
  private async validateUpgrade(originalContent: string, upgradedContent: string): Promise<{
    valid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const result = {
      valid: true,
      errors: [] as string[],
      warnings: [] as string[]
    }

    try {
      // 基础语法检查
      if (!this.isValidJavaScript(upgradedContent)) {
        result.errors.push('Upgraded content contains syntax errors')
        result.valid = false
      }

      // 检查导入是否正确
      const imports = upgradedContent.match(/import.*from.*/g)
      if (imports) {
        imports.forEach(importLine => {
          if (importLine.includes('@/plugins/old')) {
            result.warnings.push('Still contains old import paths')
          }
        })
      }

      // 检查是否保留了核心功能
      if (originalContent.includes('render:') && !upgradedContent.includes('render:')) {
        result.errors.push('Render function was lost during upgrade')
        result.valid = false
      }

      // 检查插件名称是否保持一致
      const originalName = originalContent.match(/createSimpleRenderer\s*\(\s*['"]([^'"]+)['"]/)?.[1]
      const upgradedName = upgradedContent.match(/createSimpleRenderer\s*\(\s*['"]([^'"]+)['"]/)?.[1]
      
      if (originalName && upgradedName && originalName !== upgradedName) {
        result.warnings.push(`Plugin name changed from '${originalName}' to '${upgradedName}'`)
      }

    } catch (error) {
      result.errors.push(`Validation failed: ${error}`)
      result.valid = false
    }

    return result
  }

  /**
   * 初始化迁移规则
   */
  private initializeMigrationRules(): void {
    this.migrationRules = [
      {
        name: 'Update import paths',
        description: 'Update old plugin import paths to new structure',
        fromVersion: '1.0',
        toVersion: '2.0',
        pattern: /from\s*['"]@\/plugins\/old['"]/g,
        replacement: 'from \'@/components/data-grid/plugins\''
      },
      {
        name: 'Update renderer function name',
        description: 'Update old renderer function names',
        fromVersion: '1.0',
        toVersion: '2.0',
        pattern: /createOldRenderer/g,
        replacement: 'createSimpleRenderer'
      },
      {
        name: 'Update plugin configuration',
        description: 'Update plugin configuration structure',
        fromVersion: '1.0',
        toVersion: '2.0',
        pattern: /pluginConfig:\s*{/g,
        replacement: 'options: {'
      },
      {
        name: 'Update render function signature',
        description: 'Update render function parameters',
        fromVersion: '1.0',
        toVersion: '2.0',
        pattern: /render:\s*\(([^,]+),\s*([^,]+),\s*([^)]+)\)\s*=>/g,
        replacement: 'render: ($1, { config: $2, row: $3 }) =>'
      },
      {
        name: 'Remove deprecated methods',
        description: 'Remove deprecated methods',
        fromVersion: '1.0',
        toVersion: '2.0',
        pattern: /\.deprecatedMethod\([^)]*\)/g,
        replacement: ''
      }
    ]
  }

  /**
   * 扫描插件文件
   */
  private async scanPluginFiles(projectPath: string): Promise<string[]> {
    // 这里应该实现文件扫描逻辑
    // 暂时返回示例文件路径
    return [
      `${projectPath}/plugins/status.ts`,
      `${projectPath}/plugins/currency.ts`,
      `${projectPath}/plugins/custom.ts`
    ]
  }

  /**
   * 创建备份文件
   */
  private async createBackup(filePath: string, content: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const backupPath = `${filePath}.backup.${timestamp}`
    
    await this.writeFile(backupPath, content)
    console.log(`📋 Backup created: ${backupPath}`)
    
    return backupPath
  }

  /**
   * 读取文件
   */
  private async readFile(_filePath: string): Promise<string> {
    // 这里应该使用实际的文件系统 API
    // 暂时返回示例内容
    return `
      import { createOldRenderer } from '@/plugins/old'
      
      export const MyPlugin = createOldRenderer('my-plugin', {
        pluginConfig: {
          render: (value, config, row) => \`<span>\${value}</span>\`
        }
      })
    `
  }

  /**
   * 写入文件
   */
  private async writeFile(filePath: string, content: string): Promise<void> {
    // 这里应该使用实际的文件系统 API
    console.log(`Writing to: ${filePath}`)
    console.log(content)
  }

  /**
   * 获取文件名
   */
  private getFileName(filePath: string): string {
    return filePath.split('/').pop() || 'plugin.ts'
  }

  /**
   * 检查是否为有效的 JavaScript
   */
  private isValidJavaScript(content: string): boolean {
    try {
      // 简单的语法检查
      const braceCount = (content.match(/{/g) || []).length - (content.match(/}/g) || []).length
      const parenCount = (content.match(/\(/g) || []).length - (content.match(/\)/g) || []).length
      const bracketCount = (content.match(/\[/g) || []).length - (content.match(/\]/g) || []).length
      
      return braceCount === 0 && parenCount === 0 && bracketCount === 0
    } catch {
      return false
    }
  }

  /**
   * 输出升级汇总
   */
  private outputUpgradeSummary(result: UpgradeResult): void {
    console.log('\n📊 Upgrade Summary')
    console.log(`   Upgraded: ${result.upgradedFiles.length} files`)
    console.log(`   Skipped: ${result.skippedFiles.length} files`)
    console.log(`   Errors: ${result.errors.length}`)
    console.log(`   Warnings: ${result.warnings.length}`)
    
    if (result.errors.length > 0) {
      console.log('\n❌ Errors:')
      result.errors.forEach(error => console.log(`   ${error}`))
    }
    
    if (result.warnings.length > 0) {
      console.log('\n⚠️  Warnings:')
      result.warnings.forEach(warning => console.log(`   ${warning}`))
    }
    
    if (result.success) {
      console.log('\n🎉 Upgrade completed successfully!')
    } else {
      console.log('\n❌ Upgrade completed with errors')
    }
  }

  /**
   * 解析升级参数
   */
  private parseUpgradeArgs(args: string[]): any {
    const options: any = {
      backupOriginal: true,
      dryRun: false,
      force: false
    }

    for (let i = 0; i < args.length; i++) {
      const arg = args[i]
      const nextArg = args[i + 1]

      switch (arg) {
        case '--plugin':
        case '-p':
          options.pluginPath = nextArg
          i++
          break
        case '--project':
          options.projectPath = nextArg
          i++
          break
        case '--target-version':
        case '-v':
          options.targetVersion = nextArg
          i++
          break
        case '--output':
        case '-o':
          options.outputDir = nextArg
          i++
          break
        case '--no-backup':
          options.backupOriginal = false
          break
        case '--dry-run':
          options.dryRun = true
          break
        case '--force':
          options.force = true
          break
      }
    }

    return options
  }
}