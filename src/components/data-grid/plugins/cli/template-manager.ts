/**
 * CLI 模板管理器
 * 管理和组织 CLI 模板
 */

export interface TemplateDefinition {
  name: string
  description: string
  category: 'renderer' | 'service' | 'extension' | 'business'
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  tags: string[]
  author?: string
  version: string
  lastUpdated: string
  files: TemplateFile[]
  dependencies: string[]
  configuration: TemplateConfig
}

export interface TemplateFile {
  name: string
  path: string
  content: string
  type: 'plugin' | 'test' | 'docs' | 'types' | 'style'
  required: boolean
}

export interface TemplateConfig {
  variables: TemplateVariable[]
  prompts: TemplatePrompt[]
  validations: TemplateValidation[]
}

export interface TemplateVariable {
  name: string
  type: 'string' | 'boolean' | 'number' | 'select' | 'multiselect'
  description: string
  required: boolean
  defaultValue?: any
  options?: string[]
  validation?: string
}

export interface TemplatePrompt {
  name: string
  message: string
  type: 'input' | 'confirm' | 'list' | 'checkbox'
  choices?: string[]
  when?: string
  validate?: string
}

export interface TemplateValidation {
  field: string
  rule: 'required' | 'pattern' | 'custom'
  value: string
  message: string
}

export class CliTemplateManager {
  private templates: Map<string, TemplateDefinition> = new Map()
  private categories: Map<string, string[]> = new Map()

  constructor() {
    this.initializeBuiltInTemplates()
  }

  /**
   * 获取所有模板
   */
  getAllTemplates(): TemplateDefinition[] {
    return Array.from(this.templates.values())
  }

  /**
   * 根据名称获取模板
   */
  getTemplate(name: string): TemplateDefinition | undefined {
    return this.templates.get(name)
  }

  /**
   * 根据分类获取模板
   */
  getTemplatesByCategory(category: string): TemplateDefinition[] {
    return Array.from(this.templates.values())
      .filter(template => template.category === category)
  }

  /**
   * 根据标签搜索模板
   */
  searchTemplatesByTag(tag: string): TemplateDefinition[] {
    return Array.from(this.templates.values())
      .filter(template => template.tags.includes(tag))
  }

  /**
   * 搜索模板
   */
  searchTemplates(query: string): TemplateDefinition[] {
    const searchTerm = query.toLowerCase()
    return Array.from(this.templates.values())
      .filter(template => 
        template.name.toLowerCase().includes(searchTerm) ||
        template.description.toLowerCase().includes(searchTerm) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
  }

  /**
   * 添加自定义模板
   */
  addTemplate(template: TemplateDefinition): void {
    this.templates.set(template.name, template)
    this.updateCategories()
  }

  /**
   * 删除模板
   */
  removeTemplate(name: string): boolean {
    const deleted = this.templates.delete(name)
    if (deleted) {
      this.updateCategories()
    }
    return deleted
  }

  /**
   * 验证模板
   */
  validateTemplate(template: TemplateDefinition): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // 基础验证
    if (!template.name) {
      errors.push('Template must have a name')
    }

    if (!template.description) {
      errors.push('Template must have a description')
    }

    if (!template.version) {
      errors.push('Template must have a version')
    }

    // 文件验证
    if (!template.files || template.files.length === 0) {
      errors.push('Template must have at least one file')
    } else {
      template.files.forEach((file, index) => {
        if (!file.name) {
          errors.push(`File ${index + 1} must have a name`)
        }
        if (!file.content) {
          errors.push(`File ${index + 1} must have content`)
        }
      })
    }

    // 配置验证
    if (template.configuration) {
      template.configuration.variables.forEach((variable, index) => {
        if (!variable.name) {
          errors.push(`Variable ${index + 1} must have a name`)
        }
        if (!variable.type) {
          errors.push(`Variable ${index + 1} must have a type`)
        }
      })
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 渲染模板
   */
  renderTemplate(templateName: string, variables: Record<string, any>): TemplateFile[] {
    const template = this.getTemplate(templateName)
    if (!template) {
      throw new Error(`Template '${templateName}' not found`)
    }

    return template.files.map(file => ({
      ...file,
      content: this.interpolateTemplate(file.content, variables),
      name: this.interpolateTemplate(file.name, variables),
      path: this.interpolateTemplate(file.path, variables)
    }))
  }

  /**
   * 获取模板变量
   */
  getTemplateVariables(templateName: string): TemplateVariable[] {
    const template = this.getTemplate(templateName)
    return template?.configuration.variables || []
  }

  /**
   * 获取模板提示
   */
  getTemplatePrompts(templateName: string): TemplatePrompt[] {
    const template = this.getTemplate(templateName)
    return template?.configuration.prompts || []
  }

  /**
   * 列出所有分类
   */
  getCategories(): string[] {
    return Array.from(this.categories.keys())
  }

  /**
   * 获取分类中的模板数量
   */
  getCategoryCount(category: string): number {
    return this.categories.get(category)?.length || 0
  }

  /**
   * 导出模板
   */
  exportTemplate(templateName: string): string {
    const template = this.getTemplate(templateName)
    if (!template) {
      throw new Error(`Template '${templateName}' not found`)
    }
    return JSON.stringify(template, null, 2)
  }

  /**
   * 导入模板
   */
  importTemplate(templateJson: string): void {
    try {
      const template = JSON.parse(templateJson) as TemplateDefinition
      const validation = this.validateTemplate(template)
      
      if (!validation.valid) {
        throw new Error(`Invalid template: ${validation.errors.join(', ')}`)
      }
      
      this.addTemplate(template)
    } catch (error) {
      throw new Error(`Failed to import template: ${error}`)
    }
  }

  /**
   * 克隆模板
   */
  cloneTemplate(templateName: string, newName: string): TemplateDefinition {
    const template = this.getTemplate(templateName)
    if (!template) {
      throw new Error(`Template '${templateName}' not found`)
    }

    const clonedTemplate: TemplateDefinition = {
      ...template,
      name: newName,
      version: '1.0.0',
      lastUpdated: new Date().toISOString(),
      author: 'Custom'
    }

    this.addTemplate(clonedTemplate)
    return clonedTemplate
  }

  /**
   * 插值模板字符串
   */
  private interpolateTemplate(template: string, variables: Record<string, any>): string {
    let result = template

    // 替换变量 {{variableName}}
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
      result = result.replace(regex, String(value))
    })

    // 处理条件块 {{#if condition}} ... {{/if}}
    result = result.replace(/\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g, (match, condition, content) => {
      return variables[condition] ? content : ''
    })

    // 处理循环块 {{#each items}} ... {{/each}}
    result = result.replace(/\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g, (match, arrayName, content) => {
      const array = variables[arrayName]
      if (!Array.isArray(array)) return ''
      
      return array.map(item => {
        let itemContent = content
        if (typeof item === 'object') {
          Object.entries(item).forEach(([key, value]) => {
            itemContent = itemContent.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), String(value))
          })
        } else {
          itemContent = itemContent.replace(/\{\{this\}\}/g, String(item))
        }
        return itemContent
      }).join('')
    })

    // 处理助手函数 {{helper arg1 arg2}}
    result = result.replace(/\{\{(\w+)\s+([^}]+)\}\}/g, (match, helperName, args) => {
      return this.callTemplateHelper(helperName, args.split(' '), variables)
    })

    return result
  }

  /**
   * 调用模板助手函数
   */
  private callTemplateHelper(helperName: string, args: string[], variables: Record<string, any>): string {
    switch (helperName) {
      case 'pascalCase':
        return this.toPascalCase(args[0] || '')
      case 'camelCase':
        return this.toCamelCase(args[0] || '')
      case 'kebabCase':
        return this.toKebabCase(args[0] || '')
      case 'upperCase':
        return (args[0] || '').toUpperCase()
      case 'lowerCase':
        return (args[0] || '').toLowerCase()
      case 'timestamp':
        return new Date().toISOString()
      case 'date':
        return new Date().toLocaleDateString()
      case 'uuid':
        return this.generateUUID()
      default:
        return `{{${helperName} ${args.join(' ')}}}`
    }
  }

  /**
   * 更新分类映射
   */
  private updateCategories(): void {
    this.categories.clear()
    
    Array.from(this.templates.values()).forEach(template => {
      const category = template.category
      if (!this.categories.has(category)) {
        this.categories.set(category, [])
      }
      this.categories.get(category)!.push(template.name)
    })
  }

  /**
   * 初始化内置模板
   */
  private initializeBuiltInTemplates(): void {
    // 状态渲染器模板
    this.addTemplate({
      name: 'status-renderer',
      description: '状态渲染器模板，用于显示不同状态的徽章',
      category: 'renderer',
      difficulty: 'beginner',
      tags: ['status', 'badge', 'ui'],
      author: 'DataGrid Team',
      version: '1.0.0',
      lastUpdated: new Date().toISOString(),
      dependencies: [],
      files: [
        {
          name: '{{pluginName}}.ts',
          path: './{{pluginName}}.ts',
          content: `import { createStatusRenderer } from '@/components/data-grid/plugins'

/**
 * {{description}}
 */
export const {{pascalCase pluginName}}Plugin = createStatusRenderer('{{pluginName}}', {
  statusMap: {
    {{#each statusMap}}
    '{{key}}': { text: '{{text}}', type: '{{type}}' },
    {{/each}}
  },
  variant: '{{variant}}',
  showIcon: {{showIcon}}
})

export default {{pascalCase pluginName}}Plugin`,
          type: 'plugin',
          required: true
        },
        {
          name: '{{pluginName}}.test.ts',
          path: './{{pluginName}}.test.ts',
          content: `import { {{pascalCase pluginName}}Plugin } from './{{pluginName}}'
import { validateSimplePlugin } from '@/components/data-grid/plugins'

describe('{{pascalCase pluginName}}Plugin', () => {
  test('plugin is valid', () => {
    const validation = validateSimplePlugin({{pascalCase pluginName}}Plugin)
    expect(validation.valid).toBe(true)
  })

  test('plugin has correct configuration', () => {
    expect({{pascalCase pluginName}}Plugin.name).toBe('{{pluginName}}')
    expect({{pascalCase pluginName}}Plugin.options.statusMap).toBeDefined()
  })
})`,
          type: 'test',
          required: false
        },
        {
          name: '{{pluginName}}.md',
          path: './{{pluginName}}.md',
          content: `# {{pascalCase pluginName}} 插件

{{description}}

## 使用方法

\`\`\`typescript
import { {{pascalCase pluginName}}Plugin } from './{{pluginName}}'

// 在数据网格中使用
{ ...column.{{camelCase pluginName}}('status', '状态') }
\`\`\`

## 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| variant | string | '{{variant}}' | 显示样式 |
| showIcon | boolean | {{showIcon}} | 是否显示图标 |`,
          type: 'docs',
          required: false
        }
      ],
      configuration: {
        variables: [
          {
            name: 'pluginName',
            type: 'string',
            description: '插件名称',
            required: true,
            validation: '^[a-z][a-z0-9-]*$'
          },
          {
            name: 'description',
            type: 'string',
            description: '插件描述',
            required: true,
            defaultValue: '状态渲染器插件'
          },
          {
            name: 'variant',
            type: 'select',
            description: '显示样式',
            required: false,
            defaultValue: 'badge',
            options: ['badge', 'text', 'dot']
          },
          {
            name: 'showIcon',
            type: 'boolean',
            description: '是否显示图标',
            required: false,
            defaultValue: true
          },
          {
            name: 'statusMap',
            type: 'multiselect',
            description: '状态映射',
            required: true,
            options: ['active', 'inactive', 'pending', 'error', 'success']
          }
        ],
        prompts: [
          {
            name: 'pluginName',
            message: '请输入插件名称 (kebab-case):',
            type: 'input',
            validate: '^[a-z][a-z0-9-]*$'
          },
          {
            name: 'description',
            message: '请输入插件描述:',
            type: 'input'
          },
          {
            name: 'variant',
            message: '请选择显示样式:',
            type: 'list',
            choices: ['badge', 'text', 'dot']
          },
          {
            name: 'showIcon',
            message: '是否显示图标?',
            type: 'confirm'
          },
          {
            name: 'statusOptions',
            message: '请选择需要的状态选项:',
            type: 'checkbox',
            choices: ['active', 'inactive', 'pending', 'error', 'success']
          }
        ],
        validations: [
          {
            field: 'pluginName',
            rule: 'pattern',
            value: '^[a-z][a-z0-9-]*$',
            message: '插件名称必须使用 kebab-case 格式'
          },
          {
            field: 'description',
            rule: 'required',
            value: '',
            message: '插件描述不能为空'
          }
        ]
      }
    })

    // 自定义渲染器模板
    this.addTemplate({
      name: 'custom-renderer',
      description: '通用自定义渲染器模板',
      category: 'renderer',
      difficulty: 'intermediate',
      tags: ['custom', 'renderer'],
      author: 'DataGrid Team',
      version: '1.0.0',
      lastUpdated: new Date().toISOString(),
      dependencies: [],
      files: [
        {
          name: '{{pluginName}}.ts',
          path: './{{pluginName}}.ts',
          content: `import { createSimpleRenderer } from '@/components/data-grid/plugins'

/**
 * {{description}}
 */
export const {{pascalCase pluginName}}Plugin = createSimpleRenderer('{{pluginName}}', {
  render: (value, { config, row }) => {
    // 自定义渲染逻辑
    return \`<span class="{{pluginName}}-cell">\${value}</span>\`
  },
  
  {{#if hasStyles}}
  styles: \`
    .{{pluginName}}-cell {
      /* 自定义样式 */
      display: inline-block;
      padding: 2px 6px;
      border-radius: 4px;
    }
  \`,
  {{/if}}
  
  {{#if hasConfig}}
  defaultConfig: {
    theme: 'default'
  },
  {{/if}}
  
  {{#if hasValidation}}
  validation: {
    theme: { type: 'string', enum: ['default', 'primary'] }
  },
  {{/if}}
  
  defaultWidth: {{defaultWidth}}
})

export default {{pascalCase pluginName}}Plugin`,
          type: 'plugin',
          required: true
        }
      ],
      configuration: {
        variables: [
          {
            name: 'pluginName',
            type: 'string',
            description: '插件名称',
            required: true
          },
          {
            name: 'description',
            type: 'string',
            description: '插件描述',
            required: true
          },
          {
            name: 'hasStyles',
            type: 'boolean',
            description: '是否包含样式',
            required: false,
            defaultValue: true
          },
          {
            name: 'hasConfig',
            type: 'boolean',
            description: '是否包含配置',
            required: false,
            defaultValue: true
          },
          {
            name: 'hasValidation',
            type: 'boolean',
            description: '是否包含验证',
            required: false,
            defaultValue: false
          },
          {
            name: 'defaultWidth',
            type: 'number',
            description: '默认宽度',
            required: false,
            defaultValue: 120
          }
        ],
        prompts: [],
        validations: []
      }
    })

    // 业务组件模板
    this.addTemplate({
      name: 'business-component',
      description: '业务特定组件模板',
      category: 'business',
      difficulty: 'advanced',
      tags: ['business', 'component'],
      author: 'DataGrid Team',
      version: '1.0.0',
      lastUpdated: new Date().toISOString(),
      dependencies: ['vue', '@vueuse/core'],
      files: [
        {
          name: '{{componentName}}.vue',
          path: './{{componentName}}.vue',
          content: `<template>
  <div class="{{kebabCase componentName}}">
    <!-- 业务组件内容 -->
    <slot />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  // 组件属性
}

const props = withDefaults(defineProps<Props>(), {
  // 默认值
})

const emit = defineEmits<{
  // 事件定义
}>()

// 组件逻辑
</script>

<style scoped>
.{{kebabCase componentName}} {
  /* 组件样式 */
}
</style>`,
          type: 'plugin',
          required: true
        }
      ],
      configuration: {
        variables: [
          {
            name: 'componentName',
            type: 'string',
            description: '组件名称',
            required: true
          }
        ],
        prompts: [],
        validations: []
      }
    })

    this.updateCategories()
  }

  // 助手函数
  private toPascalCase(str: string): string {
    return str
      .replace(/[-_](.)/g, (_, char) => char.toUpperCase())
      .replace(/^./, char => char.toUpperCase())
  }

  private toCamelCase(str: string): string {
    return str
      .replace(/[-_](.)/g, (_, char) => char.toUpperCase())
      .replace(/^./, char => char.toLowerCase())
  }

  private toKebabCase(str: string): string {
    return str
      .replace(/([A-Z])/g, '-$1')
      .toLowerCase()
      .replace(/^-/, '')
  }

  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
}