/**
 * 文档生成器
 * 自动生成插件的综合文档
 */

import type { SimplePlugin } from '../simple'

export interface DocsOptions {
  format?: 'markdown' | 'html' | 'json'
  includeExamples?: boolean
  includeApi?: boolean
  includeUsage?: boolean
  outputDir?: string
  template?: string
  language?: 'zh' | 'en'
}

export interface GeneratedDocs {
  content: string
  filePath: string
  format: string
  metadata: {
    title: string
    description: string
    version: string
    lastUpdated: string
    pluginName: string
  }
}

export interface PluginAnalysis {
  name: string
  type: 'simple' | 'full' | 'unknown'
  hasRender: boolean
  hasStyles: boolean
  hasValidation: boolean
  configOptions: ConfigOption[]
  examples: string[]
  dependencies: string[]
}

export interface ConfigOption {
  name: string
  type: string
  required: boolean
  defaultValue?: any
  description?: string
  enumValues?: string[]
}

export class DocumentationGenerator {
  private templates: Map<string, string> = new Map()

  constructor() {
    this.initializeTemplates()
  }

  /**
   * 生成文档
   */
  async generate(args: string[]): Promise<void> {
    const options = this.parseDocsArgs(args)
    
    if (options.pluginPath) {
      await this.generateSinglePluginDocs(options.pluginPath, options)
    } else if (options.projectPath) {
      await this.generateProjectDocs(options.projectPath, options)
    } else {
      throw new Error('Must specify --plugin or --project path')
    }
  }

  /**
   * 生成单个插件文档
   */
  async generateSinglePluginDocs(pluginPath: string, options: DocsOptions = {}): Promise<GeneratedDocs> {
    try {
      // 分析插件
      const plugin = await this.loadPlugin(pluginPath)
      const analysis = this.analyzePlugin(plugin)
      
      // 生成文档内容
      const content = await this.generateDocumentationContent(analysis, options)
      
      // 构建文档元数据
      const metadata = {
        title: `${analysis.name} 插件文档`,
        description: `${analysis.name} 插件的完整使用指南和 API 参考`,
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        pluginName: analysis.name
      }

      // 确定输出路径
      const fileName = `${analysis.name}.${options.format || 'md'}`
      const filePath = options.outputDir 
        ? `${options.outputDir}/${fileName}`
        : fileName

      const docs: GeneratedDocs = {
        content,
        filePath,
        format: options.format || 'markdown',
        metadata
      }

      // 写入文档文件
      await this.writeDocFile(docs)
      
      console.log(`📖 Generated documentation: ${filePath}`)
      return docs

    } catch (error) {
      throw new Error(`Failed to generate docs for ${pluginPath}: ${error}`)
    }
  }

  /**
   * 生成项目文档
   */
  async generateProjectDocs(projectPath: string, options: DocsOptions = {}): Promise<GeneratedDocs[]> {
    const results: GeneratedDocs[] = []
    
    try {
      // 扫描项目中的插件
      const pluginFiles = await this.scanPluginFiles(projectPath)
      
      console.log(`Found ${pluginFiles.length} plugins to document`)
      
      // 为每个插件生成文档
      for (const pluginFile of pluginFiles) {
        try {
          const docs = await this.generateSinglePluginDocs(pluginFile, options)
          results.push(docs)
        } catch (error) {
          console.error(`Failed to generate docs for ${pluginFile}:`, error)
        }
      }
      
      // 生成索引文档
      if (results.length > 0) {
        const indexDocs = await this.generateIndexDocs(results, options)
        results.push(indexDocs)
      }
      
      console.log(`📚 Generated ${results.length} documentation files`)
      
    } catch (error) {
      throw new Error(`Failed to generate project docs: ${error}`)
    }
    
    return results
  }

  /**
   * 加载插件
   */
  private async loadPlugin(pluginPath: string): Promise<any> {
    try {
      // 动态导入插件
      const pluginModule = await import(pluginPath)
      return pluginModule.default || pluginModule
    } catch (error) {
      throw new Error(`Cannot load plugin from ${pluginPath}: ${error}`)
    }
  }

  /**
   * 分析插件
   */
  private analyzePlugin(plugin: any): PluginAnalysis {
    const analysis: PluginAnalysis = {
      name: plugin.name || 'unknown',
      type: this.determinePluginType(plugin),
      hasRender: false,
      hasStyles: false,
      hasValidation: false,
      configOptions: [],
      examples: [],
      dependencies: []
    }

    // 分析简化插件
    if (this.isSimplePlugin(plugin)) {
      this.analyzeSimplePlugin(plugin, analysis)
    } else {
      this.analyzeFullPlugin(plugin, analysis)
    }

    return analysis
  }

  /**
   * 分析简化插件
   */
  private analyzeSimplePlugin(plugin: SimplePlugin, analysis: PluginAnalysis): void {
    if (plugin.options) {
      analysis.hasRender = typeof plugin.options.render === 'function'
      analysis.hasStyles = !!plugin.options.styles
      analysis.hasValidation = !!plugin.options.validation

      // 分析配置选项
      if (plugin.options.defaultConfig) {
        analysis.configOptions = this.extractConfigOptions(plugin.options.defaultConfig)
      }

      // 生成基础使用示例
      analysis.examples = this.generateSimplePluginExamples(plugin)
    }
  }

  /**
   * 分析完整插件
   */
  private analyzeFullPlugin(plugin: any, analysis: PluginAnalysis): void {
    // 检查组件
    if (plugin.components) {
      Object.keys(plugin.components).forEach(componentName => {
        analysis.dependencies.push(componentName)
      })
    }

    // 检查列助手方法
    if (plugin.columnHelperMethods) {
      analysis.examples = this.generateFullPluginExamples(plugin)
    }
  }

  /**
   * 生成文档内容
   */
  private async generateDocumentationContent(analysis: PluginAnalysis, options: DocsOptions): Promise<string> {
    const template = this.getTemplate(options.template || 'default')
    const language = options.language || 'zh'
    
    let content = template

    // 替换模板变量
    content = content.replace(/\{\{pluginName\}\}/g, analysis.name)
    content = content.replace(/\{\{pluginType\}\}/g, analysis.type)
    content = content.replace(/\{\{lastUpdated\}\}/g, new Date().toLocaleDateString())

    // 生成各个部分
    if (options.includeApi !== false) {
      content = content.replace(/\{\{apiReference\}\}/g, this.generateApiReference(analysis, language))
    }

    if (options.includeUsage !== false) {
      content = content.replace(/\{\{usageExamples\}\}/g, this.generateUsageExamples(analysis, language))
    }

    if (options.includeExamples !== false) {
      content = content.replace(/\{\{codeExamples\}\}/g, this.generateCodeExamples(analysis, language))
    }

    content = content.replace(/\{\{configOptions\}\}/g, this.generateConfigOptions(analysis, language))
    content = content.replace(/\{\{features\}\}/g, this.generateFeatures(analysis, language))

    return this.formatContent(content, options.format || 'markdown')
  }

  /**
   * 生成 API 参考
   */
  private generateApiReference(analysis: PluginAnalysis, language: 'zh' | 'en'): string {
    const texts = {
      zh: {
        title: '## API 参考',
        pluginName: '### 插件名称',
        pluginType: '### 插件类型',
        features: '### 功能特性',
        hasRender: '✅ 支持自定义渲染',
        noRender: '❌ 不支持自定义渲染',
        hasStyles: '✅ 支持自定义样式',
        noStyles: '❌ 不支持自定义样式',
        hasValidation: '✅ 支持配置验证',
        noValidation: '❌ 不支持配置验证'
      },
      en: {
        title: '## API Reference',
        pluginName: '### Plugin Name',
        pluginType: '### Plugin Type',
        features: '### Features',
        hasRender: '✅ Custom rendering supported',
        noRender: '❌ Custom rendering not supported',
        hasStyles: '✅ Custom styles supported',
        noStyles: '❌ Custom styles not supported',
        hasValidation: '✅ Config validation supported',
        noValidation: '❌ Config validation not supported'
      }
    }

    const t = texts[language]

    return `
${t.title}

${t.pluginName}
\`${analysis.name}\`

${t.pluginType}
\`${analysis.type}\`

${t.features}
- ${analysis.hasRender ? t.hasRender : t.noRender}
- ${analysis.hasStyles ? t.hasStyles : t.noStyles}
- ${analysis.hasValidation ? t.hasValidation : t.noValidation}
`
  }

  /**
   * 生成使用示例
   */
  private generateUsageExamples(analysis: PluginAnalysis, language: 'zh' | 'en'): string {
    const texts = {
      zh: {
        title: '## 使用示例',
        basicUsage: '### 基础用法',
        advancedUsage: '### 高级用法'
      },
      en: {
        title: '## Usage Examples',
        basicUsage: '### Basic Usage',
        advancedUsage: '### Advanced Usage'
      }
    }

    const t = texts[language]

    let content = `${t.title}\n\n${t.basicUsage}\n\n`

    // 基础用法示例
    if (analysis.type === 'simple') {
      content += `\`\`\`typescript
import { ${this.toPascalCase(analysis.name)}Plugin } from './${analysis.name}'

// 在数据网格中使用
const columns = [
  { field: 'name', title: '名称' },
  { ...column.${analysis.name.replace(/-/g, '_')}('field', '标题') }
]
\`\`\`\n\n`
    }

    // 高级用法示例
    if (analysis.configOptions.length > 0) {
      content += `${t.advancedUsage}\n\n`
      content += `\`\`\`typescript
// 自定义配置
{ ...column.${analysis.name.replace(/-/g, '_')}('field', '标题', {
  // 配置选项
${analysis.configOptions.map(opt => `  ${opt.name}: ${this.getExampleValue(opt)}`).join(',\n')}
}) }\`\`\`\n\n`
    }

    return content
  }

  /**
   * 生成代码示例
   */
  private generateCodeExamples(analysis: PluginAnalysis, language: 'zh' | 'en'): string {
    const texts = {
      zh: {
        title: '## 代码示例',
        complete: '### 完整示例'
      },
      en: {
        title: '## Code Examples',
        complete: '### Complete Example'
      }
    }

    const t = texts[language]

    let content = `${t.title}\n\n${t.complete}\n\n`

    // 生成完整的Vue组件示例
    content += `\`\`\`vue
<template>
  <div class="data-grid-container">
    <DataGrid
      :options="gridOptions"
      :data="tableData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useDataGrid } from '@/components/data-grid'
import { ${this.toPascalCase(analysis.name)}Plugin } from './${analysis.name}'

// 创建插件管理器
const pluginManager = createPluginManager({
  simplePlugins: [${this.toPascalCase(analysis.name)}Plugin]
})

// 使用数据网格
const { gridOptions, getColumnHelper } = useDataGrid('demo/demo')
const column = getColumnHelper()

// 配置列
gridOptions.value.columns = [
  { field: 'id', title: 'ID', width: 80 },
  { field: 'name', title: '名称', width: 120 },
  { ...column.${analysis.name.replace(/-/g, '_')}('status', '状态', {
    // 自定义配置
    ${analysis.configOptions.slice(0, 3).map(opt => `${opt.name}: ${this.getExampleValue(opt)}`).join(',\n    ')}
  }) }
]

// 示例数据
const tableData = ref([
  { id: 1, name: '示例1', status: 'active' },
  { id: 2, name: '示例2', status: 'inactive' }
])
</script>
\`\`\`\n\n`

    return content
  }

  /**
   * 生成配置选项
   */
  private generateConfigOptions(analysis: PluginAnalysis, language: 'zh' | 'en'): string {
    if (analysis.configOptions.length === 0) {
      return language === 'zh' ? '无可配置选项。' : 'No configuration options available.'
    }

    const texts = {
      zh: {
        title: '## 配置选项',
        option: '配置项',
        type: '类型',
        required: '必填',
        default: '默认值',
        description: '说明',
        yes: '是',
        no: '否'
      },
      en: {
        title: '## Configuration Options',
        option: 'Option',
        type: 'Type',
        required: 'Required',
        default: 'Default',
        description: 'Description',
        yes: 'Yes',
        no: 'No'
      }
    }

    const t = texts[language]

    let content = `${t.title}\n\n`
    content += `| ${t.option} | ${t.type} | ${t.required} | ${t.default} | ${t.description} |\n`
    content += `|---------|------|---------|---------|--------|\n`

    analysis.configOptions.forEach(option => {
      const required = option.required ? t.yes : t.no
      const defaultValue = option.defaultValue !== undefined ? `\`${option.defaultValue}\`` : '-'
      const description = option.description || '-'
      
      content += `| \`${option.name}\` | \`${option.type}\` | ${required} | ${defaultValue} | ${description} |\n`
    })

    return content + '\n'
  }

  /**
   * 生成功能特性
   */
  private generateFeatures(analysis: PluginAnalysis, language: 'zh' | 'en'): string {
    const texts = {
      zh: {
        title: '## 功能特性',
        render: '自定义渲染函数',
        styles: '内置样式支持',
        validation: '配置验证',
        typescript: 'TypeScript 支持',
        vue3: 'Vue 3 兼容'
      },
      en: {
        title: '## Features',
        render: 'Custom render function',
        styles: 'Built-in styles support',
        validation: 'Configuration validation',
        typescript: 'TypeScript support',
        vue3: 'Vue 3 compatible'
      }
    }

    const t = texts[language]

    let content = `${t.title}\n\n`

    const features = [
      { key: 'render', available: analysis.hasRender },
      { key: 'styles', available: analysis.hasStyles },
      { key: 'validation', available: analysis.hasValidation },
      { key: 'typescript', available: true },
      { key: 'vue3', available: true }
    ]

    features.forEach(feature => {
      const icon = feature.available ? '✅' : '❌'
      content += `- ${icon} ${t[feature.key as keyof typeof t]}\n`
    })

    return content + '\n'
  }

  /**
   * 生成索引文档
   */
  private async generateIndexDocs(docsList: GeneratedDocs[], options: DocsOptions): Promise<GeneratedDocs> {
    const language = options.language || 'zh'
    const texts = {
      zh: {
        title: '# 插件文档索引',
        description: '本项目中所有插件的文档索引',
        totalPlugins: '总插件数量',
        pluginList: '## 插件列表'
      },
      en: {
        title: '# Plugin Documentation Index',
        description: 'Documentation index for all plugins in this project',
        totalPlugins: 'Total Plugins',
        pluginList: '## Plugin List'
      }
    }

    const t = texts[language]

    let content = `${t.title}\n\n${t.description}\n\n`
    content += `**${t.totalPlugins}**: ${docsList.length}\n\n`
    content += `${t.pluginList}\n\n`

    docsList.forEach((docs, index) => {
      const pluginName = docs.metadata.pluginName
      const fileName = docs.filePath.split('/').pop()
      content += `${index + 1}. [${pluginName}](${fileName}) - ${docs.metadata.description}\n`
    })

    const indexDocs: GeneratedDocs = {
      content,
      filePath: options.outputDir ? `${options.outputDir}/README.md` : 'README.md',
      format: 'markdown',
      metadata: {
        title: t.title,
        description: t.description,
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        pluginName: 'index'
      }
    }

    await this.writeDocFile(indexDocs)
    return indexDocs
  }

  /**
   * 确定插件类型
   */
  private determinePluginType(plugin: any): 'simple' | 'full' | 'unknown' {
    if (this.isSimplePlugin(plugin)) {
      return 'simple'
    } else if (plugin.columnHelperMethods && Array.isArray(plugin.columnHelperMethods)) {
      return 'full'
    }
    return 'unknown'
  }

  /**
   * 判断是否为简化插件
   */
  private isSimplePlugin(plugin: any): plugin is SimplePlugin {
    return plugin.options && plugin.options.render && typeof plugin.options.render === 'function'
  }

  /**
   * 提取配置选项
   */
  private extractConfigOptions(config: any): ConfigOption[] {
    const options: ConfigOption[] = []

    Object.entries(config).forEach(([key, value]) => {
      options.push({
        name: key,
        type: typeof value,
        required: false,
        defaultValue: value,
        description: `${key} configuration option`
      })
    })

    return options
  }

  /**
   * 生成简化插件示例
   */
  private generateSimplePluginExamples(plugin: SimplePlugin): string[] {
    const examples = [
      `column.${plugin.name.replace(/-/g, '_')}('field', 'Title')`,
    ]

    if (plugin.options.defaultConfig && Object.keys(plugin.options.defaultConfig).length > 0) {
      examples.push(
        `column.${plugin.name.replace(/-/g, '_')}('field', 'Title', { /* config */ })`
      )
    }

    return examples
  }

  /**
   * 生成完整插件示例
   */
  private generateFullPluginExamples(plugin: any): string[] {
    const examples: string[] = []

    if (plugin.columnHelperMethods) {
      plugin.columnHelperMethods.forEach((method: any) => {
        examples.push(`column.${method.name}('field', 'Title')`)
      })
    }

    return examples
  }

  /**
   * 获取模板
   */
  private getTemplate(templateName: string): string {
    return this.templates.get(templateName) || this.templates.get('default')!
  }

  /**
   * 初始化模板
   */
  private initializeTemplates(): void {
    this.templates.set('default', `
# {{pluginName}} 插件

{{pluginName}} 插件的完整使用指南和 API 参考。

**最后更新**: {{lastUpdated}}

{{apiReference}}

{{configOptions}}

{{features}}

{{usageExamples}}

{{codeExamples}}

## 相关资源

- [插件开发指南](../guide/plugin-development.md)
- [API 文档](../api/index.md)
- [示例项目](../examples/index.md)
`)

    this.templates.set('minimal', `
# {{pluginName}}

{{apiReference}}

{{usageExamples}}
`)

    this.templates.set('detailed', `
# {{pluginName}} 插件文档

## 概述

{{pluginName}} 是一个 {{pluginType}} 类型的数据网格插件。

**最后更新**: {{lastUpdated}}

{{apiReference}}

{{configOptions}}

{{features}}

## 安装和使用

### 安装

\`\`\`bash
npm install @/components/data-grid/plugins
\`\`\`

### 导入

\`\`\`typescript
import { {{pluginName}}Plugin } from '@/components/data-grid/plugins'
\`\`\`

{{usageExamples}}

{{codeExamples}}

## 最佳实践

- 始终为插件提供合适的配置
- 使用 TypeScript 以获得更好的类型安全
- 在生产环境中测试所有功能

## 故障排除

常见问题和解决方案将在这里列出。

## 变更日志

### v1.0.0
- 初始版本

## 许可证

MIT License
`)
  }

  /**
   * 格式化内容
   */
  private formatContent(content: string, format: string): string {
    switch (format) {
      case 'html':
        return this.markdownToHtml(content)
      case 'json':
        return JSON.stringify({ content }, null, 2)
      default:
        return content
    }
  }

  /**
   * Markdown 转 HTML
   */
  private markdownToHtml(markdown: string): string {
    // 简单的 Markdown 转 HTML 实现
    return markdown
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre><code class="language-$1">$2</code></pre>')
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      .replace(/^\- (.*$)/gim, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
  }

  /**
   * 扫描插件文件
   */
  private async scanPluginFiles(projectPath: string): Promise<string[]> {
    // 这里应该实现实际的文件扫描
    return [
      `${projectPath}/plugins/status.ts`,
      `${projectPath}/plugins/currency.ts`,
      `${projectPath}/plugins/rating.ts`
    ]
  }

  /**
   * 写入文档文件
   */
  private async writeDocFile(docs: GeneratedDocs): Promise<void> {
    // 这里应该使用实际的文件系统 API
    console.log(`Writing documentation to: ${docs.filePath}`)
    console.log(docs.content.substring(0, 500) + '...')
  }

  /**
   * 转换为 PascalCase
   */
  private toPascalCase(str: string): string {
    return str
      .replace(/[-_](.)/g, (_, char) => char.toUpperCase())
      .replace(/^./, char => char.toUpperCase())
  }

  /**
   * 获取示例值
   */
  private getExampleValue(option: ConfigOption): string {
    switch (option.type) {
      case 'string':
        return `'${option.defaultValue || 'example'}'`
      case 'boolean':
        return option.defaultValue?.toString() || 'true'
      case 'number':
        return option.defaultValue?.toString() || '0'
      case 'object':
        return '{}'
      default:
        return `${option.defaultValue || 'null'}`
    }
  }

  /**
   * 解析文档参数
   */
  private parseDocsArgs(args: string[]): any {
    const options: any = {
      format: 'markdown',
      includeExamples: true,
      includeApi: true,
      includeUsage: true,
      language: 'zh'
    }

    for (let i = 0; i < args.length; i++) {
      const arg = args[i]
      const nextArg = args[i + 1]

      switch (arg) {
        case '--plugin':
        case '-p':
          options.pluginPath = nextArg
          i++
          break
        case '--project':
          options.projectPath = nextArg
          i++
          break
        case '--format':
        case '-f':
          options.format = nextArg
          i++
          break
        case '--output':
        case '-o':
          options.outputDir = nextArg
          i++
          break
        case '--template':
        case '-t':
          options.template = nextArg
          i++
          break
        case '--language':
        case '-l':
          options.language = nextArg
          i++
          break
        case '--no-examples':
          options.includeExamples = false
          break
        case '--no-api':
          options.includeApi = false
          break
        case '--no-usage':
          options.includeUsage = false
          break
      }
    }

    return options
  }
}