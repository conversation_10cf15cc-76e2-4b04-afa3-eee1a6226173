/**
 * 插件生成器
 * 根据模板快速生成插件代码
 */

import type { CliConfig } from './index'

export interface GeneratorOptions {
  name: string
  type: 'renderer' | 'service' | 'extension'
  template?: 'status' | 'currency' | 'link' | 'date' | 'boolean' | 'custom' | 'advanced'
  features?: ('validation' | 'styles' | 'config' | 'typescript')[]
  outputDir?: string
  description?: string
}

export interface GeneratedFile {
  path: string
  content: string
  type: 'plugin' | 'test' | 'docs' | 'types'
}

export class PluginGenerator {
  /**
   * 创建新插件
   */
  async create(args: string[]): Promise<GeneratedFile[]> {
    const options = this.parseArgs(args)
    const files: GeneratedFile[] = []

    // 生成主插件文件
    const pluginFile = await this.generatePluginFile(options)
    files.push(pluginFile)

    // 生成测试文件
    if (options.features?.includes('typescript')) {
      const testFile = await this.generateTestFile(options)
      files.push(testFile)
    }

    // 生成文档文件
    const docsFile = await this.generateDocsFile(options)
    files.push(docsFile)

    // 生成类型定义文件
    if (options.features?.includes('typescript')) {
      const typesFile = await this.generateTypesFile(options)
      files.push(typesFile)
    }

    return files
  }

  /**
   * 解析命令行参数
   */
  private parseArgs(args: string[]): GeneratorOptions {
    const options: GeneratorOptions = {
      name: 'my-plugin',
      type: 'renderer'
    }

    for (let i = 0; i < args.length; i++) {
      const arg = args[i]
      const nextArg = args[i + 1]

      switch (arg) {
        case '--name':
        case '-n':
          options.name = nextArg
          i++
          break
        case '--type':
        case '-t':
          options.type = nextArg as any
          i++
          break
        case '--template':
          options.template = nextArg as any
          i++
          break
        case '--output':
        case '-o':
          options.outputDir = nextArg
          i++
          break
        case '--description':
        case '-d':
          options.description = nextArg
          i++
          break
        case '--typescript':
          options.features = [...(options.features || []), 'typescript']
          break
        case '--with-validation':
          options.features = [...(options.features || []), 'validation']
          break
        case '--with-styles':
          options.features = [...(options.features || []), 'styles']
          break
        case '--with-config':
          options.features = [...(options.features || []), 'config']
          break
      }
    }

    return options
  }

  /**
   * 生成主插件文件
   */
  private async generatePluginFile(options: GeneratorOptions): Promise<GeneratedFile> {
    const { name, type, template } = options
    const pascalName = this.toPascalCase(name)
    
    let content = ''

    if (template && template !== 'custom') {
      // 使用预设模板
      content = this.generateFromTemplate(options)
    } else {
      // 生成自定义插件
      content = this.generateCustomPlugin(options)
    }

    return {
      path: `${options.outputDir || '.'}/${name}.ts`,
      content,
      type: 'plugin'
    }
  }

  /**
   * 从模板生成插件
   */
  private generateFromTemplate(options: GeneratorOptions): string {
    const { name, template, description } = options
    const pascalName = this.toPascalCase(name)

    const imports = `import { create${this.toPascalCase(template!)}Renderer } from '@/components/data-grid/plugins'`
    
    const pluginCode = this.getTemplateCode(template!, name, description)
    
    const exportStatement = `export default ${pascalName}Plugin`

    return `${imports}

/**
 * ${description || `${pascalName} 插件`}
 * 使用模板: ${template}
 */
${pluginCode}

${exportStatement}
`
  }

  /**
   * 生成自定义插件
   */
  private generateCustomPlugin(options: GeneratorOptions): string {
    const { name, type, description, features } = options
    const pascalName = this.toPascalCase(name)
    
    const hasValidation = features?.includes('validation')
    const hasStyles = features?.includes('styles')
    const hasConfig = features?.includes('config')

    const imports = `import { createSimpleRenderer } from '@/components/data-grid/plugins'`
    
    const renderFunction = this.generateRenderFunction(options)
    const configSection = hasConfig ? this.generateConfigSection(options) : ''
    const stylesSection = hasStyles ? this.generateStylesSection(options) : ''
    const validationSection = hasValidation ? this.generateValidationSection(options) : ''

    return `${imports}

/**
 * ${description || `${pascalName} 自定义插件`}
 * 生成时间: ${new Date().toISOString()}
 */
export const ${pascalName}Plugin = createSimpleRenderer('${name}', {
  render: ${renderFunction},${configSection}${stylesSection}${validationSection}
})

export default ${pascalName}Plugin
`
  }

  /**
   * 获取模板代码
   */
  private getTemplateCode(template: string, name: string, description?: string): string {
    const pascalName = this.toPascalCase(name)
    
    switch (template) {
      case 'status':
        return `export const ${pascalName}Plugin = createStatusRenderer('${name}', {
  statusMap: {
    'active': { text: '启用', type: 'success' },
    'inactive': { text: '禁用', type: 'default' },
    'pending': { text: '待处理', type: 'warning' },
    'error': { text: '错误', type: 'danger' }
  },
  variant: 'badge',
  showIcon: true
})`

      case 'currency':
        return `export const ${pascalName}Plugin = createCurrencyRenderer('${name}', {
  currency: 'CNY',
  precision: 2,
  showSymbol: true,
  showZero: false
})`

      case 'link':
        return `export const ${pascalName}Plugin = createLinkRenderer('${name}', {
  href: (row) => \`/details/\${row.id}\`,
  target: '_self',
  color: 'blue',
  showExternal: false
})`

      case 'date':
        return `export const ${pascalName}Plugin = createDateRenderer('${name}', {
  format: 'YYYY-MM-DD',
  showTime: false,
  relative: false
})`

      case 'boolean':
        return `export const ${pascalName}Plugin = createBooleanRenderer('${name}', {
  style: 'badge',
  trueText: '是',
  falseText: '否',
  trueColor: 'success',
  falseColor: 'default'
})`

      default:
        return `export const ${pascalName}Plugin = createSimpleRenderer('${name}', {
  render: (value) => \`<span class="custom-${name}">\${value}</span>\`
})`
    }
  }

  /**
   * 生成渲染函数
   */
  private generateRenderFunction(options: GeneratorOptions): string {
    const { name } = options
    
    return `(value, { config, row }) => {
    // 自定义渲染逻辑
    return \`<span class="${name}-cell">\${value}</span>\`
  }`
  }

  /**
   * 生成配置部分
   */
  private generateConfigSection(options: GeneratorOptions): string {
    return `
  
  defaultConfig: {
    // 默认配置选项
    theme: 'default',
    showIcon: true
  },
  
  defaultWidth: 120,`
  }

  /**
   * 生成样式部分
   */
  private generateStylesSection(options: GeneratorOptions): string {
    const { name } = options
    
    return `
  
  styles: \`
    .${name}-cell {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
    }
    
    .${name}-cell.theme-default {
      background: #f3f4f6;
      color: #374151;
    }
  \`,`
  }

  /**
   * 生成验证部分
   */
  private generateValidationSection(options: GeneratorOptions): string {
    return `
  
  validation: {
    theme: { type: 'string', enum: ['default', 'primary', 'success'] },
    showIcon: { type: 'boolean', required: false }
  },`
  }

  /**
   * 生成测试文件
   */
  private async generateTestFile(options: GeneratorOptions): Promise<GeneratedFile> {
    const { name } = options
    const pascalName = this.toPascalCase(name)
    
    const content = `import { ${pascalName}Plugin } from './${name}'
import { validateSimplePlugin } from '@/components/data-grid/plugins'

describe('${pascalName}Plugin', () => {
  test('plugin is valid', () => {
    const validation = validateSimplePlugin(${pascalName}Plugin)
    expect(validation.valid).toBe(true)
  })

  test('plugin has correct name', () => {
    expect(${pascalName}Plugin.name).toBe('${name}')
  })

  test('plugin has render function', () => {
    expect(typeof ${pascalName}Plugin.options.render).toBe('function')
  })

  test('plugin converts to full plugin', () => {
    const fullPlugin = ${pascalName}Plugin.toFullPlugin()
    expect(fullPlugin.name).toBe('${name}-simple')
    expect(fullPlugin.columnHelperMethods).toHaveLength(1)
  })
})
`

    return {
      path: `${options.outputDir || '.'}/${name}.test.ts`,
      content,
      type: 'test'
    }
  }

  /**
   * 生成文档文件
   */
  private async generateDocsFile(options: GeneratorOptions): Promise<GeneratedFile> {
    const { name, description, template, type } = options
    const pascalName = this.toPascalCase(name)
    
    const content = `# ${pascalName} 插件

${description || `${pascalName} 插件的使用说明`}

## 基本信息

- **插件名称**: ${name}
- **插件类型**: ${type}
- **模板类型**: ${template || 'custom'}
- **生成时间**: ${new Date().toLocaleString()}

## 使用方法

### 基础用法

\`\`\`typescript
import { ${pascalName}Plugin } from './${name}'
import { createPluginManager } from '@/components/data-grid'

// 注册插件
const pluginManager = createPluginManager({
  simplePlugins: [${pascalName}Plugin]
})

// 在列配置中使用
const columns = [
  { field: 'name', title: '名称' },
  { ...column.${name.replace(/-/g, '_')}('status', '状态') }
]
\`\`\`

### 配置选项

${this.generateConfigDocs(options)}

## 示例效果

${this.generateExampleDocs(options)}

## 自定义配置

\`\`\`typescript
// 自定义配置示例
{ ...column.${name.replace(/-/g, '_')}('field', '标题', {
  // 在这里添加自定义配置
}) }
\`\`\`
`

    return {
      path: `${options.outputDir || '.'}/${name}.md`,
      content,
      type: 'docs'
    }
  }

  /**
   * 生成类型定义文件
   */
  private async generateTypesFile(options: GeneratorOptions): Promise<GeneratedFile> {
    const { name } = options
    const pascalName = this.toPascalCase(name)
    
    const content = `/**
 * ${pascalName} 插件类型定义
 */

export interface ${pascalName}Config {
  // 配置选项类型定义
  theme?: 'default' | 'primary' | 'success'
  showIcon?: boolean
  [key: string]: any
}

export interface ${pascalName}Plugin {
  name: '${name}'
  type: 'renderer'
  options: {
    render: (value: any, context: { config: ${pascalName}Config, row: any }) => string
    defaultConfig?: ${pascalName}Config
    defaultWidth?: number
    styles?: string
  }
}

declare module '@/components/data-grid/plugins' {
  interface ColumnHelper {
    ${name.replace(/-/g, '_')}(field: string, title: string, config?: ${pascalName}Config): any
  }
}
`

    return {
      path: `${options.outputDir || '.'}/${name}.d.ts`,
      content,
      type: 'types'
    }
  }

  /**
   * 生成配置文档
   */
  private generateConfigDocs(options: GeneratorOptions): string {
    const { template, features } = options
    
    if (!template || template === 'custom') {
      return '根据插件功能自定义配置选项。'
    }

    // 根据模板类型生成配置文档
    const configDocs: Record<string, string> = {
      status: `
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| statusMap | object | {} | 状态映射配置 |
| variant | string | 'badge' | 显示样式 |
| showIcon | boolean | true | 是否显示图标 |`,

      currency: `
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| currency | string | 'CNY' | 货币类型 |
| precision | number | 2 | 小数位数 |
| showSymbol | boolean | true | 显示货币符号 |
| showZero | boolean | false | 显示零值 |`,

      link: `
| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| href | string/function | '' | 链接地址 |
| target | string | '_self' | 打开方式 |
| color | string | 'blue' | 链接颜色 |
| showExternal | boolean | false | 显示外部链接图标 |`
    }

    return configDocs[template] || '请参考插件源码了解配置选项。'
  }

  /**
   * 生成示例文档
   */
  private generateExampleDocs(options: GeneratorOptions): string {
    const { name, template } = options
    
    if (template === 'status') {
      return `
- 状态值 "active" 显示为绿色的"启用"徽章
- 状态值 "inactive" 显示为灰色的"禁用"徽章
- 状态值 "pending" 显示为黄色的"待处理"徽章`
    }

    return `根据配置和数据值动态渲染相应的内容。`
  }

  /**
   * 转换为 PascalCase
   */
  private toPascalCase(str: string): string {
    return str
      .replace(/[-_](.)/g, (_, char) => char.toUpperCase())
      .replace(/^./, char => char.toUpperCase())
  }
}