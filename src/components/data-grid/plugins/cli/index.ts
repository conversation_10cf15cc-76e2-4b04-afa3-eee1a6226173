/**
 * DataGrid CLI 工具
 * 实现 Serena 方案 Phase 2 的 CLI 工具
 */

export { PluginGenerator } from './generator'
export { PluginValidator } from './validator' 
export { PluginUpgrader } from './upgrader'
export { DocumentationGenerator } from './docs-generator'
export { CliTemplateManager } from './template-manager'

// CLI 命令接口
export interface CliCommand {
  name: string
  description: string
  execute(args: string[]): Promise<void>
}

// CLI 配置
export interface CliConfig {
  outputDir?: string
  typescript?: boolean
  template?: string
  verbose?: boolean
}

/**
 * CLI 主入口
 */
export class DataGridCli {
  private commands = new Map<string, CliCommand>()

  constructor() {
    this.registerCommands()
  }

  private registerCommands() {
    // 注册所有命令
    const commands = [
      new CreateCommand(),
      new UpgradeCommand(), 
      new ValidateCommand(),
      new DocsCommand()
    ]

    commands.forEach(cmd => {
      this.commands.set(cmd.name, cmd)
    })
  }

  async execute(commandName: string, args: string[]): Promise<void> {
    const command = this.commands.get(commandName)
    if (!command) {
      throw new Error(`Unknown command: ${commandName}`)
    }

    await command.execute(args)
  }

  getAvailableCommands(): CliCommand[] {
    return Array.from(this.commands.values())
  }
}

// 内部命令实现
class CreateCommand implements CliCommand {
  name = 'create'
  description = '创建新的插件'

  async execute(args: string[]): Promise<void> {
    const { PluginGenerator } = await import('./generator')
    const generator = new PluginGenerator()
    await generator.create(args)
  }
}

class UpgradeCommand implements CliCommand {
  name = 'upgrade'
  description = '升级现有插件'

  async execute(args: string[]): Promise<void> {
    const { PluginUpgrader } = await import('./upgrader')
    const upgrader = new PluginUpgrader()
    await upgrader.upgrade(args)
  }
}

class ValidateCommand implements CliCommand {
  name = 'validate'
  description = '验证插件配置'

  async execute(args: string[]): Promise<void> {
    const { PluginValidator } = await import('./validator')
    const validator = new PluginValidator()
    await validator.validate(args)
  }
}

class DocsCommand implements CliCommand {
  name = 'docs'
  description = '生成插件文档'

  async execute(args: string[]): Promise<void> {
    const { DocumentationGenerator } = await import('./docs-generator')
    const generator = new DocumentationGenerator()
    await generator.generate(args)
  }
}

/**
 * 创建默认 CLI 实例
 */
export function createCli(): DataGridCli {
  return new DataGridCli()
}