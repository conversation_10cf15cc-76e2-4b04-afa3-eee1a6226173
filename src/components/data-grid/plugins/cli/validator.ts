/**
 * 插件验证器
 * 验证插件配置、检查兼容性、确保质量标准
 */

import type { SimplePlugin, ValidationRules } from '../simple'

export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
  score: number // 0-100
  recommendations: string[]
}

export interface ValidationOptions {
  strict?: boolean
  checkCompatibility?: boolean
  checkPerformance?: boolean
  checkSecurity?: boolean
  customRules?: ValidationRules[]
}

export class PluginValidator {
  /**
   * 验证插件
   */
  async validate(args: string[]): Promise<void> {
    const options = this.parseValidationArgs(args)
    
    if (options.pluginPath) {
      await this.validateSinglePlugin(options.pluginPath, options)
    } else if (options.projectPath) {
      await this.validateProject(options.projectPath, options)
    } else {
      throw new Error('Must specify --plugin or --project path')
    }
  }

  /**
   * 验证单个插件
   */
  async validateSinglePlugin(pluginPath: string, options: ValidationOptions = {}): Promise<ValidationResult> {
    try {
      // 动态导入插件文件
      const pluginModule = await import(pluginPath)
      const plugin = pluginModule.default || pluginModule

      return this.validatePluginInstance(plugin, options)
    } catch (error) {
      return {
        valid: false,
        errors: [`Failed to load plugin: ${error}`],
        warnings: [],
        score: 0,
        recommendations: ['Check plugin file syntax and exports']
      }
    }
  }

  /**
   * 验证插件实例
   */
  validatePluginInstance(plugin: any, options: ValidationOptions = {}): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
      score: 100,
      recommendations: []
    }

    // 基础结构验证
    this.validateBasicStructure(plugin, result)
    
    // 功能验证
    this.validateFunctionality(plugin, result)
    
    if (options.checkCompatibility) {
      this.validateCompatibility(plugin, result)
    }
    
    if (options.checkPerformance) {
      this.validatePerformance(plugin, result)
    }
    
    if (options.checkSecurity) {
      this.validateSecurity(plugin, result)
    }

    // 计算最终分数
    result.score = this.calculateScore(result)
    result.valid = result.errors.length === 0 && result.score >= 60

    return result
  }

  /**
   * 验证项目中的所有插件
   */
  async validateProject(projectPath: string, options: ValidationOptions = {}): Promise<ValidationResult[]> {
    const results: ValidationResult[] = []
    
    try {
      // 扫描项目中的插件文件
      const pluginFiles = await this.scanPluginFiles(projectPath)
      
      for (const file of pluginFiles) {
        const result = await this.validateSinglePlugin(file, options)
        results.push(result)
        
        // 输出验证结果
        this.outputValidationResult(file, result)
      }
      
      // 输出汇总报告
      this.outputSummaryReport(results)
      
    } catch (error) {
      console.error('Project validation failed:', error)
    }
    
    return results
  }

  /**
   * 验证基础结构
   */
  private validateBasicStructure(plugin: any, result: ValidationResult): void {
    // 检查插件名称
    if (!plugin.name || typeof plugin.name !== 'string') {
      result.errors.push('Plugin must have a valid name property')
    } else if (!/^[a-z][a-z0-9-]*$/.test(plugin.name)) {
      result.warnings.push('Plugin name should use kebab-case format')
    }

    // 检查是否为简化插件
    if (this.isSimplePlugin(plugin)) {
      this.validateSimplePlugin(plugin, result)
    } else {
      this.validateFullPlugin(plugin, result)
    }
  }

  /**
   * 验证简化插件
   */
  private validateSimplePlugin(plugin: SimplePlugin, result: ValidationResult): void {
    // 检查 options
    if (!plugin.options) {
      result.errors.push('Simple plugin must have options property')
      return
    }

    // 检查 render 函数
    if (!plugin.options.render || typeof plugin.options.render !== 'function') {
      result.errors.push('Simple plugin must have a render function')
    }

    // 检查默认配置
    if (plugin.options.defaultConfig) {
      if (typeof plugin.options.defaultConfig !== 'object') {
        result.warnings.push('defaultConfig should be an object')
      }
    }

    // 检查样式
    if (plugin.options.styles) {
      if (typeof plugin.options.styles !== 'string') {
        result.warnings.push('styles should be a string')
      } else {
        this.validateCSS(plugin.options.styles, result)
      }
    }

    // 检查验证规则
    if (plugin.options.validation) {
      this.validateValidationRules(plugin.options.validation, result)
    }
  }

  /**
   * 验证完整插件
   */
  private validateFullPlugin(plugin: any, result: ValidationResult): void {
    // 检查版本
    if (!plugin.version) {
      result.warnings.push('Plugin should have version property')
    }

    // 检查列助手方法
    if (!plugin.columnHelperMethods || !Array.isArray(plugin.columnHelperMethods)) {
      result.errors.push('Full plugin must have columnHelperMethods array')
    }

    // 检查组件
    if (plugin.components) {
      Object.entries(plugin.components).forEach(([name, component]) => {
        if (!component || typeof component !== 'object') {
          result.warnings.push(`Component '${name}' is not properly defined`)
        }
      })
    }
  }

  /**
   * 验证功能性
   */
  private validateFunctionality(plugin: any, result: ValidationResult): void {
    try {
      // 测试渲染函数
      if (plugin.options?.render) {
        const testValue = 'test'
        const testContext = { config: {}, row: { id: 1 } }
        const renderResult = plugin.options.render(testValue, testContext)
        
        if (typeof renderResult !== 'string') {
          result.errors.push('Render function must return a string')
        }
        
        if (!renderResult.trim()) {
          result.warnings.push('Render function returns empty content')
        }
      }

      // 测试配置验证
      if (plugin.options?.validation) {
        this.testConfigValidation(plugin, result)
      }

    } catch (error) {
      result.errors.push(`Functionality test failed: ${error}`)
    }
  }

  /**
   * 验证兼容性
   */
  private validateCompatibility(plugin: any, result: ValidationResult): void {
    // 检查 Vue 版本兼容性
    if (plugin.vueVersion && plugin.vueVersion !== '3') {
      result.warnings.push('Plugin may not be compatible with Vue 3')
    }

    // 检查 API 兼容性
    const deprecatedMethods = ['$createElement', '$refs', '$children']
    const pluginCode = plugin.toString()
    
    deprecatedMethods.forEach(method => {
      if (pluginCode.includes(method)) {
        result.warnings.push(`Uses deprecated method: ${method}`)
      }
    })
  }

  /**
   * 验证性能
   */
  private validatePerformance(plugin: any, result: ValidationResult): void {
    // 检查渲染函数复杂度
    if (plugin.options?.render) {
      const renderCode = plugin.options.render.toString()
      
      // 检查循环复杂度
      const loops = (renderCode.match(/for\s*\(|while\s*\(|forEach\s*\(/g) || []).length
      if (loops > 3) {
        result.warnings.push('Render function has high loop complexity')
      }

      // 检查字符串拼接
      const stringConcats = (renderCode.match(/\+\s*['"]/g) || []).length
      if (stringConcats > 10) {
        result.warnings.push('Consider using template literals instead of string concatenation')
      }

      // 检查内联样式
      if (renderCode.includes('style=')) {
        result.recommendations.push('Consider moving inline styles to CSS classes')
      }
    }

    // 检查样式大小
    if (plugin.options?.styles) {
      const styleSize = plugin.options.styles.length
      if (styleSize > 5000) {
        result.warnings.push('Plugin styles are very large, consider optimization')
      }
    }
  }

  /**
   * 验证安全性
   */
  private validateSecurity(plugin: any, result: ValidationResult): void {
    if (plugin.options?.render) {
      const renderCode = plugin.options.render.toString()
      
      // 检查潜在的 XSS 风险
      const xssPatterns = [
        /innerHTML\s*=/,
        /document\.write/,
        /eval\s*\(/,
        /script\s*>/i,
        /javascript:/i
      ]

      xssPatterns.forEach(pattern => {
        if (pattern.test(renderCode)) {
          result.errors.push('Potential XSS vulnerability detected')
        }
      })

      // 检查用户输入处理
      if (renderCode.includes('value') && !renderCode.includes('escape')) {
        result.warnings.push('Consider escaping user input to prevent XSS')
      }
    }
  }

  /**
   * 验证 CSS
   */
  private validateCSS(css: string, result: ValidationResult): void {
    // 基础 CSS 语法检查
    const braceCount = (css.match(/{/g) || []).length - (css.match(/}/g) || []).length
    if (braceCount !== 0) {
      result.errors.push('CSS has unmatched braces')
    }

    // 检查选择器命名
    const selectors = css.match(/\.[a-zA-Z][a-zA-Z0-9-_]*/g) || []
    selectors.forEach(selector => {
      if (!/^\.([a-z][a-z0-9-]*)+$/.test(selector)) {
        result.warnings.push(`CSS selector '${selector}' should use kebab-case`)
      }
    })

    // 检查重要性声明
    if (css.includes('!important')) {
      result.warnings.push('Avoid using !important in plugin styles')
    }
  }

  /**
   * 验证验证规则
   */
  private validateValidationRules(rules: any, result: ValidationResult): void {
    if (typeof rules !== 'object') {
      result.errors.push('Validation rules must be an object')
      return
    }

    Object.entries(rules).forEach(([key, rule]: [string, any]) => {
      if (!rule.type) {
        result.warnings.push(`Validation rule '${key}' should specify type`)
      }
    })
  }

  /**
   * 测试配置验证
   */
  private testConfigValidation(plugin: any, result: ValidationResult): void {
    if (!plugin.options.validation) return

    const testConfigs = [
      {}, // 空配置
      { invalidKey: 'value' }, // 无效键
      null, // null 配置
    ]

    testConfigs.forEach((config, index) => {
      try {
        // 这里应该调用插件的配置验证逻辑
        // 暂时跳过实际验证，仅做结构检查
      } catch (error) {
        result.warnings.push(`Config validation test ${index + 1} failed`)
      }
    })
  }

  /**
   * 计算质量分数
   */
  private calculateScore(result: ValidationResult): number {
    let score = 100

    // 错误扣分
    score -= result.errors.length * 20

    // 警告扣分
    score -= result.warnings.length * 5

    // 建议奖励
    score += Math.min(result.recommendations.length * 2, 10)

    return Math.max(0, Math.min(100, score))
  }

  /**
   * 判断是否为简化插件
   */
  private isSimplePlugin(plugin: any): plugin is SimplePlugin {
    return plugin.options && plugin.options.render && typeof plugin.options.render === 'function'
  }

  /**
   * 扫描插件文件
   */
  private async scanPluginFiles(projectPath: string): Promise<string[]> {
    // 这里应该实现文件扫描逻辑
    // 暂时返回空数组，实际实现时需要使用文件系统 API
    return []
  }

  /**
   * 输出验证结果
   */
  private outputValidationResult(filePath: string, result: ValidationResult): void {
    console.log(`\n📋 ${filePath}`)
    console.log(`   Score: ${result.score}/100 ${result.valid ? '✅' : '❌'}`)
    
    if (result.errors.length > 0) {
      console.log('   Errors:')
      result.errors.forEach(error => console.log(`     ❌ ${error}`))
    }
    
    if (result.warnings.length > 0) {
      console.log('   Warnings:')
      result.warnings.forEach(warning => console.log(`     ⚠️  ${warning}`))
    }
    
    if (result.recommendations.length > 0) {
      console.log('   Recommendations:')
      result.recommendations.forEach(rec => console.log(`     💡 ${rec}`))
    }
  }

  /**
   * 输出汇总报告
   */
  private outputSummaryReport(results: ValidationResult[]): void {
    const totalPlugins = results.length
    const validPlugins = results.filter(r => r.valid).length
    const averageScore = results.reduce((sum, r) => sum + r.score, 0) / totalPlugins
    
    console.log('\n📊 Validation Summary')
    console.log(`   Total Plugins: ${totalPlugins}`)
    console.log(`   Valid Plugins: ${validPlugins}/${totalPlugins}`)
    console.log(`   Average Score: ${averageScore.toFixed(1)}/100`)
    
    if (validPlugins === totalPlugins) {
      console.log('   🎉 All plugins passed validation!')
    } else {
      console.log(`   ❌ ${totalPlugins - validPlugins} plugins need attention`)
    }
  }

  /**
   * 解析验证参数
   */
  private parseValidationArgs(args: string[]): any {
    const options: any = {
      strict: false,
      checkCompatibility: true,
      checkPerformance: true,
      checkSecurity: true
    }

    for (let i = 0; i < args.length; i++) {
      const arg = args[i]
      const nextArg = args[i + 1]

      switch (arg) {
        case '--plugin':
        case '-p':
          options.pluginPath = nextArg
          i++
          break
        case '--project':
          options.projectPath = nextArg
          i++
          break
        case '--strict':
          options.strict = true
          break
        case '--no-compat':
          options.checkCompatibility = false
          break
        case '--no-perf':
          options.checkPerformance = false
          break
        case '--no-security':
          options.checkSecurity = false
          break
      }
    }

    return options
  }
}