/**
 * Renderer Performance Optimization Utilities
 * Provides advanced optimization techniques for Modern plugin renderers
 */

import { markRaw, shallowRef, computed, type Component, type ComputedRef } from 'vue'
import { pluginPerformanceMonitor, performanceUtils } from './performanceMonitor'

/**
 * Renderer optimization configuration
 */
export interface RendererOptimizationConfig {
  enableCaching?: boolean
  enableLazyLoading?: boolean
  enableVirtualization?: boolean
  cacheSize?: number
  debounceMs?: number
  memoizationThreshold?: number
}

/**
 * Optimized renderer cache
 */
export class RendererCache {
  private cache = new Map<string, any>()
  private accessCount = new Map<string, number>()
  private maxSize: number
  
  constructor(maxSize = 1000) {
    this.maxSize = maxSize
  }

  /**
   * Get cached renderer result
   */
  get(key: string): any {
    const value = this.cache.get(key)
    if (value !== undefined) {
      // Update access count for LRU
      this.accessCount.set(key, (this.accessCount.get(key) || 0) + 1)
    }
    return value
  }

  /**
   * Set cached renderer result
   */
  set(key: string, value: any): void {
    // Evict oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }
    
    this.cache.set(key, value)
    this.accessCount.set(key, 1)
  }

  /**
   * Generate cache key from renderer parameters
   */
  generateKey(value: any, row: any, config: any, field: string): string {
    try {
      // Create a stable hash of the input parameters
      const keyData = {
        value: typeof value === 'object' ? JSON.stringify(value) : value,
        rowId: row?.id || row?._id || Math.random(), // Use row ID if available
        config: config ? JSON.stringify(config) : '',
        field
      }
      return btoa(JSON.stringify(keyData)).substring(0, 32)
    } catch (error) {
      // Fallback to simple string concatenation
      return `${field}-${value}-${Date.now()}`
    }
  }

  /**
   * Evict least recently used entries
   */
  private evictLRU(): void {
    let minAccess = Infinity
    let lruKey = ''
    
    for (const [key, count] of this.accessCount) {
      if (count < minAccess) {
        minAccess = count
        lruKey = key
      }
    }
    
    if (lruKey) {
      this.cache.delete(lruKey)
      this.accessCount.delete(lruKey)
    }
  }

  /**
   * Clear cache
   */
  clear(): void {
    this.cache.clear()
    this.accessCount.clear()
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    totalAccess: number
  } {
    const totalAccess = Array.from(this.accessCount.values()).reduce((sum, count) => sum + count, 0)
    const hitRate = totalAccess > 0 ? this.cache.size / totalAccess : 0
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate,
      totalAccess
    }
  }
}

/**
 * Global renderer cache instance
 */
const globalRendererCache = new RendererCache(1000)

/**
 * Performance-optimized renderer wrapper
 */
export function createOptimizedRenderer<T extends Component>(
  component: T,
  config: RendererOptimizationConfig = {}
): T {
  const {
    enableCaching = true,
    enableLazyLoading = true,
    cacheSize = 1000,
    debounceMs = 16, // 60fps
    memoizationThreshold = 100
  } = config

  // Mark component as raw to prevent Vue reactivity system overhead
  const optimizedComponent = markRaw(component)
  
  // Create renderer-specific cache if caching is enabled
  const cache = enableCaching ? new RendererCache(cacheSize) : null
  
  // Add performance monitoring wrapper
  const wrappedComponent = {
    ...optimizedComponent,
    setup(props: any, context: any) {
      // Performance monitoring for renderer setup
      const rendererName = component.name || 'anonymous-renderer'
      
      return performanceUtils.monitorRenderer(rendererName, () => {
        // Use original component setup if available
        if (typeof optimizedComponent.setup === 'function') {
          return optimizedComponent.setup(props, context)
        }
        
        // Default setup for components without explicit setup
        return {}
      }, {
        cacheEnabled: enableCaching,
        lazyLoading: enableLazyLoading,
        componentName: rendererName
      })
    },
    
    // Add render performance monitoring if component has render function
    render: optimizedComponent.render ? function(this: any, ...args: any[]) {
      const rendererName = component.name || 'anonymous-renderer'
      
      return performanceUtils.monitorRenderer(rendererName, () => {
        return optimizedComponent.render!.apply(this, args)
      }, {
        operation: 'render',
        timestamp: Date.now()
      })
    } : undefined
  }
  
  return wrappedComponent as T
}

/**
 * Create memoized renderer function
 */
export function createMemoizedRenderer<T>(
  renderFn: (value: any, row: any, config: any, field: string) => T,
  options: { cacheSize?: number; ttl?: number } = {}
): (value: any, row: any, config: any, field: string) => T {
  const cache = new RendererCache(options.cacheSize || 500)
  
  return (value: any, row: any, config: any, field: string): T => {
    const cacheKey = cache.generateKey(value, row, config, field)
    
    // Check cache first
    const cached = cache.get(cacheKey)
    if (cached !== undefined) {
      return cached
    }
    
    // Execute render function with performance monitoring
    const result = performanceUtils.monitorRenderer('memoized-renderer', () => {
      return renderFn(value, row, config, field)
    }, {
      cacheKey,
      cached: false
    })
    
    // Cache result
    cache.set(cacheKey, result)
    
    return result
  }
}

/**
 * Create debounced renderer for frequently changing data
 */
export function createDebouncedRenderer<T>(
  renderFn: (value: any, row: any, config: any, field: string) => T,
  debounceMs = 16
): (value: any, row: any, config: any, field: string) => T {
  let timeout: number | undefined
  let lastResult: T
  let lastArgs: [any, any, any, string]
  
  return (value: any, row: any, config: any, field: string): T => {
    lastArgs = [value, row, config, field]
    
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      lastResult = performanceUtils.monitorRenderer('debounced-renderer', () => {
        return renderFn(...lastArgs)
      }, {
        debounced: true,
        debounceMs
      })
      timeout = undefined
    }, debounceMs) as unknown as number
    
    return lastResult
  }
}

/**
 * Renderer performance utilities
 */
export const RendererOptimizer = {
  /**
   * Optimize a renderer component
   */
  optimize<T extends Component>(component: T, config?: RendererOptimizationConfig): T {
    return createOptimizedRenderer(component, config)
  },

  /**
   * Create performance-aware computed property for renderers
   */
  createComputedRenderer<T>(
    fn: () => T,
    rendererName: string
  ): ComputedRef<T> {
    return computed(() => {
      return performanceUtils.monitorRenderer(rendererName, fn, {
        computed: true,
        timestamp: Date.now()
      })
    })
  },

  /**
   * Bulk optimize multiple renderer components
   */
  optimizeBatch<T extends Record<string, Component>>(
    components: T,
    config?: RendererOptimizationConfig
  ): T {
    const optimized = {} as T
    
    for (const [name, component] of Object.entries(components)) {
      optimized[name as keyof T] = createOptimizedRenderer(component, config)
    }
    
    return optimized
  },

  /**
   * Get global renderer performance statistics
   */
  getGlobalStats(): {
    cacheStats: ReturnType<RendererCache['getStats']>
    totalRenderers: number
    averageRenderTime: number
    systemPerformance: any
  } {
    const cacheStats = globalRendererCache.getStats()
    const systemReport = pluginPerformanceMonitor.getSystemReport()
    
    return {
      cacheStats,
      totalRenderers: systemReport.totalPlugins,
      averageRenderTime: 0, // Would need to calculate from performance metrics
      systemPerformance: systemReport
    }
  },

  /**
   * Clear all renderer caches
   */
  clearAllCaches(): void {
    globalRendererCache.clear()
  },

  /**
   * Enable/disable renderer performance monitoring
   */
  setPerformanceMonitoring(enabled: boolean): void {
    pluginPerformanceMonitor.setEnabled(enabled)
  }
} as const

/**
 * Decorator for automatic renderer optimization
 */
export function optimizedRenderer(config?: RendererOptimizationConfig) {
  return function<T extends Component>(component: T): T {
    return createOptimizedRenderer(component, config)
  }
}

/**
 * Create batch renderer for handling multiple items efficiently
 */
export function createBatchRenderer<T>(
  renderFn: (items: Array<{ value: any; row: any; config: any; field: string }>) => T[],
  batchSize = 50
): (value: any, row: any, config: any, field: string) => T {
  const batchQueue: Array<{ value: any; row: any; config: any; field: string; resolve: (result: T) => void }> = []
  let processingTimeout: number | undefined
  
  const processBatch = () => {
    if (batchQueue.length === 0) return
    
    const batch = batchQueue.splice(0, batchSize)
    
    performanceUtils.monitorRenderer('batch-renderer', () => {
      const results = renderFn(batch.map(item => ({
        value: item.value,
        row: item.row,
        config: item.config,
        field: item.field
      })))
      
      batch.forEach((item, index) => {
        item.resolve(results[index])
      })
    }, {
      batchSize: batch.length,
      timestamp: Date.now()
    })
    
    // Continue processing if more items in queue
    if (batchQueue.length > 0) {
      processingTimeout = setTimeout(processBatch, 0) as unknown as number
    }
  }
  
  return (value: any, row: any, config: any, field: string): T => {
    return new Promise<T>((resolve) => {
      batchQueue.push({ value, row, config, field, resolve })
      
      if (!processingTimeout) {
        processingTimeout = setTimeout(processBatch, 0) as unknown as number
      }
    }) as any
  }
}