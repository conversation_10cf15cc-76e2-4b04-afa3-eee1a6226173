/**
 * Configuration utilities for the data grid plugin system
 * Provides comprehensive configuration management, merging, and validation
 */

import type { DeepReadonly, TypedPlugin, PluginName } from '../types'
import { TypeGuards, TypeUtils } from '../types'

/**
 * Configuration merge strategies
 */
export type MergeStrategy = 'replace' | 'merge' | 'append' | 'prepend' | 'deep-merge'

/**
 * Configuration with metadata
 */
export interface ConfigWithMeta<T = Record<string, unknown>> {
  readonly data: T
  readonly metadata: {
    readonly source: string
    readonly priority: number
    readonly timestamp: number
    readonly version?: string
    readonly environment?: string
  }
}

/**
 * Environment-specific configuration
 */
export interface EnvironmentConfig {
  readonly development?: Record<string, unknown>
  readonly production?: Record<string, unknown>
  readonly test?: Record<string, unknown>
  readonly staging?: Record<string, unknown>
}

/**
 * Plugin configuration manager with environment support
 */
export class PluginConfigManager {
  private configurations = new Map<string, ConfigWithMeta>()
  private watchers = new Map<string, Array<(config: ConfigWithMeta) => void>>()
  private environment = import.meta.env.MODE || 'development'
  
  /**
   * Set configuration for a plugin
   */
  setConfig<T extends Record<string, unknown>>(
    pluginName: PluginName | string,
    config: T,
    options: {
      source?: string
      priority?: number
      version?: string
      environment?: string
    } = {}
  ): void {
    const {
      source = 'manual',
      priority = 0,
      version,
      environment = this.environment
    } = options
    
    const configWithMeta: ConfigWithMeta<T> = {
      data: TypeUtils.deepFreeze(config),
      metadata: {
        source,
        priority,
        timestamp: Date.now(),
        version,
        environment
      }
    }
    
    const key = this.getConfigKey(pluginName, environment)
    this.configurations.set(key, configWithMeta)
    
    // Notify watchers
    this.notifyWatchers(key, configWithMeta)
  }
  
  /**
   * Get configuration for a plugin
   */
  getConfig<T extends Record<string, unknown>>(
    pluginName: PluginName | string,
    environment?: string
  ): T | null {
    const env = environment || this.environment
    const key = this.getConfigKey(pluginName, env)
    const config = this.configurations.get(key)
    
    if (!config) {
      // Try fallback to default environment
      if (env !== 'development') {
        const fallbackKey = this.getConfigKey(pluginName, 'development')
        const fallbackConfig = this.configurations.get(fallbackKey)
        return fallbackConfig?.data as T || null
      }
      return null
    }
    
    return config.data as T
  }
  
  /**
   * Merge configurations from multiple sources
   */
  mergeConfigs<T extends Record<string, unknown>>(
    configs: Array<{ config: Partial<T>; priority?: number; source?: string }>,
    strategy: MergeStrategy = 'deep-merge'
  ): T {
    // Sort by priority (higher priority first)
    const sortedConfigs = configs.sort((a, b) => (b.priority || 0) - (a.priority || 0))
    
    let result: any = {}
    
    for (const { config } of sortedConfigs) {
      switch (strategy) {
        case 'replace':
          result = { ...config }
          break
        case 'merge':
          result = { ...result, ...config }
          break
        case 'deep-merge':
          result = this.deepMerge(result, config)
          break
        case 'append':
          result = this.arrayMerge(result, config, 'append')
          break
        case 'prepend':
          result = this.arrayMerge(result, config, 'prepend')
          break
      }
    }
    
    return TypeUtils.deepFreeze(result) as T
  }
  
  /**
   * Watch for configuration changes
   */
  watchConfig(
    pluginName: PluginName | string,
    callback: (config: ConfigWithMeta) => void,
    environment?: string
  ): () => void {
    const key = this.getConfigKey(pluginName, environment || this.environment)
    
    if (!this.watchers.has(key)) {
      this.watchers.set(key, [])
    }
    
    this.watchers.get(key)!.push(callback)
    
    // Return unsubscribe function
    return () => {
      const watchers = this.watchers.get(key)
      if (watchers) {
        const index = watchers.indexOf(callback)
        if (index >= 0) {
          watchers.splice(index, 1)
        }
      }
    }
  }
  
  /**
   * Load configuration from environment variables
   */
  loadFromEnv(prefix = 'PLUGIN_'): void {
    if (typeof process === 'undefined') return // Not in Node.js environment
    
    Object.entries(process.env).forEach(([key, value]) => {
      if (key.startsWith(prefix) && value) {
        const pluginKey = key.substring(prefix.length).toLowerCase().replace(/_/g, '-')
        
        try {
          const config = JSON.parse(value)
          this.setConfig(pluginKey, config, {
            source: 'environment',
            priority: 100,
            environment: this.environment
          })
        } catch (error) {
          console.warn(`Failed to parse environment config for ${pluginKey}:`, error)
        }
      }
    })
  }
  
  /**
   * Export all configurations
   */
  exportConfigs(): Record<string, ConfigWithMeta> {
    const exported: Record<string, ConfigWithMeta> = {}
    this.configurations.forEach((config, key) => {
      exported[key] = config
    })
    return exported
  }
  
  /**
   * Import configurations
   */
  importConfigs(configs: Record<string, ConfigWithMeta>): void {
    Object.entries(configs).forEach(([key, config]) => {
      this.configurations.set(key, config)
      this.notifyWatchers(key, config)
    })
  }
  
  /**
   * Clear all configurations
   */
  clear(): void {
    this.configurations.clear()
    this.watchers.clear()
  }
  
  /**
   * Get configuration statistics
   */
  getStats(): {
    totalConfigs: number
    configsByEnvironment: Record<string, number>
    configsBySource: Record<string, number>
    watchers: number
  } {
    const configsByEnvironment: Record<string, number> = {}
    const configsBySource: Record<string, number> = {}
    let totalWatchers = 0
    
    this.configurations.forEach(config => {
      const env = config.metadata.environment || 'unknown'
      const source = config.metadata.source
      
      configsByEnvironment[env] = (configsByEnvironment[env] || 0) + 1
      configsBySource[source] = (configsBySource[source] || 0) + 1
    })
    
    this.watchers.forEach(watchers => {
      totalWatchers += watchers.length
    })
    
    return {
      totalConfigs: this.configurations.size,
      configsByEnvironment,
      configsBySource,
      watchers: totalWatchers
    }
  }
  
  private getConfigKey(pluginName: string, environment: string): string {
    return `${pluginName}:${environment}`
  }
  
  private notifyWatchers(key: string, config: ConfigWithMeta): void {
    const watchers = this.watchers.get(key)
    if (watchers) {
      watchers.forEach(watcher => {
        try {
          watcher(config)
        } catch (error) {
          console.error(`Error in config watcher for ${key}:`, error)
        }
      })
    }
  }
  
  private deepMerge(target: any, source: any): any {
    const result = { ...target }
    
    Object.keys(source).forEach(key => {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        if (target[key] && typeof target[key] === 'object' && !Array.isArray(target[key])) {
          result[key] = this.deepMerge(target[key], source[key])
        } else {
          result[key] = { ...source[key] }
        }
      } else {
        result[key] = source[key]
      }
    })
    
    return result
  }
  
  private arrayMerge(target: any, source: any, mode: 'append' | 'prepend'): any {
    const result = { ...target }
    
    Object.keys(source).forEach(key => {
      if (Array.isArray(source[key])) {
        if (Array.isArray(target[key])) {
          result[key] = mode === 'append' 
            ? [...target[key], ...source[key]]
            : [...source[key], ...target[key]]
        } else {
          result[key] = [...source[key]]
        }
      } else {
        result[key] = source[key]
      }
    })
    
    return result
  }
}

/**
 * Global configuration manager instance
 */
export const globalConfigManager = new PluginConfigManager()

/**
 * Configuration preset definitions
 */
export const ConfigPresets = {
  /**
   * Development configuration preset
   */
  development: {
    logging: {
      level: 'debug',
      enableConsole: true,
      enablePerformance: true
    },
    validation: {
      strict: true,
      enableWarnings: true
    },
    caching: {
      enabled: true,
      maxSize: 100,
      ttl: 5 * 60 * 1000 // 5 minutes
    }
  },
  
  /**
   * Production configuration preset
   */
  production: {
    logging: {
      level: 'error',
      enableConsole: false,
      enablePerformance: false
    },
    validation: {
      strict: false,
      enableWarnings: false
    },
    caching: {
      enabled: true,
      maxSize: 1000,
      ttl: 30 * 60 * 1000 // 30 minutes
    }
  },
  
  /**
   * Testing configuration preset
   */
  test: {
    logging: {
      level: 'warn',
      enableConsole: true,
      enablePerformance: false
    },
    validation: {
      strict: true,
      enableWarnings: true
    },
    caching: {
      enabled: false,
      maxSize: 10,
      ttl: 1000 // 1 second
    }
  }
} as const

/**
 * Configuration utility functions
 */
export const ConfigUtils = {
  /**
   * Apply environment-specific configuration
   */
  applyEnvironmentConfig: <T extends Record<string, unknown>>(
    baseConfig: T,
    envConfig: EnvironmentConfig,
    environment = import.meta.env.MODE || 'development'
  ): T => {
    const envSpecific = envConfig[environment as keyof EnvironmentConfig]
    if (!envSpecific) {
      return baseConfig
    }
    
    return globalConfigManager.mergeConfigs([
      { config: baseConfig, priority: 0 },
      { config: envSpecific, priority: 100 }
    ], 'deep-merge')
  },
  
  /**
   * Validate configuration schema
   */
  validateConfig: <T extends Record<string, unknown>>(
    config: unknown,
    requiredFields: (keyof T)[],
    optionalFields: (keyof T)[] = []
  ): { valid: boolean; missing: string[]; extra: string[] } => {
    if (!config || typeof config !== 'object') {
      return {
        valid: false,
        missing: requiredFields.map(String),
        extra: []
      }
    }
    
    const configObj = config as Record<string, unknown>
    const configKeys = Object.keys(configObj)
    const allValidFields = [...requiredFields.map(String), ...optionalFields.map(String)]
    
    const missing = requiredFields
      .map(String)
      .filter(field => !(field in configObj))
    
    const extra = configKeys.filter(key => !allValidFields.includes(key))
    
    return {
      valid: missing.length === 0,
      missing,
      extra
    }
  },
  
  /**
   * Create configuration from template
   */
  createFromTemplate: <T extends Record<string, unknown>>(
    template: T,
    overrides: Partial<T> = {}
  ): T => {
    return globalConfigManager.mergeConfigs([
      { config: template, priority: 0 },
      { config: overrides, priority: 100 }
    ], 'deep-merge')
  },
  
  /**
   * Get configuration with fallbacks
   */
  getWithFallbacks: <T extends Record<string, unknown>>(
    pluginName: string,
    fallbacks: T[]
  ): T => {
    const primary = globalConfigManager.getConfig<T>(pluginName)
    if (primary) {
      return primary
    }
    
    // Try fallbacks in order
    for (const fallback of fallbacks) {
      if (fallback && Object.keys(fallback).length > 0) {
        return fallback
      }
    }
    
    return {} as T
  },
  
  /**
   * Create environment-aware configuration getter
   */
  createEnvGetter: <T extends Record<string, unknown>>(
    pluginName: string,
    defaultConfig: T
  ) => {
    return (environment?: string): T => {
      const config = globalConfigManager.getConfig<T>(pluginName, environment)
      return config || defaultConfig
    }
  }
} as const

/**
 * Plugin-specific configuration helpers
 */
export const PluginConfigHelpers = {
  /**
   * Create a configuration provider for a plugin
   */
  createProvider: <T extends Record<string, unknown>>(
    pluginName: PluginName,
    defaultConfig: T
  ) => {
    return {
      get: (environment?: string): DeepReadonly<T> => {
        const config = globalConfigManager.getConfig<T>(pluginName, environment)
        return TypeUtils.deepFreeze(config || defaultConfig)
      },
      
      set: (config: Partial<T>, options?: {
        source?: string
        priority?: number
        environment?: string
      }): void => {
        const currentConfig = globalConfigManager.getConfig<T>(pluginName, options?.environment) || {}
        const mergedConfig = globalConfigManager.mergeConfigs([
          { config: currentConfig, priority: 0 },
          { config, priority: 100 }
        ], 'deep-merge')
        
        globalConfigManager.setConfig(pluginName, mergedConfig, options)
      },
      
      watch: (callback: (config: DeepReadonly<T>) => void, environment?: string) => {
        return globalConfigManager.watchConfig(
          pluginName,
          (configWithMeta) => callback(configWithMeta.data as DeepReadonly<T>),
          environment
        )
      },
      
      reset: (environment?: string): void => {
        globalConfigManager.setConfig(pluginName, defaultConfig, {
          source: 'reset',
          environment
        })
      }
    }
  },
  
  /**
   * Validate plugin configuration
   */
  validatePluginConfig: (plugin: TypedPlugin, config: unknown): {
    valid: boolean
    errors: string[]
    warnings: string[]
  } => {
    const errors: string[] = []
    const warnings: string[] = []
    
    if (!config || typeof config !== 'object') {
      errors.push('Configuration must be an object')
      return { valid: false, errors, warnings }
    }
    
    // Basic validation - plugins can extend this
    if (plugin.name && !TypeGuards.isPluginName(plugin.name)) {
      errors.push('Invalid plugin name format')
    }
    
    // Check for common configuration issues
    const configObj = config as Record<string, unknown>
    if ('version' in configObj && typeof configObj.version !== 'string') {
      warnings.push('Version should be a string')
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }
} as const