/**
 * Error handling utilities for data grid plugins
 * Provides structured error handling, validation, and logging with enhanced security
 */

import { pluginPerformanceMonitor } from './performanceMonitor'
/**
 * Import ConfigStore for error recovery
 */
import { getConfigStore } from '../core/ConfigStore'

/**
 * Modern logging interface for structured logging
 */
export interface ModernLogger {
  debug(message: string, context?: Record<string, unknown>): void
  info(message: string, context?: Record<string, unknown>): void
  warn(message: string, context?: Record<string, unknown>): void
  error(message: string, error?: Error, context?: Record<string, unknown>): void
  fatal(message: string, error?: Error, context?: Record<string, unknown>): void
  metric(name: string, value: number, tags?: Record<string, string>): void
}

/**
 * Log levels for filtering
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

/**
 * Log transport interface for extensible logging
 */
export interface LogTransport {
  name: string
  level: LogLevel
  log(level: LogLevel, message: string, context?: Record<string, unknown>): Promise<void>
}

/**
 * Console transport for development
 */
export class ConsoleTransport implements LogTransport {
  name = 'console'
  level = LogLevel.DEBUG

  async log(level: LogLevel, message: string, context?: Record<string, unknown>): Promise<void> {
    const timestamp = new Date().toISOString()
    const levelName = LogLevel[level]
    const contextStr = context ? ` ${JSON.stringify(context)}` : ''
    
    const logMessage = `[${timestamp}] ${levelName}: ${message}${contextStr}`
    
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(logMessage)
        break
      case LogLevel.INFO:
        console.info(logMessage)
        break
      case LogLevel.WARN:
        console.warn(logMessage)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(logMessage)
        break
    }
  }
}

/**
 * Memory transport for testing and debugging
 */
export class MemoryTransport implements LogTransport {
  name = 'memory'
  level = LogLevel.DEBUG
  private logs: Array<{
    level: LogLevel
    message: string
    context?: Record<string, unknown>
    timestamp: number
  }> = []
  private maxSize = 1000

  async log(level: LogLevel, message: string, context?: Record<string, unknown>): Promise<void> {
    this.logs.push({
      level,
      message,
      context,
      timestamp: Date.now()
    })

    // Prevent memory leaks by limiting log size
    if (this.logs.length > this.maxSize) {
      this.logs = this.logs.slice(-this.maxSize)
    }
  }

  getLogs(): Array<{
    level: LogLevel
    message: string
    context?: Record<string, unknown>
    timestamp: number
  }> {
    return [...this.logs]
  }

  clear(): void {
    this.logs = []
  }
}

/**
 * Enhanced Modern Logger implementation
 */
export class EnhancedModernLogger implements ModernLogger {
  private transports: LogTransport[] = []
  private minLevel = LogLevel.DEBUG
  private context: Record<string, unknown> = {}

  constructor(defaultTransports = true) {
    if (defaultTransports) {
      this.addTransport(new ConsoleTransport())
      
      // Add memory transport in development
      if (import.meta.env.DEV) {
        this.addTransport(new MemoryTransport())
      }
    }
  }

  /**
   * Add log transport
   */
  addTransport(transport: LogTransport): void {
    this.transports.push(transport)
  }

  /**
   * Remove log transport
   */
  removeTransport(name: string): void {
    this.transports = this.transports.filter(t => t.name !== name)
  }

  /**
   * Set global logging context
   */
  setContext(context: Record<string, unknown>): void {
    this.context = { ...this.context, ...context }
  }

  /**
   * Set minimum log level
   */
  setLevel(level: LogLevel): void {
    this.minLevel = level
  }

  /**
   * Create child logger with additional context
   */
  child(context: Record<string, unknown>): EnhancedModernLogger {
    const childLogger = new EnhancedModernLogger(false)
    childLogger.transports = [...this.transports]
    childLogger.minLevel = this.minLevel
    childLogger.context = { ...this.context, ...context }
    return childLogger
  }

  private async logToTransports(level: LogLevel, message: string, context?: Record<string, unknown>): Promise<void> {
    if (level < this.minLevel) return

    const fullContext = { ...this.context, ...context }
    
    await Promise.all(
      this.transports
        .filter(transport => level >= transport.level)
        .map(transport => transport.log(level, message, fullContext))
    )
  }

  debug(message: string, context?: Record<string, unknown>): void {
    this.logToTransports(LogLevel.DEBUG, message, context)
  }

  info(message: string, context?: Record<string, unknown>): void {
    this.logToTransports(LogLevel.INFO, message, context)
  }

  warn(message: string, context?: Record<string, unknown>): void {
    this.logToTransports(LogLevel.WARN, message, context)
  }

  error(message: string, error?: Error, context?: Record<string, unknown>): void {
    const errorContext = error ? {
      ...context,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    } : context

    this.logToTransports(LogLevel.ERROR, message, errorContext)
  }

  fatal(message: string, error?: Error, context?: Record<string, unknown>): void {
    const errorContext = error ? {
      ...context,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    } : context

    this.logToTransports(LogLevel.FATAL, message, errorContext)
  }

  metric(name: string, value: number, tags?: Record<string, string>): void {
    this.logToTransports(LogLevel.INFO, `Metric: ${name}`, {
      metric: true,
      name,
      value,
      tags
    })
  }
}

/**
 * Enhanced error types for plugin system with severity levels
 */
export enum PluginErrorType {
  VALIDATION = 'validation',
  RENDER = 'render',
  CONFIG = 'config',
  PERMISSION = 'permission',
  NETWORK = 'network',
  SECURITY = 'security',
  PERFORMANCE = 'performance',
  MEMORY = 'memory',
  UNKNOWN = 'unknown'
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Enhanced structured error interface with security context
 */
export interface PluginError extends Error {
  type: PluginErrorType
  severity: ErrorSeverity
  context?: Record<string, unknown>
  cause?: Error
  timestamp?: number
  pluginName?: string
  userId?: string
  sessionId?: string
  fingerprint?: string
  recoverable?: boolean
  securityImplications?: string[]
}

/**
 * Enhanced error handler configuration with security and monitoring
 */
interface ErrorHandlerConfig {
  enableLogging: boolean
  enableReporting: boolean
  enableSecurityLogging: boolean
  enablePerformanceTracking: boolean
  maxErrors: number
  maxErrorsPerPlugin: number
  sensitiveDataPatterns: RegExp[]
  reportCallback?: (error: PluginError) => void
  securityCallback?: (error: PluginError) => void
}

/**
 * Enhanced default error handler configuration
 */
const defaultConfig: ErrorHandlerConfig = {
  enableLogging: import.meta.env.DEV,
  enableReporting: false,
  enableSecurityLogging: true,
  enablePerformanceTracking: true,
  maxErrors: 1000,
  maxErrorsPerPlugin: 100,
  sensitiveDataPatterns: [
    /password/i,
    /token/i,
    /secret/i,
    /key/i,
    /auth/i,
    /session/i,
    /cookie/i,
    /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/, // Credit card pattern
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email pattern
  ]
}

/**
 * Enhanced error storage with categorization and limits
 */
interface ErrorStorage {
  errors: PluginError[]
  byType: Map<PluginErrorType, PluginError[]>
  byPlugin: Map<string, PluginError[]>
  bySeverity: Map<ErrorSeverity, PluginError[]>
}

const errorStorage: ErrorStorage = {
  errors: [],
  byType: new Map(),
  byPlugin: new Map(),
  bySeverity: new Map()
}

let errorConfig = { ...defaultConfig }

/**
 * Create a structured plugin error with enhanced security context
 */
export function createPluginError(
  message: string,
  type: PluginErrorType = PluginErrorType.UNKNOWN,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  context?: Record<string, unknown>,
  cause?: Error
): PluginError {
  // Sanitize message to remove sensitive information
  const sanitizedMessage = sanitizeErrorMessage(message, errorConfig.sensitiveDataPatterns)
  
  const error = new Error(sanitizedMessage) as PluginError
  error.type = type
  error.severity = severity
  error.context = context ? sanitizeContext(context) : undefined
  error.cause = cause
  error.timestamp = Date.now()
  error.name = 'PluginError'
  error.fingerprint = generateErrorFingerprint(sanitizedMessage, type, severity)
  error.recoverable = isRecoverableError(type, severity)
  error.securityImplications = assessSecurityImplications(type, severity, context)
  
  return error
}

/**
 * Sanitize error message to remove sensitive data
 */
function sanitizeErrorMessage(message: string, patterns: RegExp[]): string {
  let sanitized = message
  
  patterns.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '[REDACTED]')
  })
  
  return sanitized
}

/**
 * Sanitize context object to remove sensitive data
 */
function sanitizeContext(context: Record<string, unknown>): Record<string, unknown> {
  const sanitized: Record<string, unknown> = {}
  
  for (const [key, value] of Object.entries(context)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeErrorMessage(value, errorConfig.sensitiveDataPatterns)
    } else if (typeof value === 'object' && value !== null) {
      // Recursively sanitize nested objects (max depth 3)
      sanitized[key] = sanitizeNestedObject(value, 0, 3)
    } else {
      sanitized[key] = value
    }
  }
  
  return sanitized
}

/**
 * Recursively sanitize nested objects
 */
function sanitizeNestedObject(obj: any, currentDepth: number, maxDepth: number): any {
  if (currentDepth >= maxDepth || typeof obj !== 'object' || obj === null) {
    return '[OBJECT_TOO_DEEP]'
  }
  
  if (Array.isArray(obj)) {
    return obj.slice(0, 10).map(item => 
      typeof item === 'object' ? sanitizeNestedObject(item, currentDepth + 1, maxDepth) : item
    )
  }
  
  const sanitized: any = {}
  let keyCount = 0
  
  for (const [key, value] of Object.entries(obj)) {
    if (keyCount >= 20) break // Limit number of keys
    
    if (typeof value === 'string') {
      sanitized[key] = sanitizeErrorMessage(value, errorConfig.sensitiveDataPatterns)
    } else if (typeof value === 'object') {
      sanitized[key] = sanitizeNestedObject(value, currentDepth + 1, maxDepth)
    } else {
      sanitized[key] = value
    }
    
    keyCount++
  }
  
  return sanitized
}

/**
 * Generate unique error fingerprint for deduplication
 */
function generateErrorFingerprint(message: string, type: PluginErrorType, severity: ErrorSeverity): string {
  const content = `${type}:${severity}:${message.substring(0, 100)}`
  return btoa(content).substring(0, 16)
}

/**
 * Determine if error is recoverable
 */
function isRecoverableError(type: PluginErrorType, severity: ErrorSeverity): boolean {
  if (severity === ErrorSeverity.CRITICAL) return false
  
  const recoverableTypes = [
    PluginErrorType.RENDER,
    PluginErrorType.CONFIG,
    PluginErrorType.NETWORK,
    PluginErrorType.PERFORMANCE
  ]
  
  return recoverableTypes.includes(type)
}

/**
 * Assess security implications of error
 */
function assessSecurityImplications(type: PluginErrorType, severity: ErrorSeverity, context?: Record<string, unknown>): string[] {
  const implications: string[] = []
  
  if (type === PluginErrorType.SECURITY) {
    implications.push('Direct security violation detected')
  }
  
  if (type === PluginErrorType.PERMISSION) {
    implications.push('Permission boundary violation')
  }
  
  if (severity === ErrorSeverity.CRITICAL) {
    implications.push('Critical error may indicate system compromise')
  }
  
  if (context?.userInput) {
    implications.push('User input involved in error')
  }
  
  return implications
}

/**
 * Enhanced error handling with categorization and security logging
 */
export function handlePluginError(
  error: Error | PluginError,
  pluginName?: string,
  context?: Record<string, unknown>
): void {
  const pluginError = error instanceof Error && 'type' in error 
    ? error as PluginError
    : createPluginError(
        error.message, 
        PluginErrorType.UNKNOWN, 
        ErrorSeverity.MEDIUM, 
        context, 
        error
      )
  
  if (pluginName) {
    pluginError.pluginName = pluginName
  }
  
  // Add to categorized storage
  addToErrorStorage(pluginError)
  
  // Track performance impact
  if (errorConfig.enablePerformanceTracking) {
    pluginPerformanceMonitor.endTiming(pluginName || 'unknown', '', error)
  }
  
  // Log error if enabled
  if (errorConfig.enableLogging) {
    logError(pluginError)
  }
  
  // Security logging for critical errors
  if (errorConfig.enableSecurityLogging && shouldLogForSecurity(pluginError)) {
    logSecurityEvent(pluginError)
  }
  
  // Report error if callback is configured
  if (errorConfig.enableReporting && errorConfig.reportCallback) {
    try {
      errorConfig.reportCallback(pluginError)
    } catch (reportError) {
      console.error('[PluginError] Failed to report error:', reportError)
    }
  }
  
  // Security callback for security-related errors
  if (errorConfig.securityCallback && pluginError.securityImplications?.length) {
    try {
      errorConfig.securityCallback(pluginError)
    } catch (securityError) {
      console.error('[PluginError] Failed to handle security callback:', securityError)
    }
  }
}

/**
 * Add error to categorized storage with limits
 */
function addToErrorStorage(error: PluginError): void {
  // Add to main storage with limit
  errorStorage.errors.unshift(error)
  if (errorStorage.errors.length > errorConfig.maxErrors) {
    errorStorage.errors.splice(errorConfig.maxErrors)
  }
  
  // Add to type categorization
  if (!errorStorage.byType.has(error.type)) {
    errorStorage.byType.set(error.type, [])
  }
  const typeErrors = errorStorage.byType.get(error.type)!
  typeErrors.unshift(error)
  if (typeErrors.length > 50) typeErrors.splice(50)
  
  // Add to plugin categorization
  if (error.pluginName) {
    if (!errorStorage.byPlugin.has(error.pluginName)) {
      errorStorage.byPlugin.set(error.pluginName, [])
    }
    const pluginErrors = errorStorage.byPlugin.get(error.pluginName)!
    pluginErrors.unshift(error)
    if (pluginErrors.length > errorConfig.maxErrorsPerPlugin) {
      pluginErrors.splice(errorConfig.maxErrorsPerPlugin)
    }
  }
  
  // Add to severity categorization
  if (!errorStorage.bySeverity.has(error.severity)) {
    errorStorage.bySeverity.set(error.severity, [])
  }
  const severityErrors = errorStorage.bySeverity.get(error.severity)!
  severityErrors.unshift(error)
  if (severityErrors.length > 100) severityErrors.splice(100)
}

/**
 * Enhanced error logging with sanitization
 */
function logError(error: PluginError): void {
  const errorInfo = {
    message: error.message,
    type: error.type,
    severity: error.severity,
    plugin: error.pluginName,
    fingerprint: error.fingerprint,
    recoverable: error.recoverable,
    context: error.context,
    timestamp: new Date(error.timestamp || Date.now()).toISOString()
  }
  
  // Use appropriate console method based on severity
  switch (error.severity) {
    case ErrorSeverity.CRITICAL:
      console.error(`[PluginError:CRITICAL] ${error.type.toUpperCase()}:`, errorInfo)
      break
    case ErrorSeverity.HIGH:
      console.error(`[PluginError:HIGH] ${error.type.toUpperCase()}:`, errorInfo)
      break
    case ErrorSeverity.MEDIUM:
      console.warn(`[PluginError:MEDIUM] ${error.type.toUpperCase()}:`, errorInfo)
      break
    case ErrorSeverity.LOW:
      console.log(`[PluginError:LOW] ${error.type.toUpperCase()}:`, errorInfo)
      break
  }
  
  if (error.cause) {
    console.error('[PluginError] Caused by:', error.cause)
  }
  
  if (error.securityImplications?.length) {
    console.warn('[PluginError] Security implications:', error.securityImplications)
  }
}

/**
 * Determine if error should be logged for security purposes
 */
function shouldLogForSecurity(error: PluginError): boolean {
  return error.type === PluginErrorType.SECURITY ||
         error.type === PluginErrorType.PERMISSION ||
         error.severity === ErrorSeverity.CRITICAL ||
         (error.securityImplications?.length || 0) > 0
}

/**
 * Log security-related events
 */
function logSecurityEvent(error: PluginError): void {
  const securityLog = {
    timestamp: new Date().toISOString(),
    type: 'PLUGIN_SECURITY_EVENT',
    errorType: error.type,
    severity: error.severity,
    plugin: error.pluginName,
    fingerprint: error.fingerprint,
    implications: error.securityImplications,
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    url: typeof window !== 'undefined' ? window.location.href : 'unknown'
  }
  
  // This could be sent to a security logging service
  console.warn('[SECURITY] Plugin security event:', securityLog)
}

/**
 * Enhanced safe function wrapper with retry logic and circuit breaker
 */
export function safeExecute<T extends unknown[], R>(
  fn: (...args: T) => R,
  fallback: R,
  options: {
    pluginName?: string
    errorType?: PluginErrorType
    maxRetries?: number
    retryDelay?: number
    circuitBreakerThreshold?: number
  } = {}
): (...args: T) => R {
  const {
    pluginName,
    errorType = PluginErrorType.UNKNOWN,
    maxRetries = 0,
    retryDelay = 100,
    circuitBreakerThreshold = 5
  } = options

  let failureCount = 0
  let lastFailureTime = 0
  const circuitBreakerWindow = 60000 // 1 minute

  return (...args: T): R => {
    // Circuit breaker check
    const now = Date.now()
    if (failureCount >= circuitBreakerThreshold && 
        now - lastFailureTime < circuitBreakerWindow) {
      console.warn(`[SafeExecute] Circuit breaker open for ${pluginName || 'unknown function'}`)
      return fallback
    }

    let attempt = 0
    const executeWithRetry = (): R => {
      try {
        const result = fn(...args)
        
        // Reset failure count on success
        if (failureCount > 0) {
          failureCount = 0
          console.log(`[SafeExecute] Circuit breaker reset for ${pluginName || 'unknown function'}`)
        }
        
        return result
      } catch (error) {
        attempt++
        failureCount++
        lastFailureTime = now
        
        if (attempt <= maxRetries) {
          console.warn(`[SafeExecute] Retry ${attempt}/${maxRetries} for ${pluginName || 'unknown function'}`)
          // Simple exponential backoff
          const delay = retryDelay * Math.pow(2, attempt - 1)
          setTimeout(() => {}, delay)
          return executeWithRetry()
        }
        
        const pluginError = createPluginError(
          `Safe execution failed after ${attempt} attempts: ${error instanceof Error ? error.message : String(error)}`,
          errorType,
          ErrorSeverity.HIGH,
          { 
            args: args.map(arg => typeof arg === 'object' ? '[Object]' : arg),
            attempts: attempt,
            failureCount
          }
        )
        
        handlePluginError(pluginError, pluginName)
        return fallback
      }
    }

    return executeWithRetry()
  }
}

/**
 * Async safe function wrapper with enhanced retry and circuit breaker
 */
export function safeExecuteAsync<T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  fallback: R,
  options: {
    pluginName?: string
    errorType?: PluginErrorType
    maxRetries?: number
    retryDelay?: number
    circuitBreakerThreshold?: number
    timeout?: number
  } = {}
): (...args: T) => Promise<R> {
  const {
    pluginName,
    errorType = PluginErrorType.UNKNOWN,
    maxRetries = 0,
    retryDelay = 100,
    circuitBreakerThreshold = 5,
    timeout = 5000
  } = options

  let failureCount = 0
  let lastFailureTime = 0
  const circuitBreakerWindow = 60000 // 1 minute

  return async (...args: T): Promise<R> => {
    // Circuit breaker check
    const now = Date.now()
    if (failureCount >= circuitBreakerThreshold && 
        now - lastFailureTime < circuitBreakerWindow) {
      console.warn(`[SafeExecuteAsync] Circuit breaker open for ${pluginName || 'unknown function'}`)
      return fallback
    }

    let attempt = 0
    const executeWithRetry = async (): Promise<R> => {
      try {
        // Add timeout wrapper for async operations
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Operation timeout')), timeout)
        })
        
        const result = await Promise.race([
          fn(...args),
          timeoutPromise
        ])
        
        // Reset failure count on success
        if (failureCount > 0) {
          failureCount = 0
          console.log(`[SafeExecuteAsync] Circuit breaker reset for ${pluginName || 'unknown function'}`)
        }
        
        return result
      } catch (error) {
        attempt++
        failureCount++
        lastFailureTime = now
        
        if (attempt <= maxRetries) {
          console.warn(`[SafeExecuteAsync] Retry ${attempt}/${maxRetries} for ${pluginName || 'unknown function'}`)
          // Exponential backoff with jitter
          const delay = retryDelay * Math.pow(2, attempt - 1) + Math.random() * 100
          await new Promise(resolve => setTimeout(resolve, delay))
          return executeWithRetry()
        }
        
        const pluginError = createPluginError(
          `Async safe execution failed after ${attempt} attempts: ${error instanceof Error ? error.message : String(error)}`,
          errorType,
          ErrorSeverity.HIGH,
          { 
            args: args.map(arg => typeof arg === 'object' ? '[Object]' : arg),
            attempts: attempt,
            failureCount,
            timeout
          }
        )
        
        handlePluginError(pluginError, pluginName)
        return fallback
      }
    }

    return executeWithRetry()
  }
}

/**
 * Enhanced validation helper functions with comprehensive type checking and sanitization
 */
export const validators = {
  /**
   * Validate required field with custom error messages
   */
  required<T>(value: T, fieldName: string, customMessage?: string): T {
    if (value === null || value === undefined || value === '') {
      throw createPluginError(
        customMessage || `Required field '${fieldName}' is missing or empty`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value }
      )
    }
    return value
  },

  /**
   * Validate string field with length constraints
   */
  string(value: unknown, fieldName: string, options: {
    required?: boolean
    minLength?: number
    maxLength?: number
    pattern?: RegExp
    allowEmpty?: boolean
  } = {}): string {
    const { required = true, minLength, maxLength, pattern, allowEmpty = false } = options
    
    if (!required && (value === null || value === undefined)) {
      return ''
    }
    
    this.required(value, fieldName)
    
    if (typeof value !== 'string') {
      throw createPluginError(
        `Field '${fieldName}' must be a string, got ${typeof value}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value, type: typeof value }
      )
    }

    if (!allowEmpty && value.length === 0) {
      throw createPluginError(
        `Field '${fieldName}' cannot be empty`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value }
      )
    }

    if (minLength !== undefined && value.length < minLength) {
      throw createPluginError(
        `Field '${fieldName}' must be at least ${minLength} characters long`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value, minLength, actualLength: value.length }
      )
    }

    if (maxLength !== undefined && value.length > maxLength) {
      throw createPluginError(
        `Field '${fieldName}' must be at most ${maxLength} characters long`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value, maxLength, actualLength: value.length }
      )
    }

    if (pattern && !pattern.test(value)) {
      throw createPluginError(
        `Field '${fieldName}' does not match the required pattern`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value, pattern: pattern.source }
      )
    }
    
    return value
  },

  /**
   * Validate number field with range constraints
   */
  number(value: unknown, fieldName: string, options: {
    required?: boolean
    min?: number
    max?: number
    integer?: boolean
  } = {}): number {
    const { required = true, min, max, integer = false } = options
    
    if (!required && (value === null || value === undefined)) {
      return 0
    }
    
    this.required(value, fieldName)
    
    const num = Number(value)
    if (isNaN(num)) {
      throw createPluginError(
        `Field '${fieldName}' must be a valid number, got ${value}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value }
      )
    }

    if (integer && !Number.isInteger(num)) {
      throw createPluginError(
        `Field '${fieldName}' must be an integer, got ${num}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value: num }
      )
    }

    if (min !== undefined && num < min) {
      throw createPluginError(
        `Field '${fieldName}' must be at least ${min}, got ${num}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value: num, min }
      )
    }

    if (max !== undefined && num > max) {
      throw createPluginError(
        `Field '${fieldName}' must be at most ${max}, got ${num}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value: num, max }
      )
    }
    
    return num
  },

  /**
   * Validate function field with arity checking
   */
  function(value: unknown, fieldName: string, options: {
    required?: boolean
    arity?: number
  } = {}): Function {
    const { required = true, arity } = options
    
    if (!required && (value === null || value === undefined)) {
      return () => {}
    }
    
    this.required(value, fieldName)
    
    if (typeof value !== 'function') {
      throw createPluginError(
        `Field '${fieldName}' must be a function, got ${typeof value}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, type: typeof value }
      )
    }

    if (arity !== undefined && value.length !== arity) {
      throw createPluginError(
        `Field '${fieldName}' function must have ${arity} parameters, got ${value.length}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, expectedArity: arity, actualArity: value.length }
      )
    }
    
    return value
  },

  /**
   * Validate object field with property checking
   */
  object<T extends Record<string, unknown>>(value: unknown, fieldName: string, options: {
    required?: boolean
    allowNull?: boolean
    requiredProps?: string[]
    allowedProps?: string[]
  } = {}): T {
    const { required = true, allowNull = false, requiredProps = [], allowedProps } = options
    
    if (!required && (value === null || value === undefined)) {
      return {} as T
    }
    
    if (allowNull && value === null) {
      return null as T
    }
    
    this.required(value, fieldName)
    
    if (typeof value !== 'object' || value === null || Array.isArray(value)) {
      throw createPluginError(
        `Field '${fieldName}' must be an object, got ${typeof value}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value, type: typeof value }
      )
    }

    const obj = value as Record<string, unknown>

    // Check required properties
    for (const prop of requiredProps) {
      if (!(prop in obj) || obj[prop] === undefined) {
        throw createPluginError(
          `Field '${fieldName}' missing required property '${prop}'`,
          PluginErrorType.VALIDATION,
          ErrorSeverity.MEDIUM,
          { fieldName, missingProperty: prop, availableProps: Object.keys(obj) }
        )
      }
    }

    // Check allowed properties if specified
    if (allowedProps) {
      const invalidProps = Object.keys(obj).filter(prop => !allowedProps.includes(prop))
      if (invalidProps.length > 0) {
        throw createPluginError(
          `Field '${fieldName}' contains invalid properties: ${invalidProps.join(', ')}`,
          PluginErrorType.VALIDATION,
          ErrorSeverity.MEDIUM,
          { fieldName, invalidProps, allowedProps }
        )
      }
    }
    
    return obj as T
  },

  /**
   * Validate array field with length and element constraints
   */
  array<T>(value: unknown, fieldName: string, options: {
    required?: boolean
    minLength?: number
    maxLength?: number
    elementValidator?: (item: unknown, index: number) => T
  } = {}): T[] {
    const { required = true, minLength, maxLength, elementValidator } = options
    
    if (!required && (value === null || value === undefined)) {
      return []
    }
    
    this.required(value, fieldName)
    
    if (!Array.isArray(value)) {
      throw createPluginError(
        `Field '${fieldName}' must be an array, got ${typeof value}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value, type: typeof value }
      )
    }

    if (minLength !== undefined && value.length < minLength) {
      throw createPluginError(
        `Field '${fieldName}' must have at least ${minLength} elements, got ${value.length}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, minLength, actualLength: value.length }
      )
    }

    if (maxLength !== undefined && value.length > maxLength) {
      throw createPluginError(
        `Field '${fieldName}' must have at most ${maxLength} elements, got ${value.length}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, maxLength, actualLength: value.length }
      )
    }

    // Validate each element if validator provided
    if (elementValidator) {
      return value.map((item, index) => {
        try {
          return elementValidator(item, index)
        } catch (error) {
          throw createPluginError(
            `Field '${fieldName}[${index}]' validation failed: ${error instanceof Error ? error.message : String(error)}`,
            PluginErrorType.VALIDATION,
            ErrorSeverity.MEDIUM,
            { fieldName, elementIndex: index, elementValue: item, originalError: error }
          )
        }
      })
    }
    
    return value as T[]
  },

  /**
   * Validate enum value
   */
  enum<T extends string | number>(value: unknown, fieldName: string, enumValues: readonly T[], required = true): T {
    if (!required && (value === null || value === undefined)) {
      return enumValues[0]
    }
    
    this.required(value, fieldName)
    
    if (!enumValues.includes(value as T)) {
      throw createPluginError(
        `Field '${fieldName}' must be one of: ${enumValues.join(', ')}, got ${value}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value, allowedValues: enumValues }
      )
    }
    
    return value as T
  },

  /**
   * Validate URL field
   */
  url(value: unknown, fieldName: string, options: {
    required?: boolean
    allowedProtocols?: string[]
  } = {}): string {
    const { required = true, allowedProtocols = ['http', 'https'] } = options
    
    const urlString = this.string(value, fieldName, { required })
    
    if (!required && !urlString) {
      return urlString
    }

    try {
      const url = new URL(urlString)
      
      if (!allowedProtocols.includes(url.protocol.replace(':', ''))) {
        throw createPluginError(
          `Field '${fieldName}' must use one of the allowed protocols: ${allowedProtocols.join(', ')}`,
          PluginErrorType.VALIDATION,
          ErrorSeverity.MEDIUM,
          { fieldName, value: urlString, protocol: url.protocol, allowedProtocols }
        )
      }
      
      return urlString
    } catch (error) {
      throw createPluginError(
        `Field '${fieldName}' must be a valid URL, got ${urlString}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value: urlString, error: error instanceof Error ? error.message : String(error) }
      )
    }
  },

  /**
   * Validate email field
   */
  email(value: unknown, fieldName: string, required = true): string {
    const emailString = this.string(value, fieldName, { required })
    
    if (!required && !emailString) {
      return emailString
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(emailString)) {
      throw createPluginError(
        `Field '${fieldName}' must be a valid email address, got ${emailString}`,
        PluginErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { fieldName, value: emailString }
      )
    }
    
    return emailString
  }
}

/**
 * Configure error handler
 */
export function configureErrorHandler(config: Partial<ErrorHandlerConfig>): void {
  errorConfig = { ...errorConfig, ...config }
}

/**
 * Get error statistics
 */
export function getErrorStats(): {
  total: number
  byType: Record<PluginErrorType, number>
  byPlugin: Record<string, number>
  bySeverity: Record<ErrorSeverity, number>
  recent: PluginError[]
  systemHealth: {
    errorRate: number
    criticalErrors: number
    recentTrend: 'improving' | 'stable' | 'worsening'
  }
} {
  const byType = {} as Record<PluginErrorType, number>
  const byPlugin = {} as Record<string, number>
  const bySeverity = {} as Record<ErrorSeverity, number>
  
  // Initialize counters
  Object.values(PluginErrorType).forEach(type => {
    byType[type] = 0
  })
  Object.values(ErrorSeverity).forEach(severity => {
    bySeverity[severity] = 0
  })
  
  // Count errors from storage
  errorStorage.errors.forEach(error => {
    byType[error.type] = (byType[error.type] || 0) + 1
    bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1
    if (error.pluginName) {
      byPlugin[error.pluginName] = (byPlugin[error.pluginName] || 0) + 1
    }
  })

  // Calculate system health metrics
  const total = errorStorage.errors.length
  const criticalErrors = bySeverity[ErrorSeverity.CRITICAL] || 0
  const errorRate = total > 0 ? (criticalErrors / total) * 100 : 0
  
  // Calculate recent trend (last 10 vs previous 10 errors)
  const recent10 = errorStorage.errors.slice(0, 10)
  const previous10 = errorStorage.errors.slice(10, 20)
  const recentCritical = recent10.filter(e => e.severity === ErrorSeverity.CRITICAL).length
  const previousCritical = previous10.filter(e => e.severity === ErrorSeverity.CRITICAL).length
  
  let recentTrend: 'improving' | 'stable' | 'worsening' = 'stable'
  if (recentCritical < previousCritical) {
    recentTrend = 'improving'
  } else if (recentCritical > previousCritical) {
    recentTrend = 'worsening'
  }
  
  return {
    total,
    byType,
    byPlugin,
    bySeverity,
    recent: errorStorage.errors.slice(0, 10),
    systemHealth: {
      errorRate,
      criticalErrors,
      recentTrend
    }
  }
}

/**
 * Clear error history and reset metrics
 */
export function clearErrorHistory(): void {
  errorStorage.errors.length = 0
  errorStorage.byType.clear()
  errorStorage.byPlugin.clear()
  errorStorage.bySeverity.clear()
  
  if (import.meta.env.DEV) {
    console.log('[PluginError] Error history and storage cleared')
  }
}

/**
 * Error boundary helper for Vue components with enhanced recovery
 */
export function createErrorBoundary(componentName: string, options: {
  onError?: (error: PluginError) => void
  fallbackRender?: () => string
  maxErrors?: number
  resetTimeout?: number
} = {}) {
  const { onError, fallbackRender, maxErrors = 5, resetTimeout = 60000 } = options
  let errorCount = 0
  let lastErrorTime = 0

  return {
    errorCaptured(error: Error, instance: unknown, info: string) {
      const now = Date.now()
      
      // Reset error count if enough time has passed
      if (now - lastErrorTime > resetTimeout) {
        errorCount = 0
      }
      
      errorCount++
      lastErrorTime = now
      
      const pluginError = createPluginError(
        `Component error in ${componentName}: ${error.message}`,
        PluginErrorType.RENDER,
        errorCount >= maxErrors ? ErrorSeverity.CRITICAL : ErrorSeverity.HIGH,
        { 
          componentName, 
          info, 
          instance: '[Vue Component Instance]',
          errorCount,
          maxErrors
        },
        error
      )
      
      handlePluginError(pluginError, componentName)
      
      // Call custom error handler if provided
      if (onError) {
        try {
          onError(pluginError)
        } catch (handlerError) {
          console.error('[ErrorBoundary] Error in custom error handler:', handlerError)
        }
      }
      
      // If too many errors, disable the component
      if (errorCount >= maxErrors) {
        console.error(`[ErrorBoundary] Component ${componentName} disabled due to excessive errors (${errorCount}/${maxErrors})`)
        return false // Stop propagation and disable component
      }
      
      // Return false to prevent the error from propagating further
      return false
    },

    // Provide fallback render function
    render() {
      if (errorCount >= maxErrors && fallbackRender) {
        return fallbackRender()
      }
      return null
    },

    // Reset error count manually
    reset() {
      errorCount = 0
      lastErrorTime = 0
    },

    // Get current error state
    getErrorState() {
      return {
        errorCount,
        lastErrorTime,
        isDisabled: errorCount >= maxErrors,
        timeToReset: Math.max(0, resetTimeout - (Date.now() - lastErrorTime))
      }
    }
  }
}

/**
 * Promise-based error recovery with backoff strategy
 */
export async function withErrorRecovery<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number
    baseDelay?: number
    maxDelay?: number
    backoffFactor?: number
    shouldRetry?: (error: Error, attempt: number) => boolean
    onRetry?: (error: Error, attempt: number) => void
    fallback?: () => Promise<T> | T
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    baseDelay = 100,
    maxDelay = 5000,
    backoffFactor = 2,
    shouldRetry = () => true,
    onRetry,
    fallback
  } = options

  let lastError: Error | null = null
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))
      
      // Don't retry on last attempt or if shouldRetry returns false
      if (attempt === maxRetries || !shouldRetry(lastError, attempt)) {
        break
      }
      
      // Call retry callback if provided
      if (onRetry) {
        try {
          onRetry(lastError, attempt)
        } catch (callbackError) {
          console.warn('[ErrorRecovery] Error in retry callback:', callbackError)
        }
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  // If we have a fallback, try it
  if (fallback) {
    try {
      return await fallback()
    } catch (fallbackError) {
      console.error('[ErrorRecovery] Fallback operation failed:', fallbackError)
    }
  }
  
  // If all retries failed and no fallback worked, throw the last error
  throw lastError || new Error('Unknown error in error recovery')
}

/**
 * Debounced error handler to prevent error spam
 */
export function createDebouncedErrorHandler(
  handler: (errors: PluginError[]) => void,
  delay = 1000
) {
  let timeoutId: ReturnType<typeof setTimeout> | null = null
  let errorQueue: PluginError[] = []

  return {
    handle(error: PluginError) {
      errorQueue.push(error)
      
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      timeoutId = setTimeout(() => {
        if (errorQueue.length > 0) {
          try {
            handler([...errorQueue])
          } catch (handlerError) {
            console.error('[DebouncedErrorHandler] Error in batch handler:', handlerError)
          }
          errorQueue = []
        }
        timeoutId = null
      }, delay)
    },

    flush() {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      
      if (errorQueue.length > 0) {
        try {
          handler([...errorQueue])
        } catch (handlerError) {
          console.error('[DebouncedErrorHandler] Error in flush handler:', handlerError)
        }
        errorQueue = []
      }
    },

    clear() {
      if (timeoutId) {
        clearTimeout(timeoutId)
        timeoutId = null
      }
      errorQueue = []
    }
  }
}

/**
 * Global error recovery manager
 */
export class ErrorRecoveryManager {
  private static instance: ErrorRecoveryManager | null = null
  private recoveryStrategies = new Map<PluginErrorType, (error: PluginError) => boolean>()
  private isEnabled = true

  static getInstance(): ErrorRecoveryManager {
    if (!ErrorRecoveryManager.instance) {
      ErrorRecoveryManager.instance = new ErrorRecoveryManager()
    }
    return ErrorRecoveryManager.instance
  }

  /**
   * Register a recovery strategy for specific error types
   */
  registerRecoveryStrategy(errorType: PluginErrorType, strategy: (error: PluginError) => boolean): void {
    this.recoveryStrategies.set(errorType, strategy)
  }

  /**
   * Attempt to recover from an error
   */
  attemptRecovery(error: PluginError): boolean {
    if (!this.isEnabled) {
      return false
    }

    const strategy = this.recoveryStrategies.get(error.type)
    if (!strategy) {
      return false
    }

    try {
      const recovered = strategy(error)
      if (recovered && import.meta.env.DEV) {
        console.log(`[ErrorRecovery] Successfully recovered from ${error.type} error in ${error.pluginName || 'unknown plugin'}`)
      }
      return recovered
    } catch (recoveryError) {
      console.error('[ErrorRecovery] Recovery strategy failed:', recoveryError)
      return false
    }
  }

  /**
   * Enable or disable error recovery
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  /**
   * Get registered recovery strategies
   */
  getRecoveryStrategies(): ReadonlyMap<PluginErrorType, (error: PluginError) => boolean> {
    return this.recoveryStrategies
  }

  /**
   * Clear all recovery strategies
   */
  clearStrategies(): void {
    this.recoveryStrategies.clear()
  }
}

/**
 * Default recovery strategies for common error types
 */
export function setupDefaultRecoveryStrategies(): void {
  const recoveryManager = ErrorRecoveryManager.getInstance()

  // Render error recovery - try to re-render with fallback
  recoveryManager.registerRecoveryStrategy(PluginErrorType.RENDER, (error) => {
    if (error.context?.fallbackRender && typeof error.context.fallbackRender === 'function') {
      try {
        error.context.fallbackRender()
        return true
      } catch {
        return false
      }
    }
    return false
  })

  // Config error recovery - reset to default config
  recoveryManager.registerRecoveryStrategy(PluginErrorType.CONFIG, (error) => {
    if (error.pluginName) {
      try {
        const store = getConfigStore()
        // Clear corrupted config
        if (error.context?.configId) {
          store.remove(error.context.configId as string)
        }
        return true
      } catch {
        return false
      }
    }
    return false
  })

  // Network error recovery - retry with backoff
  recoveryManager.registerRecoveryStrategy(PluginErrorType.NETWORK, (error) => {
    // Mark for retry (actual retry logic would be handled by the calling code)
    return error.severity !== ErrorSeverity.CRITICAL
  })
}

// Auto-setup recovery strategies when module loads
setupDefaultRecoveryStrategies()