import type { PluginDefinition } from '../core/injection'
import type { SimplePlugin } from '../simple/api'

/**
 * 简化插件到完整插件的自动适配器
 * 实现 Serena 方案中的自动转换机制
 */
export class SimpleToFullAdapter {
  /**
   * 将简化插件转换为完整插件定义
   */
  static adapt(simplePlugin: SimplePlugin): PluginDefinition {
    return simplePlugin.toFullPlugin()
  }

  /**
   * 批量转换简化插件
   */
  static adaptMany(simplePlugins: SimplePlugin[]): PluginDefinition[] {
    return simplePlugins.map(plugin => this.adapt(plugin))
  }

  /**
   * 智能合并简化插件与完整插件配置
   * 允许在简化插件基础上添加完整插件的高级功能
   */
  static mergeWithFullPlugin(
    simplePlugin: SimplePlugin,
    fullPluginOverrides: Partial<PluginDefinition>
  ): PluginDefinition {
    const basePlugin = simplePlugin.toFullPlugin()
    
    return {
      ...basePlugin,
      ...fullPluginOverrides,
      
      // 智能合并 setup 函数
      setup: async (context) => {
        // 先执行简化插件的 setup
        if (basePlugin.setup) {
          await basePlugin.setup(context)
        }
        
        // 再执行完整插件的 setup 覆盖
        if (fullPluginOverrides.setup) {
          await fullPluginOverrides.setup(context)
        }
      },
      
      // 智能合并 teardown 函数
      teardown: async (context) => {
        // 先执行完整插件的 teardown
        if (fullPluginOverrides.teardown) {
          await fullPluginOverrides.teardown(context)
        }
        
        // 再执行简化插件的 teardown
        if (basePlugin.teardown) {
          await basePlugin.teardown(context)
        }
      },
      
      // 合并 provides 服务
      provides: [
        ...(basePlugin.provides || []),
        ...(fullPluginOverrides.provides || [])
      ],
      
      // 合并 requires 依赖
      requires: [
        ...(basePlugin.requires || []),
        ...(fullPluginOverrides.requires || [])
      ],
      
      // 合并列助手方法
      columnHelperMethods: [
        ...(basePlugin.columnHelperMethods || []),
        ...(fullPluginOverrides.columnHelperMethods || [])
      ]
    }
  }

  /**
   * 验证简化插件是否可以安全转换
   */
  static validateConversion(simplePlugin: SimplePlugin): {
    valid: boolean
    warnings: string[]
    errors: string[]
  } {
    const warnings: string[] = []
    const errors: string[] = []

    // 检查插件名称
    if (!simplePlugin.name) {
      errors.push('Plugin name is required')
    } else if (simplePlugin.name.includes(' ')) {
      warnings.push('Plugin name contains spaces, consider using kebab-case')
    }

    // 检查渲染函数
    if (simplePlugin.type === 'renderer' && !simplePlugin.options.render) {
      errors.push('Renderer plugin must have a render function')
    }

    // 检查配置冲突
    if (simplePlugin.options.validation) {
      const config = simplePlugin.options.defaultConfig || {}
      Object.keys(simplePlugin.options.validation).forEach(key => {
        if (!(key in config)) {
          warnings.push(`Validation defined for '${key}' but no default value provided`)
        }
      })
    }

    return {
      valid: errors.length === 0,
      warnings,
      errors
    }
  }

  /**
   * 自动修复常见的简化插件问题
   */
  static autoFix(simplePlugin: SimplePlugin): SimplePlugin {
    // 修复插件名称
    if (simplePlugin.name.includes(' ')) {
      simplePlugin.name = simplePlugin.name
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '')
    }

    // 添加默认配置
    if (!simplePlugin.options.defaultConfig) {
      simplePlugin.options.defaultConfig = {}
    }

    // 为验证规则添加默认值
    if (simplePlugin.options.validation) {
      Object.entries(simplePlugin.options.validation).forEach(([key, rule]) => {
        if (rule.default !== undefined && !(key in simplePlugin.options.defaultConfig!)) {
          simplePlugin.options.defaultConfig![key] = rule.default
        }
      })
    }

    // 添加默认宽度
    if (!simplePlugin.options.defaultWidth) {
      simplePlugin.options.defaultWidth = 120
    }

    return simplePlugin
  }

  /**
   * 性能优化：批量转换与注册
   */
  static async batchAdaptAndRegister(
    pluginManager: any,
    simplePlugins: SimplePlugin[],
    options: {
      autoFix?: boolean
      validateFirst?: boolean
      continueOnError?: boolean
    } = {}
  ): Promise<{
    success: PluginDefinition[]
    failed: Array<{ plugin: SimplePlugin; error: Error }>
    warnings: string[]
  }> {
    const success: PluginDefinition[] = []
    const failed: Array<{ plugin: SimplePlugin; error: Error }> = []
    const warnings: string[] = []

    for (const simplePlugin of simplePlugins) {
      try {
        // 可选的自动修复
        let plugin = simplePlugin
        if (options.autoFix) {
          plugin = this.autoFix({ ...plugin })
        }

        // 可选的验证
        if (options.validateFirst) {
          const validation = this.validateConversion(plugin)
          if (!validation.valid) {
            if (options.continueOnError) {
              failed.push({ 
                plugin, 
                error: new Error(`Validation failed: ${validation.errors.join(', ')}`) 
              })
              continue
            } else {
              throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
            }
          }
          warnings.push(...validation.warnings)
        }

        // 转换并注册
        const fullPlugin = this.adapt(plugin)
        await pluginManager.registerPlugin(fullPlugin)
        success.push(fullPlugin)

      } catch (error) {
        const errorObj = error instanceof Error ? error : new Error(String(error))
        failed.push({ plugin: simplePlugin, error: errorObj })
        
        if (!options.continueOnError) {
          throw errorObj
        }
      }
    }

    return { success, failed, warnings }
  }

  /**
   * 创建插件升级路径
   * 帮助用户从简化插件逐步升级到完整插件
   */
  static createUpgradePath(simplePlugin: SimplePlugin): {
    currentLevel: 'simple'
    nextLevel: 'standard' | 'enterprise'
    upgradeSteps: string[]
    codeExample: string
  } {
    const isComplex = !!(
      simplePlugin.options.validation ||
      simplePlugin.options.styles ||
      (simplePlugin.options.defaultConfig && Object.keys(simplePlugin.options.defaultConfig).length > 3)
    )

    return {
      currentLevel: 'simple',
      nextLevel: isComplex ? 'enterprise' : 'standard',
      upgradeSteps: isComplex ? [
        '1. 提取验证规则到独立的配置schema',
        '2. 将样式移到独立的CSS文件',
        '3. 添加错误处理和性能监控',
        '4. 实现完整的插件生命周期',
        '5. 添加依赖注入和服务注册'
      ] : [
        '1. 添加配置验证',
        '2. 增加更多配置选项',
        '3. 考虑添加依赖关系',
        '4. 优化性能和错误处理'
      ],
      codeExample: this.generateUpgradeExample(simplePlugin, isComplex ? 'enterprise' : 'standard')
    }
  }

  /**
   * 生成升级示例代码
   */
  private static generateUpgradeExample(simplePlugin: SimplePlugin, targetLevel: 'standard' | 'enterprise'): string {
    if (targetLevel === 'standard') {
      return `
// 升级到 Standard 级别
import { definePlugin, defineRenderer } from '@/components/data-grid/plugins/core/DevUtils'

export const ${simplePlugin.name}Plugin = definePlugin({
  name: '${simplePlugin.name}',
  version: '1.0.0',
  description: 'Upgraded from simple plugin',
  
  setup: async (context) => {
    // 添加更丰富的初始化逻辑
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    // ... 更多配置
  }
})
`
    } else {
      return `
// 升级到 Enterprise 级别
import { definePlugin } from '@/components/data-grid/plugins/core/DevUtils'
import { ConfigValidator } from '@/components/data-grid/plugins/core/DevUtils'

export const ${simplePlugin.name}Plugin = definePlugin({
  name: '${simplePlugin.name}',
  version: '1.0.0',
  description: 'Enterprise-grade plugin',
  
  provides: [
    // 服务定义
  ],
  
  requires: [
    // 依赖声明
  ],
  
  setup: async (context) => {
    // 企业级初始化逻辑
    // 性能监控、错误处理、缓存策略等
  },
  
  teardown: async (context) => {
    // 资源清理
  }
})
`
    }
  }
}