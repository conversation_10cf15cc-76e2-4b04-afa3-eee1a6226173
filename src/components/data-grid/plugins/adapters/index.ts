/**
 * 插件适配器系统
 * 实现 Serena 方案中的自动适配机制
 */

export {
  SimpleToFullAdapter
} from './simple-to-full'

/**
 * 自动注册适配器
 * 简化批量插件注册流程
 */
export class AutoRegisterAdapter {
  /**
   * 自动检测插件类型并注册
   */
  static async autoRegister(
    pluginManager: any,
    plugins: Array<any>,
    options: {
      enableAutoFix?: boolean
      validateBeforeRegister?: boolean
      continueOnError?: boolean
    } = {}
  ): Promise<{
    registered: number
    failed: number
    warnings: string[]
  }> {
    const { SimpleToFullAdapter } = await import('./simple-to-full')
    
    let registered = 0
    let failed = 0
    const warnings: string[] = []

    for (const plugin of plugins) {
      try {
        // 检测是否为简化插件
        if (this.isSimplePlugin(plugin)) {
          // 使用简化插件适配器
          const result = await SimpleToFullAdapter.batchAdaptAndRegister(
            pluginManager,
            [plugin],
            options
          )
          
          registered += result.success.length
          failed += result.failed.length
          warnings.push(...result.warnings)
        } else {
          // 直接注册完整插件
          await pluginManager.registerPlugin(plugin)
          registered++
        }
      } catch (error) {
        failed++
        if (!options.continueOnError) {
          throw error
        }
        console.warn('Failed to register plugin:', plugin, error)
      }
    }

    return { registered, failed, warnings }
  }

  /**
   * 检测是否为简化插件
   */
  private static isSimplePlugin(plugin: any): boolean {
    return !!(
      plugin &&
      typeof plugin.toFullPlugin === 'function' &&
      plugin.type &&
      plugin.options
    )
  }
}

/**
 * 插件兼容性检查器
 */
export class CompatibilityChecker {
  /**
   * 检查插件兼容性
   */
  static checkCompatibility(plugin: any): {
    compatible: boolean
    version: string
    issues: string[]
    recommendations: string[]
  } {
    const issues: string[] = []
    const recommendations: string[] = []

    // 检查版本格式
    if (!plugin.version || !/^\d+\.\d+\.\d+/.test(plugin.version)) {
      issues.push('Plugin version should follow semantic versioning (x.y.z)')
    }

    // 检查名称规范
    if (!plugin.name || plugin.name.includes(' ')) {
      issues.push('Plugin name should use kebab-case without spaces')
    }

    // 检查循环依赖
    if (plugin.requires && plugin.requires.includes(plugin.name)) {
      issues.push('Plugin cannot depend on itself')
    }

    // 性能建议
    if (plugin.requires && plugin.requires.length > 5) {
      recommendations.push('Consider reducing plugin dependencies for better performance')
    }

    // 安全检查
    if (plugin.setup && plugin.setup.toString().includes('eval')) {
      issues.push('Plugin contains potentially unsafe code (eval)')
    }

    return {
      compatible: issues.length === 0,
      version: plugin.version || '0.0.0',
      issues,
      recommendations
    }
  }

  /**
   * 生成兼容性报告
   */
  static generateReport(plugins: any[]): string {
    const results = plugins.map(plugin => ({
      name: plugin.name || 'Unknown',
      ...this.checkCompatibility(plugin)
    }))

    const compatible = results.filter(r => r.compatible).length
    const total = results.length

    let report = `# Plugin Compatibility Report\n\n`
    report += `**Summary**: ${compatible}/${total} plugins are compatible\n\n`

    results.forEach(result => {
      report += `## ${result.name} (v${result.version})\n`
      report += `**Status**: ${result.compatible ? '✅ Compatible' : '❌ Issues Found'}\n\n`

      if (result.issues.length > 0) {
        report += `**Issues**:\n`
        result.issues.forEach(issue => report += `- ${issue}\n`)
        report += `\n`
      }

      if (result.recommendations.length > 0) {
        report += `**Recommendations**:\n`
        result.recommendations.forEach(rec => report += `- ${rec}\n`)
        report += `\n`
      }
    })

    return report
  }
}