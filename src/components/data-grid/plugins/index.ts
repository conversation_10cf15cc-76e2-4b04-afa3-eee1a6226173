// ============================================================================
// Modern Plugin System - 核心API入口
// ============================================================================

// 核心类型导出
export type {
  PluginDefinition,
  ServiceDefinition,
  ServiceProvider,
  ExtensionPoint,
  PluginSetupFunction,
  PluginTeardownFunction,
  PluginContext,
  PluginContainer,
} from './core/injection'

// 核心类导出
export { InjectionContainer } from './core/InjectionContainer'
export { ModernPluginManager } from './core/ModernPluginManager'

// 内部导入（用于类型）
import { ModernPluginManager } from './core/ModernPluginManager'

// 渲染器和工具 - 通过子模块导入
export * from './renderers'
export * from './dev'
export * from './simple'

// ============================================================================
// 内部导入（用于插件注册和配置）
// ============================================================================

// 导入所有可用插件
import { ModernStatusPlugin } from './renderers/status/modern'
import { ModernBooleanPlugin } from './renderers/boolean/modern'
import { ModernLinkPlugin } from './renderers/link/modern'
import { ModernActionsPlugin } from './renderers/actions/modern'
import { ModernRatingPlugin } from './renderers/rating/modern'
import { CompositePlugin } from './renderers/composite/modern'
import { ModernCurrencyPlugin } from './renderers/currency/modern'
import { ModernDatePlugin } from './renderers/date/modern'
import { ModernImagePlugin } from './renderers/image/modern'

// 导入配置相关
import type { PluginDefinition } from './core/injection'

// 基础插件配置接口
export interface PluginConfig {
  plugins?: PluginDefinition[]
  enableDefaults?: boolean
  debug?: boolean
}

// 可用插件列表
export const AVAILABLE_PLUGINS = [
  ModernStatusPlugin,
  ModernBooleanPlugin,
  ModernLinkPlugin,
  ModernActionsPlugin,
  ModernRatingPlugin,
  CompositePlugin,
  ModernCurrencyPlugin,
  ModernDatePlugin,
  ModernImagePlugin,
] as const

// 预设配置
export const PluginPresets = {
  minimal: {
    plugins: [],
    enableDefaults: false,
  },
  basic: {
    plugins: [ModernStatusPlugin, ModernBooleanPlugin, ModernLinkPlugin],
    enableDefaults: true,
  },
  full: {
    plugins: AVAILABLE_PLUGINS,
    enableDefaults: true,
  },
} as const

// ============================================================================
// 智能配置预设
// ============================================================================

export const SmartPresets = {
  minimal: {
    enableDefaults: false,
    debug: false,
    plugins: [] as PluginDefinition[],
  },
  development: {
    enableDefaults: true,
    debug: true,
    sync: true,
  },
  production: {
    enableDefaults: true,
    debug: false,
    sync: false,
  },
  full: {
    enableDefaults: true,
    debug: import.meta.env.DEV,
    plugins: [...AVAILABLE_PLUGINS] as PluginDefinition[],
    enableAutoAdapter: true,
  },
  forms: {
    enableDefaults: false,
    debug: import.meta.env.DEV,
    plugins: [ModernBooleanPlugin, ModernStatusPlugin] as PluginDefinition[],
  },
  display: {
    enableDefaults: false,
    debug: import.meta.env.DEV,
    plugins: [
      ModernStatusPlugin,
      ModernLinkPlugin,
      ModernDatePlugin,
      ModernCurrencyPlugin,
    ] as PluginDefinition[],
  },
} as const

export type PresetName = keyof typeof SmartPresets

// ============================================================================
// 扩展配置接口
// ============================================================================

export interface ExtendedPluginConfig extends PluginConfig {
  plugins?: PluginDefinition[]
  enableDefaults?: boolean
  debug?: boolean
  simplePlugins?: any[]
  enableAutoAdapter?: boolean
  sync?: boolean
  simpleOptions?: {
    autoFix?: boolean
    validateFirst?: boolean
    continueOnError?: boolean
  }
  performance?: {
    enableMonitoring?: boolean
    enableCache?: boolean
    lazyLoad?: boolean
  }
}

export interface UnifiedManagerOptions extends ExtendedPluginConfig {
  preset?: PresetName
}

// ============================================================================
// 核心创建函数
// ============================================================================

// 全局管理器实例
let globalPluginManager: ModernPluginManager | null = null

/**
 * 统一的插件管理器创建方法
 */
export function createManager(
  options?: PresetName | UnifiedManagerOptions
): ModernPluginManager {
  let config: ExtendedPluginConfig

  if (typeof options === 'string') {
    const preset = SmartPresets[options] as any
    config = {
      ...preset,
      plugins: preset.plugins ? [...preset.plugins] : [],
    }
  } else if (options?.preset) {
    const presetConfig = SmartPresets[options.preset] as any
    config = {
      ...presetConfig,
      ...options,
      plugins: [...(presetConfig.plugins || []), ...(options.plugins || [])],
    }
  } else {
    config = {
      enableDefaults: true,
      debug: import.meta.env.DEV,
      ...options,
    }
  }

  return createPluginManager(config)
}

/**
 * 便利的创建方法 - 扩展createManager
 */
createManager.sync = function (
  options?: PresetName | UnifiedManagerOptions
): ModernPluginManager {
  const config =
    typeof options === 'string'
      ? { ...SmartPresets[options], sync: true }
      : { ...options, sync: true }
  return createManager(config)
}

createManager.async = function (
  options?: PresetName | UnifiedManagerOptions
): ModernPluginManager {
  const config =
    typeof options === 'string'
      ? { ...SmartPresets[options], sync: false }
      : { ...options, sync: false }
  return createManager(config)
}

createManager.preset = function (
  name: PresetName,
  overrides?: Partial<ExtendedPluginConfig>
): ModernPluginManager {
  return createManager({ preset: name, ...overrides })
}

createManager.dev = function (
  overrides?: Partial<ExtendedPluginConfig>
): ModernPluginManager {
  return createManager({ preset: 'development', ...overrides })
}

createManager.prod = function (
  overrides?: Partial<ExtendedPluginConfig>
): ModernPluginManager {
  return createManager({ preset: 'production', ...overrides })
}

/**
 * 全局管理器的统一接口
 */
export const GlobalManager = {
  get(): ModernPluginManager | Promise<ModernPluginManager> {
    if (globalPluginManager) {
      return globalPluginManager
    }
    return getGlobalPluginManager()
  },

  getSync(): ModernPluginManager {
    return getGlobalPluginManagerSyncInternal()
  },

  async getAsync(): Promise<ModernPluginManager> {
    return await getGlobalPluginManager()
  },

  reset(): void {
    resetGlobalPluginManager()
  },
}

/**
 * 内部创建函数 - 由createManager()调用
 */
function createPluginManager(
  config?: ExtendedPluginConfig
): ModernPluginManager {
  const finalConfig: ExtendedPluginConfig = {
    enableDefaults: true,
    debug: import.meta.env.DEV,
    sync: false,
    simpleOptions: {
      autoFix: true,
      validateFirst: true,
      continueOnError: false,
    },
    performance: {
      enableMonitoring: import.meta.env.DEV,
      enableCache: true,
      lazyLoad: false,
    },
    ...config,
    enableAutoAdapter: config?.simplePlugins?.length
      ? true
      : (config?.enableAutoAdapter ?? true),
  }

  const manager = new ModernPluginManager({
    debug: finalConfig.debug ?? import.meta.env.DEV,
  })

  const isSync = finalConfig.sync === true

  // Register default modern plugins
  if (finalConfig.enableDefaults !== false) {
    if (isSync) {
      for (const plugin of AVAILABLE_PLUGINS) {
        manager.registerPluginSync(plugin)
      }
    } else {
      Promise.all(
        AVAILABLE_PLUGINS.map((plugin) => manager.registerPlugin(plugin))
      ).catch((error) => {
        if (finalConfig.debug) {
          console.error('Failed to register default plugins:', error)
        }
      })
    }
  }

  // Register custom plugins
  if (finalConfig.plugins?.length) {
    if (isSync) {
      for (const plugin of finalConfig.plugins) {
        manager.registerPluginSync(plugin)
      }
      if (finalConfig.debug) {
        console.log(
          `✅ Successfully registered ${finalConfig.plugins!.length} custom plugins:`,
          finalConfig.plugins!.map((p) => p.name)
        )
      }
    } else {
      Promise.all(
        finalConfig.plugins.map((plugin) => manager.registerPlugin(plugin))
      ).then(() => {
        if (finalConfig.debug) {
          console.log(
            `✅ Successfully registered ${finalConfig.plugins!.length} custom plugins:`,
            finalConfig.plugins!.map((p) => p.name)
          )
        }
      })
    }
  }

  return manager
}

/**
 * 内部函数 - 获取全局插件管理器
 */
async function getGlobalPluginManager(): Promise<ModernPluginManager> {
  if (!globalPluginManager) {
    globalPluginManager = createPluginManager({ enableDefaults: true })
  }
  return globalPluginManager
}

/**
 * 内部函数 - 同步获取全局插件管理器
 */
function getGlobalPluginManagerSyncInternal(): ModernPluginManager {
  if (!globalPluginManager) {
    globalPluginManager = createPluginManager({
      enableDefaults: true,
      sync: true,
    })
  }
  return globalPluginManager
}

/**
 * 内部函数 - 重置全局插件管理器
 */
function resetGlobalPluginManager(): void {
  globalPluginManager = null
}

// ============================================================================
// 核心API导出完成
// ============================================================================
