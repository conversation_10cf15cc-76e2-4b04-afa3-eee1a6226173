import {
  definePlugin,
  defineRenderer,
  defineConfigSchema,
  createRendererDefinition,
} from '../../core/DevUtils'
import type { InferConfigType } from '../../core/injection'
import { computed, markRaw } from 'vue'
import { cn } from '@/lib/utils'
import { monitorPerformance, performanceUtils } from '../../utils/performanceMonitor'
import StatusRenderer from './renderer.vue'

// Performance-optimized renderer component (pre-mark as raw to prevent reactivity)
const OptimizedStatusRenderer = markRaw(StatusRenderer)

/**
 * 状态渲染器配置Schema
 */
export const StatusConfigSchema = defineConfigSchema({
  statusMap: {
    type: 'object',
    description: '状态映射配置',
    properties: {
      // 动态键值对，键为状态值，值为状态配置
    },
    default: {},
  },
  defaultStatus: {
    type: 'object',
    description: '默认状态配置',
    properties: {
      text: { type: 'string', required: true },
      type: {
        type: 'string',
        enum: ['primary', 'success', 'warning', 'danger', 'info'],
        default: 'default',
      },
      icon: { type: 'string' },
    },
  },
  variant: {
    type: 'string',
    description: '状态显示变体',
    enum: ['badge', 'dot', 'text', 'progress'],
    default: 'badge',
  },
  autoFromMetadata: {
    type: 'boolean',
    description: '是否自动从metadata获取状态配置',
    default: true,
  },
  width: {
    type: 'number',
    description: '列宽度',
    default: 120,
  },
})

/**
 * 状态配置类型（自动推导）
 */
export type StatusConfig = InferConfigType<typeof StatusConfigSchema>

// Import common types from core
// Note: RowData is defined locally above

/**
 * 状态列配置 (从 core/types.ts 迁移)
 */
export interface StatusColumnConfig {
  /** 状态映射（可选，当未配置时会自动从 metadata 中获取） */
  statusMap?: Record<
    string | number,
    {
      text: string
      color?: string
      type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
      icon?: string
    }
  >
  /** 默认状态 */
  defaultStatus?: {
    text: string
    color?: string
    type?: string
    icon?: string
  }
  /** 列宽 */
  width?: number
  /** 是否自动从 metadata 获取枚举值 */
  autoFromMetadata?: boolean
  /** 允许扩展属性 */
  [key: string]: unknown
}

/**
 * 状态渲染器定义（新的声明式API）
 */
export const StatusRendererDefinition = createRendererDefinition({
  name: 'status',
  component: OptimizedStatusRenderer,
  description: '状态列渲染器，支持徽章、圆点、文本和进度条等多种显示方式',
  defaultWidth: 120,
  defaultConfig: {
    statusMap: {},
    variant: 'badge',
    autoFromMetadata: true,
    showIcon: true,
  },
  validator: (config: StatusConfig) => {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证状态映射
    if (config.statusMap) {
      Object.entries(config.statusMap).forEach(([key, value]) => {
        if (!value || typeof value !== 'object') {
          errors.push(`Invalid status mapping for '${key}': must be an object`)
        } else if (!(value as any).text) {
          errors.push(
            `Invalid status mapping for '${key}': missing text property`
          )
        }
      })
    }

    // 验证默认状态
    if (config.defaultStatus && !config.defaultStatus.text) {
      errors.push('Default status must have a text property')
    }

    // 验证变体
    const validVariants = ['badge', 'dot', 'text', 'progress']
    if (config.variant && !validVariants.includes(config.variant)) {
      warnings.push(
        `Unknown variant '${config.variant}', will fall back to 'badge'`
      )
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  },
})

/**
 * 状态插件（新的声明式定义）
 */
export const ModernStatusPlugin = definePlugin({
  name: 'status-modern',
  version: '2.0.0',
  description: '现代化状态渲染器插件，支持多种显示变体和自动配置',

  provides: [
    {
      token: 'StatusRenderer',
      provider: {
        value: StatusRendererDefinition,
        singleton: true,
      },
    },
  ],

  // 注册 ColumnHelper 方法
  columnHelperMethods: [
    {
      name: 'status',
      implementation: function(
        field: string,
        title: string,
        config: StatusColumnConfig = {}
      ) {
        return (this as any).createPluginColumn(field, title, 'status', { field, ...config })
      },
      rendererConfig: {
        component: 'StatusRenderer',
        defaultWidth: 100,
      },
      description: '状态列',
      priority: 2,
    },
  ],

  setup: async (context) => {
    // Performance monitoring for plugin setup
    const setupMetricId = performanceUtils.monitorPluginInit('status-plugin', async () => {
      // 获取渲染器注册表
      const rendererRegistry =
        context.container.resolveService('RendererRegistry')
      if (!rendererRegistry) {
        throw new Error('RendererRegistry service not found')
      }

      // 注册状态渲染器及其变体
      rendererRegistry.set('status', StatusRendererDefinition)

      // 注册变体渲染器
      const variants = [
        { name: 'status-badge', variant: 'badge' },
        { name: 'status-dot', variant: 'dot' },
        { name: 'status-text', variant: 'text' },
        { name: 'status-progress', variant: 'progress' },
      ]

      variants.forEach(({ name, variant }) => {
        const variantDefinition = {
          ...StatusRendererDefinition,
          name,
          defaultConfig: {
            ...StatusRendererDefinition.defaultConfig,
            variant,
          },
        }
        rendererRegistry.set(name, variantDefinition)
      })

      // 注册Vue组件
      context.utils.registerComponent('StatusRenderer', OptimizedStatusRenderer)

      // 注册列助手方法
      const columnHelper = context.container.resolveService('ColumnHelper')
      if (columnHelper) {
        // 扩展列助手，添加状态列方法
        context.container.registerExtensionPoint(
          'column.helper.status',
          (extensionContext, field, title, config) => {
            return {
              field,
              title,
              width: config?.width || 120,
              plugin: 'status',
              pluginConfig: config,
            }
          }
        )
      }

      context.utils.logger.info(
        'Modern status plugin initialized with variants:',
        variants.map((v) => v.name)
      )
    })
  },

  teardown: async (context) => {
    // 清理注册的组件和服务
    const rendererRegistry =
      context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.delete('status')
      rendererRegistry.delete('status-badge')
      rendererRegistry.delete('status-dot')
      rendererRegistry.delete('status-text')
      rendererRegistry.delete('status-progress')
    }

    context.utils.logger.info('Modern status plugin cleaned up')
  },
})

/**
 * 状态渲染器响应式组合式API (用于渲染组件)
 */
export function useStatusRenderer(props: any) {
  // Get status configuration from props
  const config = computed(() => ({
    ...StatusRendererDefinition.defaultConfig,
    ...props.config,
  }))

  console.log('useStatusRenderer', config.value)

  // Get status value from props
  const value = computed(() => {
    return props.field ? props.row?.[props.field] : props.value
  })

  // Get status mapping and default
  const statusMap = computed(() => config.value.statusMap || {})
  const defaultStatus = computed(
    () => config.value.defaultStatus || { text: 'Unknown', type: 'default' }
  )

  // Get current status configuration
  const status = computed(() => {
    const currentValue = value.value
    if (statusMap.value[currentValue]) {
      return statusMap.value[currentValue]
    }
    return defaultStatus.value
  })

  // Generate badge classes based on status type
  const badgeClasses = computed(() => {
    const statusType = status.value.type || 'default'
    const variant = config.value.variant || 'badge'

    return cn('inline-flex items-center', {
      // Badge variants
      'bg-blue-100 text-blue-800':
        statusType === 'primary' && variant === 'badge',
      'bg-green-100 text-green-800':
        statusType === 'success' && variant === 'badge',
      'bg-yellow-100 text-yellow-800':
        statusType === 'warning' && variant === 'badge',
      'bg-red-100 text-red-800': statusType === 'danger' && variant === 'badge',
      'bg-gray-100 text-gray-800': statusType === 'info' && variant === 'badge',
      'bg-muted text-muted-foreground':
        statusType === 'default' && variant === 'badge',
    })
  })

  return {
    status,
    badgeClasses,
    config,
    value,
  }
}

/**
 * 状态渲染器列配置API
 */
export function useStatusColumnAPI() {
  return {
    /**
     * 创建状态列配置
     */
    createStatusColumn: (
      field: string,
      title: string,
      config?: Partial<StatusConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 120,
        plugin: 'status',
        pluginConfig: {
          ...StatusRendererDefinition.defaultConfig,
          ...config,
        },
      }
    },

    /**
     * 创建徽章状态列
     */
    createBadgeColumn: (
      field: string,
      title: string,
      config?: Partial<StatusConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 120,
        plugin: 'status-badge',
        pluginConfig: {
          ...StatusRendererDefinition.defaultConfig,
          variant: 'badge',
          ...config,
        },
      }
    },

    /**
     * 创建圆点状态列
     */
    createDotColumn: (
      field: string,
      title: string,
      config?: Partial<StatusConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 80,
        plugin: 'status-dot',
        pluginConfig: {
          ...StatusRendererDefinition.defaultConfig,
          variant: 'dot',
          ...config,
        },
      }
    },

    /**
     * 创建进度条状态列
     */
    createProgressColumn: (
      field: string,
      title: string,
      config?: Partial<StatusConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 150,
        plugin: 'status-progress',
        pluginConfig: {
          ...StatusRendererDefinition.defaultConfig,
          variant: 'progress',
          ...config,
        },
      }
    },

    /**
     * 验证状态配置
     */
    validateConfig: StatusRendererDefinition.validator,

    /**
     * 获取默认配置
     */
    getDefaultConfig: () => ({ ...StatusRendererDefinition.defaultConfig }),
  }
}

// 导出Vue组件用于向后兼容
export { StatusRenderer }

/**
 * 快速状态映射创建器
 */
export class StatusMapBuilder {
  private statusMap: Record<string | number, any> = {}

  /**
   * 添加状态映射
   */
  add(
    value: string | number | boolean,
    config: { text: string; type?: string; icon?: string; color?: string }
  ) {
    this.statusMap[String(value)] = config
    return this
  }

  /**
   * 添加成功状态
   */
  success(value: string | number | boolean, text: string, icon?: string) {
    return this.add(value, { text, type: 'success', icon })
  }

  /**
   * 添加警告状态
   */
  warning(value: string | number | boolean, text: string, icon?: string) {
    return this.add(value, { text, type: 'warning', icon })
  }

  /**
   * 添加错误状态
   */
  error(value: string | number | boolean, text: string, icon?: string) {
    return this.add(value, { text, type: 'danger', icon })
  }

  /**
   * 添加信息状态
   */
  info(value: string | number | boolean, text: string, icon?: string) {
    return this.add(value, { text, type: 'info', icon })
  }

  /**
   * 构建状态映射
   */
  build() {
    return { ...this.statusMap }
  }

  /**
   * 重置构建器
   */
  reset() {
    this.statusMap = {}
    return this
  }
}

/**
 * 便利函数：创建状态映射
 */
export function createStatusMap() {
  return new StatusMapBuilder()
}

/**
 * 便利函数：快速创建常见状态映射
 */
export const CommonStatusMaps = {
  /**
   * 启用/禁用状态
   */
  enabledDisabled: createStatusMap()
    .success(1, '启用', 'check-circle')
    .success('enabled', '启用', 'check-circle')
    .success(true, '启用', 'check-circle')
    .error(0, '禁用', 'x-circle')
    .error('disabled', '禁用', 'x-circle')
    .error(false, '禁用', 'x-circle')
    .build(),

  /**
   * 活跃/非活跃状态
   */
  activeInactive: createStatusMap()
    .success('active', '活跃', 'activity')
    .warning('inactive', '非活跃', 'pause-circle')
    .build(),

  /**
   * 订单状态
   */
  orderStatus: createStatusMap()
    .info('pending', '待处理', 'clock')
    .warning('processing', '处理中', 'loader')
    .success('completed', '已完成', 'check-circle')
    .error('cancelled', '已取消', 'x-circle')
    .build(),

  /**
   * 支付状态
   */
  paymentStatus: createStatusMap()
    .warning('pending', '待支付', 'clock')
    .success('paid', '已支付', 'credit-card')
    .error('failed', '支付失败', 'alert-circle')
    .info('refunded', '已退款', 'rotate-ccw')
    .build(),
}
