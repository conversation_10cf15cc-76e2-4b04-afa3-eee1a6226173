# Status Renderer Plugin

状态列渲染器插件，用于在数据网格中显示带有不同类型和颜色的状态徽章。

## 目录结构

```
status/
├── index.ts          # 插件入口文件，提供 composable API 和插件类
├── renderer.vue      # Vue 组件，处理渲染逻辑
└── README.md         # 插件文档（当前文件）
```

## 功能特性

- 🎨 **多种样式**: 支持徽章、圆点、进度条三种显示样式
- 🎯 **状态映射**: 支持自定义状态值到显示文本和颜色的映射
- 🔍 **智能获取**: 自动从 metadata 中获取枚举值作为状态配置
- 🎭 **类型支持**: 内置多种状态类型（primary、success、warning、danger、info）
- 🎨 **图标支持**: 支持为状态添加图标
- 🔧 **高度可配置**: 支持默认状态、自定义样式和颜色
- 📱 **响应式**: 完全响应式设计，支持动态配置

## 基本用法

### 1. 安装插件

```typescript
import { StatusPlugin } from '@/components/data-grid/plugins/renderers/status'
import { PluginManager } from '@/components/data-grid/plugins'

// 注册插件
const manager = new PluginManager()
manager.register(new StatusPlugin())
```

### 2. 使用 composable API

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { useStatusRenderer } from '@/components/data-grid/plugins/renderers/status'

const { status, badgeClasses } = useStatusRenderer({
  value: 'active',
  row: { id: 1, status: 'active', name: 'John' },
  config: {
    statusMap: {
      active: { text: '激活', type: 'success' },
      inactive: { text: '未激活', type: 'warning' },
    },
  },
  field: 'status',
})
</script>
```

### 3. 直接使用组件

```vue
<template>
  <StatusRenderer
    :value="'active'"
    :row="{ id: 1, status: 'active', name: 'John' }"
    :config="statusConfig"
    field="status"
  />
</template>

<script setup lang="ts">
import { StatusRenderer } from '@/components/data-grid/plugins/renderers/status'
import type { StatusColumnConfig } from '@/components/data-grid/plugins/renderers/status'

const statusConfig: StatusColumnConfig = {
  statusMap: {
    active: { text: '激活', type: 'success', icon: 'mdi:check-circle' },
    inactive: { text: '未激活', type: 'warning', icon: 'mdi:alert-circle' },
    disabled: { text: '已禁用', type: 'danger', icon: 'mdi:close-circle' },
  },
  defaultStatus: {
    text: '未知状态',
    type: 'info',
  },
}
</script>
```

### 4. 在数据网格中使用

```vue
<script setup lang="ts">
import { useColumnHelper } from '@/components/data-grid/plugins'

const { status } = useColumnHelper()

const columns = [
  // 基本状态列
  status('status', '状态'),

  // 自定义配置的状态列
  status('userStatus', '用户状态', {
    statusMap: {
      1: { text: '正常', type: 'success' },
      2: { text: '冻结', type: 'warning' },
      3: { text: '禁用', type: 'danger' },
    },
    width: 100,
  }),

  // 带图标的状态列
  status('orderStatus', '订单状态', {
    statusMap: {
      pending: { text: '待处理', type: 'warning', icon: 'mdi:clock' },
      processing: { text: '处理中', type: 'info', icon: 'mdi:cog' },
      completed: { text: '已完成', type: 'success', icon: 'mdi:check' },
      cancelled: { text: '已取消', type: 'danger', icon: 'mdi:close' },
    },
  }),
]
</script>
```

## 配置选项

### StatusColumnConfig

```typescript
interface StatusColumnConfig {
  // 状态映射（可选，当未配置时会自动从 metadata 中获取）
  statusMap?: Record<
    string | number,
    {
      text: string // 显示文本
      color?: string // 自定义颜色
      type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' // 状态类型
      icon?: string // 图标名称
    }
  >

  // 默认状态
  defaultStatus?: {
    text: string // 默认显示文本
    color?: string // 默认颜色
    type?: string // 默认类型
    icon?: string // 默认图标
  }

  // 列宽
  width?: number

  // 是否自动从 metadata 获取枚举值
  autoFromMetadata?: boolean // 默认为 true
}
```

### 状态类型样式

不同的状态类型对应不同的颜色样式：

- `primary`: 蓝色，用于主要状态
- `success`: 绿色，用于成功状态
- `warning`: 黄色，用于警告状态
- `danger`: 红色，用于危险/错误状态
- `info`: 灰色，用于信息状态
- `default`: 默认灰色

## 渲染器变体

### 1. 标准徽章 (status)

默认的徽章样式，显示带有背景颜色和文本的徽章。

```typescript
// 自动使用 'status' 渲染器
status('status', '状态', {
  statusMap: {
    active: { text: '激活', type: 'success' },
  },
})
```

### 2. 圆点样式 (status-dot)

显示圆点加文本的样式，更简洁的视觉效果。

```typescript
// 需要在插件管理器中支持 'status-dot' 变体
// 实际使用时可能需要特殊配置
const column = {
  field: 'status',
  title: '状态',
  plugin: 'status',
  pluginConfig: {
    variant: 'dot',
    statusMap: {
      active: { text: '激活', type: 'success' },
    },
  },
}
```

### 3. 进度条样式 (status-progress)

显示进度条样式，适用于表示进度状态。

```typescript
// 需要在插件管理器中支持 'status-progress' 变体
const column = {
  field: 'progress',
  title: '进度',
  plugin: 'status',
  pluginConfig: {
    variant: 'progress',
    showPercentage: true,
    statusMap: {
      0: { text: '未开始', type: 'info' },
      50: { text: '进行中', type: 'warning' },
      100: { text: '已完成', type: 'success' },
    },
  },
}
```

## 使用示例

### 示例 1: 用户状态管理

```typescript
const userStatusConfig: StatusColumnConfig = {
  statusMap: {
    1: {
      text: '正常',
      type: 'success',
      icon: 'mdi:account-check',
    },
    2: {
      text: '冻结',
      type: 'warning',
      icon: 'mdi:account-clock',
    },
    3: {
      text: '禁用',
      type: 'danger',
      icon: 'mdi:account-off',
    },
  },
  defaultStatus: {
    text: '未知',
    type: 'info',
    icon: 'mdi:help-circle',
  },
  width: 120,
}

// 在表格列配置中使用
const columns = [status('userStatus', '用户状态', userStatusConfig)]
```

### 示例 2: 订单状态跟踪

```typescript
const orderStatusConfig: StatusColumnConfig = {
  statusMap: {
    pending: {
      text: '待支付',
      type: 'warning',
      icon: 'mdi:credit-card-clock',
    },
    paid: {
      text: '已支付',
      type: 'info',
      icon: 'mdi:credit-card-check',
    },
    shipped: {
      text: '已发货',
      type: 'primary',
      icon: 'mdi:truck',
    },
    delivered: {
      text: '已送达',
      type: 'success',
      icon: 'mdi:package-check',
    },
    cancelled: {
      text: '已取消',
      type: 'danger',
      icon: 'mdi:cancel',
    },
  },
  autoFromMetadata: false, // 不自动从 metadata 获取
}
```

### 示例 3: 设备在线状态

```typescript
const deviceStatusConfig: StatusColumnConfig = {
  statusMap: {
    online: {
      text: '在线',
      type: 'success',
      icon: 'mdi:wifi',
      color: '#10b981', // 自定义绿色
    },
    offline: {
      text: '离线',
      type: 'danger',
      icon: 'mdi:wifi-off',
      color: '#ef4444', // 自定义红色
    },
    maintenance: {
      text: '维护中',
      type: 'warning',
      icon: 'mdi:wrench',
    },
  },
  defaultStatus: {
    text: '未知',
    type: 'info',
  },
}
```

### 示例 4: 任务完成度

```typescript
const taskStatusConfig: StatusColumnConfig = {
  statusMap: {
    0: { text: '未开始', type: 'info' },
    25: { text: '25%', type: 'warning' },
    50: { text: '50%', type: 'warning' },
    75: { text: '75%', type: 'primary' },
    100: { text: '已完成', type: 'success', icon: 'mdi:check-all' },
  },
  // 启用自动从 metadata 获取，如果 metadata 中有相关配置
  autoFromMetadata: true,
}
```

## API 参考

### useStatusRenderer

composable 函数，提供状态列渲染所需的响应式数据和计算属性。

```typescript
function useStatusRenderer(props: {
  value?: any
  row: any
  config: StatusColumnConfig
  field: string
}): {
  status: ComputedRef<{
    text: string
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
    icon?: string
  }>
  badgeClasses: ComputedRef<string>
}
```

#### 参数说明

- `value`: 当前单元格的值
- `row`: 当前行的完整数据对象
- `config`: 状态列配置对象
- `field`: 字段名称，用于从 metadata 中获取配置

#### 返回值

- `status`: 计算后的状态对象，包含显示文本、类型和图标
- `badgeClasses`: 计算后的 CSS 类名字符串

### StatusPlugin

插件类，用于在插件管理器中注册状态列渲染器。

```typescript
class StatusPlugin implements Plugin {
  name: string = 'status'
  version: string = '1.0.0'
  description: string =
    'Status column plugin for displaying status badges with different types and colors'

  install(manager: PluginManager): void
  uninstall(): void
}
```

### StatusRenderer

Vue 组件，处理状态列的实际渲染。

```typescript
interface Props {
  value?: any
  row: any
  config: StatusColumnConfig
  field: string
}
```

### getStatusFromMetadata

辅助函数，用于从全局 metadata 中获取状态配置。

```typescript
function getStatusFromMetadata(
  field: string,
  value: any
): {
  text: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
} | null
```

这个函数目前返回 `null`，但可以根据项目需求进行扩展，从全局状态、API 或其他数据源中获取状态配置。

## 样式定制

### CSS 类名

状态列渲染器使用以下 CSS 类名，可以通过自定义样式进行定制：

```css
/* 状态徽章基础类名 */
.status-badge           /* 状态徽章容器 */
.status-badge.status-primary    /* 主要状态样式 */
.status-badge.status-success    /* 成功状态样式 */
.status-badge.status-warning    /* 警告状态样式 */
.status-badge.status-danger     /* 危险状态样式 */
.status-badge.status-info       /* 信息状态样式 */
.status-badge.status-default    /* 默认状态样式 */

/* 圆点变体类名 */
.status-dot             /* 圆点状态容器 */

/* 进度条变体类名 */
.status-progress        /* 进度条状态容器 */
```

### Tailwind CSS 定制

组件使用 Tailwind CSS 类名进行样式设置，可以通过修改样式工具函数来定制：

```typescript
// 在 styleUtils.ts 中定制状态类型样式
export const statusTypeClasses = {
  primary: 'bg-blue-100 text-blue-800 border-blue-200',
  success: 'bg-green-100 text-green-800 border-green-200',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  danger: 'bg-red-100 text-red-800 border-red-200',
  info: 'bg-gray-100 text-gray-800 border-gray-200',
  default: 'bg-gray-100 text-gray-600 border-gray-200',
}
```

## 性能优化

### 响应式计算

组件使用 Vue 3 的 `computed` 来确保只在必要时重新计算状态：

```typescript
const status = computed(() => {
  // 优先使用直接配置的状态映射
  if (props.config.statusMap && props.config.statusMap[props.value]) {
    return props.config.statusMap[props.value]
  }

  // 其次尝试从 metadata 获取
  // 最后使用默认状态或 fallback
})
```

### 条件渲染

合理使用 `v-if` 指令避免不必要的渲染：

```vue
<template>
  <Badge :class="badgeClasses">
    <Icon v-if="status.icon" :icon="status.icon" class="mr-1 inline-block" />
    {{ status.text }}
  </Badge>
</template>
```

## 故障排除

### 常见问题

1. **状态不显示或显示异常**

   - 检查 `statusMap` 配置是否正确
   - 确认状态值是否匹配映射中的 key
   - 验证 `defaultStatus` 是否配置

2. **图标不显示**

   - 确认图标库已正确安装和配置
   - 检查 `icon` 字段的图标名称是否正确
   - 验证 `@iconify/vue` 组件是否正确导入

3. **样式异常**

   - 确认 Tailwind CSS 已正确配置
   - 检查是否有样式冲突
   - 验证 `badgeClasses` 计算是否正确

4. **metadata 自动获取不工作**
   - 检查 `autoFromMetadata` 是否设置为 `true`
   - 确认 `getStatusFromMetadata` 函数是否正确实现
   - 验证全局 metadata 是否正确设置

### 调试技巧

1. **使用 Vue DevTools**: 查看组件的 props 和计算属性
2. **控制台调试**: 在配置中添加调试日志

```typescript
const statusConfig: StatusColumnConfig = {
  statusMap: {
    active: {
      text: '激活',
      type: 'success',
      // 可以在这里添加调试逻辑
    },
  },
}

// 在 composable 中调试
const { status } = useStatusRenderer({
  // ... props
})

// 监听状态变化
watchEffect(() => {
  console.log('当前状态:', status.value)
})
```

3. **检查渲染结果**: 在浏览器开发者工具中检查生成的 HTML 结构

## 扩展开发

### 自定义状态获取逻辑

可以扩展 `getStatusFromMetadata` 函数来实现自定义的状态获取逻辑：

```typescript
function getStatusFromMetadata(field: string, value: any) {
  try {
    // 从 API 获取状态配置
    const apiConfig = window.__APP_CONFIG__?.statusConfig?.[field]
    if (apiConfig && apiConfig[value]) {
      return apiConfig[value]
    }

    // 从全局状态管理获取
    const store = useStatusStore()
    const storeConfig = store.getStatusConfig(field, value)
    if (storeConfig) {
      return storeConfig
    }

    return null
  } catch (error) {
    console.warn(`获取状态配置失败: ${field}`, error)
    return null
  }
}
```

### 添加新的渲染器变体

可以通过扩展插件来添加新的状态渲染器变体：

```typescript
export class ExtendedStatusPlugin extends StatusPlugin {
  install(manager: PluginManager): void {
    super.install(manager)

    const registry = manager.getRendererRegistry()

    // 添加自定义的状态渲染器变体
    registry.register('status-chip', {
      name: 'status-chip',
      component: 'StatusRenderer',
      render: (params: any) => {
        const chipConfig = {
          ...params.config,
          variant: 'chip',
        }
        return `__COMPONENT__:StatusRenderer:${JSON.stringify({
          value: params.value,
          row: params.row,
          config: chipConfig,
          field: params.field,
        })}`
      },
    })
  }
}
```

## 最佳实践

1. **合理使用状态类型**: 为不同的业务状态选择合适的类型（success、warning、danger 等）
2. **提供有意义的文本**: 状态文本应该简洁明了，易于用户理解
3. **使用图标增强体验**: 在适当的地方添加图标来提升用户体验
4. **考虑无障碍访问**: 确保颜色对比度足够，不仅仅依赖颜色来传达信息
5. **性能优化**: 合理使用 `autoFromMetadata`，避免不必要的 metadata 查询
6. **错误处理**: 为异常情况提供合适的默认状态
7. **国际化支持**: 在多语言项目中，考虑状态文本的国际化

## 更新日志

### v1.0.0

- 初始版本发布
- 支持基本的状态徽章渲染
- 提供 composable API
- 支持多种状态类型和图标
- 支持从 metadata 自动获取状态配置
- 提供三种渲染器变体（status、status-dot、status-progress）

## 许可证

MIT License
