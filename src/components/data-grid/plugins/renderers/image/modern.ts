import { computed, ref } from 'vue'
import {
  definePlugin,
  defineRenderer,
  defineConfigSchema,
} from '../../core/DevUtils'
import type { InferConfigType } from '../../core/injection'
import { getRealConfig } from '../../core/ConfigStore'
import ImageRenderer from './renderer.vue'

/**
 * 图片配置Schema
 */
export const ImageConfigSchema = defineConfigSchema({
  width: {
    type: 'number',
    description: '图片宽度',
    default: 60,
    minimum: 20,
    maximum: 300,
  },
  height: {
    type: 'number',
    description: '图片高度',
    default: 60,
    minimum: 20,
    maximum: 300,
  },
  fit: {
    type: 'string',
    description: '图片适应方式',
    enum: ['cover', 'contain', 'fill', 'scale-down', 'none'],
    default: 'cover',
  },
  shape: {
    type: 'string',
    description: '图片形状',
    enum: ['square', 'circle', 'rounded'],
    default: 'rounded',
  },
  lazy: {
    type: 'boolean',
    description: '是否启用懒加载',
    default: true,
  },
  preview: {
    type: 'boolean',
    description: '是否支持预览',
    default: true,
  },
  fallback: {
    type: 'string',
    description: '失败时的后备图片URL',
    default: '',
  },
  placeholder: {
    type: 'string',
    description: '加载时的占位图片URL',
    default: '',
  },
  showAlt: {
    type: 'boolean',
    description: '是否显示alt文本作为tooltip',
    default: true,
  },
  quality: {
    type: 'number',
    description: '图片质量(0-100)',
    default: 85,
    minimum: 1,
    maximum: 100,
  },
  compress: {
    type: 'boolean',
    description: '是否启用压缩',
    default: true,
  },
})

/**
 * 图片配置类型
 */
export type ImageConfig = InferConfigType<typeof ImageConfigSchema>

/**
 * 图片列配置
 */
export interface ImageColumnConfig extends ImageConfig {
  /** 列宽 */
  width?: number
  /** 是否固定 */
  fixed?: 'left' | 'right'
  /** 允许扩展属性 */
  [key: string]: unknown
}

/**
 * 图片渲染器定义
 */
export const ImageRendererDefinition = defineRenderer({
  name: 'image',
  component: ImageRenderer,
  description: '图片渲染器，支持懒加载、预览和多种显示模式',
  defaultWidth: 100,
  defaultConfig: {
    width: 60,
    height: 60,
    fit: 'cover',
    shape: 'rounded',
    lazy: true,
    preview: true,
    fallback: '',
    placeholder: '',
    showAlt: true,
    quality: 85,
    compress: true,
  },
  props: [
    {
      name: 'value',
      type: ['string', 'object'],
      required: true,
      description: '图片URL或图片对象',
    },
    {
      name: 'config',
      type: 'object',
      required: true,
      description: '图片配置',
    },
    {
      name: 'row',
      type: 'object',
      description: '行数据',
    },
  ],
  validator: (config: ImageConfig) => {
    const errors: string[] = []
    const warnings: string[] = []

    if (config.width <= 0 || config.height <= 0) {
      errors.push('width and height must be positive numbers')
    }

    if (config.width < 20 || config.height < 20) {
      warnings.push('Very small images may not display properly')
    }

    if (config.width > 300 || config.height > 300) {
      warnings.push('Large images may impact performance')
    }

    if (config.quality < 50) {
      warnings.push('Low quality setting may result in blurry images')
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  },
})

/**
 * 图片值处理器
 */
export class ImageValueProcessor {
  /**
   * 解析图片值
   */
  static parseValue(value: any): { url: string; alt?: string; title?: string } | null {
    if (!value) return null

    if (typeof value === 'string') {
      return { url: value }
    }

    if (typeof value === 'object' && value.url) {
      return {
        url: value.url,
        alt: value.alt || value.name || '',
        title: value.title || value.description || value.alt || '',
      }
    }

    return null
  }

  /**
   * 验证图片URL
   */
  static validateUrl(url: string): boolean {
    if (!url || typeof url !== 'string') return false

    // 检查是否是有效的URL格式
    try {
      new URL(url)
      return true
    } catch {
      // 检查是否是相对路径
      return /^[./]/.test(url) || /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(url)
    }
  }

  /**
   * 生成缩略图URL
   */
  static generateThumbnail(url: string, config: ImageConfig): string {
    if (!url) return ''

    // 如果已经是缩略图或者不需要压缩，直接返回
    if (!config.compress || url.includes('thumbnail') || url.includes('thumb')) {
      return url
    }

    // 简单的缩略图生成逻辑（实际项目中可能需要调用专门的图片服务）
    try {
      const urlObj = new URL(url)
      urlObj.searchParams.set('w', String(config.width))
      urlObj.searchParams.set('h', String(config.height))
      urlObj.searchParams.set('q', String(config.quality))
      urlObj.searchParams.set('fit', config.fit)
      return urlObj.toString()
    } catch {
      // 如果不是完整URL，返回原始值
      return url
    }
  }

  /**
   * 获取默认占位图
   */
  static getDefaultPlaceholder(config: ImageConfig): string {
    const { width, height } = config
    // 生成SVG占位图
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f3f4f6"/>
        <text x="50%" y="50%" font-family="Arial" font-size="12" fill="#9ca3af" text-anchor="middle" dy=".3em">
          ${width}×${height}
        </text>
      </svg>
    `
    return `data:image/svg+xml;base64,${btoa(svg)}`
  }

  /**
   * 获取默认错误图片
   */
  static getDefaultFallback(config: ImageConfig): string {
    const { width, height } = config
    // 生成SVG错误图
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#fef2f2"/>
        <path d="M12 8l-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14l-6-6z" fill="#ef4444" transform="translate(${width/2-12}, ${height/2-12})"/>
        <text x="50%" y="75%" font-family="Arial" font-size="10" fill="#ef4444" text-anchor="middle">
          加载失败
        </text>
      </svg>
    `
    return `data:image/svg+xml;base64,${btoa(svg)}`
  }
}

/**
 * 图片URL生成器
 */
export class ImageUrlGenerator {
  private config: ImageConfig

  constructor(config: ImageConfig) {
    this.config = config
  }

  /**
   * 生成优化后的图片URL
   */
  generateOptimizedUrl(originalUrl: string): string {
    if (!originalUrl) return ''

    return ImageValueProcessor.generateThumbnail(originalUrl, this.config)
  }

  /**
   * 生成占位图URL
   */
  generatePlaceholderUrl(): string {
    return this.config.placeholder || ImageValueProcessor.getDefaultPlaceholder(this.config)
  }

  /**
   * 生成错误回退图URL
   */
  generateFallbackUrl(): string {
    return this.config.fallback || ImageValueProcessor.getDefaultFallback(this.config)
  }
}

/**
 * 图片渲染器组合式API
 */
export function useImageRenderer(props?: any) {
  if (props) {
    // 获取真实配置
    const realConfig = computed(() => {
      if (props.configId) {
        const storedConfig = getRealConfig(props.configId)
        if (storedConfig) {
          return storedConfig
        }
      }
      return {
        ...ImageRendererDefinition.defaultConfig,
        ...props.config,
      }
    })

    // 解析图片值
    const imageData = computed(() => {
      return ImageValueProcessor.parseValue(props.value)
    })

    // URL生成器
    const urlGenerator = computed(() => {
      return new ImageUrlGenerator(realConfig.value)
    })

    // 优化后的图片URL
    const optimizedUrl = computed(() => {
      if (!imageData.value?.url) return ''
      return urlGenerator.value.generateOptimizedUrl(imageData.value.url)
    })

    // 占位图URL
    const placeholderUrl = computed(() => {
      return urlGenerator.value.generatePlaceholderUrl()
    })

    // 错误回退图URL
    const fallbackUrl = computed(() => {
      return urlGenerator.value.generateFallbackUrl()
    })

    // 图片样式
    const imageStyle = computed(() => {
      const { width, height, fit } = realConfig.value
      return {
        width: `${width}px`,
        height: `${height}px`,
        objectFit: fit,
      }
    })

    // 容器样式类
    const containerClass = computed(() => {
      const classes = ['image-container']
      
      switch (realConfig.value.shape) {
        case 'circle':
          classes.push('rounded-full')
          break
        case 'rounded':
          classes.push('rounded-lg')
          break
        case 'square':
        default:
          break
      }

      if (realConfig.value.preview) {
        classes.push('cursor-pointer hover:opacity-80 transition-opacity')
      }

      return classes.join(' ')
    })

    // 是否有效图片
    const isValidImage = computed(() => {
      return imageData.value && ImageValueProcessor.validateUrl(imageData.value.url)
    })

    // 加载状态
    const loading = ref(true)
    const error = ref(false)

    // 处理图片加载完成
    const handleLoad = () => {
      loading.value = false
      error.value = false
    }

    // 处理图片加载错误
    const handleError = () => {
      loading.value = false
      error.value = true
    }

    // 处理预览点击
    const handlePreview = () => {
      if (!realConfig.value.preview || !imageData.value?.url) return

      // 这里可以集成图片预览组件，比如弹窗或者lightbox
      // 简单实现：在新窗口打开图片
      window.open(imageData.value.url, '_blank')
    }

    return {
      imageData,
      optimizedUrl,
      placeholderUrl,
      fallbackUrl,
      imageStyle,
      containerClass,
      isValidImage,
      loading,
      error,
      handleLoad,
      handleError,
      handlePreview,
      realConfig,
    }
  }

  // 工具函数API
  return {
    /**
     * 创建图片列配置
     */
    createImageColumn: (
      field: string,
      title: string,
      config?: Partial<ImageConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 100,
        plugin: 'image',
        pluginConfig: {
          ...ImageRendererDefinition.defaultConfig,
          ...config,
        },
      }
    },

    /**
     * 验证配置
     */
    validateConfig: ImageRendererDefinition.validator,

    /**
     * 获取默认配置
     */
    getDefaultConfig: () => ({ ...ImageRendererDefinition.defaultConfig }),

    /**
     * 创建URL生成器
     */
    createUrlGenerator: (config: ImageConfig) => new ImageUrlGenerator(config),
  }
}

/**
 * 预定义图片配置
 */
export const CommonImageConfigs = {
  /**
   * 头像配置
   */
  avatar: {
    width: 40,
    height: 40,
    shape: 'circle',
    fit: 'cover',
    lazy: true,
    preview: true,
  } as ImageConfig,

  /**
   * 缩略图配置
   */
  thumbnail: {
    width: 60,
    height: 60,
    shape: 'rounded',
    fit: 'cover',
    lazy: true,
    preview: true,
    compress: true,
  } as ImageConfig,

  /**
   * 产品图片配置
   */
  product: {
    width: 80,
    height: 60,
    shape: 'rounded',
    fit: 'contain',
    lazy: true,
    preview: true,
    quality: 90,
  } as ImageConfig,

  /**
   * 大图配置
   */
  large: {
    width: 120,
    height: 90,
    shape: 'rounded',
    fit: 'cover',
    lazy: true,
    preview: true,
    quality: 95,
  } as ImageConfig,

  /**
   * 方形小图标
   */
  icon: {
    width: 24,
    height: 24,
    shape: 'square',
    fit: 'contain',
    lazy: false,
    preview: false,
    compress: false,
  } as ImageConfig,

  /**
   * 横幅图配置
   */
  banner: {
    width: 200,
    height: 60,
    shape: 'rounded',
    fit: 'cover',
    lazy: true,
    preview: true,
    quality: 80,
  } as ImageConfig,
}

/**
 * 图片插件
 */
export const ModernImagePlugin = definePlugin({
  name: 'image-modern',
  version: '2.0.0',
  description: '现代化图片渲染器插件，支持懒加载、预览和多种显示模式',

  provides: [
    {
      token: 'ImageRenderer',
      provider: {
        value: ImageRendererDefinition,
        singleton: true,
      },
    },
  ],

  // 注册 ColumnHelper 方法
  columnHelperMethods: [
    {
      name: 'image',
      implementation: function (
        field: string,
        title: string,
        config: ImageColumnConfig = {}
      ) {
        return (this as any).createPluginColumn(field, title, 'image', config)
      },
      rendererConfig: {
        component: 'ImageRenderer',
        defaultWidth: 100,
      },
      description: '图片列',
      priority: 6,
    },
  ],

  setup: async (context) => {
    // 获取渲染器注册表
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    if (!rendererRegistry) {
      throw new Error('RendererRegistry service not found')
    }

    // 注册图片渲染器
    rendererRegistry.set('image', ImageRendererDefinition)

    // 注册预定义图片变体
    Object.entries(CommonImageConfigs).forEach(([name, config]) => {
      const variantName = `image-${name}`
      const variantDefinition = {
        ...ImageRendererDefinition,
        name: variantName,
        defaultConfig: {
          ...ImageRendererDefinition.defaultConfig,
          ...config,
        },
      }
      rendererRegistry.set(variantName, variantDefinition)
    })

    // 注册Vue组件
    context.utils.registerComponent('ImageRenderer', ImageRenderer)

    context.utils.logger.info(
      'Modern image plugin initialized with variants:',
      Object.keys(CommonImageConfigs).map(name => `image-${name}`)
    )
  },

  teardown: async (context) => {
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.delete('image')
      Object.keys(CommonImageConfigs).forEach(name => {
        rendererRegistry.delete(`image-${name}`)
      })
    }

    context.utils.logger.info('Modern image plugin cleaned up')
  },
})

// 重新导出兼容性类型
export const ImagePlugin = ModernImagePlugin
export { ImageRenderer }
