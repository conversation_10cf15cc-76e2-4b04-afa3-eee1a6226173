import { computed } from 'vue'
import {
  definePlugin,
  defineRenderer,
  defineConfigSchema,
} from '../../core/DevUtils'
import type { InferConfigType } from '../../core/injection'
import { getRealConfig } from '../../core/ConfigStore'
import DateRenderer from './renderer.vue'

/**
 * 日期配置Schema
 */
export const DateConfigSchema = defineConfigSchema({
  format: {
    type: 'string',
    description: '日期格式',
    default: 'YYYY-MM-DD',
  },
  showTime: {
    type: 'boolean',
    description: '是否显示时间',
    default: false,
  },
  timeFormat: {
    type: 'string',
    description: '时间格式',
    default: 'HH:mm:ss',
  },
  locale: {
    type: 'string',
    description: '本地化设置',
    default: 'zh-CN',
  },
  relative: {
    type: 'boolean',
    description: '是否显示相对时间',
    default: false,
  },
  showTooltip: {
    type: 'boolean',
    description: '是否显示工具提示',
    default: true,
  },
  emptyText: {
    type: 'string',
    description: '空值显示文本',
    default: '--',
  },
  highlightToday: {
    type: 'boolean',
    description: '是否高亮今天',
    default: false,
  },
  weekStart: {
    type: 'number',
    description: '一周开始的天(0=周日,1=周一)',
    default: 1,
    minimum: 0,
    maximum: 6,
  },
})

/**
 * 日期配置类型
 */
export type DateConfig = InferConfigType<typeof DateConfigSchema>

/**
 * 日期列配置
 */
export interface DateColumnConfig extends DateConfig {
  /** 列宽 */
  width?: number
  /** 是否固定 */
  fixed?: 'left' | 'right'
  /** 允许扩展属性 */
  [key: string]: unknown
}

/**
 * 日期渲染器定义
 */
export const DateRendererDefinition = defineRenderer({
  name: 'date',
  component: DateRenderer,
  description: '日期渲染器，支持多种日期格式和本地化',
  defaultWidth: 140,
  defaultConfig: {
    format: 'YYYY-MM-DD',
    showTime: false,
    timeFormat: 'HH:mm:ss',
    locale: 'zh-CN',
    relative: false,
    showTooltip: true,
    emptyText: '--',
    highlightToday: false,
    weekStart: 1,
  },
  props: [
    {
      name: 'value',
      type: ['string', 'number', 'object'],
      required: true,
      description: '日期值',
    },
    {
      name: 'config',
      type: 'object',
      required: true,
      description: '日期配置',
    },
    {
      name: 'row',
      type: 'object',
      description: '行数据',
    },
  ],
  validator: (config: DateConfig) => {
    const errors: string[] = []
    const warnings: string[] = []

    if (!config.format) {
      errors.push('format is required')
    }

    if (config.weekStart < 0 || config.weekStart > 6) {
      warnings.push('weekStart should be between 0 and 6')
    }

    // 验证日期格式
    const validFormats = [
      'YYYY-MM-DD',
      'YYYY/MM/DD',
      'DD-MM-YYYY',
      'DD/MM/YYYY',
      'MM-DD-YYYY',
      'MM/DD/YYYY',
      'YYYY年MM月DD日',
    ]
    
    if (!validFormats.some(fmt => config.format.includes(fmt.replace(/[YMD]/g, '')))) {
      warnings.push(`Format '${config.format}' may not be supported`)
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  },
})

/**
 * 日期值处理器
 */
export class DateValueProcessor {
  /**
   * 格式化日期值
   */
  static format(value: any, config: DateConfig): string {
    if (!value) {
      return config.emptyText || '--'
    }

    try {
      const date = this.parseDate(value)
      if (!date || isNaN(date.getTime())) {
        return config.emptyText || '--'
      }

      if (config.relative) {
        return this.formatRelative(date, config.locale)
      }

      return this.formatDate(date, config)
    } catch (error) {
      console.warn('Date formatting error:', error)
      return config.emptyText || '--'
    }
  }

  /**
   * 解析日期值
   */
  static parseDate(value: any): Date | null {
    if (!value) return null

    if (value instanceof Date) {
      return value
    }

    if (typeof value === 'string') {
      // 尝试解析各种日期格式
      const date = new Date(value)
      if (!isNaN(date.getTime())) {
        return date
      }

      // 尝试解析时间戳字符串
      const timestamp = parseInt(value, 10)
      if (!isNaN(timestamp)) {
        return new Date(timestamp)
      }
    }

    if (typeof value === 'number') {
      // 时间戳
      return new Date(value)
    }

    return null
  }

  /**
   * 格式化日期
   */
  static formatDate(date: Date, config: DateConfig): string {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    let formatted = config.format
      .replace(/YYYY/g, String(year))
      .replace(/YY/g, String(year).slice(-2))
      .replace(/MM/g, month)
      .replace(/M/g, String(date.getMonth() + 1))
      .replace(/DD/g, day)
      .replace(/D/g, String(date.getDate()))

    if (config.showTime) {
      const timeFormatted = config.timeFormat
        .replace(/HH/g, hours)
        .replace(/H/g, String(date.getHours()))
        .replace(/mm/g, minutes)
        .replace(/m/g, String(date.getMinutes()))
        .replace(/ss/g, seconds)
        .replace(/s/g, String(date.getSeconds()))

      formatted += ` ${timeFormatted}`
    }

    return formatted
  }

  /**
   * 格式化相对时间
   */
  static formatRelative(date: Date, locale: string): string {
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) {
      return locale === 'zh-CN' ? '刚刚' : 'just now'
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60)
    if (diffInMinutes < 60) {
      return locale === 'zh-CN' ? `${diffInMinutes}分钟前` : `${diffInMinutes}m ago`
    }

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) {
      return locale === 'zh-CN' ? `${diffInHours}小时前` : `${diffInHours}h ago`
    }

    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) {
      return locale === 'zh-CN' ? `${diffInDays}天前` : `${diffInDays}d ago`
    }

    // 超过一周显示具体日期
    return this.formatDate(date, {
      format: 'YYYY-MM-DD',
      showTime: false,
      timeFormat: 'HH:mm:ss',
      locale,
      relative: false,
      showTooltip: true,
      emptyText: '--',
      highlightToday: false,
      weekStart: 1,
    })
  }

  /**
   * 检查是否是今天
   */
  static isToday(date: Date): boolean {
    const today = new Date()
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    )
  }

  /**
   * 获取工具提示文本
   */
  static getTooltip(date: Date, config: DateConfig): string {
    if (!config.showTooltip) return ''

    const formatted = this.formatDate(date, {
      ...config,
      format: 'YYYY年MM月DD日 dddd',
      showTime: true,
      timeFormat: 'HH:mm:ss',
    })

    const weekdays = config.locale === 'zh-CN' 
      ? ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      : ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

    const weekday = weekdays[date.getDay()]
    
    return formatted.replace('dddd', weekday)
  }

  /**
   * 验证日期值
   */
  static validate(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return true // 空值允许
    }

    const date = this.parseDate(value)
    return date !== null && !isNaN(date.getTime())
  }
}

/**
 * 日期格式化工具
 */
export class DateFormatter {
  private config: DateConfig

  constructor(config: DateConfig) {
    this.config = config
  }

  /**
   * 格式化为显示文本
   */
  format(value: any): string {
    return DateValueProcessor.format(value, this.config)
  }

  /**
   * 格式化为本地化日期
   */
  formatLocalized(value: any): string {
    const date = DateValueProcessor.parseDate(value)
    if (!date || isNaN(date.getTime())) {
      return this.config.emptyText || '--'
    }

    try {
      const options: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      }

      if (this.config.showTime) {
        options.hour = '2-digit'
        options.minute = '2-digit'
        options.second = '2-digit'
      }

      return new Intl.DateTimeFormat(this.config.locale, options).format(date)
    } catch (error) {
      // 回退到简单格式化
      return this.format(value)
    }
  }

  /**
   * 获取工具提示
   */
  getTooltip(value: any): string {
    const date = DateValueProcessor.parseDate(value)
    if (!date || isNaN(date.getTime())) {
      return ''
    }

    return DateValueProcessor.getTooltip(date, this.config)
  }
}

/**
 * 日期渲染器组合式API
 */
export function useDateRenderer(props?: any) {
  if (props) {
    // 获取真实配置
    const realConfig = computed(() => {
      if (props.configId) {
        const storedConfig = getRealConfig(props.configId)
        if (storedConfig) {
          return storedConfig
        }
      }
      return {
        ...DateRendererDefinition.defaultConfig,
        ...props.config,
      }
    })

    // 格式化后的值
    const formattedValue = computed(() => {
      return DateValueProcessor.format(props.value, realConfig.value)
    })

    // 工具提示文本
    const tooltipText = computed(() => {
      const date = DateValueProcessor.parseDate(props.value)
      if (!date || isNaN(date.getTime())) {
        return ''
      }
      return DateValueProcessor.getTooltip(date, realConfig.value)
    })

    // 日期样式类
    const dateClass = computed(() => {
      const classes = ['date-value']
      
      if (realConfig.value.highlightToday) {
        const date = DateValueProcessor.parseDate(props.value)
        if (date && DateValueProcessor.isToday(date)) {
          classes.push('today')
        }
      }

      if (realConfig.value.relative) {
        classes.push('relative')
      }

      return classes.join(' ')
    })

    // 是否有效值
    const isValid = computed(() => {
      return DateValueProcessor.validate(props.value)
    })

    return {
      formattedValue,
      tooltipText,
      dateClass,
      isValid,
      realConfig,
    }
  }

  // 工具函数API
  return {
    /**
     * 创建日期列配置
     */
    createDateColumn: (
      field: string,
      title: string,
      config?: Partial<DateConfig>
    ) => {
      return {
        field,
        title,
        width: config?.width || 140,
        plugin: 'date',
        pluginConfig: {
          ...DateRendererDefinition.defaultConfig,
          ...config,
        },
      }
    },

    /**
     * 验证配置
     */
    validateConfig: DateRendererDefinition.validator,

    /**
     * 获取默认配置
     */
    getDefaultConfig: () => ({ ...DateRendererDefinition.defaultConfig }),

    /**
     * 创建格式化器
     */
    createFormatter: (config: DateConfig) => new DateFormatter(config),
  }
}

/**
 * 预定义日期配置
 */
export const CommonDateConfigs = {
  /**
   * 标准日期格式
   */
  standard: {
    format: 'YYYY-MM-DD',
    showTime: false,
    locale: 'zh-CN',
  } as DateConfig,

  /**
   * 日期时间格式
   */
  datetime: {
    format: 'YYYY-MM-DD',
    showTime: true,
    timeFormat: 'HH:mm:ss',
    locale: 'zh-CN',
  } as DateConfig,

  /**
   * 相对时间格式
   */
  relative: {
    format: 'YYYY-MM-DD',
    showTime: false,
    relative: true,
    locale: 'zh-CN',
  } as DateConfig,

  /**
   * 中文格式
   */
  chinese: {
    format: 'YYYY年MM月DD日',
    showTime: false,
    locale: 'zh-CN',
    highlightToday: true,
  } as DateConfig,

  /**
   * 美式格式
   */
  us: {
    format: 'MM/DD/YYYY',
    showTime: false,
    locale: 'en-US',
    weekStart: 0,
  } as DateConfig,

  /**
   * 欧式格式
   */
  eu: {
    format: 'DD/MM/YYYY',
    showTime: false,
    locale: 'en-GB',
    weekStart: 1,
  } as DateConfig,

  /**
   * ISO格式
   */
  iso: {
    format: 'YYYY-MM-DD',
    showTime: true,
    timeFormat: 'HH:mm:ss',
    locale: 'en-US',
  } as DateConfig,
}

/**
 * 日期插件
 */
export const ModernDatePlugin = definePlugin({
  name: 'date-modern',
  version: '2.0.0',
  description: '现代化日期渲染器插件，支持多种日期格式和本地化',

  provides: [
    {
      token: 'DateRenderer',
      provider: {
        value: DateRendererDefinition,
        singleton: true,
      },
    },
  ],

  // 注册 ColumnHelper 方法
  columnHelperMethods: [
    {
      name: 'date',
      implementation: function (
        field: string,
        title: string,
        config: DateColumnConfig = {}
      ) {
        return (this as any).createPluginColumn(field, title, 'date', config)
      },
      rendererConfig: {
        component: 'DateRenderer',
        defaultWidth: 140,
      },
      description: '日期列',
      priority: 9,
    },
  ],

  setup: async (context) => {
    // 获取渲染器注册表
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    if (!rendererRegistry) {
      throw new Error('RendererRegistry service not found')
    }

    // 注册日期渲染器
    rendererRegistry.set('date', DateRendererDefinition)

    // 注册预定义日期变体
    Object.entries(CommonDateConfigs).forEach(([name, config]) => {
      const variantName = `date-${name}`
      const variantDefinition = {
        ...DateRendererDefinition,
        name: variantName,
        defaultConfig: {
          ...DateRendererDefinition.defaultConfig,
          ...config,
        },
      }
      rendererRegistry.set(variantName, variantDefinition)
    })

    // 注册Vue组件
    context.utils.registerComponent('DateRenderer', DateRenderer)

    context.utils.logger.info(
      'Modern date plugin initialized with variants:',
      Object.keys(CommonDateConfigs).map(name => `date-${name}`)
    )
  },

  teardown: async (context) => {
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.delete('date')
      Object.keys(CommonDateConfigs).forEach(name => {
        rendererRegistry.delete(`date-${name}`)
      })
    }

    context.utils.logger.info('Modern date plugin cleaned up')
  },
})

// 重新导出兼容性类型
export const DatePlugin = ModernDatePlugin
export { DateRenderer }
