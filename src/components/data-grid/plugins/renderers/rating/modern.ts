import { computed, ref } from 'vue'
import { definePlugin, defineRenderer, defineConfigSchema } from '../../core/DevUtils'
import type { InferConfigType } from '../../core/injection'
import type { RowData } from '../../core/types'
import RatingRenderer from './renderer.vue'


/**
 * 评分渲染器配置Schema
 */
export const RatingConfigSchema = defineConfigSchema({
  max: {
    type: 'number',
    description: '最大评分',
    default: 5
  },
  precision: {
    type: 'number',
    description: '精度（0.5表示支持半星）',
    default: 1
  },
  color: {
    type: 'string',
    description: '评分颜色',
    default: '#f59e0b'
  },
  emptyColor: {
    type: 'string',
    description: '空星颜色',
    default: '#d9d9d9'
  },
  showValue: {
    type: 'boolean',
    description: '是否显示数值',
    default: true
  },
  size: {
    type: 'string',
    description: '星星大小',
    enum: ['sm', 'md', 'lg'],
    default: 'md'
  },
  variant: {
    type: 'string',
    description: '显示样式',
    enum: ['star', 'heart', 'thumb', 'dot', 'circle'],
    default: 'star'
  },
  allowHalf: {
    type: 'boolean',
    description: '是否允许半星',
    default: true
  },
  readonly: {
    type: 'boolean',
    description: '是否只读',
    default: true
  },
  interactive: {
    type: 'boolean',
    description: '是否可交互',
    default: false
  },
  valuePosition: {
    type: 'string',
    description: '数值显示位置',
    enum: ['right', 'left', 'top', 'bottom'],
    default: 'right'
  },
  showEmptyStars: {
    type: 'boolean',
    description: '是否显示空星',
    default: true
  },
  width: {
    type: 'number',
    description: '列宽度',
    default: 150
  }
})

/**
 * 评分配置类型（自动推导）
 */
export type RatingConfig = InferConfigType<typeof RatingConfigSchema>

// Import common types from core
// Note: RowData is defined locally above

/**
 * 评分列配置 (从 core/types.ts 迁移)
 */
export interface RatingColumnConfig {
  /** 最大评分（默认5） */
  max?: number
  /** 评分颜色（默认#f59e0b） */
  color?: string
  /** 空星颜色（默认#d9d9d9） */
  emptyColor?: string
  /** 是否显示数值（默认true） */
  showValue?: boolean
  /** 数值格式化函数 */
  valueFormatter?: (value: number, max: number) => string
  /** 星星大小（默认16px） */
  size?: number | 'sm' | 'md' | 'lg'
  /** 显示样式 */
  variant?: 'star' | 'heart' | 'thumb' | 'dot'
  /** 是否启用半星（默认true） */
  allowHalf?: boolean
  /** 是否只读（默认true） */
  readonly?: boolean
  /** 评分变更回调 */
  onChange?: (value: number, row: RowData) => void
  /** 自定义图标 */
  icons?: {
    full?: string
    half?: string
    empty?: string
  }
  /** 列宽 */
  width?: number
  /** 允许扩展属性 */
  [key: string]: unknown
}

/**
 * 评分渲染器定义（新的声明式API）
 */
export const RatingRendererDefinition = defineRenderer({
  name: 'rating',
  component: RatingRenderer,
  description: '评分渲染器，支持星级、爱心、点赞等多种显示方式',
  defaultWidth: 150,
  defaultConfig: {
    max: 5,
    precision: 1,
    color: '#f59e0b',
    emptyColor: '#d9d9d9',
    showValue: true,
    size: 'md',
    variant: 'star',
    allowHalf: true,
    readonly: true,
    interactive: false,
    valuePosition: 'right',
    showEmptyStars: true
  },
  props: [
    {
      name: 'value',
      type: 'number',
      required: true,
      description: '评分值'
    },
    {
      name: 'row',
      type: 'object',
      required: true,
      description: '行数据'
    },
    {
      name: 'config',
      type: 'object',
      description: '渲染器配置'
    },
    {
      name: 'onChange',
      type: 'function',
      description: '评分变更回调函数'
    }
  ],
  validator: (config: RatingConfig) => {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证最大评分
    if (config.max && (config.max <= 0 || config.max > 10)) {
      warnings.push('max should be between 1 and 10')
    }

    // 验证精度
    if (config.precision && (config.precision <= 0 || config.precision > 1)) {
      warnings.push('precision should be between 0 and 1')
    }

    // 验证颜色格式
    if (config.color && !/^#[0-9A-F]{6}$/i.test(config.color)) {
      warnings.push('color should be a valid hex color')
    }

    // 验证变体
    const validVariants = ['star', 'heart', 'thumb', 'dot', 'circle']
    if (config.variant && !validVariants.includes(config.variant)) {
      warnings.push(`Unknown variant '${config.variant}', will fall back to 'star'`)
    }

    // 验证大小
    const validSizes = ['sm', 'md', 'lg']
    if (config.size && !validSizes.includes(config.size)) {
      warnings.push(`Unknown size '${config.size}', will fall back to 'md'`)
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined
    }
  }
})

/**
 * 评分插件（新的声明式定义）
 */
export const ModernRatingPlugin = definePlugin({
  name: 'rating-modern',
  version: '2.0.0',
  description: '现代化评分渲染器插件，支持多种显示样式和交互模式',
  
  provides: [
    {
      token: 'RatingRenderer',
      provider: {
        value: RatingRendererDefinition,
        singleton: true
      }
    }
  ],

  // 注册 ColumnHelper 方法
  columnHelperMethods: [
    {
      name: 'rating',
      implementation: function(
        field: string,
        title: string,
        config: RatingColumnConfig = {}
      ) {
        return (this as any).createPluginColumn(field, title, 'rating', config)
      },
      rendererConfig: {
        component: 'RatingRenderer',
        defaultWidth: 120,
      },
      description: '评分列',
      priority: 6,
    },
  ],
  
  setup: async (context) => {
    // 获取渲染器注册表
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    if (!rendererRegistry) {
      throw new Error('RendererRegistry service not found')
    }

    // 注册评分渲染器及其变体
    rendererRegistry.set('rating', RatingRendererDefinition)
    
    // 注册变体渲染器
    const variants = [
      { name: 'rating-star', variant: 'star' },
      { name: 'rating-heart', variant: 'heart' },
      { name: 'rating-thumb', variant: 'thumb' },
      { name: 'rating-dot', variant: 'dot' },
      { name: 'rating-circle', variant: 'circle' },
      { name: 'rating-interactive', interactive: true, readonly: false },
      { name: 'rating-simple', showValue: false, showEmptyStars: false }
    ]

    variants.forEach(({ name, variant, interactive, readonly, showValue, showEmptyStars }) => {
      const variantDefinition = {
        ...RatingRendererDefinition,
        name,
        defaultConfig: {
          ...RatingRendererDefinition.defaultConfig,
          variant: variant || RatingRendererDefinition.defaultConfig.variant,
          interactive: interactive !== undefined ? interactive : RatingRendererDefinition.defaultConfig.interactive,
          readonly: readonly !== undefined ? readonly : RatingRendererDefinition.defaultConfig.readonly,
          showValue: showValue !== undefined ? showValue : RatingRendererDefinition.defaultConfig.showValue,
          showEmptyStars: showEmptyStars !== undefined ? showEmptyStars : RatingRendererDefinition.defaultConfig.showEmptyStars
        }
      }
      rendererRegistry.set(name, variantDefinition)
    })

    // 注册Vue组件
    context.utils.registerComponent('RatingRenderer', RatingRenderer)

    // 注册列助手方法
    context.container.registerExtensionPoint('column.helper.rating', (_extensionContext: any, field: string, title: string, config?: Partial<RatingConfig>) => {
      return {
        field,
        title,
        width: config?.width || 150,
        plugin: 'rating',
        pluginConfig: config
      }
    })

    context.utils.logger.info('Modern rating plugin initialized with variants:', variants.map(v => v.name))
  },

  teardown: async (context) => {
    // 清理注册的组件和服务
    const rendererRegistry = context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.delete('rating')
      rendererRegistry.delete('rating-star')
      rendererRegistry.delete('rating-heart')
      rendererRegistry.delete('rating-thumb')
      rendererRegistry.delete('rating-dot')
      rendererRegistry.delete('rating-circle')
      rendererRegistry.delete('rating-interactive')
      rendererRegistry.delete('rating-simple')
    }

    context.utils.logger.info('Modern rating plugin cleaned up')
  }
})

/**
 * 评分渲染器组合式API - 支持Vue组件使用
 */
export function useRatingRenderer(props?: any) {
  // 如果传入了props，返回响应式的组件API
  if (props) {
    // 获取真实配置
    const config = computed(() => ({
      ...RatingRendererDefinition.defaultConfig,
      ...props.config
    }))

    // 获取评分值
    const value = computed(() => {
      return props.field ? props.row?.[props.field] : props.value
    })

    // 标准化评分值
    const score = computed(() => {
      const val = value.value
      if (typeof val === 'number') return Math.max(0, Math.min(val, config.value.max))
      if (typeof val === 'string') {
        const parsed = parseFloat(val)
        return isNaN(parsed) ? 0 : Math.max(0, Math.min(parsed, config.value.max))
      }
      return 0
    })

    // 最大评分
    const maxRating = computed(() => config.value.max)

    // 是否显示数值
    const showValue = computed(() => config.value.showValue)

    // 格式化后的数值
    const formattedValue = computed(() => {
      if (!showValue.value) return ''
      const val = score.value
      if (config.value.precision === 0.5) {
        return val.toFixed(1)
      }
      return val.toString()
    })

    // 悬停状态
    const hoverValue = ref(0)

    // 获取图标类型
    const getIcon = (filled: boolean) => {
      const variant = config.value.variant
      switch (variant) {
        case 'heart':
          return filled ? 'mdi:heart' : 'mdi:heart-outline'
        case 'thumb':
          return filled ? 'mdi:thumb-up' : 'mdi:thumb-up-outline'
        case 'dot':
          return filled ? 'mdi:circle' : 'mdi:circle-outline'
        case 'circle':
          return filled ? 'mdi:checkbox-marked-circle' : 'mdi:checkbox-blank-circle-outline'
        default: // star
          return filled ? 'mdi:star' : 'mdi:star-outline'
      }
    }

    // 评分项目列表
    const ratingItems = computed(() => {
      const items = []
      const currentScore = hoverValue.value || score.value
      const maxVal = maxRating.value
      
      for (let i = 1; i <= maxVal; i++) {
        const filled = i <= currentScore
        const color = filled ? config.value.color : config.value.emptyColor
        
        items.push({
          icon: getIcon(filled),
          color,
          filled
        })
      }
      
      return items
    })

    // 容器样式类
    const containerClasses = computed(() => {
      const classes = ['rating-renderer']
      
      if (config.value.interactive) {
        classes.push('interactive')
      }
      
      return classes.join(' ')
    })

    // 图标样式类
    const iconClasses = computed(() => {
      const classes = ['rating-icon']
      const size = config.value.size
      
      switch (size) {
        case 'sm':
          classes.push('w-3 h-3')
          break
        case 'lg':
          classes.push('w-6 h-6')
          break
        default: // md
          classes.push('w-4 h-4')
      }
      
      return classes.join(' ')
    })

    // 处理评分点击
    const handleRatingClick = (rating: number) => {
      if (!config.value.interactive) return
      
      if (props.onChange) {
        props.onChange(rating, props.row, props.field)
      }
    }

    // 处理悬停
    const handleRatingHover = (rating: number) => {
      if (!config.value.interactive) return
      hoverValue.value = rating
    }

    // 处理离开悬停
    const handleRatingLeave = () => {
      if (!config.value.interactive) return
      hoverValue.value = 0
    }

    return {
      score,
      maxRating,
      showValue,
      formattedValue,
      ratingItems,
      containerClasses,
      iconClasses,
      handleRatingClick,
      handleRatingHover,
      handleRatingLeave,
      config,
      value
    }
  }

  // 如果没有传入props，返回列创建工具API
  return {
    /**
     * 创建评分列配置
     */
    createRatingColumn: (field: string, title: string, config?: Partial<RatingConfig>) => {
      return {
        field,
        title,
        width: config?.width || 150,
        plugin: 'rating',
        pluginConfig: {
          ...RatingRendererDefinition.defaultConfig,
          ...config
        }
      }
    },

    /**
     * 创建星级评分列
     */
    createStarRatingColumn: (field: string, title: string, config?: Partial<RatingConfig>) => {
      return {
        field,
        title,
        width: config?.width || 150,
        plugin: 'rating-star',
        pluginConfig: {
          ...RatingRendererDefinition.defaultConfig,
          variant: 'star',
          ...config
        }
      }
    },

    /**
     * 创建爱心评分列
     */
    createHeartRatingColumn: (field: string, title: string, config?: Partial<RatingConfig>) => {
      return {
        field,
        title,
        width: config?.width || 150,
        plugin: 'rating-heart',
        pluginConfig: {
          ...RatingRendererDefinition.defaultConfig,
          variant: 'heart',
          color: '#ef4444',
          ...config
        }
      }
    },

    /**
     * 创建点赞评分列
     */
    createThumbRatingColumn: (field: string, title: string, config?: Partial<RatingConfig>) => {
      return {
        field,
        title,
        width: config?.width || 120,
        plugin: 'rating-thumb',
        pluginConfig: {
          ...RatingRendererDefinition.defaultConfig,
          variant: 'thumb',
          max: 1,
          showValue: false,
          ...config
        }
      }
    },

    /**
     * 创建圆点评分列
     */
    createDotRatingColumn: (field: string, title: string, config?: Partial<RatingConfig>) => {
      return {
        field,
        title,
        width: config?.width || 120,
        plugin: 'rating-dot',
        pluginConfig: {
          ...RatingRendererDefinition.defaultConfig,
          variant: 'dot',
          ...config
        }
      }
    },

    /**
     * 创建交互式评分列
     */
    createInteractiveRatingColumn: (field: string, title: string, onChange: (value: number, row: RowData) => void, config?: Partial<RatingConfig>) => {
      return {
        field,
        title,
        width: config?.width || 150,
        plugin: 'rating-interactive',
        pluginConfig: {
          ...RatingRendererDefinition.defaultConfig,
          interactive: true,
          readonly: false,
          onChange,
          ...config
        }
      }
    },

    /**
     * 创建简单评分列（无数值显示）
     */
    createSimpleRatingColumn: (field: string, title: string, config?: Partial<RatingConfig>) => {
      return {
        field,
        title,
        width: config?.width || 100,
        plugin: 'rating-simple',
        pluginConfig: {
          ...RatingRendererDefinition.defaultConfig,
          showValue: false,
          showEmptyStars: false,
          ...config
        }
      }
    },

    /**
     * 验证评分配置
     */
    validateConfig: RatingRendererDefinition.validator,

    /**
     * 获取默认配置
     */
    getDefaultConfig: () => ({ ...RatingRendererDefinition.defaultConfig })
  }
}

/**
 * 评分值处理器
 */
export class RatingValueProcessor {
  /**
   * 标准化评分值
   */
  static normalize(value: any, max: number = 5): number {
    const numValue = Number(value)
    if (isNaN(numValue)) return 0
    return Math.max(0, Math.min(max, numValue))
  }

  /**
   * 格式化评分显示
   */
  static format(value: number, max: number = 5, precision: number = 1): string {
    const normalizedValue = this.normalize(value, max)
    if (precision < 1) {
      return normalizedValue.toFixed(1)
    }
    return normalizedValue.toString()
  }

  /**
   * 计算填充的星星数量
   */
  static calculateFilledStars(value: number, max: number = 5, allowHalf: boolean = true): {
    full: number
    half: number
    empty: number
  } {
    const normalizedValue = this.normalize(value, max)
    
    if (allowHalf) {
      const fullStars = Math.floor(normalizedValue)
      const hasHalfStar = (normalizedValue - fullStars) >= 0.5
      const halfStars = hasHalfStar ? 1 : 0
      const emptyStars = max - fullStars - halfStars
      
      return {
        full: fullStars,
        half: halfStars,
        empty: emptyStars
      }
    } else {
      const fullStars = Math.round(normalizedValue)
      const emptyStars = max - fullStars
      
      return {
        full: fullStars,
        half: 0,
        empty: emptyStars
      }
    }
  }

  /**
   * 获取评分对应的百分比
   */
  static getPercentage(value: number, max: number = 5): number {
    const normalizedValue = this.normalize(value, max)
    return (normalizedValue / max) * 100
  }

  /**
   * 获取评分等级
   */
  static getRatingLevel(value: number, max: number = 5): 'excellent' | 'good' | 'average' | 'poor' | 'bad' {
    const percentage = this.getPercentage(value, max)
    
    if (percentage >= 90) return 'excellent'
    if (percentage >= 70) return 'good'
    if (percentage >= 50) return 'average'
    if (percentage >= 30) return 'poor'
    return 'bad'
  }
}

/**
 * 评分样式配置
 */
export const RatingStyles = {
  /**
   * 星级样式
   */
  star: {
    sizes: {
      sm: { size: 14, spacing: 2 },
      md: { size: 18, spacing: 3 },
      lg: { size: 24, spacing: 4 }
    },
    colors: {
      default: '#f59e0b',
      blue: '#3b82f6',
      red: '#ef4444',
      green: '#10b981',
      purple: '#8b5cf6'
    }
  },

  /**
   * 爱心样式
   */
  heart: {
    sizes: {
      sm: { size: 12, spacing: 2 },
      md: { size: 16, spacing: 3 },
      lg: { size: 20, spacing: 4 }
    },
    colors: {
      default: '#ef4444',
      pink: '#ec4899',
      red: '#dc2626'
    }
  },

  /**
   * 点赞样式
   */
  thumb: {
    sizes: {
      sm: { size: 14, spacing: 2 },
      md: { size: 18, spacing: 3 },
      lg: { size: 24, spacing: 4 }
    },
    colors: {
      default: '#10b981',
      blue: '#3b82f6'
    }
  }
}

/**
 * 预定义评分配置
 */
export const CommonRatingConfigs = {
  /**
   * 5星评分
   */
  fiveStar: {
    max: 5,
    variant: 'star' as const,
    allowHalf: true,
    showValue: true,
    color: '#f59e0b'
  },

  /**
   * 10分制评分
   */
  tenPoint: {
    max: 10,
    variant: 'star' as const,
    allowHalf: false,
    showValue: true,
    precision: 1
  },

  /**
   * 爱心评分
   */
  heartRating: {
    max: 5,
    variant: 'heart' as const,
    allowHalf: true,
    showValue: false,
    color: '#ef4444'
  },

  /**
   * 点赞评分
   */
  thumbsUp: {
    max: 1,
    variant: 'thumb' as const,
    allowHalf: false,
    showValue: false,
    showEmptyStars: false
  },

  /**
   * 简约星级
   */
  minimal: {
    max: 5,
    variant: 'star' as const,
    allowHalf: true,
    showValue: false,
    showEmptyStars: false,
    size: 'sm' as const
  },

  /**
   * 交互式评分
   */
  interactive: {
    max: 5,
    variant: 'star' as const,
    allowHalf: true,
    showValue: true,
    interactive: true,
    readonly: false
  }
}

// Export the Vue component
export { RatingRenderer }