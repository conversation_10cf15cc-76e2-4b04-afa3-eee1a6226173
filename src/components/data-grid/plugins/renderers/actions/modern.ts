import { computed } from 'vue'
import {
  definePlugin,
  defineRenderer,
  defineConfigSchema,
} from '../../core/DevUtils'
import type { InferConfigType } from '../../core/injection'
import ActionsRenderer from './renderer.vue'

/**
 * 操作按钮定义
 */
export interface ActionButton {
  /** 按钮文本 */
  text?: string
  /** 按钮图标 */
  icon?: string
  /** 按钮类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  /** 按钮变体 */
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
  /** 按钮大小 */
  size?: 'small' | 'medium' | 'large' | 'default' | 'sm' | 'lg' | 'icon'
  /** 提示文本 */
  tooltip?: string
  /** 显示条件 */
  condition?: (row: RowData) => boolean
  /** 点击事件 */
  onClick: (row: RowData) => void
  /** 是否禁用 */
  disabled?: (row: RowData) => boolean
  /** 确认提示 */
  confirm?: string | ((row: RowData) => string)
}

/**
 * 操作渲染器配置Schema
 */
export const ActionsConfigSchema = defineConfigSchema({
  actions: {
    type: 'array',
    description: '操作按钮列表',
    items: {
      type: 'object',
      properties: {
        text: { type: 'string' },
        icon: { type: 'string' },
        type: {
          type: 'string',
          enum: ['primary', 'success', 'warning', 'danger', 'info'],
        },
        variant: {
          type: 'string',
          enum: [
            'default',
            'destructive',
            'outline',
            'secondary',
            'ghost',
            'link',
          ],
        },
        size: {
          type: 'string',
          enum: ['small', 'medium', 'large', 'default', 'sm', 'lg', 'icon'],
        },
        tooltip: { type: 'string' },
      },
    },
    required: true,
  },
  showActionsCount: {
    type: 'number',
    description: '显示操作按钮数量，超出的自动收入下拉菜单',
    default: 2,
  },
  layout: {
    type: 'string',
    description: '布局方式',
    enum: ['horizontal', 'vertical', 'dropdown'],
    default: 'horizontal',
  },
  align: {
    type: 'string',
    description: '对齐方式',
    enum: ['left', 'center', 'right'],
    default: 'left',
  },
  spacing: {
    type: 'string',
    description: '按钮间距',
    enum: ['tight', 'normal', 'loose'],
    default: 'normal',
  },
  fixed: {
    type: 'string',
    description: '是否固定列',
    enum: ['left', 'right'],
  },
  width: {
    type: 'number',
    description: '列宽度',
    default: 120,
  },
})

/**
 * 操作配置类型（自动推导）
 */
export type ActionsConfig = InferConfigType<typeof ActionsConfigSchema> & {
  actions: ActionButton[]
}

// Import common types from core
// Note: RowData is defined locally above

/**
 * 操作列配置 (从 core/types.ts 迁移)
 */
export interface ActionsColumnConfig {
  /** 操作按钮列表 */
  actions: Array<{
    text?: string
    icon?: string
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
    variant?:
      | 'default'
      | 'destructive'
      | 'outline'
      | 'secondary'
      | 'ghost'
      | 'link'
    size?: 'small' | 'medium' | 'large' | 'default' | 'sm' | 'lg' | 'icon'
    tooltip?: string
    condition?: (row: RowData) => boolean
    onClick: (row: RowData) => void
  }>
  /** 显示操作按钮数量，超出的自动收入下拉菜单 */
  showActionsCount?: number
  /** 列宽 */
  width?: number
  /** 是否固定 */
  fixed?: 'left' | 'right'
  /** 允许扩展属性 */
  [key: string]: unknown
}

/**
 * 操作渲染器定义（新的声明式API）
 */
export const ActionsRendererDefinition = defineRenderer({
  name: 'actions',
  component: ActionsRenderer,
  description: '操作按钮渲染器，支持多种布局和交互方式',
  defaultWidth: 120,
  defaultConfig: {
    actions: [],
    showActionsCount: 2,
    layout: 'horizontal',
    align: 'left',
    spacing: 'normal',
  },
  props: [
    {
      name: 'row',
      type: 'object',
      required: true,
      description: '行数据',
    },
    {
      name: 'config',
      type: 'object',
      required: true,
      description: '操作配置',
    },
    {
      name: 'rowIndex',
      type: 'number',
      description: '行索引',
    },
  ],
  validator: (config: ActionsConfig) => {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证操作列表
    if (!Array.isArray(config.actions)) {
      errors.push('actions must be an array')
    } else if (config.actions.length === 0) {
      warnings.push('actions array is empty')
    } else {
      config.actions.forEach((action, index) => {
        if (!action.onClick || typeof action.onClick !== 'function') {
          errors.push(`Action ${index}: onClick function is required`)
        }
        if (!action.text && !action.icon) {
          warnings.push(`Action ${index}: should have either text or icon`)
        }
      })
    }

    // 验证布局
    const validLayouts = ['horizontal', 'vertical', 'dropdown']
    if (config.layout && !validLayouts.includes(config.layout)) {
      warnings.push(
        `Unknown layout '${config.layout}', will fall back to 'horizontal'`
      )
    }

    // 验证对齐方式
    const validAligns = ['left', 'center', 'right']
    if (config.align && !validAligns.includes(config.align)) {
      warnings.push(`Unknown align '${config.align}', will fall back to 'left'`)
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
    }
  },
})

/**
 * 操作插件（新的声明式定义）
 */
export const ModernActionsPlugin = definePlugin({
  name: 'actions-modern',
  version: '2.0.0',
  description: '现代化操作按钮渲染器插件，支持多种布局和交互方式',

  provides: [
    {
      token: 'ActionsRenderer',
      provider: {
        value: ActionsRendererDefinition,
        singleton: true,
      },
    },
  ],

  // 注册 ColumnHelper 方法
  columnHelperMethods: [
    {
      name: 'actions',
      implementation: function (title: string, config: ActionsColumnConfig) {
        return (this as any).createPluginColumn(undefined, title, 'actions', {
          ...config,
          fixed: config.fixed || 'right',
        })
      },
      rendererConfig: {
        component: 'ActionsRenderer',
        defaultWidth: 150,
      },
      description: '操作列',
      priority: 10,
    },
  ],

  setup: async (context) => {
    // 获取渲染器注册表
    const rendererRegistry =
      context.container.resolveService('RendererRegistry')
    if (!rendererRegistry) {
      throw new Error('RendererRegistry service not found')
    }

    // 注册操作渲染器及其变体
    rendererRegistry.set('actions', ActionsRendererDefinition)

    // 注册变体渲染器
    const variants = [
      { name: 'actions-horizontal', layout: 'horizontal' },
      { name: 'actions-vertical', layout: 'vertical' },
      { name: 'actions-dropdown', layout: 'dropdown' },
      { name: 'actions-compact', showActionsCount: 2, spacing: 'tight' },
      { name: 'actions-expanded', showActionsCount: 5, spacing: 'loose' },
    ]

    variants.forEach(({ name, layout, showActionsCount, spacing }) => {
      const variantDefinition = {
        ...ActionsRendererDefinition,
        name,
        defaultConfig: {
          ...ActionsRendererDefinition.defaultConfig,
          layout: layout || ActionsRendererDefinition.defaultConfig.layout,
          showActionsCount:
            showActionsCount ||
            ActionsRendererDefinition.defaultConfig.showActionsCount,
          spacing: spacing || ActionsRendererDefinition.defaultConfig.spacing,
        },
      }
      rendererRegistry.set(name, variantDefinition)
    })

    // 注册Vue组件
    context.utils.registerComponent('ActionsRenderer', ActionsRenderer)

    // 注册列助手方法
    context.container.registerExtensionPoint(
      'column.helper.actions',
      (_extensionContext: any, title: string, config: ActionsConfig) => {
        return {
          title,
          width: config?.width || 120,
          plugin: 'actions',
          pluginConfig: config,
          fixed: config?.fixed,
        }
      }
    )

    context.utils.logger.info(
      'Modern actions plugin initialized with variants:',
      variants.map((v) => v.name)
    )
  },

  teardown: async (context) => {
    // 清理注册的组件和服务
    const rendererRegistry =
      context.container.resolveService('RendererRegistry')
    if (rendererRegistry) {
      rendererRegistry.delete('actions')
      rendererRegistry.delete('actions-horizontal')
      rendererRegistry.delete('actions-vertical')
      rendererRegistry.delete('actions-dropdown')
      rendererRegistry.delete('actions-compact')
      rendererRegistry.delete('actions-expanded')
    }

    context.utils.logger.info('Modern actions plugin cleaned up')
  },
})

// Import the actual getRealConfig function from ConfigStore
import { getRealConfig } from '../../core/ConfigStore'
import { RowData } from '../../core/types'

/**
 * 类型保护函数：检查行数据是否有enabled属性
 */
function hasEnabledProperty(
  row: RowData
): row is RowData & { enabled: boolean } {
  return typeof row.enabled === 'boolean'
}

/**
 * 操作渲染器组合式API - 支持Vue组件使用
 */
export function useActionsRenderer(props?: any) {
  // 如果传入了props，返回响应式的组件API
  if (props) {
    // Get real configuration object with functions preserved
    const realConfig = computed(() => {
      if (props.configId) {
        const storedConfig = getRealConfig(props.configId)
        if (storedConfig) {
          return storedConfig
        }
      }
      return {
        ...ActionsRendererDefinition.defaultConfig,
        ...props.config,
      }
    })

    // 获取所有操作按钮
    const allActions = computed(() => {
      const actions = realConfig.value.actions || []
      return actions.filter((action: ActionButton) => {
        // 检查显示条件
        if (action.condition && typeof action.condition === 'function') {
          return action.condition(props.row)
        }
        return true
      })
    })

    // 获取可见的操作按钮
    const visibleActions = computed(() => {
      return allActions.value
    })

    // 获取直接显示的操作按钮
    const directActions = computed(() => {
      const showCount = realConfig.value.showActionsCount || 2
      const layout = realConfig.value.layout || 'horizontal'

      if (layout === 'dropdown') {
        return []
      }

      return visibleActions.value.slice(0, showCount)
    })

    // 获取下拉菜单中的操作按钮
    const dropdownActions = computed(() => {
      const showCount = realConfig.value.showActionsCount || 2
      const layout = realConfig.value.layout || 'horizontal'

      if (layout === 'dropdown') {
        return visibleActions.value
      }

      return visibleActions.value.slice(showCount)
    })

    // 是否有更多操作
    const hasMoreActions = computed(() => {
      return dropdownActions.value.length > 0
    })

    // 获取按钮变体
    const getButtonVariant = (action: ActionButton) => {
      if (action.variant) return action.variant

      // 根据类型映射变体
      switch (action.type) {
        case 'primary':
          return 'default'
        case 'success':
          return 'default'
        case 'warning':
          return 'outline'
        case 'danger':
          return 'destructive'
        case 'info':
          return 'secondary'
        default:
          return 'outline'
      }
    }

    // 获取按钮大小
    const getButtonSize = (action: ActionButton) => {
      return action.size || 'sm'
    }

    // 处理操作点击
    const handleActionClick = async (action: ActionButton) => {
      try {
        // 检查是否禁用
        if (action.disabled && typeof action.disabled === 'function') {
          if (action.disabled(props.row)) {
            return
          }
        }

        // 显示确认对话框
        if (action.confirm) {
          const confirmMessage =
            typeof action.confirm === 'function'
              ? action.confirm(props.row)
              : action.confirm

          if (!window.confirm(confirmMessage)) {
            return
          }
        }

        // 执行操作
        if (action.onClick && typeof action.onClick === 'function') {
          await action.onClick(props.row)
        }
      } catch (error) {
        console.error('Action execution failed:', error)
      }
    }

    return {
      visibleActions,
      directActions,
      dropdownActions,
      hasMoreActions,
      getButtonVariant,
      getButtonSize,
      handleActionClick,
      realConfig,
    }
  }

  // 如果没有传入props，返回列创建工具API
  return {
    /**
     * 创建操作列配置
     */
    createActionsColumn: (
      title: string,
      actions: ActionButton[],
      config?: Partial<ActionsConfig>
    ) => {
      return {
        title,
        width: config?.width || 140,
        plugin: 'actions',
        pluginConfig: {
          ...ActionsRendererDefinition.defaultConfig,
          actions,
          ...config,
        },
      }
    },

    /**
     * 创建水平布局操作列
     */
    createHorizontalActionsColumn: (
      title: string,
      actions: ActionButton[],
      config?: Partial<ActionsConfig>
    ) => {
      return {
        title,
        width: config?.width || 150,
        plugin: 'actions-horizontal',
        pluginConfig: {
          ...ActionsRendererDefinition.defaultConfig,
          actions,
          layout: 'horizontal',
          ...config,
        },
      }
    },

    /**
     * 创建下拉菜单操作列
     */
    createDropdownActionsColumn: (
      title: string,
      actions: ActionButton[],
      config?: Partial<ActionsConfig>
    ) => {
      return {
        title,
        width: config?.width || 80,
        plugin: 'actions-dropdown',
        pluginConfig: {
          ...ActionsRendererDefinition.defaultConfig,
          actions,
          layout: 'dropdown',
          showActionsCount: 1,
          ...config,
        },
      }
    },

    /**
     * 创建紧凑操作列
     */
    createCompactActionsColumn: (
      title: string,
      actions: ActionButton[],
      config?: Partial<ActionsConfig>
    ) => {
      return {
        title,
        width: config?.width || 100,
        plugin: 'actions-compact',
        pluginConfig: {
          ...ActionsRendererDefinition.defaultConfig,
          actions,
          showActionsCount: 2,
          spacing: 'tight',
          ...config,
        },
      }
    },

    /**
     * 验证操作配置
     */
    validateConfig: ActionsRendererDefinition.validator,

    /**
     * 获取默认配置
     */
    getDefaultConfig: () => ({ ...ActionsRendererDefinition.defaultConfig }),
  }
}

/**
 * 操作按钮构建器
 */
export class ActionButtonBuilder {
  private actions: ActionButton[] = []

  /**
   * 添加编辑按钮
   */
  edit(onClick: (row: RowData) => void, options?: Partial<ActionButton>) {
    this.actions.push({
      text: '编辑',
      icon: 'edit',
      type: 'primary',
      variant: 'outline',
      tooltip: '编辑此项',
      onClick,
      ...options,
    })
    return this
  }

  /**
   * 添加删除按钮
   */
  delete(onClick: (row: RowData) => void, options?: Partial<ActionButton>) {
    this.actions.push({
      text: '删除',
      icon: 'trash',
      type: 'danger',
      variant: 'outline',
      tooltip: '删除此项',
      confirm: '确定要删除这条记录吗？',
      onClick,
      ...options,
    })
    return this
  }

  /**
   * 添加查看按钮
   */
  view(onClick: (row: RowData) => void, options?: Partial<ActionButton>) {
    this.actions.push({
      text: '查看',
      icon: 'eye',
      type: 'info',
      variant: 'ghost',
      tooltip: '查看详情',
      onClick,
      ...options,
    })
    return this
  }

  /**
   * 添加复制按钮
   */
  copy(onClick: (row: RowData) => void, options?: Partial<ActionButton>) {
    this.actions.push({
      text: '复制',
      icon: 'copy',
      type: 'info',
      variant: 'outline',
      tooltip: '复制此项',
      onClick,
      ...options,
    })
    return this
  }

  /**
   * 添加启用/禁用按钮
   */
  toggle(onClick: (row: RowData) => void, options?: Partial<ActionButton>) {
    this.actions.push({
      text: '切换状态',
      icon: 'toggle-left',
      type: 'warning',
      variant: 'outline',
      tooltip: '切换启用状态',
      onClick,
      ...options,
    })
    return this
  }

  /**
   * 添加自定义按钮
   */
  custom(button: ActionButton) {
    this.actions.push(button)
    return this
  }

  /**
   * 添加条件按钮
   */
  conditional(condition: (row: RowData) => boolean, button: ActionButton) {
    this.actions.push({
      ...button,
      condition,
    })
    return this
  }

  /**
   * 构建操作列表
   */
  build(): ActionButton[] {
    return [...this.actions]
  }

  /**
   * 重置构建器
   */
  reset() {
    this.actions = []
    return this
  }
}

/**
 * 预定义操作配置
 */
export const CommonActionConfigs = {
  /**
   * 标准CRUD操作
   */
  crud: (handlers: {
    onView?: (row: RowData) => void
    onEdit?: (row: RowData) => void
    onDelete?: (row: RowData) => void
  }) =>
    new ActionButtonBuilder()
      .conditional(() => !!handlers.onView, {
        text: '查看',
        icon: 'eye',
        type: 'info',
        variant: 'ghost',
        onClick: handlers.onView!,
      })
      .conditional(() => !!handlers.onEdit, {
        text: '编辑',
        icon: 'edit',
        type: 'primary',
        variant: 'outline',
        onClick: handlers.onEdit!,
      })
      .conditional(() => !!handlers.onDelete, {
        text: '删除',
        icon: 'trash',
        type: 'danger',
        variant: 'outline',
        confirm: '确定要删除这条记录吗？',
        onClick: handlers.onDelete!,
      })
      .build(),

  /**
   * 状态管理操作
   */
  statusManagement: (handlers: {
    onEnable?: (row: RowData) => boolean
    onDisable?: (row: RowData) => boolean
    onToggle?: (row: RowData) => boolean
  }) =>
    new ActionButtonBuilder()
      .conditional(
        (row) =>
          !!handlers.onEnable && (!hasEnabledProperty(row) || !row.enabled),
        {
          text: '启用',
          icon: 'play',
          type: 'success',
          variant: 'outline',
          onClick: handlers.onEnable!,
        }
      )
      .conditional(
        (row) => !!handlers.onDisable && hasEnabledProperty(row) && row.enabled,
        {
          text: '禁用',
          icon: 'pause',
          type: 'warning',
          variant: 'outline',
          onClick: handlers.onDisable!,
        }
      )
      .conditional(() => !!handlers.onToggle, {
        text: '切换',
        icon: 'toggle-left',
        type: 'info',
        variant: 'ghost',
        onClick: handlers.onToggle!,
      })
      .build(),

  /**
   * 工作流操作
   */
  workflow: (handlers: {
    onApprove?: (row: RowData) => void
    onReject?: (row: RowData) => void
    onSubmit?: (row: RowData) => void
  }) =>
    new ActionButtonBuilder()
      .conditional(() => !!handlers.onSubmit, {
        text: '提交',
        icon: 'send',
        type: 'primary',
        variant: 'default',
        onClick: handlers.onSubmit!,
      })
      .conditional(() => !!handlers.onApprove, {
        text: '批准',
        icon: 'check',
        type: 'success',
        variant: 'outline',
        onClick: handlers.onApprove!,
      })
      .conditional(() => !!handlers.onReject, {
        text: '拒绝',
        icon: 'x',
        type: 'danger',
        variant: 'outline',
        confirm: '确定要拒绝吗？',
        onClick: handlers.onReject!,
      })
      .build(),
}

/**
 * 便利函数：创建操作按钮
 */
export function createActions() {
  return new ActionButtonBuilder()
}

// Export the Vue component
export { ActionsRenderer }
