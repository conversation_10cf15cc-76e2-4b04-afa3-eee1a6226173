# Actions Renderer Plugin

操作列渲染器插件，用于在数据网格中创建包含多个操作按钮的操作列，支持直接显示和下拉菜单两种展示模式。

## 目录结构

```
actions/
├── index.ts          # 插件入口文件，提供 composable API 和插件类
├── renderer.vue      # Vue 组件，处理渲染逻辑
└── README.md         # 插件文档（当前文件）
```

## 功能特性

- 🎯 **多操作按钮**: 支持在单个列中显示多个操作按钮
- 📱 **智能布局**: 自动管理直接显示和下拉菜单的按钮分配
- 🎨 **多种样式**: 支持多种按钮样式变体（outline、default、destructive 等）
- 🔒 **条件显示**: 支持基于行数据的条件显示逻辑
- 🖱️ **交互友好**: 提供清晰的悬停效果和点击反馈
- 🎛️ **高度可配置**: 支持图标、文本、提示信息和自定义点击处理
- 📍 **灵活定位**: 支持左右固定定位

## 基本用法

### 1. 安装插件

```typescript
import { ActionsPlugin } from '@/components/data-grid/plugins/renderers/actions'
import { PluginManager } from '@/components/data-grid/plugins'

// 注册插件
const manager = new PluginManager()
manager.register(new ActionsPlugin())
```

### 2. 使用 composable API

```vue
<script setup lang="ts">
import { useActionsRenderer } from '@/components/data-grid/plugins/renderers/actions'

const {
  visibleActions,
  directActions,
  dropdownActions,
  hasMoreActions,
  getButtonVariant,
  getButtonSize,
  handleActionClick,
} = useActionsRenderer({
  row: { id: 1, name: 'John Doe', status: 'active' },
  config: {
    actions: [
      {
        icon: 'mdi:pencil',
        text: '编辑',
        tooltip: '编辑用户信息',
        onClick: (row) => console.log('编辑', row),
      },
    ],
  },
})
</script>
```

### 3. 直接使用组件

```vue
<template>
  <ActionsRenderer :row="{ id: 1, name: 'John Doe' }" :config="actionsConfig" />
</template>

<script setup lang="ts">
import { ActionsRenderer } from '@/components/data-grid/plugins/renderers/actions'
import type { ActionsColumnConfig } from '@/components/data-grid/plugins/renderers/actions'

const actionsConfig: ActionsColumnConfig = {
  actions: [
    {
      icon: 'mdi:pencil',
      text: '编辑',
      tooltip: '编辑用户信息',
      onClick: (row) => editUser(row.id),
    },
    {
      icon: 'mdi:delete',
      text: '删除',
      tooltip: '删除用户',
      variant: 'destructive',
      condition: (row) => row.status !== 'locked',
      onClick: (row) => deleteUser(row.id),
    },
  ],
  showActionsCount: 2,
}
</script>
```

## 配置选项

### ActionsColumnConfig

```typescript
interface ActionsColumnConfig {
  // 操作按钮列表
  actions: Array<{
    text?: string // 按钮文本
    icon?: string // 按钮图标（使用 Iconify 图标名称）
    tooltip?: string // 悬停提示文本

    // 按钮样式（两种方式任选其一）
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' // 预设类型
    variant?:
      | 'default'
      | 'destructive'
      | 'outline'
      | 'secondary'
      | 'ghost'
      | 'link' // ShadCN 变体

    // 按钮大小
    size?: 'small' | 'medium' | 'large' | 'default' | 'sm' | 'lg' | 'icon'

    // 显示条件
    condition?: (row: any) => boolean // 返回 true 时显示该按钮

    // 点击处理函数
    onClick: (row: any) => void // 必需：点击时执行的函数
  }>

  // 布局配置
  showActionsCount?: number // 直接显示的按钮数量，默认为 2

  // 列配置
  width?: number // 列宽度
  fixed?: 'left' | 'right' // 是否固定在左侧或右侧
}
```

## 使用示例

### 示例 1: 基础用户管理操作

```typescript
const userActionsConfig: ActionsColumnConfig = {
  actions: [
    {
      icon: 'mdi:pencil',
      text: '编辑',
      tooltip: '编辑用户信息',
      variant: 'outline',
      onClick: (row) => editUser(row.id),
    },
    {
      icon: 'mdi:eye',
      text: '查看',
      tooltip: '查看用户详情',
      variant: 'ghost',
      onClick: (row) => viewUser(row.id),
    },
    {
      icon: 'mdi:delete',
      text: '删除',
      tooltip: '删除用户',
      variant: 'destructive',
      condition: (row) => row.status !== 'locked',
      onClick: (row) => {
        if (confirm('确定要删除这个用户吗？')) {
          deleteUser(row.id)
        }
      },
    },
  ],
  showActionsCount: 2,
  fixed: 'right',
}
```

### 示例 2: 订单管理操作

```typescript
const orderActionsConfig: ActionsColumnConfig = {
  actions: [
    {
      icon: 'mdi:eye',
      tooltip: '查看订单详情',
      size: 'sm',
      onClick: (row) => viewOrder(row.id),
    },
    {
      icon: 'mdi:printer',
      tooltip: '打印订单',
      condition: (row) => ['confirmed', 'shipped'].includes(row.status),
      onClick: (row) => printOrder(row.id),
    },
    {
      icon: 'mdi:truck',
      text: '发货',
      tooltip: '标记为已发货',
      type: 'success',
      condition: (row) => row.status === 'confirmed',
      onClick: (row) => shipOrder(row.id),
    },
    {
      icon: 'mdi:cancel',
      text: '取消',
      tooltip: '取消订单',
      variant: 'destructive',
      condition: (row) => row.status === 'pending',
      onClick: (row) => cancelOrder(row.id),
    },
    {
      icon: 'mdi:message',
      tooltip: '联系客户',
      onClick: (row) => contactCustomer(row.customerId),
    },
  ],
  showActionsCount: 1, // 只直接显示1个按钮，其余进入下拉菜单
  width: 120,
}
```

### 示例 3: 文件管理操作

```typescript
const fileActionsConfig: ActionsColumnConfig = {
  actions: [
    {
      icon: 'mdi:download',
      tooltip: '下载文件',
      onClick: (row) => downloadFile(row.id),
    },
    {
      icon: 'mdi:share',
      tooltip: '分享文件',
      condition: (row) => row.permissions.includes('share'),
      onClick: (row) => shareFile(row.id),
    },
    {
      icon: 'mdi:pencil',
      tooltip: '重命名',
      condition: (row) => row.permissions.includes('write'),
      onClick: (row) => renameFile(row.id),
    },
    {
      icon: 'mdi:content-copy',
      tooltip: '复制',
      onClick: (row) => copyFile(row.id),
    },
    {
      icon: 'mdi:folder-move',
      tooltip: '移动',
      condition: (row) => row.permissions.includes('move'),
      onClick: (row) => moveFile(row.id),
    },
    {
      icon: 'mdi:delete',
      tooltip: '删除',
      variant: 'destructive',
      condition: (row) => row.permissions.includes('delete'),
      onClick: (row) => {
        if (confirm(`确定要删除文件 "${row.name}" 吗？`)) {
          deleteFile(row.id)
        }
      },
    },
  ],
  showActionsCount: 3,
}
```

## API 参考

### useActionsRenderer

composable 函数，提供操作列渲染所需的响应式数据和计算属性。

```typescript
function useActionsRenderer(props: {
  value?: any
  row: any
  config?: ActionsColumnConfig
  configId?: string
}): {
  realConfig: ComputedRef<ActionsColumnConfig>
  visibleActions: ComputedRef<any[]>
  showActionsCount: ComputedRef<number>
  directActions: ComputedRef<any[]>
  dropdownActions: ComputedRef<any[]>
  hasMoreActions: ComputedRef<boolean>
  getButtonVariant: (action: any) => string
  getButtonSize: (action: any) => string
  handleActionClick: (action: any) => void
}
```

### ActionsPlugin

插件类，用于在插件管理器中注册操作列渲染器。

```typescript
class ActionsPlugin implements Plugin {
  name: string = 'actions'
  version: string = '1.0.0'
  description: string

  install(manager: PluginManager): void
  uninstall(): void
}
```

### ActionsRenderer

Vue 组件，处理操作列的实际渲染。

```typescript
interface Props {
  value?: any
  row: any
  config?: ActionsColumnConfig
  configId?: string
}
```

## 样式定制

### 按钮样式映射

插件内置了 `type` 到 `variant` 的映射关系：

```typescript
const typeMap = {
  primary: 'default',
  success: 'default',
  warning: 'outline',
  danger: 'destructive',
  info: 'outline',
}
```

### 大小映射

支持多种大小表示方式：

```typescript
const sizeMap = {
  small: 'sm',
  medium: 'default',
  large: 'lg',
}
```

### CSS 类名

操作列使用以下 CSS 类名结构：

```css
/* 主容器 */
.flex.items-center.justify-center.gap-1

/* 操作按钮 */
.p-1.gap-0

/* 更多操作按钮 */
.p-1.gap-0

/* 下拉菜单项 */
.w-4.h-4.mr-2
```

## 性能优化

### 条件渲染优化

使用条件函数避免不必要的按钮渲染：

```typescript
const config: ActionsColumnConfig = {
  actions: [
    {
      icon: 'edit',
      condition: (row) => row.editable, // 只在可编辑时显示
      onClick: (row) => editRow(row),
    },
    {
      icon: 'delete',
      condition: (row) => row.deletable && !row.hasChildren, // 复杂条件
      onClick: (row) => deleteRow(row),
    },
  ],
}
```

### 事件处理优化

在处理大量数据时，建议使用事件委托：

```typescript
const config: ActionsColumnConfig = {
  actions: [
    {
      icon: 'edit',
      onClick: (row) => {
        // 使用节流或防抖避免频繁操作
        throttle(() => editRow(row.id), 1000)()
      },
    },
  ],
}
```

## 故障排除

### 常见问题

1. **按钮不显示**

   - 检查 `actions` 数组是否为空
   - 确认 `condition` 函数返回 `true`
   - 验证 `onClick` 函数已定义

2. **图标不显示**

   - 确认 Iconify 图标库已正确加载
   - 检查图标名称是否正确（如：`mdi:pencil`）
   - 验证网络连接是否正常

3. **下拉菜单不显示**

   - 检查 `showActionsCount` 设置
   - 确认总操作数量大于 `showActionsCount`
   - 验证 ShadCN DropdownMenu 组件可用

4. **点击事件无响应**
   - 确认 `onClick` 函数已正确定义
   - 检查浏览器控制台是否有错误
   - 验证行数据是否正确传递

### 调试技巧

1. **开发模式日志**: 插件在开发模式下会输出详细日志

```typescript
// 在开发模式下，点击按钮会有控制台日志
if (import.meta.env.DEV) {
  console.log('ActionsRenderer: 按钮被点击', action.text || action.tooltip)
}
```

2. **条件函数调试**: 在条件函数中添加日志

```typescript
const config: ActionsColumnConfig = {
  actions: [
    {
      icon: 'edit',
      condition: (row) => {
        const result = row.editable
        console.log('Edit button condition:', { row, result }) // 调试日志
        return result
      },
      onClick: (row) => editRow(row),
    },
  ],
}
```

3. **使用 Vue DevTools**: 查看组件属性和计算属性的实时值

## 最佳实践

### 1. 操作数量管理

- **建议直接显示 1-3 个操作**: 避免界面过于拥挤
- **优先显示常用操作**: 将最频繁使用的操作放在前面
- **合理使用下拉菜单**: 将次要操作收入下拉菜单

```typescript
const config: ActionsColumnConfig = {
  actions: [
    // 最常用的操作 - 直接显示
    { icon: 'mdi:eye', tooltip: '查看', onClick: view },
    { icon: 'mdi:pencil', tooltip: '编辑', onClick: edit },

    // 次要操作 - 收入下拉菜单
    { icon: 'mdi:content-copy', tooltip: '复制', onClick: copy },
    { icon: 'mdi:download', tooltip: '导出', onClick: export },
    { icon: 'mdi:delete', tooltip: '删除', onClick: delete }
  ],
  showActionsCount: 2
}
```

### 2. 权限控制

使用条件函数实现基于权限的按钮显示：

```typescript
const config: ActionsColumnConfig = {
  actions: [
    {
      icon: 'mdi:pencil',
      tooltip: '编辑',
      condition: (row) => {
        // 检查用户权限和数据状态
        return hasPermission('edit') && row.status !== 'locked'
      },
      onClick: edit,
    },
  ],
}
```

### 3. 用户体验优化

- **提供清晰的图标**: 使用语义明确的图标
- **添加提示信息**: 为每个按钮提供 `tooltip`
- **确认重要操作**: 对删除等危险操作添加确认对话框

```typescript
{
  icon: 'mdi:delete',
  tooltip: '删除用户',
  variant: 'destructive',
  onClick: (row) => {
    if (confirm(`确定要删除用户 "${row.name}" 吗？此操作不可恢复。`)) {
      deleteUser(row.id)
    }
  }
}
```

### 4. 国际化支持

为多语言应用提供国际化支持：

```typescript
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const config: ActionsColumnConfig = {
  actions: [
    {
      icon: 'mdi:pencil',
      text: t('actions.edit'),
      tooltip: t('actions.editTooltip'),
      onClick: edit,
    },
  ],
}
```

### 5. 性能考虑

- **避免在渲染函数中创建新对象**: 将配置对象定义在组件外部
- **使用条件渲染**: 通过 `condition` 函数避免不必要的按钮渲染
- **合理使用图标**: 避免加载过多不必要的图标

## 更新日志

### v1.0.0

- 初始版本发布
- 支持基本的操作按钮渲染功能
- 提供 composable API
- 支持直接显示和下拉菜单模式
- 支持条件显示和多种按钮样式

## 许可证

MIT License
