# Link Renderer Plugin

链接列渲染器插件，用于在数据网格中创建可点击的链接，支持网址、邮箱和电话三种链接类型，提供完整的无障碍访问支持。

## 目录结构

```
link/
├── index.ts          # 插件入口文件，提供 composable API 和插件类
├── renderer.vue      # Vue 组件，处理渲染逻辑
└── README.md         # 插件文档（当前文件）
```

## 功能特性

- 🔗 **多链接类型**: 支持网址 (url)、邮箱 (mail) 和电话 (phone) 三种链接类型
- 🎯 **智能识别**: 根据链接类型自动生成正确的协议（http/https、mailto、tel）
- 🎨 **类型图标**: 为不同链接类型显示对应的图标（网页、邮件、电话）
- 🔗 **外链标识**: 为新窗口打开的链接显示外链图标
- 🎛️ **灵活配置**: 支持动态和静态链接地址生成
- 🖱️ **自定义点击**: 支持自定义点击处理函数
- ♿ **无障碍支持**: 完整的键盘导航和屏幕阅读器支持
- 🎨 **智能样式**: 根据链接类型自动应用不同的颜色主题

## 基本用法

### 1. 安装插件

```typescript
import { LinkPlugin } from '@/components/data-grid/plugins/renderers/link'
import { PluginManager } from '@/components/data-grid/plugins'

// 注册插件
const manager = new PluginManager()
manager.register(new LinkPlugin())
```

### 2. 使用 composable API

```vue
<script setup lang="ts">
import { useLinkRenderer } from '@/components/data-grid/plugins/renderers/link'

const {
  linkType,
  computedHref,
  displayValue,
  typeIcon,
  linkClasses,
  handleClick,
} = useLinkRenderer({
  value: 'https://example.com',
  row: { id: 1, website: 'https://example.com' },
  config: {
    type: 'url',
    showTypeIcon: true,
    target: '_blank',
  },
})
</script>
```

### 3. 直接使用组件

```vue
<template>
  <LinkRenderer
    value="<EMAIL>"
    :row="{ email: '<EMAIL>' }"
    :config="linkConfig"
  />
</template>

<script setup lang="ts">
import { LinkRenderer } from '@/components/data-grid/plugins/renderers/link'
import type { LinkColumnConfig } from '@/components/data-grid/plugins/renderers/link'

const linkConfig: LinkColumnConfig = {
  type: 'mail',
  showTypeIcon: true,
  showExternalIcon: true,
}
</script>
```

## 配置选项

### LinkColumnConfig

```typescript
interface LinkColumnConfig {
  // 链接类型
  type?: 'url' | 'mail' | 'phone' // 链接类型，默认为 'url'

  // 链接地址
  href?: string | ((row: any) => string) // 静态链接或动态生成函数

  // 打开方式
  target?: '_blank' | '_self' | '_parent' | '_top' // 链接打开方式

  // 显示配置
  showExternalIcon?: boolean // 是否显示外链图标，默认根据类型和目标自动判断
  showTypeIcon?: boolean // 是否显示类型图标，默认 false

  // 交互配置
  onClick?: (row: any, event: Event) => void // 自定义点击处理函数

  // 列配置
  width?: number // 列宽度
}
```

## 使用示例

### 示例 1: 网站链接

```typescript
const websiteConfig: LinkColumnConfig = {
  type: 'url',
  showTypeIcon: true,
  showExternalIcon: true,
  target: '_blank',
  // 自动从 value 生成链接，支持自动添加 https://
}

// 使用方式
// value: "example.com" -> href: "https://example.com"
// value: "https://example.com" -> href: "https://example.com"
```

### 示例 2: 邮箱链接

```typescript
const emailConfig: LinkColumnConfig = {
  type: 'mail',
  showTypeIcon: true,
  // 自动生成 mailto: 链接
  onClick: (row, event) => {
    // 可选：发送邮件前的自定义处理
    console.log('即将发送邮件到:', row.email)
  },
}

// 使用方式
// value: "<EMAIL>" -> href: "mailto:<EMAIL>"
```

### 示例 3: 电话链接

```typescript
const phoneConfig: LinkColumnConfig = {
  type: 'phone',
  showTypeIcon: true,
  // 自动清理电话号码格式并生成 tel: 链接
}

// 使用方式
// value: "+86 138-0013-8000" -> href: "tel:+8613800138000"
// value: "(*************" -> href: "tel:5551234567"
```

### 示例 4: 动态链接生成

```typescript
const dynamicConfig: LinkColumnConfig = {
  type: 'url',
  href: (row) => {
    // 根据行数据动态生成链接
    if (row.type === 'product') {
      return `/products/${row.id}`
    } else if (row.type === 'user') {
      return `/users/${row.id}/profile`
    }
    return '#'
  },
  target: '_self',
  showTypeIcon: false,
}
```

### 示例 5: 社交媒体链接

```typescript
const socialConfig: LinkColumnConfig = {
  type: 'url',
  href: (row) => {
    // 根据平台生成对应的链接
    switch (row.platform) {
      case 'twitter':
        return `https://twitter.com/${row.username}`
      case 'github':
        return `https://github.com/${row.username}`
      case 'linkedin':
        return `https://linkedin.com/in/${row.username}`
      default:
        return row.profile_url
    }
  },
  showTypeIcon: true,
  target: '_blank',
  onClick: (row, event) => {
    // 跟踪社交媒体链接点击
    analytics.track('social_link_clicked', {
      platform: row.platform,
      username: row.username,
    })
  },
}
```

### 示例 6: 条件链接显示

```typescript
const conditionalConfig: LinkColumnConfig = {
  type: 'mail',
  href: (row) => {
    // 只有验证过的邮箱才生成链接
    return row.email_verified ? `mailto:${row.email}` : '#'
  },
  showTypeIcon: true,
  onClick: (row, event) => {
    if (!row.email_verified) {
      event.preventDefault()
      alert('邮箱尚未验证，无法发送邮件')
      return
    }
  },
}
```

## API 参考

### useLinkRenderer

composable 函数，提供链接列渲染所需的响应式数据和计算属性。

```typescript
function useLinkRenderer(props: {
  value?: any
  row: any
  config: LinkColumnConfig
}): {
  linkType: ComputedRef<string> // 链接类型
  computedHref: ComputedRef<string> // 计算后的链接地址
  target: ComputedRef<string> // 目标窗口
  displayValue: ComputedRef<string> // 显示的文本值
  typeIcon: ComputedRef<string | null> // 类型图标名称
  showExternalIcon: ComputedRef<boolean> // 是否显示外链图标配置
  shouldShowExternalIcon: ComputedRef<boolean> // 是否应该显示外链图标
  linkClasses: ComputedRef<string> // 链接样式类名
  tooltipText: ComputedRef<string> // 工具提示文本
  ariaLabel: ComputedRef<string> // 无障碍标签
  handleClick: (event?: Event) => void // 点击处理函数
}
```

### LinkPlugin

插件类，用于在插件管理器中注册链接列渲染器。

```typescript
class LinkPlugin implements Plugin {
  name: string = 'link'
  version: string = '1.0.0'
  description: string

  install(manager: PluginManager): void
  uninstall(): void
}
```

### LinkRenderer

Vue 组件，处理链接列的实际渲染。

```typescript
interface Props {
  value?: any
  row: any
  config: LinkColumnConfig
}
```

## 链接类型详解

### URL 链接 (url)

- **自动协议**: 自动为没有协议的链接添加 `https://`
- **新窗口**: 默认在新窗口 (`_blank`) 打开
- **图标**: 使用 "mdi:web" 图标
- **样式**: 蓝色主题 (`text-blue-600 hover:text-blue-800`)

```typescript
// 示例转换
"example.com" → "https://example.com"
"https://example.com" → "https://example.com"
"http://example.com" → "http://example.com"
```

### 邮箱链接 (mail)

- **协议**: 自动添加 `mailto:` 协议
- **应用调用**: 调用系统默认邮件客户端
- **图标**: 使用 "mdi:email" 图标
- **样式**: 绿色主题 (`text-green-600 hover:text-green-800`)

```typescript
// 示例转换
"<EMAIL>" → "mailto:<EMAIL>"
```

### 电话链接 (phone)

- **格式清理**: 自动清理电话号码格式，只保留数字和 + 号
- **协议**: 自动添加 `tel:` 协议
- **应用调用**: 调用系统拨号应用
- **图标**: 使用 "mdi:phone" 图标
- **样式**: 橙色主题 (`text-orange-600 hover:text-orange-800`)

```typescript
// 示例转换
"+86 138-0013-8000" → "tel:+8613800138000"
"(*************" → "tel:5551234567"
"138 0013 8000" → "tel:13800138000"
```

## 样式定制

### 内置样式类

链接渲染器根据类型自动应用不同的颜色主题：

```css
/* URL 链接 */
.text-blue-600.hover\:text-blue-800

/* 邮箱链接 */
.text-green-600.hover\:text-green-800

/* 电话链接 */
.text-orange-600.hover\:text-orange-800
```

### 通用样式类

所有链接都应用以下基础样式：

```css
.h-auto.p-0.gap-0.text-left.justify-start
.no-underline.hover\:underline
.transition-colors.duration-200
.truncate
```

### 自定义样式

可以通过 CSS 覆盖默认样式：

```css
/* 自定义链接颜色 */
.data-grid .link-renderer .text-blue-600 {
  @apply text-purple-600;
}

.data-grid .link-renderer .text-blue-600:hover {
  @apply text-purple-800;
}

/* 自定义图标样式 */
.data-grid .link-renderer .mr-1 {
  @apply mr-2 text-lg;
}
```

## 无障碍支持

### 键盘导航

- **Tab 键**: 可以使用 Tab 键导航到链接
- **Enter 键**: 按 Enter 键激活链接
- **空格键**: 按空格键激活链接（阻止页面滚动）

### 屏幕阅读器

- **aria-label**: 为每个链接提供描述性标签
- **title**: 提供详细的工具提示信息
- **语义化**: 使用正确的 HTML 语义和 ARIA 属性

```typescript
// 自动生成的无障碍属性示例
{
  'aria-label': '邮件链接: <EMAIL>',
  'title': '发送邮件到 mailto:<EMAIL>'
}
```

### 焦点管理

- **可见焦点**: 清晰的焦点指示器
- **焦点顺序**: 逻辑的 Tab 顺序
- **焦点陷阱**: 在模态对话框中正确管理焦点

## 性能优化

### 计算属性缓存

所有计算属性都使用 Vue 的响应式系统进行缓存，只在依赖项变化时重新计算：

```typescript
const computedHref = computed(() => {
  // 只在 config.href 或 row 数据变化时重新计算
  // Vue 会自动缓存计算结果
})
```

### 条件渲染

使用 v-if 进行条件渲染，避免不必要的 DOM 元素：

```vue
<template>
  <Icon v-if="typeIcon" :icon="typeIcon" />
  <Icon v-if="showExternalIcon && shouldShowExternalIcon" />
</template>
```

### 事件处理优化

使用事件修饰符优化事件处理：

```vue
<template>
  <Button @keydown.enter="handleClick" @keydown.space.prevent="handleClick" />
</template>
```

## 故障排除

### 常见问题

1. **链接不可点击**

   - 检查 `value` 是否为空或无效
   - 确认 `href` 函数是否返回有效值
   - 验证是否有自定义 `onClick` 阻止了默认行为

2. **图标不显示**

   - 确认 Iconify 图标库已正确加载
   - 检查 `showTypeIcon` 配置是否为 `true`
   - 验证网络连接是否正常

3. **新窗口不打开**

   - 检查 `target` 设置是否正确
   - 确认浏览器是否阻止了弹窗
   - 验证是否有自定义 `onClick` 覆盖了默认行为

4. **邮件/电话应用不启动**
   - 确认系统是否有对应的默认应用
   - 检查浏览器安全设置
   - 验证 URL 格式是否正确 (`mailto:`, `tel:`)

### 调试技巧

1. **查看计算属性**: 使用 Vue DevTools 查看计算属性的值

```typescript
const { computedHref, linkType } = useLinkRenderer(props)

// 在模板中临时显示调试信息
// {{ computedHref }} - {{ linkType }}
```

2. **自定义点击处理器调试**:

```typescript
const config: LinkColumnConfig = {
  onClick: (row, event) => {
    console.log('链接点击:', { row, event, href: computedHref.value })
    // 正常处理...
  },
}
```

3. **网络请求监控**: 使用浏览器开发者工具的网络面板监控链接请求

## 最佳实践

### 1. 链接验证

在生成链接前验证数据的有效性：

```typescript
const config: LinkColumnConfig = {
  href: (row) => {
    // 验证邮箱格式
    if (row.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email)) {
      return `mailto:${row.email}`
    }
    return '#' // 返回空链接而不是无效链接
  },
  onClick: (row, event) => {
    if (!row.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email)) {
      event.preventDefault()
      alert('邮箱地址无效')
    }
  },
}
```

### 2. 用户体验优化

- **加载状态**: 为长时间加载的链接提供加载指示器
- **错误处理**: 优雅地处理链接错误
- **确认对话框**: 为重要操作提供确认

```typescript
const config: LinkColumnConfig = {
  onClick: async (row, event) => {
    event.preventDefault()

    if (row.type === 'external') {
      const confirmed = confirm('即将跳转到外部网站，是否继续？')
      if (!confirmed) return
    }

    try {
      window.open(row.url, '_blank', 'noopener,noreferrer')
    } catch (error) {
      alert('无法打开链接，请稍后重试')
    }
  },
}
```

### 3. 安全考虑

- **防止 XSS**: 验证和清理用户输入的链接
- **安全的外链**: 为外部链接使用 `noopener,noreferrer`
- **协议验证**: 只允许安全的协议

```typescript
const safeLinkConfig: LinkColumnConfig = {
  href: (row) => {
    const url = row.url
    // 只允许 http, https, mailto, tel 协议
    if (!/^(https?:|mailto:|tel:)/.test(url)) {
      return '#'
    }
    return url
  },
  target: '_blank', // 外链总是在新窗口打开
}
```

### 4. 国际化支持

为多语言应用提供国际化的工具提示：

```typescript
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const config: LinkColumnConfig = {
  type: 'mail',
  // 可以通过自定义组件或后处理来实现国际化的 tooltip
}

// 在组件中
const tooltipText = computed(() => {
  switch (linkType.value) {
    case 'mail':
      return t('link.sendEmailTo', { email: computedHref.value })
    case 'phone':
      return t('link.callNumber', { number: props.value })
    default:
      return t('link.visitUrl', { url: computedHref.value })
  }
})
```

### 5. 性能监控

监控链接点击和错误率：

```typescript
const config: LinkColumnConfig = {
  onClick: (row, event) => {
    // 跟踪链接点击
    analytics.track('link_clicked', {
      type: config.type,
      target: config.target,
      source: 'data_grid',
    })

    // 处理链接打开错误
    try {
      // 正常链接处理...
    } catch (error) {
      analytics.track('link_error', {
        error: error.message,
        url: computedHref.value,
      })
    }
  },
}
```

## 更新日志

### v1.0.0

- 初始版本发布
- 支持 URL、邮箱、电话三种链接类型
- 提供 composable API
- 支持动态链接生成和自定义点击处理
- 完整的无障碍访问支持
- 智能的图标和样式系统

## 许可证

MIT License
