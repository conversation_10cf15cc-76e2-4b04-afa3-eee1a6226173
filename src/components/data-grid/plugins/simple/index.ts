/**
 * 简化插件系统入口文件
 * 实现 Serena 方案的 Layer 1 (Simple) 架构
 */

// 核心 API
export {
  createSimpleRenderer,
  createSimpleService,
  createSimpleExtension,
  type SimpleRendererOptions,
  type RenderContext,
  type ValidationRules,
  type SimplePlugin
} from './api'

// 工厂类
export {
  SimplePlugin as SimplePluginClass,
  getCurrencySymbol
} from './factory'

// 预设模板
export {
  CommonTemplates,
  type TemplateDefinition,
  type TemplateFactory
} from './templates'

// 初始化 SimplePlugin 类到全局
import { SimplePlugin as SimplePluginClass } from './factory';
(globalThis as any).__SimplePluginClass = SimplePluginClass

// 便利导出：快捷创建常用渲染器
import { CommonTemplates } from './templates'
const { status, link, currency, date, boolean } = CommonTemplates

/**
 * 快捷创建状态渲染器
 * @example
 * ```typescript
 * export const MyStatus = createStatusRenderer('my-status', {
 *   statusMap: { 1: { text: '启用', type: 'success' } }
 * })
 * ```
 */
export const createStatusRenderer = (name: string, config?: Record<string, any>) => 
  status.create(name, config)

/**
 * 快捷创建链接渲染器
 */
export const createLinkRenderer = (name: string, config?: Record<string, any>) => 
  link.create(name, config)

/**
 * 快捷创建货币渲染器
 */
export const createCurrencyRenderer = (name: string, config?: Record<string, any>) => 
  currency.create(name, config)

/**
 * 快捷创建日期渲染器
 */
export const createDateRenderer = (name: string, config?: Record<string, any>) => 
  date.create(name, config)

/**
 * 快捷创建布尔值渲染器
 */
export const createBooleanRenderer = (name: string, config?: Record<string, any>) => 
  boolean.create(name, config)

/**
 * 插件预设集合
 */
export const SimplePluginPresets = {
  /**
   * 基础套装：状态、链接、货币
   */
  basic: [
    createStatusRenderer('status'),
    createLinkRenderer('link'),
    createCurrencyRenderer('currency')
  ],
  
  /**
   * 完整套装：包含所有常用渲染器
   */
  full: [
    createStatusRenderer('status'),
    createLinkRenderer('link'),
    createCurrencyRenderer('currency'),
    createDateRenderer('date'),
    createBooleanRenderer('boolean')
  ],
  
  /**
   * 业务套装：适用于企业管理系统
   */
  business: [
    createStatusRenderer('status', {
      statusMap: {
        1: { text: '启用', type: 'success' },
        0: { text: '禁用', type: 'default' }
      }
    }),
    createCurrencyRenderer('amount'),
    createDateRenderer('date', { showTime: false }),
    createBooleanRenderer('enabled')
  ]
}

/**
 * 批量注册简化插件到插件管理器
 */
export async function registerSimplePlugins(
  pluginManager: any,
  plugins: any[]
): Promise<void> {
  const results = await Promise.allSettled(
    plugins.map(plugin => pluginManager.registerPlugin(plugin.toFullPlugin()))
  )
  
  const failures = results.filter(result => result.status === 'rejected')
  if (failures.length > 0 && import.meta.env.DEV) {
    console.warn(`Failed to register ${failures.length} simple plugins:`, failures)
  } else if (import.meta.env.DEV) {
    console.log(`✅ Successfully registered ${plugins.length} simple plugins`)
  }
}

/**
 * 开发工具：验证简化插件配置
 */
export function validateSimplePlugin(plugin: any): { valid: boolean; errors?: string[] } {
  const errors: string[] = []
  
  if (!plugin.name) {
    errors.push('Plugin must have a name')
  }
  
  if (!plugin.options?.render) {
    errors.push('Plugin must have a render function')
  }
  
  return {
    valid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined
  }
}

/**
 * 使用指南常量
 */
export const SIMPLE_PLUGIN_USAGE_GUIDE = {
  quickStart: `
// 1. 最简单的渲染器（3行代码）
export const MyRenderer = createSimpleRenderer('my-renderer', {
  render: (value) => \`<span class="custom">\${value}</span>\`
})

// 2. 使用预设模板
export const MyStatus = createStatusRenderer('my-status', {
  statusMap: { 1: { text: '启用', type: 'success' } }
})

// 3. 注册到插件管理器
await registerSimplePlugins(pluginManager, [MyRenderer, MyStatus])
`,

  migration: `
// 从复杂插件迁移到简化插件

// 之前（50+ 行）
export const ComplexPlugin: PluginDefinition = {
  name: 'status-renderer',
  version: '1.0.0',
  // ... 大量配置代码
}

// 现在（3 行）
export const SimpleStatus = createStatusRenderer('status', {
  statusMap: { 1: { text: '启用', type: 'success' } }
})
`,

  bestPractices: `
1. 使用语义化的插件名称
2. 利用预设模板减少重复代码
3. 合理设置默认配置
4. 添加适当的样式
5. 处理边界情况（空值、错误等）
`
}