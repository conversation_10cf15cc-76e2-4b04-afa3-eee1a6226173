import { defineComponent, h, computed, type PropType, type Component } from 'vue'
import type { PluginDefinition, ColumnHelperMethodDefinition, PluginContext } from '../core/injection'
import type { SimpleRendererOptions, RenderContext, SimplePlugin as ISimplePlugin, RowData, ColumnConfig } from './api'

/**
 * 渲染器参数类型
 */
export interface RendererParams {
  value: any
  row: RowData
  column?: ColumnConfig
  rowIndex?: number
  config?: Record<string, any>
}

/**
 * 简化插件类
 * 自动将简化配置转换为完整插件定义
 */
export class SimplePlugin implements ISimplePlugin {
  constructor(
    public name: string,
    public type: 'renderer' | 'service' | 'extension',
    public options: SimpleRendererOptions
  ) {}

  /**
   * 转换为完整插件定义
   */
  toFullPlugin(): PluginDefinition {
    return {
      name: `${this.name}-simple`,
      version: '1.0.0',
      description: `Auto-generated simple ${this.type}: ${this.name}`,
      
      columnHelperMethods: this.generateColumnHelperMethods(),
      
      setup: async (context: PluginContext) => {
        await this.autoSetup(context)
      }
    }
  }

  /**
   * 自动生成列助手方法
   */
  private generateColumnHelperMethods(): ColumnHelperMethodDefinition[] {
    return [{
      name: this.name.replace(/-/g, '_'), // 转换为有效的方法名
      implementation: this.createColumnMethod(),
      rendererConfig: {
        component: `${this.pascalCase(this.name)}SimpleRenderer`,
        defaultWidth: this.options.defaultWidth || 100
      },
      description: `Simple ${this.name} renderer`,
      priority: 1
    }]
  }

  /**
   * 创建列方法实现
   */
  private createColumnMethod() {
    const that = this
    return function(
      this: any, 
      field: string, 
      title: string, 
      config: Record<string, any> = {}
    ) {
      const mergedConfig = { 
        ...that.options.defaultConfig, 
        ...config 
      }
      
      return (this as any).createPluginColumn(field, title, that.name, mergedConfig)
    }
  }

  /**
   * 自动设置逻辑
   */
  private async autoSetup(context: PluginContext) {
    // 自动注册Vue组件
    if (context.app && this.options.render) {
      const componentName = `${this.pascalCase(this.name)}SimpleRenderer`
      const component = this.createVueComponent()
      
      context.app.component(componentName, component)
    }

    // 自动注入样式
    if (this.options.styles) {
      this.injectStyles(this.options.styles)
    }

    // 自动注册到渲染器注册表
    const rendererRegistry = context.container.resolveService('RendererRegistry') as Map<string, any>
    if (rendererRegistry) {
      rendererRegistry.set(this.name, {
        name: this.name,
        component: `${this.pascalCase(this.name)}SimpleRenderer`,
        render: this.options.render
      })
    }

    if (import.meta.env.DEV) {
      console.log(`✅ Simple plugin '${this.name}' auto-setup completed`)
    }
  }

  /**
   * 创建Vue组件
   */
  private createVueComponent(): Component {
    const renderFn = this.options.render
    const pluginName = this.name
    
    return defineComponent({
      name: `${this.pascalCase(this.name)}SimpleRenderer`,
      props: {
        params: {
          type: Object as PropType<RendererParams>,
          required: true
        }
      },
      setup(props) {
        const config = computed(() => props.params.config || {})
        const context = computed((): RenderContext => ({
          config: config.value,
          row: props.params.row,
          column: props.params.column,
          rowIndex: props.params.rowIndex
        }))

        return () => {
          try {
            const result = renderFn(props.params.value, context.value)
            
            // 如果返回字符串，包装为VNode
            if (typeof result === 'string') {
              return h('span', { 
                innerHTML: result,
                class: `simple-renderer simple-renderer-${pluginName}`
              })
            }
            
            return result
          } catch (error) {
            console.error(`Simple renderer '${pluginName}' error:`, error)
            return h('span', { 
              class: 'simple-renderer-error',
              style: { color: 'red' }
            }, `Error: ${props.params.value}`)
          }
        }
      }
    })
  }

  /**
   * 注入样式
   */
  private injectStyles(styles: string) {
    const styleId = `simple-plugin-${this.name}`
    
    if (!document.getElementById(styleId)) {
      const styleElement = document.createElement('style')
      styleElement.id = styleId
      styleElement.textContent = `
        /* Simple Plugin Styles: ${this.name} */
        ${styles}
        
        /* Default simple renderer styles */
        .simple-renderer {
          display: inline-block;
        }
        
        .simple-renderer-error {
          background: #fee2e2;
          padding: 2px 4px;
          border-radius: 2px;
          font-size: 12px;
        }
      `
      document.head.appendChild(styleElement)
    }
  }

  /**
   * 转换为PascalCase
   */
  private pascalCase(str: string): string {
    return str
      .replace(/[-_](.)/g, (_, char) => char.toUpperCase())
      .replace(/^./, char => char.toUpperCase())
  }
}

/**
 * 工具函数：获取货币符号
 */
export function getCurrencySymbol(currency: string): string {
  const symbols: Record<string, string> = {
    'CNY': '¥',
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥'
  }
  return symbols[currency] || currency
}