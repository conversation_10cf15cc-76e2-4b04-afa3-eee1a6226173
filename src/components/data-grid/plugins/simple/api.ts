import type { VNode } from 'vue'

/**
 * 行数据类型
 */
export type RowData = Record<string, any>

/**
 * 列配置类型
 */
export type ColumnConfig = Record<string, any>

/**
 * 渲染上下文
 */
export interface RenderContext {
  /** 配置对象 */
  config: Record<string, any>
  /** 行数据 */
  row: RowData
  /** 列配置 */
  column?: ColumnConfig
  /** 行索引 */
  rowIndex?: number
}

/**
 * 验证规则
 */
export interface ValidationRules {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'object' | 'array'
    required?: boolean
    enum?: any[]
    default?: any
  }
}

/**
 * 简化渲染器选项
 */
export interface SimpleRendererOptions {
  /** 渲染函数 */
  render: (value: any, context: RenderContext) => string | VNode
  /** 默认配置 */
  defaultConfig?: Record<string, any>
  /** 默认列宽 */
  defaultWidth?: number
  /** 自动注入的样式 */
  styles?: string
  /** 配置验证规则 */
  validation?: ValidationRules
}

/**
 * 简化插件接口
 */
export interface SimplePlugin {
  /** 插件名称 */
  name: string
  /** 插件类型 */
  type: 'renderer' | 'service' | 'extension'
  /** 选项配置 */
  options: SimpleRendererOptions
  /** 转换为完整插件定义 */
  toFullPlugin(): any
}

/**
 * 创建简化渲染器
 * 
 * @param name 渲染器名称
 * @param options 渲染器选项
 * @returns 简化插件实例
 * 
 * @example
 * ```typescript
 * export const MyRenderer = createSimpleRenderer('my-renderer', {
 *   render: (value) => `<span class="custom">${value}</span>`
 * })
 * ```
 */
export function createSimpleRenderer(
  name: string, 
  options: SimpleRendererOptions
): SimplePlugin {
  // 使用动态导入避免循环依赖
  const SimplePluginClass = (globalThis as any).__SimplePluginClass
  if (!SimplePluginClass) {
    throw new Error('SimplePlugin class not initialized. Make sure to import from simple/index.ts')
  }
  return new SimplePluginClass(name, 'renderer', options)
}

/**
 * 创建简化服务
 */
export function createSimpleService(
  name: string,
  options: any
): SimplePlugin {
  const SimplePluginClass = (globalThis as any).__SimplePluginClass
  if (!SimplePluginClass) {
    throw new Error('SimplePlugin class not initialized. Make sure to import from simple/index.ts')
  }
  return new SimplePluginClass(name, 'service', options)
}

/**
 * 创建简化扩展
 */
export function createSimpleExtension(
  name: string,
  options: any
): SimplePlugin {
  const SimplePluginClass = (globalThis as any).__SimplePluginClass
  if (!SimplePluginClass) {
    throw new Error('SimplePlugin class not initialized. Make sure to import from simple/index.ts')
  }
  return new SimplePluginClass(name, 'extension', options)
}