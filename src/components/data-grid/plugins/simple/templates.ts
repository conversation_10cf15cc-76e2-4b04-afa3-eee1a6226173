import type { SimpleRendererOptions } from './api'
import { getCurrencySymbol } from './factory'

// 使用全局引用避免循环依赖
function createSimpleRenderer(name: string, options: SimpleRendererOptions) {
  const SimplePluginClass = (globalThis as any).__SimplePluginClass
  if (!SimplePluginClass) {
    throw new Error('SimplePlugin class not initialized')
  }
  return new SimplePluginClass(name, 'renderer', options)
}

/**
 * 模板定义接口
 */
export interface TemplateDefinition {
  render: (value: any, context: any) => string
  defaultConfig?: Record<string, any>
  styles?: string
  validation?: any
}

/**
 * 模板工厂接口
 */
export interface TemplateFactory {
  create: (pluginName: string, overrides?: Record<string, any>) => any
  customize: (customizations: Partial<TemplateDefinition>) => TemplateFactory
}

/**
 * 通用模板库
 */
export const CommonTemplates = {
  /**
   * 状态模板
   */
  status: createPluginTemplate('status', {
    defaultConfig: {
      statusMap: {},
      variant: 'badge',
      showIcon: true
    },
    
    render: (value, { config }) => {
      const status = config.statusMap[value] || { 
        text: String(value), 
        type: 'default' 
      }
      
      const iconHtml = config.showIcon 
        ? `<i class="status-icon status-icon-${status.type}"></i>` 
        : ''
      
      return `
        <span class="status-${config.variant} status-${status.type}">
          ${iconHtml}
          ${status.text}
        </span>
      `
    },
    
    styles: `
      .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        line-height: 1.2;
      }
      .status-default { background: #f3f4f6; color: #374151; }
      .status-success { background: #d1fae5; color: #065f46; }
      .status-warning { background: #fef3c7; color: #92400e; }
      .status-danger { background: #fee2e2; color: #991b1b; }
      .status-info { background: #dbeafe; color: #1e40af; }
      
      .status-dot {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }
      .status-dot::before {
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 50%;
        display: inline-block;
      }
      .status-dot.status-success::before { background: #10b981; }
      .status-dot.status-warning::before { background: #f59e0b; }
      .status-dot.status-danger::before { background: #ef4444; }
      .status-dot.status-info::before { background: #3b82f6; }
      .status-dot.status-default::before { background: #6b7280; }
      
      .status-icon {
        margin-right: 4px;
        font-size: 10px;
      }
    `,
    
    validation: {
      statusMap: { type: 'object', required: false },
      variant: { type: 'string', enum: ['badge', 'dot', 'text'] },
      showIcon: { type: 'boolean' }
    }
  }),

  /**
   * 链接模板
   */
  link: createPluginTemplate('link', {
    defaultConfig: {
      target: '_blank',
      showExternal: true,
      color: 'blue'
    },
    
    render: (value, { config, row }) => {
      const href = typeof config.href === 'function' 
        ? config.href(row) 
        : (config.href || value)
      
      const externalIcon = config.showExternal && config.target === '_blank'
        ? '<i class="link-external">↗</i>'
        : ''
      
      return `
        <a href="${href}" target="${config.target}" class="data-grid-link link-${config.color}">
          ${value}${externalIcon}
        </a>
      `
    },
    
    styles: `
      .data-grid-link {
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 2px;
        border-radius: 2px;
        padding: 1px 2px;
        transition: all 0.2s;
      }
      .data-grid-link:hover {
        text-decoration: underline;
      }
      
      .link-blue { color: #3b82f6; }
      .link-blue:hover { background: #dbeafe; }
      
      .link-green { color: #10b981; }
      .link-green:hover { background: #d1fae5; }
      
      .link-red { color: #ef4444; }
      .link-red:hover { background: #fee2e2; }
      
      .link-external {
        font-size: 10px;
        opacity: 0.7;
      }
    `
  }),

  /**
   * 货币模板
   */
  currency: createPluginTemplate('currency', {
    defaultConfig: {
      currency: 'CNY',
      precision: 2,
      showSymbol: true,
      showZero: true
    },
    
    render: (value, { config }) => {
      const num = Number(value) || 0
      
      if (num === 0 && !config.showZero) {
        return '<span class="currency-zero">-</span>'
      }
      
      const formatted = num.toFixed(config.precision)
      const symbol = config.showSymbol ? getCurrencySymbol(config.currency) : ''
      const isNegative = num < 0
      
      return `
        <span class="currency ${isNegative ? 'currency-negative' : 'currency-positive'}">
          ${symbol}${Math.abs(num).toFixed(config.precision)}
        </span>
      `
    },
    
    styles: `
      .currency {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', monospace;
        font-weight: 500;
      }
      .currency-positive {
        color: #065f46;
      }
      .currency-negative {
        color: #991b1b;
      }
      .currency-zero {
        color: #6b7280;
        font-style: italic;
      }
    `
  }),

  /**
   * 日期模板
   */
  date: createPluginTemplate('date', {
    defaultConfig: {
      format: 'YYYY-MM-DD',
      showTime: false,
      relative: false
    },
    
    render: (value, { config }) => {
      if (!value) return '<span class="date-empty">-</span>'
      
      const date = new Date(value)
      if (isNaN(date.getTime())) {
        return `<span class="date-error">Invalid Date</span>`
      }
      
      if (config.relative) {
        const now = new Date()
        const diffMs = now.getTime() - date.getTime()
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
        
        if (diffDays === 0) return '<span class="date-today">今天</span>'
        if (diffDays === 1) return '<span class="date-yesterday">昨天</span>'
        if (diffDays < 7) return `<span class="date-recent">${diffDays}天前</span>`
      }
      
      let formatted = date.toISOString().split('T')[0] // YYYY-MM-DD
      
      if (config.showTime) {
        const time = date.toTimeString().split(' ')[0].slice(0, 5) // HH:MM
        formatted += ` ${time}`
      }
      
      return `<span class="date-formatted">${formatted}</span>`
    },
    
    styles: `
      .date-formatted {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', monospace;
      }
      .date-today { color: #059669; font-weight: 500; }
      .date-yesterday { color: #0891b2; }
      .date-recent { color: #6b7280; }
      .date-empty { color: #9ca3af; font-style: italic; }
      .date-error { color: #ef4444; font-size: 11px; }
    `
  }),

  /**
   * 布尔值模板
   */
  boolean: createPluginTemplate('boolean', {
    defaultConfig: {
      style: 'badge', // badge, switch, icon, text
      trueText: '是',
      falseText: '否',
      trueColor: 'success',
      falseColor: 'default'
    },
    
    render: (value, { config }) => {
      const isTrue = Boolean(value)
      const text = isTrue ? config.trueText : config.falseText
      const color = isTrue ? config.trueColor : config.falseColor
      
      switch (config.style) {
        case 'badge':
          return `<span class="bool-badge bool-${color}">${text}</span>`
        case 'icon':
          const icon = isTrue ? '✓' : '✗'
          return `<span class="bool-icon bool-${color}">${icon}</span>`
        case 'switch':
          return `<span class="bool-switch ${isTrue ? 'bool-switch-on' : 'bool-switch-off'}"></span>`
        default:
          return `<span class="bool-text bool-${color}">${text}</span>`
      }
    },
    
    styles: `
      .bool-badge {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 11px;
        font-weight: 500;
        line-height: 1;
      }
      .bool-badge.bool-success { background: #d1fae5; color: #065f46; }
      .bool-badge.bool-default { background: #f3f4f6; color: #374151; }
      
      .bool-icon {
        font-weight: bold;
        font-size: 12px;
      }
      .bool-icon.bool-success { color: #10b981; }
      .bool-icon.bool-default { color: #6b7280; }
      
      .bool-switch {
        display: inline-block;
        width: 24px;
        height: 12px;
        border-radius: 6px;
        position: relative;
        transition: all 0.2s;
      }
      .bool-switch::after {
        content: '';
        position: absolute;
        top: 1px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: white;
        transition: all 0.2s;
      }
      .bool-switch-on {
        background: #10b981;
      }
      .bool-switch-on::after {
        transform: translateX(12px);
      }
      .bool-switch-off {
        background: #d1d5db;
      }
      .bool-switch-off::after {
        transform: translateX(1px);
      }
    `
  })
}

/**
 * 模板工厂函数
 */
function createPluginTemplate(
  name: string, 
  template: TemplateDefinition
): TemplateFactory {
  return {
    create: (pluginName: string, overrides = {}) => {
      const mergedConfig = { ...template.defaultConfig, ...overrides }
      
      return createSimpleRenderer(pluginName, {
        render: template.render,
        defaultConfig: mergedConfig,
        styles: template.styles,
        validation: template.validation
      })
    },
    
    customize: (customizations: Partial<TemplateDefinition>) => {
      return createPluginTemplate(name, { ...template, ...customizations })
    }
  }
}