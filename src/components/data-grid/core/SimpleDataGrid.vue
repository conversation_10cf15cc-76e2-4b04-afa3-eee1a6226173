<template>
  <div
    class="flex flex-col overflow-hidden relative"
    :class="containerClasses"
    :style="containerStyle"
    ref="containerRef"
  >
    <!-- 工具栏 -->
    <DGToolbar
      v-if="toolbarOptions"
      v-bind="toolbarOptions"
      ref="toolbarRef"
      class="flex-none"
    />
    
    <!-- 表格容器 -->
    <div class="flex-1 min-h-0 relative" ref="gridContainerRef">
      <vxe-grid
        v-bind="processedGridOptions"
        height="100%"
        ref="gridRef"
        v-on="gridEvents"
      >
        <!-- 动态渲染自定义插槽 -->
        <template
          v-for="(slotConfig, slotName) in customSlots"
          :key="slotName"
          #[slotName]="params"
        >
          <slot :name="slotName" v-bind="params">
            <!-- 渲染器组件 -->
            <component
              v-if="slotConfig.component"
              :is="slotConfig.component"
              v-bind="getRendererProps(slotConfig, params)"
            />
            <!-- 渲染函数 -->
            <div
              v-else-if="slotConfig.render"
              v-html="executeRenderFunction(slotConfig, params)"
            />
            <!-- 默认内容 -->
            <span v-else>{{ params.value }}</span>
          </slot>
        </template>
      </vxe-grid>

      <!-- 自定义 Loading 遮罩层 -->
      <DataGridLoading :visible="isLoading" :config="loadingConfig" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  ref,
  onMounted,
  onUnmounted,
  watch,
  nextTick,
  provide,
  shallowRef,
  markRaw,
} from 'vue'
import { useResizeObserver } from '@vueuse/core'
import type { SimpleDataGridProps, SimpleColumnConfig } from '../types/simple'
import DGToolbar from './DGToolbar.vue'
import DataGridLoading from './DataGridLoading.vue'

// 静态导入所有渲染器组件
import StatusRenderer from '../renderers/status/renderer.vue'
import BooleanRenderer from '../renderers/boolean/renderer.vue'
import LinkRenderer from '../renderers/link/renderer.vue'
import ActionsRenderer from '../renderers/actions/renderer.vue'
import RatingRenderer from '../renderers/rating/renderer.vue'
import CompositeRenderer from '../renderers/composite/renderer.vue'
import CurrencyRenderer from '../renderers/currency/renderer.vue'
import DateRenderer from '../renderers/date/renderer.vue'
import ImageRenderer from '../renderers/image/renderer.vue'

/**
 * 简化版 DataGrid 组件属性
 */
const props = withDefaults(defineProps<SimpleDataGridProps>(), {
  columns: () => [],
  data: () => [],
  loading: false,
  height: 'auto',
})

// 预注册的渲染器组件映射
const RENDERER_COMPONENTS = markRaw({
  status: StatusRenderer,
  boolean: BooleanRenderer,
  link: LinkRenderer,
  actions: ActionsRenderer,
  rating: RatingRenderer,
  composite: CompositeRenderer,
  currency: CurrencyRenderer,
  date: DateRenderer,
  image: ImageRenderer,
})

// 响应式引用
const gridRef = ref()
const toolbarRef = ref()
const containerRef = ref()
const gridContainerRef = ref()

// 响应式的高度状态
const viewportHeight = shallowRef(window.innerHeight)
const containerOffsetTop = shallowRef(0)

// 提取 toolbar 配置
const toolbarOptions = computed(() => props.toolbarOptions)

// 提取 loading 状态和配置
const isLoading = computed(() => props.loading)
const loadingConfig = computed(() => ({
  type: 'spinner',
  text: '数据加载中...',
  size: 'medium',
  color: 'primary',
  theme: 'auto',
  ...props.loadingConfig,
}))

// 计算自动高度
const calculateAutoHeight = () => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect()
    containerOffsetTop.value = rect.top
    const availableHeight = viewportHeight.value - rect.top - 20
    return Math.max(availableHeight, 300)
  }
  return 600
}

// 计算容器样式
const containerStyle = computed(() => {
  const styles: Record<string, string> = {}
  
  if (props.height && props.height !== 'auto') {
    if (typeof props.height === 'number' || !isNaN(Number(props.height))) {
      styles.height = props.height + 'px'
    } else {
      styles.height = props.height
    }
  } else {
    const autoHeight = calculateAutoHeight()
    styles.height = autoHeight + 'px'
  }
  
  return styles
})

// 计算容器类名
const containerClasses = computed(() => '')

// 获取事件处理器
const gridEvents = computed(() => props.gridEvents || {})

// 生成插槽名称
const generateSlotName = (col: SimpleColumnConfig, slotType: string): string => {
  return `${col.field || col.type || 'col'}_${slotType}_${Math.random().toString(36).substring(2, 9)}`
}

// 提取自定义插槽映射
const customSlots = computed(() => {
  const slots: Record<string, any> = {}
  
  const processColumns = (cols: SimpleColumnConfig[]) => {
    cols.forEach((col) => {
      if (col.renderer) {
        const slotName = generateSlotName(col, 'default')
        
        // 设置插槽配置
        slots[slotName] = {
          component: typeof col.renderer === 'string' ? RENDERER_COMPONENTS[col.renderer] : null,
          render: typeof col.renderer === 'function' ? col.renderer : null,
          config: col.rendererConfig || {},
          field: col.field,
        }
        
        // 更新列配置以使用生成的插槽名称
        if (!col.slots) col.slots = {}
        col.slots.default = slotName
      }
      
      // 处理子列
      if (col.children && Array.isArray(col.children)) {
        processColumns(col.children)
      }
    })
  }
  
  processColumns([...props.columns])
  return slots
})

// 处理后的网格选项
const processedGridOptions = computed(() => {
  const options = {
    columns: props.columns,
    data: props.data,
    autoResize: false,
    syncResize: true,
    keepSource: false,
    loading: false,
    ...props.gridOptions,
  }
  
  // 移除工具栏配置，因为我们使用自定义工具栏
  const { toolbarOptions, height, loading, loadingConfig, ...gridOptionsWithoutToolbar } = options
  
  return gridOptionsWithoutToolbar
})

// 获取渲染器属性
const getRendererProps = (slotConfig: any, params: any) => {
  return {
    value: params.value,
    row: params.row,
    column: params.column,
    field: slotConfig.field,
    config: slotConfig.config,
    ...params,
  }
}

// 执行渲染函数
const executeRenderFunction = (slotConfig: any, params: any) => {
  try {
    const renderProps = getRendererProps(slotConfig, params)
    const result = slotConfig.render(renderProps)
    return result || params.value || ''
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('[SimpleDataGrid] 渲染函数执行失败:', error)
    }
    return params.value || ''
  }
}

// 监听视窗尺寸变化
const updateViewportHeight = () => {
  viewportHeight.value = window.innerHeight
}

// 监听容器位置变化
useResizeObserver(containerRef, () => {
  nextTick(() => {
    if (containerRef.value) {
      const rect = containerRef.value.getBoundingClientRect()
      containerOffsetTop.value = rect.top
    }
  })
})

// 生命周期钩子
onMounted(() => {
  window.addEventListener('resize', updateViewportHeight)
  window.addEventListener('scroll', updateViewportHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateViewportHeight)
  window.removeEventListener('scroll', updateViewportHeight)
})

// 暴露方法给父组件
defineExpose({
  getGridInstance: () => gridRef.value,
  getCheckboxRecords: () => gridRef.value?.getCheckboxRecords(),
  getRadioRecord: () => gridRef.value?.getRadioRecord(),
  getCurrentRecord: () => gridRef.value?.getCurrentRecord(),
  setCheckboxRow: (rows: any, checked: boolean) => gridRef.value?.setCheckboxRow(rows, checked),
  setAllCheckboxRow: (checked: boolean) => gridRef.value?.setAllCheckboxRow(checked),
  clearCheckboxRow: () => gridRef.value?.clearCheckboxRow(),
  clearCurrentRow: () => gridRef.value?.clearCurrentRow(),
  refreshData: () => {
    if (gridRef.value) {
      const currentData = gridRef.value.getData()
      if (currentData) {
        gridRef.value.setData(currentData)
      }
    }
  },
})
</script>

<style scoped>
/* DataGrid Footer 统计行样式 */
:deep(.vxe-table .vxe-table--footer-wrapper) {
  .vxe-footer--row {
    background-color: #fafafa;
    border-top: 2px solid #e8e8e8;
    font-weight: 500;

    .vxe-footer--column {
      color: #333;

      &:first-child {
        font-weight: bold;
      }
    }
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  :deep(.vxe-table .vxe-table--footer-wrapper) {
    .vxe-footer--row {
      font-size: 12px;
    }
  }
}
</style>
