import { ref, computed, reactive, type Ref } from 'vue'
import type {
  SimpleColumnConfig,
  SimpleDataGridProps,
  SimpleToolbarConfig,
  SimpleLoadingConfig,
} from '../types/simple'

/**
 * 简化的 DataGrid 状态管理
 */
export interface SimpleDataGridState {
  /** 数据 */
  data: any[]
  /** 加载状态 */
  loading: boolean
  /** 选中的行 */
  selectedRows: any[]
  /** 当前页码 */
  currentPage: number
  /** 每页大小 */
  pageSize: number
  /** 总数 */
  total: number
  /** 排序配置 */
  sortConfig: {
    field?: string
    order?: 'asc' | 'desc'
  }
  /** 筛选配置 */
  filterConfig: Record<string, any>
}

/**
 * 简化的 DataGrid 操作方法
 */
export interface SimpleDataGridActions {
  /** 设置数据 */
  setData: (data: any[]) => void
  /** 添加数据 */
  addData: (data: any | any[]) => void
  /** 更新数据 */
  updateData: (data: any, index?: number) => void
  /** 删除数据 */
  removeData: (index: number | any) => void
  /** 清空数据 */
  clearData: () => void
  /** 设置加载状态 */
  setLoading: (loading: boolean) => void
  /** 刷新数据 */
  refresh: () => Promise<void>
  /** 获取选中行 */
  getSelectedRows: () => any[]
  /** 设置选中行 */
  setSelectedRows: (rows: any[]) => void
  /** 清空选中 */
  clearSelection: () => void
  /** 设置分页 */
  setPagination: (page: number, size?: number) => void
  /** 设置排序 */
  setSort: (field: string, order: 'asc' | 'desc') => void
  /** 设置筛选 */
  setFilter: (field: string, value: any) => void
  /** 重置筛选 */
  resetFilter: () => void
}

/**
 * 简化的 DataGrid 配置
 */
export interface SimpleDataGridConfig {
  /** 列配置 */
  columns: SimpleColumnConfig[]
  /** 初始数据 */
  initialData?: any[]
  /** 工具栏配置 */
  toolbar?: SimpleToolbarConfig
  /** 加载配置 */
  loading?: SimpleLoadingConfig
  /** 分页配置 */
  pagination?: {
    enabled?: boolean
    pageSize?: number
    pageSizes?: number[]
  }
  /** 数据加载函数 */
  loadData?: (params: {
    page: number
    size: number
    sort?: { field: string; order: 'asc' | 'desc' }
    filter?: Record<string, any>
  }) => Promise<{ data: any[]; total: number }>
  /** 其他 VxeGrid 配置 */
  gridOptions?: any
}

/**
 * 简化的 DataGrid 返回值
 */
export interface SimpleDataGridReturn {
  /** 状态 */
  state: SimpleDataGridState
  /** 操作方法 */
  actions: SimpleDataGridActions
  /** 计算属性 */
  computed: {
    /** DataGrid 属性 */
    dataGridProps: Ref<SimpleDataGridProps>
    /** 是否有数据 */
    hasData: Ref<boolean>
    /** 是否有选中行 */
    hasSelection: Ref<boolean>
    /** 分页信息 */
    paginationInfo: Ref<{
      current: number
      size: number
      total: number
      pages: number
    }>
  }
}

/**
 * 简化的 useDataGrid composable
 */
export function useSimpleDataGrid(config: SimpleDataGridConfig): SimpleDataGridReturn {
  // 响应式状态
  const state = reactive<SimpleDataGridState>({
    data: config.initialData || [],
    loading: false,
    selectedRows: [],
    currentPage: 1,
    pageSize: config.pagination?.pageSize || 20,
    total: 0,
    sortConfig: {},
    filterConfig: {},
  })

  // 操作方法
  const actions: SimpleDataGridActions = {
    setData: (data: any[]) => {
      state.data = data
      state.total = data.length
    },

    addData: (data: any | any[]) => {
      if (Array.isArray(data)) {
        state.data.push(...data)
      } else {
        state.data.push(data)
      }
      state.total = state.data.length
    },

    updateData: (data: any, index?: number) => {
      if (typeof index === 'number') {
        if (index >= 0 && index < state.data.length) {
          state.data[index] = { ...state.data[index], ...data }
        }
      } else {
        // 根据 ID 或其他唯一标识更新
        const id = data.id
        if (id !== undefined) {
          const targetIndex = state.data.findIndex(item => item.id === id)
          if (targetIndex !== -1) {
            state.data[targetIndex] = { ...state.data[targetIndex], ...data }
          }
        }
      }
    },

    removeData: (index: number | any) => {
      if (typeof index === 'number') {
        if (index >= 0 && index < state.data.length) {
          state.data.splice(index, 1)
        }
      } else {
        // 根据 ID 或其他唯一标识删除
        const id = index.id || index
        const targetIndex = state.data.findIndex(item => item.id === id)
        if (targetIndex !== -1) {
          state.data.splice(targetIndex, 1)
        }
      }
      state.total = state.data.length
    },

    clearData: () => {
      state.data = []
      state.total = 0
    },

    setLoading: (loading: boolean) => {
      state.loading = loading
    },

    refresh: async () => {
      if (config.loadData) {
        state.loading = true
        try {
          const result = await config.loadData({
            page: state.currentPage,
            size: state.pageSize,
            sort: state.sortConfig.field ? state.sortConfig : undefined,
            filter: Object.keys(state.filterConfig).length > 0 ? state.filterConfig : undefined,
          })
          state.data = result.data
          state.total = result.total
        } catch (error) {
          console.error('数据加载失败:', error)
        } finally {
          state.loading = false
        }
      }
    },

    getSelectedRows: () => state.selectedRows,

    setSelectedRows: (rows: any[]) => {
      state.selectedRows = rows
    },

    clearSelection: () => {
      state.selectedRows = []
    },

    setPagination: (page: number, size?: number) => {
      state.currentPage = page
      if (size) {
        state.pageSize = size
      }
      if (config.loadData) {
        actions.refresh()
      }
    },

    setSort: (field: string, order: 'asc' | 'desc') => {
      state.sortConfig = { field, order }
      if (config.loadData) {
        actions.refresh()
      }
    },

    setFilter: (field: string, value: any) => {
      if (value === null || value === undefined || value === '') {
        delete state.filterConfig[field]
      } else {
        state.filterConfig[field] = value
      }
      if (config.loadData) {
        actions.refresh()
      }
    },

    resetFilter: () => {
      state.filterConfig = {}
      if (config.loadData) {
        actions.refresh()
      }
    },
  }

  // 计算属性
  const dataGridProps = computed<SimpleDataGridProps>(() => ({
    columns: config.columns,
    data: state.data,
    loading: state.loading,
    toolbarOptions: config.toolbar,
    loadingConfig: config.loading,
    gridOptions: {
      ...config.gridOptions,
      // 分页配置
      ...(config.pagination?.enabled && {
        pagerConfig: {
          enabled: true,
          currentPage: state.currentPage,
          pageSize: state.pageSize,
          total: state.total,
          pageSizes: config.pagination.pageSizes || [10, 20, 50, 100],
        },
      }),
      // 排序配置
      sortConfig: state.sortConfig,
      // 筛选配置
      filterConfig: state.filterConfig,
    },
    gridEvents: {
      // 分页事件
      'page-change': ({ currentPage, pageSize }: any) => {
        actions.setPagination(currentPage, pageSize)
      },
      // 排序事件
      'sort-change': ({ field, order }: any) => {
        actions.setSort(field, order)
      },
      // 选择事件
      'checkbox-change': ({ records }: any) => {
        actions.setSelectedRows(records)
      },
      'checkbox-all': ({ records }: any) => {
        actions.setSelectedRows(records)
      },
      'radio-change': ({ record }: any) => {
        actions.setSelectedRows(record ? [record] : [])
      },
    },
  }))

  const hasData = computed(() => state.data.length > 0)
  const hasSelection = computed(() => state.selectedRows.length > 0)
  const paginationInfo = computed(() => ({
    current: state.currentPage,
    size: state.pageSize,
    total: state.total,
    pages: Math.ceil(state.total / state.pageSize),
  }))

  return {
    state,
    actions,
    computed: {
      dataGridProps,
      hasData,
      hasSelection,
      paginationInfo,
    },
  }
}

/**
 * 创建简单的静态数据表格
 */
export function createStaticDataGrid(
  columns: SimpleColumnConfig[],
  data: any[],
  options?: Partial<SimpleDataGridConfig>
): SimpleDataGridReturn {
  return useSimpleDataGrid({
    columns,
    initialData: data,
    ...options,
  })
}

/**
 * 创建带数据加载的动态表格
 */
export function createDynamicDataGrid(
  columns: SimpleColumnConfig[],
  loadData: SimpleDataGridConfig['loadData'],
  options?: Partial<SimpleDataGridConfig>
): SimpleDataGridReturn {
  const grid = useSimpleDataGrid({
    columns,
    loadData,
    pagination: { enabled: true },
    ...options,
  })

  // 初始加载数据
  grid.actions.refresh()

  return grid
}
