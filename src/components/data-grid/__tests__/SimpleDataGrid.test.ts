import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import SimpleDataGrid from '../core/SimpleDataGrid.vue'
import { column, statusMaps } from '../simple'
import type { SimpleColumnConfig } from '../types/simple'

// Mock VxeGrid
vi.mock('vxe-table', () => ({
  VxeGrid: {
    name: 'VxeGrid',
    template: '<div class="mock-vxe-grid"><slot /></div>',
    props: ['columns', 'data', 'height'],
    emits: ['page-change', 'sort-change', 'checkbox-change']
  }
}))

describe('SimpleDataGrid', () => {
  const mockData = [
    { id: 1, name: '张三', age: 25, status: 1, email: '<EMAIL>' },
    { id: 2, name: '李四', age: 30, status: 0, email: '<EMAIL>' },
    { id: 3, name: '王五', age: 28, status: 1, email: '<EMAIL>' }
  ]

  const basicColumns: SimpleColumnConfig[] = [
    column.seq(),
    column.basic('name', '姓名', 120),
    column.basic('age', '年龄', 80),
    column.basic('email', '邮箱', 200),
    column.status('status', '状态', { statusMap: statusMaps.enabledDisabled })
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基础功能', () => {
    it('应该正确渲染基础表格', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData
        }
      })

      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.mock-vxe-grid').exists()).toBe(true)
    })

    it('应该正确传递列配置', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData
        }
      })

      const vxeGrid = wrapper.findComponent({ name: 'VxeGrid' })
      expect(vxeGrid.props('columns')).toEqual(basicColumns)
    })

    it('应该正确传递数据', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData
        }
      })

      const vxeGrid = wrapper.findComponent({ name: 'VxeGrid' })
      expect(vxeGrid.props('data')).toEqual(mockData)
    })
  })

  describe('渲染器功能', () => {
    it('应该正确处理状态渲染器', () => {
      const columns = [
        column.status('status', '状态', {
          statusMap: {
            1: { text: '启用', type: 'success' },
            0: { text: '禁用', type: 'danger' }
          }
        })
      ]

      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns,
          data: mockData
        }
      })

      expect(wrapper.exists()).toBe(true)
    })

    it('应该正确处理操作渲染器', () => {
      const mockEdit = vi.fn()
      const mockDelete = vi.fn()

      const columns = [
        column.actions('操作', {
          actions: [
            { text: '编辑', type: 'primary', onClick: mockEdit },
            { text: '删除', type: 'danger', onClick: mockDelete }
          ]
        })
      ]

      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns,
          data: mockData
        }
      })

      expect(wrapper.exists()).toBe(true)
    })

    it('应该正确处理自定义渲染函数', () => {
      const customRenderer = vi.fn((params) => `自定义: ${params.value}`)

      const columns = [
        column.custom('name', '姓名', customRenderer)
      ]

      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns,
          data: mockData
        }
      })

      expect(wrapper.exists()).toBe(true)
    })
  })

  describe('事件处理', () => {
    it('应该正确处理分页事件', async () => {
      const mockPageChange = vi.fn()

      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData,
          gridEvents: {
            'page-change': mockPageChange
          }
        }
      })

      const vxeGrid = wrapper.findComponent({ name: 'VxeGrid' })
      await vxeGrid.vm.$emit('page-change', { currentPage: 2, pageSize: 20 })

      expect(mockPageChange).toHaveBeenCalledWith({ currentPage: 2, pageSize: 20 })
    })

    it('应该正确处理排序事件', async () => {
      const mockSortChange = vi.fn()

      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData,
          gridEvents: {
            'sort-change': mockSortChange
          }
        }
      })

      const vxeGrid = wrapper.findComponent({ name: 'VxeGrid' })
      await vxeGrid.vm.$emit('sort-change', { field: 'name', order: 'asc' })

      expect(mockSortChange).toHaveBeenCalledWith({ field: 'name', order: 'asc' })
    })
  })

  describe('加载状态', () => {
    it('应该正确显示加载状态', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData,
          loading: true
        }
      })

      expect(wrapper.find('[data-testid="loading"]').exists()).toBe(true)
    })

    it('应该正确隐藏加载状态', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData,
          loading: false
        }
      })

      expect(wrapper.find('[data-testid="loading"]').exists()).toBe(false)
    })
  })

  describe('高度计算', () => {
    it('应该正确处理固定高度', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData,
          height: 500
        }
      })

      const container = wrapper.find('.flex.flex-col')
      expect(container.attributes('style')).toContain('height: 500px')
    })

    it('应该正确处理自动高度', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData,
          height: 'auto'
        }
      })

      expect(wrapper.exists()).toBe(true)
    })
  })

  describe('工具栏', () => {
    it('应该正确显示工具栏', () => {
      const toolbarOptions = {
        title: '用户列表',
        total: mockData.length,
        refresh: true,
        export: true
      }

      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData,
          toolbarOptions
        }
      })

      expect(wrapper.findComponent({ name: 'DGToolbar' }).exists()).toBe(true)
    })

    it('应该正确隐藏工具栏', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData
        }
      })

      expect(wrapper.findComponent({ name: 'DGToolbar' }).exists()).toBe(false)
    })
  })

  describe('暴露的方法', () => {
    it('应该正确暴露网格实例方法', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: mockData
        }
      })

      const instance = wrapper.vm as any
      expect(typeof instance.getGridInstance).toBe('function')
      expect(typeof instance.getCheckboxRecords).toBe('function')
      expect(typeof instance.refreshData).toBe('function')
    })
  })

  describe('性能优化', () => {
    it('应该正确使用 markRaw 优化渲染器组件', () => {
      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: [column.status('status', '状态')],
          data: mockData
        }
      })

      expect(wrapper.exists()).toBe(true)
    })

    it('应该正确处理大量数据', () => {
      const largeData = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        name: `用户${i + 1}`,
        age: 20 + (i % 50),
        status: i % 2
      }))

      const wrapper = mount(SimpleDataGrid, {
        props: {
          columns: basicColumns,
          data: largeData
        }
      })

      expect(wrapper.exists()).toBe(true)
    })
  })
})
