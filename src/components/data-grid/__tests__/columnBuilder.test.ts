import { describe, it, expect, vi } from 'vitest'
import { ColumnBuilder, createColumn, column, statusMaps } from '../api/columnBuilder'
import type { SimpleColumnConfig } from '../types/simple'

describe('ColumnBuilder', () => {
  describe('基础功能', () => {
    it('应该正确创建基础列', () => {
      const col = new ColumnBuilder('name', '姓名').build()
      
      expect(col).toEqual({
        field: 'name',
        title: '姓名'
      })
    })

    it('应该支持链式调用', () => {
      const col = new ColumnBuilder('name', '姓名')
        .width(120)
        .align('center')
        .sortable(true)
        .build()
      
      expect(col).toEqual({
        field: 'name',
        title: '姓名',
        width: 120,
        align: 'center',
        sortable: true
      })
    })

    it('应该正确设置固定列', () => {
      const leftCol = createColumn('id', 'ID').fixed('left').build()
      const rightCol = createColumn('actions', '操作').fixed('right').build()
      
      expect(leftCol.fixed).toBe('left')
      expect(rightCol.fixed).toBe('right')
    })
  })

  describe('渲染器配置', () => {
    it('应该正确配置状态渲染器', () => {
      const statusConfig = {
        statusMap: {
          1: { text: '启用', type: 'success' },
          0: { text: '禁用', type: 'danger' }
        }
      }

      const col = createColumn('status', '状态')
        .status(statusConfig)
        .build()
      
      expect(col.renderer).toBe('status')
      expect(col.rendererConfig).toEqual(statusConfig)
    })

    it('应该正确配置布尔渲染器', () => {
      const booleanConfig = {
        trueText: '是',
        falseText: '否',
        type: 'switch'
      }

      const col = createColumn('enabled', '启用')
        .boolean(booleanConfig)
        .build()
      
      expect(col.renderer).toBe('boolean')
      expect(col.rendererConfig).toEqual(booleanConfig)
    })

    it('应该正确配置操作渲染器', () => {
      const mockEdit = vi.fn()
      const mockDelete = vi.fn()

      const actionsConfig = {
        actions: [
          { text: '编辑', type: 'primary', onClick: mockEdit },
          { text: '删除', type: 'danger', onClick: mockDelete }
        ]
      }

      const col = createColumn('actions', '操作')
        .actions(actionsConfig)
        .build()
      
      expect(col.renderer).toBe('actions')
      expect(col.rendererConfig).toEqual(actionsConfig)
    })

    it('应该正确配置自定义渲染函数', () => {
      const renderFn = vi.fn((params) => `自定义: ${params.value}`)

      const col = createColumn('custom', '自定义')
        .render(renderFn)
        .build()
      
      expect(col.renderer).toBe(renderFn)
    })
  })

  describe('自定义属性', () => {
    it('应该支持设置自定义属性', () => {
      const col = createColumn('name', '姓名')
        .prop('customProp', 'customValue')
        .prop('anotherProp', 123)
        .build()
      
      expect(col.customProp).toBe('customValue')
      expect(col.anotherProp).toBe(123)
    })
  })
})

describe('column 工厂函数', () => {
  describe('基础列类型', () => {
    it('应该正确创建基础列', () => {
      const col = column.basic('name', '姓名', 120)
      
      expect(col).toEqual({
        field: 'name',
        title: '姓名',
        width: 120
      })
    })

    it('应该正确创建序号列', () => {
      const col = column.seq('序号', 60)
      
      expect(col).toEqual({
        type: 'seq',
        title: '序号',
        width: 60,
        align: 'center',
        fixed: 'left'
      })
    })

    it('应该正确创建复选框列', () => {
      const col = column.checkbox(50)
      
      expect(col).toEqual({
        type: 'checkbox',
        width: 50,
        align: 'center',
        fixed: 'left'
      })
    })

    it('应该正确创建单选框列', () => {
      const col = column.radio(50)
      
      expect(col).toEqual({
        type: 'radio',
        width: 50,
        align: 'center',
        fixed: 'left'
      })
    })
  })

  describe('渲染器列类型', () => {
    it('应该正确创建状态列', () => {
      const config = { statusMap: statusMaps.enabledDisabled }
      const col = column.status('status', '状态', config, 120)
      
      expect(col).toEqual({
        field: 'status',
        title: '状态',
        width: 120,
        align: 'center',
        renderer: 'status',
        rendererConfig: config
      })
    })

    it('应该正确创建布尔列', () => {
      const config = { trueText: '是', falseText: '否' }
      const col = column.boolean('enabled', '启用', config, 100)
      
      expect(col).toEqual({
        field: 'enabled',
        title: '启用',
        width: 100,
        align: 'center',
        renderer: 'boolean',
        rendererConfig: config
      })
    })

    it('应该正确创建链接列', () => {
      const config = { target: '_blank' }
      const col = column.link('url', '链接', config, 150)
      
      expect(col).toEqual({
        field: 'url',
        title: '链接',
        width: 150,
        renderer: 'link',
        rendererConfig: config
      })
    })

    it('应该正确创建操作列', () => {
      const config = {
        actions: [
          { text: '编辑', onClick: vi.fn() }
        ]
      }
      const col = column.actions('操作', config, 150)
      
      expect(col).toEqual({
        field: 'actions',
        title: '操作',
        width: 150,
        align: 'center',
        fixed: 'right',
        renderer: 'actions',
        rendererConfig: config
      })
    })

    it('应该正确创建评分列', () => {
      const config = { max: 5, readonly: true }
      const col = column.rating('rating', '评分', config, 150)
      
      expect(col).toEqual({
        field: 'rating',
        title: '评分',
        width: 150,
        align: 'center',
        renderer: 'rating',
        rendererConfig: config
      })
    })

    it('应该正确创建货币列', () => {
      const config = { currency: 'CNY', precision: 2 }
      const col = column.currency('amount', '金额', config, 120)
      
      expect(col).toEqual({
        field: 'amount',
        title: '金额',
        width: 120,
        align: 'right',
        renderer: 'currency',
        rendererConfig: config
      })
    })

    it('应该正确创建日期列', () => {
      const config = { format: 'YYYY-MM-DD' }
      const col = column.date('createdAt', '创建时间', config, 150)
      
      expect(col).toEqual({
        field: 'createdAt',
        title: '创建时间',
        width: 150,
        align: 'center',
        renderer: 'date',
        rendererConfig: config
      })
    })

    it('应该正确创建图片列', () => {
      const config = { width: 50, height: 50 }
      const col = column.image('avatar', '头像', config, 100)
      
      expect(col).toEqual({
        field: 'avatar',
        title: '头像',
        width: 100,
        align: 'center',
        renderer: 'image',
        rendererConfig: config
      })
    })

    it('应该正确创建复合列', () => {
      const config = {
        main: { field: 'name' },
        subs: [{ field: 'email' }]
      }
      const col = column.composite('user', '用户', config, 200)
      
      expect(col).toEqual({
        field: 'user',
        title: '用户',
        width: 200,
        renderer: 'composite',
        rendererConfig: config
      })
    })

    it('应该正确创建自定义渲染列', () => {
      const renderFn = vi.fn()
      const col = column.custom('custom', '自定义', renderFn, 150)
      
      expect(col).toEqual({
        field: 'custom',
        title: '自定义',
        width: 150,
        renderer: renderFn
      })
    })
  })
})

describe('statusMaps 预设', () => {
  it('应该包含启用/禁用状态映射', () => {
    expect(statusMaps.enabledDisabled).toEqual({
      1: { text: '启用', type: 'success', icon: 'check-circle' },
      0: { text: '禁用', type: 'danger', icon: 'x-circle' },
      true: { text: '启用', type: 'success', icon: 'check-circle' },
      false: { text: '禁用', type: 'danger', icon: 'x-circle' },
      enabled: { text: '启用', type: 'success', icon: 'check-circle' },
      disabled: { text: '禁用', type: 'danger', icon: 'x-circle' }
    })
  })

  it('应该包含活跃/非活跃状态映射', () => {
    expect(statusMaps.activeInactive).toEqual({
      active: { text: '活跃', type: 'success', icon: 'activity' },
      inactive: { text: '非活跃', type: 'warning', icon: 'pause-circle' }
    })
  })

  it('应该包含订单状态映射', () => {
    expect(statusMaps.orderStatus).toEqual({
      pending: { text: '待处理', type: 'info', icon: 'clock' },
      processing: { text: '处理中', type: 'warning', icon: 'loader' },
      completed: { text: '已完成', type: 'success', icon: 'check-circle' },
      cancelled: { text: '已取消', type: 'danger', icon: 'x-circle' }
    })
  })

  it('应该包含支付状态映射', () => {
    expect(statusMaps.paymentStatus).toEqual({
      pending: { text: '待支付', type: 'warning', icon: 'clock' },
      paid: { text: '已支付', type: 'success', icon: 'credit-card' },
      failed: { text: '支付失败', type: 'danger', icon: 'alert-circle' },
      refunded: { text: '已退款', type: 'info', icon: 'rotate-ccw' }
    })
  })
})
