import { describe, it, expect, vi, beforeEach } from 'vitest'
import { nextTick } from 'vue'
import { useSimpleDataGrid, createStaticDataGrid, createDynamicDataGrid } from '../composables/useSimpleDataGrid'
import { column } from '../api/columnBuilder'

describe('useSimpleDataGrid', () => {
  const mockColumns = [
    column.basic('id', 'ID', 80),
    column.basic('name', '姓名', 120),
    column.basic('age', '年龄', 80),
    column.status('status', '状态')
  ]

  const mockData = [
    { id: 1, name: '张三', age: 25, status: 1 },
    { id: 2, name: '李四', age: 30, status: 0 },
    { id: 3, name: '王五', age: 28, status: 1 }
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('基础功能', () => {
    it('应该正确初始化状态', () => {
      const { state } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      expect(state.data).toEqual(mockData)
      expect(state.loading).toBe(false)
      expect(state.selectedRows).toEqual([])
      expect(state.currentPage).toBe(1)
      expect(state.pageSize).toBe(20)
      expect(state.total).toBe(3)
    })

    it('应该正确生成 DataGrid 属性', () => {
      const { computed } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      const props = computed.dataGridProps.value
      expect(props.columns).toEqual(mockColumns)
      expect(props.data).toEqual(mockData)
      expect(props.loading).toBe(false)
    })
  })

  describe('数据操作', () => {
    it('应该正确设置数据', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns
      })

      actions.setData(mockData)
      expect(state.data).toEqual(mockData)
      expect(state.total).toBe(3)
    })

    it('应该正确添加数据', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      const newItem = { id: 4, name: '赵六', age: 32, status: 1 }
      actions.addData(newItem)

      expect(state.data).toHaveLength(4)
      expect(state.data[3]).toEqual(newItem)
      expect(state.total).toBe(4)
    })

    it('应该正确添加多条数据', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      const newItems = [
        { id: 4, name: '赵六', age: 32, status: 1 },
        { id: 5, name: '钱七', age: 29, status: 0 }
      ]
      actions.addData(newItems)

      expect(state.data).toHaveLength(5)
      expect(state.total).toBe(5)
    })

    it('应该正确更新数据（按索引）', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      actions.updateData({ name: '张三三' }, 0)
      expect(state.data[0].name).toBe('张三三')
      expect(state.data[0].id).toBe(1) // 其他字段保持不变
    })

    it('应该正确更新数据（按 ID）', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      actions.updateData({ id: 2, name: '李四四' })
      const updatedItem = state.data.find(item => item.id === 2)
      expect(updatedItem?.name).toBe('李四四')
    })

    it('应该正确删除数据（按索引）', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      actions.removeData(1)
      expect(state.data).toHaveLength(2)
      expect(state.data.find(item => item.id === 2)).toBeUndefined()
      expect(state.total).toBe(2)
    })

    it('应该正确删除数据（按 ID）', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      actions.removeData({ id: 2 })
      expect(state.data).toHaveLength(2)
      expect(state.data.find(item => item.id === 2)).toBeUndefined()
      expect(state.total).toBe(2)
    })

    it('应该正确清空数据', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      actions.clearData()
      expect(state.data).toEqual([])
      expect(state.total).toBe(0)
    })
  })

  describe('加载状态', () => {
    it('应该正确设置加载状态', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns
      })

      actions.setLoading(true)
      expect(state.loading).toBe(true)

      actions.setLoading(false)
      expect(state.loading).toBe(false)
    })
  })

  describe('选择功能', () => {
    it('应该正确设置选中行', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      const selectedRows = [mockData[0], mockData[2]]
      actions.setSelectedRows(selectedRows)
      expect(state.selectedRows).toEqual(selectedRows)
    })

    it('应该正确获取选中行', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      const selectedRows = [mockData[0]]
      actions.setSelectedRows(selectedRows)
      expect(actions.getSelectedRows()).toEqual(selectedRows)
    })

    it('应该正确清空选中', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      actions.setSelectedRows([mockData[0]])
      actions.clearSelection()
      expect(state.selectedRows).toEqual([])
    })
  })

  describe('分页功能', () => {
    it('应该正确设置分页', () => {
      const mockLoadData = vi.fn().mockResolvedValue({
        data: mockData,
        total: 100
      })

      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        loadData: mockLoadData
      })

      actions.setPagination(2, 50)
      expect(state.currentPage).toBe(2)
      expect(state.pageSize).toBe(50)
      expect(mockLoadData).toHaveBeenCalledWith({
        page: 2,
        size: 50,
        sort: undefined,
        filter: undefined
      })
    })
  })

  describe('排序功能', () => {
    it('应该正确设置排序', () => {
      const mockLoadData = vi.fn().mockResolvedValue({
        data: mockData,
        total: 100
      })

      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        loadData: mockLoadData
      })

      actions.setSort('name', 'asc')
      expect(state.sortConfig).toEqual({ field: 'name', order: 'asc' })
      expect(mockLoadData).toHaveBeenCalledWith({
        page: 1,
        size: 20,
        sort: { field: 'name', order: 'asc' },
        filter: undefined
      })
    })
  })

  describe('筛选功能', () => {
    it('应该正确设置筛选', () => {
      const mockLoadData = vi.fn().mockResolvedValue({
        data: mockData,
        total: 100
      })

      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        loadData: mockLoadData
      })

      actions.setFilter('status', 1)
      expect(state.filterConfig).toEqual({ status: 1 })
      expect(mockLoadData).toHaveBeenCalledWith({
        page: 1,
        size: 20,
        sort: undefined,
        filter: { status: 1 }
      })
    })

    it('应该正确重置筛选', () => {
      const mockLoadData = vi.fn().mockResolvedValue({
        data: mockData,
        total: 100
      })

      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        loadData: mockLoadData
      })

      actions.setFilter('status', 1)
      actions.resetFilter()
      expect(state.filterConfig).toEqual({})
    })

    it('应该正确删除空值筛选', () => {
      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns
      })

      actions.setFilter('status', 1)
      actions.setFilter('status', '')
      expect(state.filterConfig).toEqual({})
    })
  })

  describe('数据刷新', () => {
    it('应该正确刷新数据', async () => {
      const mockLoadData = vi.fn().mockResolvedValue({
        data: mockData,
        total: 100
      })

      const { state, actions } = useSimpleDataGrid({
        columns: mockColumns,
        loadData: mockLoadData
      })

      await actions.refresh()
      expect(state.data).toEqual(mockData)
      expect(state.total).toBe(100)
      expect(mockLoadData).toHaveBeenCalledWith({
        page: 1,
        size: 20,
        sort: undefined,
        filter: undefined
      })
    })

    it('应该正确处理刷新错误', async () => {
      const mockLoadData = vi.fn().mockRejectedValue(new Error('加载失败'))
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const { actions } = useSimpleDataGrid({
        columns: mockColumns,
        loadData: mockLoadData
      })

      await actions.refresh()
      expect(consoleSpy).toHaveBeenCalledWith('数据加载失败:', expect.any(Error))
      
      consoleSpy.mockRestore()
    })
  })

  describe('计算属性', () => {
    it('应该正确计算是否有数据', () => {
      const { computed } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      expect(computed.hasData.value).toBe(true)
    })

    it('应该正确计算是否有选中行', () => {
      const { actions, computed } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      expect(computed.hasSelection.value).toBe(false)
      
      actions.setSelectedRows([mockData[0]])
      expect(computed.hasSelection.value).toBe(true)
    })

    it('应该正确计算分页信息', () => {
      const { state, computed } = useSimpleDataGrid({
        columns: mockColumns,
        initialData: mockData
      })

      state.total = 100
      state.pageSize = 20
      state.currentPage = 3

      const paginationInfo = computed.paginationInfo.value
      expect(paginationInfo).toEqual({
        current: 3,
        size: 20,
        total: 100,
        pages: 5
      })
    })
  })
})

describe('createStaticDataGrid', () => {
  it('应该正确创建静态数据表格', () => {
    const grid = createStaticDataGrid(mockColumns, mockData)
    
    expect(grid.state.data).toEqual(mockData)
    expect(grid.state.total).toBe(3)
    expect(grid.computed.hasData.value).toBe(true)
  })
})

describe('createDynamicDataGrid', () => {
  it('应该正确创建动态数据表格', () => {
    const mockLoadData = vi.fn().mockResolvedValue({
      data: mockData,
      total: 100
    })

    const grid = createDynamicDataGrid(mockColumns, mockLoadData)
    
    expect(grid.state.data).toEqual([])
    expect(mockLoadData).toHaveBeenCalled()
  })
})
