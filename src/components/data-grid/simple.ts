/**
 * 简化版 DataGrid - 移除插件系统，回归 VxeTable 原生能力
 * 
 * 这个模块提供了一个简化的 DataGrid 实现，具有以下特点：
 * 1. 移除复杂的插件系统
 * 2. 直接静态导入渲染器组件
 * 3. 使用 VxeTable 原生 slots 机制
 * 4. 提供类型安全的配置 API
 * 5. 简化的状态管理
 */

// 核心组件
export { default as SimpleDataGrid } from './core/SimpleDataGrid.vue'

// 类型定义
export type {
  SimpleDataGridProps,
  SimpleColumnConfig,
  SimpleToolbarConfig,
  SimpleLoadingConfig,
  RendererParams,
  RendererFunction,
  BuiltinRendererType,
  RendererConfig,
  StatusRendererConfig,
  BooleanRendererConfig,
  LinkRendererConfig,
  ActionsRendererConfig,
  RatingRendererConfig,
  CurrencyRendererConfig,
  DateRendererConfig,
  ImageRendererConfig,
  CompositeRendererConfig,
} from './types/simple'

// 列配置 API
export {
  ColumnBuilder,
  createColumn,
  column,
  statusMaps,
} from './api/columnBuilder'

// Composable
export {
  useSimpleDataGrid,
  createStaticDataGrid,
  createDynamicDataGrid,
  type SimpleDataGridReturn,
  type SimpleDataGridConfig,
  type SimpleDataGridState,
  type SimpleDataGridActions,
} from './composables/useSimpleDataGrid'

// 渲染器组件（可选导出，用于高级自定义）
export { default as StatusRenderer } from './renderers/status/renderer.vue'
export { default as BooleanRenderer } from './renderers/boolean/renderer.vue'
export { default as LinkRenderer } from './renderers/link/renderer.vue'
export { default as ActionsRenderer } from './renderers/actions/renderer.vue'
export { default as RatingRenderer } from './renderers/rating/renderer.vue'
export { default as CompositeRenderer } from './renderers/composite/renderer.vue'
export { default as CurrencyRenderer } from './renderers/currency/renderer.vue'
export { default as DateRenderer } from './renderers/date/renderer.vue'
export { default as ImageRenderer } from './renderers/image/renderer.vue'

// 渲染器工厂函数
export const renderers = {
  /**
   * 创建状态渲染器
   */
  status: (config?: StatusRendererConfig) => ({
    renderer: 'status' as const,
    rendererConfig: config,
  }),

  /**
   * 创建布尔渲染器
   */
  boolean: (config?: BooleanRendererConfig) => ({
    renderer: 'boolean' as const,
    rendererConfig: config,
  }),

  /**
   * 创建链接渲染器
   */
  link: (config?: LinkRendererConfig) => ({
    renderer: 'link' as const,
    rendererConfig: config,
  }),

  /**
   * 创建操作渲染器
   */
  actions: (config: ActionsRendererConfig) => ({
    renderer: 'actions' as const,
    rendererConfig: config,
  }),

  /**
   * 创建评分渲染器
   */
  rating: (config?: RatingRendererConfig) => ({
    renderer: 'rating' as const,
    rendererConfig: config,
  }),

  /**
   * 创建货币渲染器
   */
  currency: (config?: CurrencyRendererConfig) => ({
    renderer: 'currency' as const,
    rendererConfig: config,
  }),

  /**
   * 创建日期渲染器
   */
  date: (config?: DateRendererConfig) => ({
    renderer: 'date' as const,
    rendererConfig: config,
  }),

  /**
   * 创建图片渲染器
   */
  image: (config?: ImageRendererConfig) => ({
    renderer: 'image' as const,
    rendererConfig: config,
  }),

  /**
   * 创建复合渲染器
   */
  composite: (config: CompositeRendererConfig) => ({
    renderer: 'composite' as const,
    rendererConfig: config,
  }),

  /**
   * 创建自定义渲染器
   */
  custom: (renderFn: RendererFunction) => ({
    renderer: renderFn,
  }),
}

// 便利函数
export const utils = {
  /**
   * 创建快速状态映射
   */
  createStatusMap: (
    mappings: Record<string | number, { text: string; type?: string; icon?: string }>
  ) => mappings,

  /**
   * 创建操作按钮配置
   */
  createActions: (
    actions: Array<{
      text?: string
      icon?: string
      type?: string
      onClick?: (params: RendererParams) => void
      disabled?: boolean | ((params: RendererParams) => boolean)
      visible?: boolean | ((params: RendererParams) => boolean)
    }>
  ): ActionsRendererConfig => ({ actions }),

  /**
   * 创建复合列配置
   */
  createComposite: (config: {
    main: { field: string; formatter?: (value: any, row: any) => string }
    subs?: Array<{
      field: string
      template?: string
      condition?: (row: any) => boolean
    }>
    icon?: {
      type: 'avatar' | 'icon'
      avatarField?: string
      imageField?: string
      iconField?: string
      size?: number
    }
    actions?: Array<{
      icon: string
      tooltip?: string
      onClick?: (row: any) => void
    }>
  }): CompositeRendererConfig => config,
}

// 预设配置
export const presets = {
  /**
   * 用户信息列预设
   */
  userColumn: (
    nameField: string = 'name',
    avatarField: string = 'avatar',
    emailField?: string
  ) => column.composite(nameField, '用户', {
    main: { field: nameField },
    subs: emailField ? [{ field: emailField }] : [],
    icon: {
      type: 'avatar',
      avatarField: nameField,
      imageField: avatarField,
      size: 40,
    },
  }),

  /**
   * 状态列预设
   */
  statusColumn: (field: string, title: string, type: 'enable' | 'order' | 'payment' = 'enable') => {
    const statusMap = type === 'enable' ? statusMaps.enabledDisabled :
                     type === 'order' ? statusMaps.orderStatus :
                     statusMaps.paymentStatus
    
    return column.status(field, title, { statusMap })
  },

  /**
   * 操作列预设
   */
  actionsColumn: (
    actions: Array<{
      text: string
      type?: string
      onClick: (params: RendererParams) => void
    }>
  ) => column.actions('操作', { actions }),

  /**
   * 时间列预设
   */
  timeColumn: (field: string, title: string, relative: boolean = false) =>
    column.date(field, title, { relative }),

  /**
   * 金额列预设
   */
  moneyColumn: (field: string, title: string, currency: string = 'CNY') =>
    column.currency(field, title, { currency }),
}

/**
 * 默认导出 - 提供最常用的 API
 */
export default {
  // 核心组件
  SimpleDataGrid,
  
  // 主要 API
  useSimpleDataGrid,
  createStaticDataGrid,
  createDynamicDataGrid,
  column,
  renderers,
  
  // 预设
  presets,
  statusMaps,
  
  // 工具函数
  utils,
}
