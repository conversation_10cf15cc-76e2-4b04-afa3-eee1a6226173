<template>
  <div class="p-6 space-y-6">
    <div class="text-2xl font-bold">简化版 DataGrid 使用示例</div>
    
    <!-- 基础用法 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">基础用法</h3>
      <SimpleDataGrid v-bind="basicGrid.computed.dataGridProps.value" />
    </div>

    <!-- 带操作的表格 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">带操作的表格</h3>
      <SimpleDataGrid v-bind="actionGrid.computed.dataGridProps.value" />
    </div>

    <!-- 复杂渲染器示例 -->
    <div class="space-y-4">
      <h3 class="text-lg font-semibold">复杂渲染器示例</h3>
      <SimpleDataGrid v-bind="complexGrid.computed.dataGridProps.value" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { SimpleDataGrid, column, statusMaps, presets, createStaticDataGrid } from '../simple'

// 基础示例数据
const basicData = [
  { id: 1, name: '张三', age: 25, email: '<EMAIL>', status: 1 },
  { id: 2, name: '李四', age: 30, email: '<EMAIL>', status: 0 },
  { id: 3, name: '王五', age: 28, email: '<EMAIL>', status: 1 },
]

// 基础表格配置
const basicGrid = createStaticDataGrid([
  column.seq(),
  column.basic('name', '姓名', 120),
  column.basic('age', '年龄', 80),
  column.basic('email', '邮箱', 200),
  column.status('status', '状态', { statusMap: statusMaps.enabledDisabled }),
], basicData)

// 带操作的表格数据
const actionData = [
  { id: 1, name: '产品A', price: 99.99, rating: 4.5, published: true },
  { id: 2, name: '产品B', price: 149.99, rating: 4.2, published: false },
  { id: 3, name: '产品C', price: 79.99, rating: 4.8, published: true },
]

// 带操作的表格配置
const actionGrid = createStaticDataGrid([
  column.checkbox(),
  column.basic('name', '产品名称', 150),
  column.currency('price', '价格', { currency: 'CNY' }),
  column.rating('rating', '评分'),
  column.boolean('published', '已发布', {
    trueText: '已发布',
    falseText: '未发布',
    type: 'text',
  }),
  column.actions('操作', {
    actions: [
      {
        text: '编辑',
        type: 'primary',
        onClick: (params) => {
          console.log('编辑', params.row)
        },
      },
      {
        text: '删除',
        type: 'danger',
        onClick: (params) => {
          console.log('删除', params.row)
        },
      },
    ],
  }),
], actionData)

// 复杂渲染器示例数据
const complexData = [
  {
    id: 1,
    user: {
      name: '张三',
      avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
      email: '<EMAIL>',
    },
    order: {
      id: 'ORD001',
      status: 'completed',
      amount: 299.99,
      date: '2024-01-15T10:30:00Z',
    },
    tags: ['VIP', '新用户'],
  },
  {
    id: 2,
    user: {
      name: '李四',
      avatar: 'https://avatars.githubusercontent.com/u/2?v=4',
      email: '<EMAIL>',
    },
    order: {
      id: 'ORD002',
      status: 'pending',
      amount: 199.99,
      date: '2024-01-16T14:20:00Z',
    },
    tags: ['老用户'],
  },
]

// 复杂表格配置
const complexGrid = createStaticDataGrid([
  column.seq(),
  column.composite('user', '用户信息', {
    main: { field: 'user.name' },
    subs: [{ field: 'user.email' }],
    icon: {
      type: 'avatar',
      imageField: 'user.avatar',
      size: 40,
    },
    actions: [
      {
        icon: 'message-circle',
        tooltip: '发送消息',
        onClick: (row) => console.log('发送消息给', row.user.name),
      },
      {
        icon: 'user',
        tooltip: '查看详情',
        onClick: (row) => console.log('查看用户详情', row.user),
      },
    ],
  }, 200),
  column.basic('order.id', '订单号', 120),
  column.status('order.status', '订单状态', { statusMap: statusMaps.orderStatus }),
  column.currency('order.amount', '订单金额', { currency: 'CNY' }),
  column.date('order.date', '下单时间', { format: 'YYYY-MM-DD HH:mm' }),
  column.custom('tags', '标签', (params) => {
    const tags = params.value || []
    return tags.map((tag: string) => 
      `<span class="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full mr-1">${tag}</span>`
    ).join('')
  }, 150),
  column.actions('操作', {
    actions: [
      {
        text: '查看',
        type: 'primary',
        onClick: (params) => console.log('查看订单', params.row.order),
      },
      {
        text: '编辑',
        type: 'default',
        onClick: (params) => console.log('编辑订单', params.row.order),
        visible: (params) => params.row.order.status === 'pending',
      },
      {
        text: '取消',
        type: 'danger',
        onClick: (params) => console.log('取消订单', params.row.order),
        visible: (params) => params.row.order.status === 'pending',
      },
    ],
    maxVisible: 2,
  }),
], complexData)
</script>

<style scoped>
/* 示例页面样式 */
</style>
