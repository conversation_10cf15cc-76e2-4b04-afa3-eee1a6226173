import type {
  SimpleColumnConfig,
  StatusRendererConfig,
  BooleanRendererConfig,
  LinkRendererConfig,
  ActionsRendererConfig,
  RatingRendererConfig,
  CurrencyRendererConfig,
  DateRendererConfig,
  ImageRendererConfig,
  CompositeRendererConfig,
  RendererFunction,
} from '../types/simple'

/**
 * 列构建器类 - 提供链式 API 来构建列配置
 */
export class ColumnBuilder {
  private config: SimpleColumnConfig

  constructor(field: string, title: string) {
    this.config = {
      field,
      title,
    }
  }

  /**
   * 设置列宽度
   */
  width(width: number | string): this {
    this.config.width = width
    return this
  }

  /**
   * 设置最小宽度
   */
  minWidth(minWidth: number | string): this {
    this.config.minWidth = minWidth
    return this
  }

  /**
   * 设置最大宽度
   */
  maxWidth(maxWidth: number | string): this {
    this.config.maxWidth = maxWidth
    return this
  }

  /**
   * 设置固定列
   */
  fixed(position: 'left' | 'right'): this {
    this.config.fixed = position
    return this
  }

  /**
   * 设置对齐方式
   */
  align(align: 'left' | 'center' | 'right'): this {
    this.config.align = align
    return this
  }

  /**
   * 设置可排序
   */
  sortable(sortable: boolean = true): this {
    this.config.sortable = sortable
    return this
  }

  /**
   * 设置可筛选
   */
  filterable(filterable: boolean = true): this {
    this.config.filterable = filterable
    return this
  }

  /**
   * 设置状态渲染器
   */
  status(config?: StatusRendererConfig): this {
    this.config.renderer = 'status'
    this.config.rendererConfig = config
    return this
  }

  /**
   * 设置布尔渲染器
   */
  boolean(config?: BooleanRendererConfig): this {
    this.config.renderer = 'boolean'
    this.config.rendererConfig = config
    return this
  }

  /**
   * 设置链接渲染器
   */
  link(config?: LinkRendererConfig): this {
    this.config.renderer = 'link'
    this.config.rendererConfig = config
    return this
  }

  /**
   * 设置操作渲染器
   */
  actions(config: ActionsRendererConfig): this {
    this.config.renderer = 'actions'
    this.config.rendererConfig = config
    return this
  }

  /**
   * 设置评分渲染器
   */
  rating(config?: RatingRendererConfig): this {
    this.config.renderer = 'rating'
    this.config.rendererConfig = config
    return this
  }

  /**
   * 设置货币渲染器
   */
  currency(config?: CurrencyRendererConfig): this {
    this.config.renderer = 'currency'
    this.config.rendererConfig = config
    return this
  }

  /**
   * 设置日期渲染器
   */
  date(config?: DateRendererConfig): this {
    this.config.renderer = 'date'
    this.config.rendererConfig = config
    return this
  }

  /**
   * 设置图片渲染器
   */
  image(config?: ImageRendererConfig): this {
    this.config.renderer = 'image'
    this.config.rendererConfig = config
    return this
  }

  /**
   * 设置复合渲染器
   */
  composite(config: CompositeRendererConfig): this {
    this.config.renderer = 'composite'
    this.config.rendererConfig = config
    return this
  }

  /**
   * 设置自定义渲染函数
   */
  render(renderFn: RendererFunction): this {
    this.config.renderer = renderFn
    return this
  }

  /**
   * 设置自定义属性
   */
  prop(key: string, value: any): this {
    this.config[key] = value
    return this
  }

  /**
   * 构建列配置
   */
  build(): SimpleColumnConfig {
    return { ...this.config }
  }
}

/**
 * 列配置工厂函数
 */
export function createColumn(field: string, title: string): ColumnBuilder {
  return new ColumnBuilder(field, title)
}

/**
 * 快速创建常用列的工厂函数
 */
export const column = {
  /**
   * 创建基础列
   */
  basic: (field: string, title: string, width?: number | string): SimpleColumnConfig => {
    const builder = createColumn(field, title)
    if (width) builder.width(width)
    return builder.build()
  },

  /**
   * 创建序号列
   */
  seq: (title: string = '序号', width: number = 60): SimpleColumnConfig => ({
    type: 'seq',
    title,
    width,
    align: 'center',
    fixed: 'left',
  }),

  /**
   * 创建复选框列
   */
  checkbox: (width: number = 50): SimpleColumnConfig => ({
    type: 'checkbox',
    width,
    align: 'center',
    fixed: 'left',
  }),

  /**
   * 创建单选框列
   */
  radio: (width: number = 50): SimpleColumnConfig => ({
    type: 'radio',
    width,
    align: 'center',
    fixed: 'left',
  }),

  /**
   * 创建状态列
   */
  status: (
    field: string,
    title: string,
    config?: StatusRendererConfig,
    width: number = 120
  ): SimpleColumnConfig => ({
    field,
    title,
    width,
    align: 'center',
    renderer: 'status',
    rendererConfig: config,
  }),

  /**
   * 创建布尔列
   */
  boolean: (
    field: string,
    title: string,
    config?: BooleanRendererConfig,
    width: number = 100
  ): SimpleColumnConfig => ({
    field,
    title,
    width,
    align: 'center',
    renderer: 'boolean',
    rendererConfig: config,
  }),

  /**
   * 创建链接列
   */
  link: (
    field: string,
    title: string,
    config?: LinkRendererConfig,
    width: number = 150
  ): SimpleColumnConfig => ({
    field,
    title,
    width,
    renderer: 'link',
    rendererConfig: config,
  }),

  /**
   * 创建操作列
   */
  actions: (
    title: string,
    config: ActionsRendererConfig,
    width: number = 150
  ): SimpleColumnConfig => ({
    field: 'actions',
    title,
    width,
    align: 'center',
    fixed: 'right',
    renderer: 'actions',
    rendererConfig: config,
  }),

  /**
   * 创建评分列
   */
  rating: (
    field: string,
    title: string,
    config?: RatingRendererConfig,
    width: number = 150
  ): SimpleColumnConfig => ({
    field,
    title,
    width,
    align: 'center',
    renderer: 'rating',
    rendererConfig: config,
  }),

  /**
   * 创建货币列
   */
  currency: (
    field: string,
    title: string,
    config?: CurrencyRendererConfig,
    width: number = 120
  ): SimpleColumnConfig => ({
    field,
    title,
    width,
    align: 'right',
    renderer: 'currency',
    rendererConfig: config,
  }),

  /**
   * 创建日期列
   */
  date: (
    field: string,
    title: string,
    config?: DateRendererConfig,
    width: number = 150
  ): SimpleColumnConfig => ({
    field,
    title,
    width,
    align: 'center',
    renderer: 'date',
    rendererConfig: config,
  }),

  /**
   * 创建图片列
   */
  image: (
    field: string,
    title: string,
    config?: ImageRendererConfig,
    width: number = 100
  ): SimpleColumnConfig => ({
    field,
    title,
    width,
    align: 'center',
    renderer: 'image',
    rendererConfig: config,
  }),

  /**
   * 创建复合列
   */
  composite: (
    field: string,
    title: string,
    config: CompositeRendererConfig,
    width: number = 200
  ): SimpleColumnConfig => ({
    field,
    title,
    width,
    renderer: 'composite',
    rendererConfig: config,
  }),

  /**
   * 创建自定义渲染列
   */
  custom: (
    field: string,
    title: string,
    renderFn: RendererFunction,
    width?: number
  ): SimpleColumnConfig => ({
    field,
    title,
    width,
    renderer: renderFn,
  }),
}

/**
 * 预设状态映射
 */
export const statusMaps = {
  /**
   * 启用/禁用状态
   */
  enabledDisabled: {
    1: { text: '启用', type: 'success' as const, icon: 'check-circle' },
    0: { text: '禁用', type: 'danger' as const, icon: 'x-circle' },
    true: { text: '启用', type: 'success' as const, icon: 'check-circle' },
    false: { text: '禁用', type: 'danger' as const, icon: 'x-circle' },
    enabled: { text: '启用', type: 'success' as const, icon: 'check-circle' },
    disabled: { text: '禁用', type: 'danger' as const, icon: 'x-circle' },
  },

  /**
   * 活跃/非活跃状态
   */
  activeInactive: {
    active: { text: '活跃', type: 'success' as const, icon: 'activity' },
    inactive: { text: '非活跃', type: 'warning' as const, icon: 'pause-circle' },
  },

  /**
   * 订单状态
   */
  orderStatus: {
    pending: { text: '待处理', type: 'info' as const, icon: 'clock' },
    processing: { text: '处理中', type: 'warning' as const, icon: 'loader' },
    completed: { text: '已完成', type: 'success' as const, icon: 'check-circle' },
    cancelled: { text: '已取消', type: 'danger' as const, icon: 'x-circle' },
  },

  /**
   * 支付状态
   */
  paymentStatus: {
    pending: { text: '待支付', type: 'warning' as const, icon: 'clock' },
    paid: { text: '已支付', type: 'success' as const, icon: 'credit-card' },
    failed: { text: '支付失败', type: 'danger' as const, icon: 'alert-circle' },
    refunded: { text: '已退款', type: 'info' as const, icon: 'rotate-ccw' },
  },
}
