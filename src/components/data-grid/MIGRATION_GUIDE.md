# DataGrid 重构迁移指南

## 📋 概述

本指南将帮助您从复杂的插件系统迁移到简化的 DataGrid 架构。新架构移除了所有插件相关的抽象层，直接使用 VxeTable 的原生能力和静态导入的渲染器。

## 🎯 重构目标

- ✅ **移除插件系统**：不再使用 `GlobalManager`、`ModernPluginManager` 等
- ✅ **静态导入渲染器**：直接从 `renderers/` 目录导入组件
- ✅ **使用 VxeTable 原生 slots**：充分利用 VxeTable 的插槽机制
- ✅ **简化配置 API**：提供直观的列配置方式
- ✅ **类型安全**：完整的 TypeScript 支持
- ✅ **性能优化**：减少运行时开销

## 🔄 迁移步骤

### 第一步：更新导入语句

**旧的导入方式：**
```typescript
import DataGrid from '@/components/data-grid/core/DataGrid.vue'
import { useDataGrid } from '@/components/data-grid/composables/useDataGrid'
```

**新的导入方式：**
```typescript
import { SimpleDataGrid, useSimpleDataGrid, column } from '@/components/data-grid/simple'
```

### 第二步：更新列配置

**旧的列配置：**
```typescript
const columns = [
  {
    field: 'status',
    title: '状态',
    width: 120,
    slots: {
      default: 'status_renderer'
    }
  }
]
```

**新的列配置：**
```typescript
const columns = [
  column.status('status', '状态', {
    statusMap: {
      1: { text: '启用', type: 'success' },
      0: { text: '禁用', type: 'danger' }
    }
  })
]
```

### 第三步：更新组件使用

**旧的组件使用：**
```vue
<template>
  <DataGrid
    :columns="columns"
    :data="data"
    :loading="loading"
    :toolbar-options="toolbarOptions"
  />
</template>

<script setup>
import DataGrid from '@/components/data-grid/core/DataGrid.vue'

const { state, actions } = useDataGrid({
  columns,
  loadData: fetchData
})
</script>
```

**新的组件使用：**
```vue
<template>
  <SimpleDataGrid v-bind="grid.computed.dataGridProps.value" />
</template>

<script setup>
import { SimpleDataGrid, useSimpleDataGrid, column } from '@/components/data-grid/simple'

const grid = useSimpleDataGrid({
  columns,
  loadData: fetchData
})
</script>
```

## 📚 API 对比

### 列配置 API

| 功能 | 旧 API | 新 API |
|------|--------|--------|
| 基础列 | `{ field: 'name', title: '姓名' }` | `column.basic('name', '姓名')` |
| 状态列 | 复杂的插件配置 | `column.status('status', '状态', config)` |
| 操作列 | 复杂的插件配置 | `column.actions('操作', { actions: [...] })` |
| 自定义渲染 | 插件系统 | `column.custom('field', '标题', renderFn)` |

### 渲染器配置

**旧的渲染器配置：**
```typescript
// 需要通过插件系统注册
const statusRenderer = {
  name: 'status',
  component: StatusRenderer,
  config: { ... }
}
```

**新的渲染器配置：**
```typescript
// 直接使用，无需注册
column.status('status', '状态', {
  statusMap: {
    active: { text: '活跃', type: 'success' },
    inactive: { text: '非活跃', type: 'warning' }
  }
})
```

### 状态管理

**旧的状态管理：**
```typescript
const { state, actions } = useDataGrid({
  columns,
  plugins: [...],
  loadData
})
```

**新的状态管理：**
```typescript
const { state, actions, computed } = useSimpleDataGrid({
  columns,
  loadData
})
```

## 🛠️ 常见迁移场景

### 场景 1：用户列表页面

**迁移前：**
```vue
<template>
  <DataGrid
    :columns="userColumns"
    :data="users"
    :loading="loading"
  />
</template>

<script setup>
const userColumns = [
  { field: 'id', title: 'ID', width: 80 },
  { field: 'name', title: '姓名', width: 120 },
  { field: 'email', title: '邮箱', width: 200 },
  {
    field: 'status',
    title: '状态',
    width: 100,
    slots: { default: 'status_renderer' }
  },
  {
    field: 'actions',
    title: '操作',
    width: 150,
    slots: { default: 'actions_renderer' }
  }
]
</script>
```

**迁移后：**
```vue
<template>
  <SimpleDataGrid v-bind="userGrid.computed.dataGridProps.value" />
</template>

<script setup>
import { SimpleDataGrid, useSimpleDataGrid, column, statusMaps } from '@/components/data-grid/simple'

const columns = [
  column.seq(),
  column.basic('name', '姓名', 120),
  column.basic('email', '邮箱', 200),
  column.status('status', '状态', { statusMap: statusMaps.enabledDisabled }),
  column.actions('操作', {
    actions: [
      { text: '编辑', type: 'primary', onClick: editUser },
      { text: '删除', type: 'danger', onClick: deleteUser }
    ]
  })
]

const userGrid = useSimpleDataGrid({
  columns,
  initialData: users,
  loading: loading.value
})

function editUser(params) {
  console.log('编辑用户', params.row)
}

function deleteUser(params) {
  console.log('删除用户', params.row)
}
</script>
```

### 场景 2：订单管理页面

**迁移前：**
```typescript
// 复杂的插件配置
const orderColumns = [
  // ... 复杂的插件配置
]
```

**迁移后：**
```typescript
const columns = [
  column.checkbox(),
  column.basic('orderNo', '订单号', 150),
  column.composite('customer', '客户信息', {
    main: { field: 'customer.name' },
    subs: [{ field: 'customer.phone' }],
    icon: { type: 'avatar', imageField: 'customer.avatar' }
  }),
  column.currency('amount', '订单金额', { currency: 'CNY' }),
  column.status('status', '订单状态', { statusMap: statusMaps.orderStatus }),
  column.date('createdAt', '创建时间', { format: 'YYYY-MM-DD HH:mm' }),
  column.actions('操作', {
    actions: [
      { text: '查看', onClick: viewOrder },
      { text: '编辑', onClick: editOrder, visible: (params) => params.row.status === 'pending' }
    ]
  })
]
```

## 🔧 自动化迁移工具

我们提供了一个自动化迁移脚本来帮助您快速迁移：

```bash
# 运行迁移脚本
npm run migrate:datagrid

# 或者手动运行
node scripts/migrate-datagrid.js
```

迁移脚本将：
1. 分析现有的 DataGrid 使用
2. 自动转换列配置
3. 更新导入语句
4. 生成迁移报告

## ⚠️ 注意事项

### 1. 插件系统完全移除
- 所有 `plugins/` 目录下的文件将不再使用
- `GlobalManager`、`ModernPluginManager` 等将被移除
- 需要手动迁移自定义插件逻辑

### 2. 配置格式变化
- 列配置格式有较大变化
- 事件处理方式有所调整
- 某些高级配置可能需要重新实现

### 3. 类型定义更新
- 导入的类型定义有变化
- 某些旧的类型将不再可用
- 建议使用新的类型定义

### 4. 性能影响
- 新架构性能更好，但初次加载可能有差异
- 建议进行性能测试
- 某些复杂场景可能需要优化

## 🧪 测试建议

### 1. 单元测试
```typescript
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { SimpleDataGrid, column } from '@/components/data-grid/simple'

describe('SimpleDataGrid', () => {
  it('should render basic columns', () => {
    const columns = [
      column.basic('name', '姓名'),
      column.status('status', '状态')
    ]
    
    const wrapper = mount(SimpleDataGrid, {
      props: { columns, data: [] }
    })
    
    expect(wrapper.exists()).toBe(true)
  })
})
```

### 2. 集成测试
- 测试完整的数据流
- 验证渲染器功能
- 检查事件处理

### 3. 性能测试
- 对比新旧架构的性能
- 测试大数据量场景
- 验证内存使用情况

## 📞 获取帮助

如果在迁移过程中遇到问题：

1. **查看示例**：参考 `examples/SimpleDataGridExample.vue`
2. **阅读文档**：查看详细的 API 文档
3. **提交 Issue**：在项目仓库中提交问题
4. **联系团队**：直接联系开发团队获取支持

## 🎉 迁移完成检查清单

- [ ] 更新所有导入语句
- [ ] 转换所有列配置
- [ ] 更新组件使用方式
- [ ] 测试所有功能
- [ ] 验证性能表现
- [ ] 更新相关文档
- [ ] 删除旧的插件文件
- [ ] 运行完整测试套件
