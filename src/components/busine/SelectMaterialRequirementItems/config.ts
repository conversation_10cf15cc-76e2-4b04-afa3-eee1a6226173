import {
  createPluginManager,
  PluginPresets,
  showProducts,
} from '@/components/common/Table'
import { get } from 'lodash-es'

// 表格配置项
const pluginManager = createPluginManager(PluginPresets.full)
const column = pluginManager.getColumnHelper()

// 定义表格列配置
export const columns = [
  {
    title: '物料类别',
    field: 'type.name',
    width: 140,
    formatter({ row }) {
      return row?.product_type?.name || row?.material_type?.name || ''
    },
  },
  { field: 'code', title: '物料编码', showTooltip: true, width: 140 },
  // column.text('variant_id', '变体规格', {
  //   width: 140,
  // }),
  { field: 'specs', title: '规格型号', width: 240, showTooltip: true, formatter({row}) {
      try {
        if (row.product_id) return row.specs || ''
        let specs = row.variant_id ? row.variant_specs : row.specs
        if (specs) {
          const specsArr = JSON.parse(specs)
          const resArr = Object.keys(specsArr)?.map((key) => {
            return `${key}：${specsArr?.[key]}`
          })
          const resStr = resArr.join(' | ')
          return resStr
        }
      } catch (error) {
        
      }
    }},
  { field: 'name', title: '物料名称', showTooltip: true, width: 140 },
  column.text('material_quality', '材质', { width: 140 }),
  column.text('width', '宽(mm)', { width: 140 }),
  column.text('height', '高(mm)', { width: 140 }),
  column.text('deep', '厚度', { width: 140 }),
  column.text('weight', '重量', { width: 140 }),
  column.text('sale_unit_price', '销售单价', { width: 140 }),
  column.text('uom.name', '单位', { width: 140 }),
  column.text('qty', '数量', { width: 140 }),
  column.text('unit_area', '单件面积(m²)', { width: 140 }),
  column.text('total_area', '总面积(m²)', { width: 140 }),
  column.text('total_weight', '总重量(kg)', { width: 140 }),

  { width: undefined },
]
