import { createPluginManager, PluginPresets } from '@/components/common/Table'

const pluginManager = createPluginManager(PluginPresets.full)
const column = pluginManager.getColumnHelper()

export const columns: Recordable[] = [
  column.composite('product_info', '产品信息', {
    sortable: true,
    main: {
      field: 'material_name',
      formatter: (value) => value || '-',
    },
    subs: [
      {
        field: 'material_code',
        formatter: (value) => {
          return value ? `🔤编码:${value}` : ''
        },
      },
    ],
    layout: 'horizontal',
    fixed: 'left',
  }),
  column.composite('ge_info', '规格', {
    main: {
      field: 'ge_info',
      formatter: (value) => value || '',
    },
    subs: [
      {
        field: 'height',
        formatter: (value) => {
          return value ? `📏长/高:${Number(value).toFixed(2)}(MM)` : ''
        },
        style: { color: '#666', fontSize: '12px' },
      },
      {
        field: 'width',
        formatter: (value) => {
          return value ? `📐宽:${Number(value).toFixed(2)}(MM)` : ''
        },
        style: { color: '#666', fontSize: '12px' },
      },

      {
        field: 'weight',
        formatter: (value) => {
          return value ? `⚖️重量:${Number(value).toFixed(2)}(KG)` : ''
        },
        style: { color: '#666', fontSize: '12px' },
      },
      {
        field: 'deep',
        formatter: (value) => {
          return value ? `📏厚度:${Number(value).toFixed(2)}(MM)` : ''
        },
        style: { color: '#666', fontSize: '12px' },
      },
    ],
    layout: 'horizontal',
    width: 300,
  }),
  {
    field: 'delivery_qty',
    title: '已送数量',
    width: 140,
    align: 'right',
  },
  {
    field: 'arrears_qty',
    title: '未送数量',
    width: 140,
    align: 'right',
  },
  {
    field: 'cost_amount',
    title: '成本金额',
    width: 140,
    align: 'right',
  },
  {
    field: 'sale_amount',
    title: '销售金额',
    width: 140,
    align: 'right',
  },
  {
    field: 'price',
    title: '单价',
    width: 140,
    align: 'right',
  },
  {
    field: 'work_content',
    title: '加工要求',
    width: 140,
  },
  column.name('material_quality', '材质'),
  column.name('color', '颜色'),
  // {
  //   field: 'unit_id',
  //   title: '单位',
  //   width: 100,
  // },
  {
    field: 'specs',
    title: '规格型号',
    width: 140,
  },
  {
    field: 'variant_specs',
    title: '变体规格',
    width: 140,
  },
  column.image('pic', '图片'),
]
