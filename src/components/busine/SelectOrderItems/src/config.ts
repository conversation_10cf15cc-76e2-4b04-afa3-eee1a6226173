import {
    createPluginManager,
    PluginPresets,
    showCustomer,
} from '@/components/common/Table';
import dayjs from 'dayjs';

const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

export const commonTableProps = {
    height: 400,
    // tableWrapConfig: {
    //     wrapProps: {
    //         class: 'flex gap-[10px]',
    //     },
    //     leftProps: {
    //         class: 'flex-1 mr-[10px] w-[350px]',
    //     },
    //     mainProps: {
    //         style: {
    //             width: 'calc(100% - 350px) !important',
    //         },
    //     },
    // },
    // 表格样式配置
    tableStyle: {
        striped: true,
        border: false,
        compact: true,
        headerStyle: 'gray',
        rowHeight: 'medium',
        rounded: 'md',
        shadow: 'sm',
        responsive: true,
        theme: 'light',
    },

    // 启用默认样式
    enableDefaultStyle: true,

    // VxeTable自适应行高配置
    showOverflow: false,
    rowConfig: {
        isHover: true,
    },
};

export const columns = [
    column.text('order_no', '订单编号'),

    column.text('customer_name', '客户信息'),

    column.text('order_no', '项目名称'),
    column.text('order_no', '品名'),

    column.text('specs', '规格型号'),
    { field: 'width', title: '宽(mm)', width: 100 },
    { field: 'height', title: '高(mm)', width: 100 },
    { field: 'uom', title: '计量单位', width: 100 },

    column.composite('delivery_date', '交货时间信息', {
        layout: 'horizontal',
        main: {
            field: 'delivery_date',
            formatter: (value) =>
                `订单交货日期: ${dayjs(value).format('YYYY-MM-DD')}`,
            style: {
                fontWeight: 'bold',
                fontSize: '14px',
                color: '#1f2937',
            },
        },
        subs: [
            {
                field: 'order_date',
                formatter: (value) =>
                    `订单日期: ${dayjs(value).format('YYYY-MM-DD')}`,
                style: { color: '#999', fontSize: '12px' },
            },
        ],
        width: 260,
    }),
    column.number('org_qty', '订单数量', {
        precision: 0,
    }),
    column.number('qty', '未下生产单数量', {
        precision: 0,
        width: 140,
    }),
];
