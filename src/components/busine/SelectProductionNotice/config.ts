import {
    createPluginManager,
    PluginPresets,
    showProducts,
} from '@/components/common/Table';
import { $t } from '@/locales';
import { get } from 'lodash-es';

// 表格配置项
const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

// 定义表格列配置
export const columns = [
    column.name('notice_no', '单号', {}),

    column.dict('doc_status', '状态', {
        width: 160,
        dict: 'DOCUMENT_TYPE',
    }),

    // column.dict('team_type.name', '审核否', {
    //     width: 160,
    //     dict: 'DOCUMENT_TYPE',
    // }),

    column.boolean('is_give', '下达否'),

    column.dict('is_case_close', '结案否'),

    column.text('factory.factory_name', '机构', {
        width: 140,
        formatter: ({ row }) => get(row, 'factory.factory_name'),
    }),
    column.text('dept.name', '生产部门', {
        width: 140,
        formatter: ({ row }) => get(row, 'dept.name'),
    }),

    column.date('plan_start_date', '计划开始时间'),
    column.date('plan_end_date', '计划完成时间'),
    // column.text('plan_qty', '总数量'),
    // column.text('line', '总面积'),
    // column.text('line', '附件数'),

    // column.oneToMany('items', '明细', {
    //     itemName: '条',
    //     clickable: true,
    //     onItemClick: (items, row) => {
    //         showProducts(row);
    //         return;
    //     },
    //     width: 100,
    // }),

    column.text('notes', $t('product.table.notes'), {
        width: undefined,
    }),
];
