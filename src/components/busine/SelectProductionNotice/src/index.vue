<template>
    <slot name="target" v-bind="getBindParams" />
    <div
        v-if="showPlaceholder"
        class="items-center truncate flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-80 disabled:bg-disabledBg"
        @click="() => open()"
    >
        {{ getFormatter }}
    </div>

    <DialogForm @register="register"> </DialogForm>
</template>

<script lang="ts" setup>
import { DialogForm, useDialogForm } from '@/components';
import { isFunction } from 'lodash-es';
import { computed, nextTick, ref, unref } from 'vue';
import {
    getProductionNoticeMetadata,
    getProductionNoticeList,
} from '@/api/mes/production_notice';
import { commonTableProps } from '../../SelectOrderItems/src/config';
import { columns } from '../config';
// import { commonTableProps } from '../../SelectOrderItems/src/config';

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();

const newValue = computed({
    get() {
        try {
            return props.modelValue;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    },
    set(value) {
        emits('update:modelValue', value);
    },
});

const getFormatter = computed(() => {
    if (isFunction(props.formatter)) {
        return props.formatter(newValue.value, props.record);
    } else {
        return newValue.value;
    }
});

const [register, api] = useDialogForm({
    dialogProps: {
        title: '选择生产通知单',
        size: 'middle',
        height: 380,
    },
    cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
    },
    fields: [
        {
            field: 'list',
            component: 'table',
            span: true,
            componentProps: {
                checkType: 'radio',
                ...commonTableProps,
                ...props.tableProps,
                columns,
                columnsApi: getProductionNoticeMetadata,
                fetchApi: async (
                    pageParams,
                    searchParams,
                    otherParams,
                    reloadParams
                ) => {
                    const searchConditions =
                        searchParams?.filters?.conditions || [];

                    const extraSearchConditions =
                        extraSearch.value?.filters?.conditions || [];

                    const filters = {
                        conditions: [
                            ...searchConditions,
                            ...extraSearchConditions,
                        ],
                    };

                    const params = {
                        ...(filters.conditions?.length ? { filters } : {}),
                        ...pageParams,
                        ...extraSearch.value,
                    };

                    const response = await getProductionNoticeList(params);
                    return response;
                },
            },
        },
    ],
    submit() {
        const tableApi = api.getApi('list');

        emits(
            'submit',
            unref(unref(tableApi.checked)),
            props.record,
            originRef.value
        );
        emits(
            'update:modelValue',
            unref(unref(tableApi.checked)),
            props.record,
            originRef.value
        );

        api.close();

        return undefined;
    },
});

const open = async (obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) => {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    api?.open();
    await nextTick();
};

const getBindParams = computed(() => {
    return {
        modalValue: newValue.value,
        api: {
            ...api,
            open,
        },
    };
});

defineExpose({
    ...api,
    open,
});
</script>
