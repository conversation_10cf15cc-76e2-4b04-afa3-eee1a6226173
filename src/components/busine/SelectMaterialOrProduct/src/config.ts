import { createPluginManager, PluginPresets } from '@/components/common/Table'
import { $t } from '@/locales'

const pluginManager = createPluginManager(PluginPresets.full)
const column = pluginManager.getColumnHelper()

export const commonTableProps = {
  height: 350,
  tableWrapConfig: {
    wrapProps: {
      class: 'flex gap-[10px]',
    },
    leftProps: {
      class: 'flex-1 mr-[10px] w-[350px]',
    },
    mainProps: {
      style: {
        width: 'calc(100% - 350px) !important',
      },
    },
  },
  // 表格样式配置
  tableStyle: {
    striped: true,
    border: false,
    compact: true,
    headerStyle: 'gray',
    rowHeight: 'medium',
    rounded: 'md',
    shadow: 'sm',
    responsive: true,
    theme: 'light',
  },

  // 启用默认样式
  enableDefaultStyle: true,

  // VxeTable自适应行高配置
  showOverflow: false,
  rowConfig: {
    isHover: true,
  },
}

export const productColumns = [
  column.composite('product_info', '产品信息', {
    sortable: true,
    main: {
      field: 'name',
      formatter: (value) => value || '-',
    },
    subs: [
      {
        field: 'code',
        formatter: (value) => {
          return value ? `🔤编码:${value}` : ''
        },
      },
    ],
    layout: 'horizontal',
    fixed: 'left',
  }),
  column.composite('ge_info', '规格', {
    main: {
      field: 'ge_info',
      formatter: (value) => value || '',
    },
    subs: [
      {
        field: 'height',
        formatter: (value) => {
          return value ? `📏长/高:${Number(value).toFixed(2)}(MM)` : ''
        },
        style: { color: '#666', fontSize: '12px' },
      },
      {
        field: 'width',
        formatter: (value) => {
          return value ? `📐宽:${Number(value).toFixed(2)}(MM)` : ''
        },
        style: { color: '#666', fontSize: '12px' },
      },

      {
        field: 'weight',
        formatter: (value) => {
          return value ? `⚖️重量:${Number(value).toFixed(2)}(KG)` : ''
        },
        style: { color: '#666', fontSize: '12px' },
      },
      {
        field: 'deep',
        formatter: (value) => {
          return value ? `📏厚度:${Number(value).toFixed(2)}(MM)` : ''
        },
        style: { color: '#666', fontSize: '12px' },
      },
    ],
    layout: 'horizontal',
    width: 300,
  }),
  column.name('type.name', '类别', {
    width: 100,
  }),
  column.dict('brand.name', '品牌', {
    dict: 'BRAND',
  }),
  column.dict('color.name', '颜色', {
    dict: 'COLOR',
  }),
  column.dict('pricing_mode', '计价方式', {
    dict: 'pricing_mode_enum',
  }),
  column.boolean('valid', '是否生效', {
    style: 'switch', //text,tag,switch,badge
    trueText: '启用',
    falseText: '禁用',
    width: 100,
  }),

  column.currency('unit_price', '单价', '￥', {
    precision: 2,
    width: 100,
  }),

  column.dict('base_unit.name', '单位', {
    dict: 'UOM',
  }),
  // column.composite('base_unit', '单位', {
  //     main: {
  //         field: 'base_unit.name',
  //         formatter: (value) => value || '-',
  //         style: { fontWeight: '500' },
  //     },

  //     layout: 'vertical',
  //     width: 80,
  // }),

  column.currency('mini_sales_area', '最小结算面积（m²）', ' ', {
    precision: 2,
    width: 180,
  }),

  // column.link('product_route_id', '工艺路线', {
  //     href: '',
  //     target: '_blank',
  //     width: 100,
  // }),

  column.manyToOne('process_route_id', '工艺路线', {
    displayField: 'process_route_id',
    clickable: true,
    onItemClick: (item, row) => {
      if (item) {
        console.log('工艺路线:', item)
        alert(`工艺路线: `)
      }
    },
    width: 100,
  }),
  column.userMapping('created_by', '创建者'),

  column.userMapping('updated_by', '更新者'),

  column.dateOnly('created_at', '创建时间'),

  column.text('notes', $t('product.table.notes'), { width: 150 }),
]

export const materialColumns = [
  column.composite('name', '物料名称', {
    main: {
      field: 'name',
      formatter: (value) => value || '未设置',
    },
    subs: [
      {
        field: 'code',
        template: '🔤编码: ${value}',
        condition: (record) => !!record.code,
      },
      {
        field: 'material_type',
        template: '🏷️类别: ${value}',
        condition: (record) => !!record.material_type,
      },
    ],
    width: 240,
    layout: 'horizontal',
    fixed: 'left',
  }),

  column.composite('specs', '规格', {
    main: {
      formatter: (_value, row) => {
        try {
          if (row.material_specs_json) {
            const specsArr = JSON.parse(row.material_specs_json)
            const resArr = Object.keys(specsArr)?.map((key) => {
              return `${key}：${specsArr?.[key]}`
            })
            const resStr = resArr.join(' | ')
            return resStr
          }
          return ''
        } catch (error) {
          console.error('解析规格JSON失败:', error)
          return ''
        }
      },
    },
    width: 240,
  }),
  column.composite('relations', '计价信息', {
    main: {
      formatter: (value, row) => {
        const relations = []
        if (row.pricing_mode) relations.push(`计价方式：${row.pricing_mode}`)
        return relations.length > 0 ? relations[0] : '无关联信息'
      },
    },
    subs: [
      {
        field: 'sale_unit_price',
        template: '💰单价：${value}',
        condition: (row) => !!row.sale_unit_price,
      },
    ],
    layout: 'horizontal',
    width: 200,
  }),
  column.text('base_unit', '单位', { width: 90 }),
  column.composite('relations', '物料信息', {
    main: {
      formatter: (value, row) => {
        const relations = []
        if (row.model) relations.push(`型号：${row.model}`)
        return relations.length > 0 ? relations[0] : '无关联信息'
      },
    },
    subs: [
      {
        field: 'material_quality',
        template: '🧊材质：${value}',
        condition: (row) => !!row.material_quality,
      },
      {
        field: 'color',
        template: '🎨颜色：${value}',
        condition: (row) => !!row.color,
      },
      {
        field: 'deep',
        template: '📏厚度：${value}',
        condition: (row) => !!row.deep,
      },
    ],
    layout: 'horizontal',
    width: 300,
  }),
  column.boolean('valid', '是否生效', {
    style: 'switch',
    trueText: '启用',
    falseText: '禁用',
    width: 80,
  }),
  column.text('notes', $t('product.table.notes')),
  column.userMapping('created_by', $t('project.list.created_by')),
  column.userMapping('updated_by', $t('project.list.updated_by')),
  column.userMapping('created_at', $t('project.list.created_at')),
  column.userMapping('updated_at', $t('project.list.updated_at')),
]
