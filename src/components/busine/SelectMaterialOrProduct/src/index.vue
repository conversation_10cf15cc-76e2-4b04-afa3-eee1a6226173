<template>
    <slot name="target" v-bind="getBindParams" />
    <div
        v-if="showPlaceholder"
        class="items-center truncate flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-80 disabled:bg-disabledBg"
        @click="() => open()"
    >
        {{ getFormatter }}
    </div>
    <DialogForm @register="register">
        <template #productLeft>
            <div class="h-[350px] overflow-auto">
                <NTree
                    :data="productTypeData"
                    @checkbox-change="handleProductCheckboxChange"
                />
            </div>
        </template>
        <template #materialLeft>
            <div class="h-[350px] overflow-auto">
                <NTree
                    :data="treeMaterialsTypeData"
                    @checkbox-change="handleMaterialCheckboxChange"
                />
            </div>
        </template>
    </DialogForm>
</template>

<script lang="ts" setup>
import { DialogForm, useDialogForm } from '@/components';
import { debounce, isEmpty, isFunction } from 'lodash-es';
import { computed, nextTick, onMounted, ref, unref } from 'vue';
import { NTree } from '@/components/common/NTree';
import { TreeNode } from '@/components/common/NTree/types';
import { useRequest } from 'alova/client';
import { commonTableProps, materialColumns, productColumns } from './config';
import {
    getPageMetaData,
    getProductList,
    getProductTypeList,
} from '@/api/ops/product';
import {
    getMaterialInfoAndVariantInfo,
    getMaterialInfoMetadata,
    getMaterialTypeTree,
} from '@/api/ops/material_list';

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const productTypeData = ref<TreeNode[]>([]);
const treeMaterialsTypeData = ref<TreeNode[]>([]);
const productCheckedKeys = ref<any[]>([]);
const materialCheckedKeys = ref<any[]>([]);

const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();

const newValue = computed({
    get() {
        try {
            return props.modelValue;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    },
    set(value) {
        emits('update:modelValue', value);
    },
});

const getFormatter = computed(() => {
    if (isFunction(props.formatter)) {
        return props.formatter(newValue.value, props.record);
    } else {
        return newValue.value;
    }
});

const [register, api] = useDialogForm({
    dialogProps: {
        title: '选择产品/物料',
        size: 'middle',
        height: 380,
    },
    cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
    },

    fields: [
        {
            component: 'tabs',
            field: 'type',
            defaultValue: 'product',
            span: true,
            formItemProps: {
                class: 'absolute top-[13px]',
            },
            componentProps: {
                options: [
                    { label: '产品', value: 'product' },
                    { label: '物料', value: 'material' },
                ],
                style: {
                    minHeight: '48px',
                },
            },
        },

        // 产品
        {
            field: 'product',
            component: 'table',
            show: (formData) => formData.type == 'product',
            componentProps: {
                ...commonTableProps,
                ...props.tableProps,
                columns: productColumns,
                columnsApi: getPageMetaData,
                fetchApi: (
                    pageParams,
                    searchParams,
                    otherParams,
                    reloadParams
                ) => {
                    const typeCondition = productCheckedKeys.value?.length
                        ? [
                              {
                                  field: 'type_id',
                                  op: 'in',
                                  value: productCheckedKeys.value,
                              },
                          ]
                        : [];

                    const searchConditions =
                        searchParams?.filters?.conditions || [];

                    const extraSearchConditions =
                        extraSearch.value?.filters?.conditions || [];

                    const filters = {
                        conditions: [
                            ...typeCondition,
                            ...searchConditions,
                            ...extraSearchConditions,
                        ],
                    };

                    const params = {
                        ...(filters.conditions?.length ? { filters } : {}),
                        ...pageParams,
                    };

                    return getProductList(params);
                },
            },
            slots: {
                'table-left': 'productLeft',
            },
        },

        // 物料
        {
            field: 'material',
            component: 'table',
            show: (formData) => formData.type == 'material',
            componentProps: {
                ...commonTableProps,
                ...props.tableProps,
                columns: materialColumns,
                columnsApi: getMaterialInfoMetadata,
                fetchApi: async (searchParams: Recordable, pageParams) => {
                    const baseConditions = [
                        { field: 'is_variant', op: 'eq', value: false },
                    ];

                    const typeCondition = materialCheckedKeys.value?.length
                        ? [
                              {
                                  field: 'type_id',
                                  op: 'in',
                                  value: materialCheckedKeys.value,
                              },
                          ]
                        : [];

                    const searchConditions =
                        searchParams?.filters?.conditions || [];

                    const extraSearchConditions =
                        extraSearch.value?.filters?.conditions || [];

                    const filters = {
                        conditions: [
                            ...baseConditions,
                            ...typeCondition,
                            ...searchConditions,
                            ...extraSearchConditions,
                        ],
                    };

                    const params = {
                        ...(filters.conditions?.length ? { filters } : {}),
                        ...pageParams,
                        ...extraSearch.value,
                    };

                    const response =
                        await getMaterialInfoAndVariantInfo(params);
                    return response;
                },
            },
            slots: {
                'table-left': 'materialLeft',
            },
        },
    ],
    async submit({ type }) {
        const productTableApi = api.getApi('product');
        const materialTableApi = api.getApi('material');
        const params = {
            type,
            product: unref(unref(productTableApi.checked)),
            material: unref(unref(materialTableApi.checked)),
        };

        emits('submit', params, props.record, originRef.value);
        emits('update:modelValue', params, props.record, originRef.value);
        return undefined;
    },
});

const getProductTypeData = () => {
    let queryData = {
        offset: 1,
        limit: 1000,
    };
    const { onSuccess, onComplete } = useRequest(getProductTypeList(queryData));
    onSuccess(({ data }) => {
        const rootDepts: Recordable[] = [];
        if (data && data.items) {
            // 创建一个映射表，用于快速查找部门
            const deptMap = new Map();
            // 第一步：将所有部门添加到映射表中
            data.items.forEach((dept: Recordable) => {
                // 修改 name 属性为 name + '-' + code 形式
                if (dept.name && dept.code) {
                    dept.name = `${dept.name}-（${dept.code}）`;
                }
                // 确保每个部门有一个children数组
                dept.children = [];
                deptMap.set(dept.id, dept);
            });
            // 第二步：构建树形结构
            data.items.forEach((dept: Recordable) => {
                // 如果有父部门，将当前部门添加到父部门的children中
                if (dept.parent_id && deptMap.has(dept.parent_id)) {
                    const parentDept = deptMap.get(dept.parent_id);
                    parentDept.children.push(dept);
                } else {
                    // 没有父部门或父部门不存在，作为根节点
                    rootDepts.push(dept);
                }
            });
            // 将处理后的树形结构赋值给deptData
            console.log('处理后的部门树形结构:', rootDepts);
        }
        // @ts-ignore
        productTypeData.value = rootDepts || [];
        console.log('productTypeData', productTypeData.value);
    });
    onComplete(() => {
        console.log('请求完成');
    });
};

const getMaterialsTypeData = async () => {
    treeMaterialsTypeData.value = await getMaterialTypeTree();
};

// 产品类型选择
const handleProductCheckboxChange = (data: any) => {
    const tableApi = api.getApi('product');
    productCheckedKeys.value = data.allCheckedKeys;
    tableApi.reload();
};

// 物料类型选择
const handleMaterialCheckboxChange = (data: Recordable) => {
    const tableApi = api.getApi('material');
    materialCheckedKeys.value = data.allCheckedKeys;
    tableApi.reload();
};

const open = async (obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) => {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    api?.open();
    await nextTick();
};

const getBindParams = computed(() => {
    return {
        modalValue: newValue.value,
        record: props.record,
        api: {
            ...api,
            open,
        },
    };
});

onMounted(() => {
    getProductTypeData();
    getMaterialsTypeData();
});

defineExpose({
    ...api,
    open,
});
</script>
