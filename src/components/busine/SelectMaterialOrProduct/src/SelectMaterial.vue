<template>
  <slot name="target" v-bind="getBindParams" />
  <div
    v-if="showPlaceholder"
    class="items-center backdrop:truncate flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-80 disabled:bg-disabledBg"
    @click="() => open()"
  >
    {{ getFormatter }}
  </div>

  <DialogForm @register="register">
    <template #materialLeft>
      <div class="h-[350px] overflow-auto">
        <NTree
          :data="treeMaterialsTypeData"
          @checkbox-change="handleMaterialCheckboxChange"
        />
      </div>
    </template>
  </DialogForm>
</template>

<script lang="ts" setup>
import { DialogForm, useDialogForm } from '@/components'
import { debounce, isEmpty, isFunction } from 'lodash-es'
import { computed, nextTick, onMounted, ref, unref } from 'vue'
import { NTree } from '@/components/common/NTree'
import { TreeNode } from '@/components/common/NTree/types'
import { commonTableProps, materialColumns } from './config'
import {
  getMaterialInfoAndVariantInfo,
  getMaterialInfoMetadata,
  getMaterialTypeTree,
} from '@/api/ops/material_list'

const props = defineProps({
  submit: {
    type: Function,
  },
  getFormData: {
    type: Function,
  },
  modelValue: {
    type: String,
  },
  disabled: {
    type: Boolean,
  },
  formatter: {
    type: Function,
  },
  record: {
    type: Object,
  },
  tableProps: {
    type: Object,
  },
  showPlaceholder: {
    type: Boolean,
  },
  extraSearch: {
    type: Object,
  },
})
const emits = defineEmits(['update:modelValue', 'submit'])

const treeMaterialsTypeData = ref<TreeNode[]>([])
const materialCheckedKeys = ref<any[]>([])

const extraSearch = ref<Recordable>(props.extraSearch)
const originRef = ref<Recordable>()

const newValue = computed({
  get() {
    try {
      return props.modelValue
    } catch (error) {
      console.error(error)
      return undefined
    }
  },
  set(value) {
    emits('update:modelValue', value)
  },
})

const getFormatter = computed(() => {
  if (isFunction(props.formatter)) {
    return props.formatter(newValue.value, props.record)
  } else {
    return newValue.value
  }
})

const [register, api] = useDialogForm({
  dialogProps: {
    title: '选择物料',
    size: 'middle',
    height: 380,
  },
  cols: {
    sm: 1,
    md: 1,
    lg: 1,
    xl: 1,
    '2xl': 1,
  },

  fields: [
    // 物料
    {
      field: 'material',
      component: 'table',
      componentProps: {
        ...commonTableProps,
        ...props.tableProps,
        columns: materialColumns,
        columnsApi: getMaterialInfoMetadata,
        on: {
          'cell-dblclick': ({ row }) => {
            emits('submit', [row], props.record, originRef.value)
            emits('update:modelValue', [row], props.record, originRef.value)
            api.close()
          },
        },
        fetchApi: async (searchParams: Recordable, pageParams) => {
          const baseConditions = [
            { field: 'is_variant', op: 'eq', value: false },
          ]

          const typeCondition = materialCheckedKeys.value?.length
            ? [
                {
                  field: 'type_id',
                  op: 'in',
                  value: materialCheckedKeys.value,
                },
              ]
            : []

          const searchConditions = searchParams?.filters?.conditions || []

          const extraSearchConditions =
            extraSearch.value?.filters?.conditions || []

          const filters = {
            conditions: [
              ...baseConditions,
              ...typeCondition,
              ...searchConditions,
              ...extraSearchConditions,
            ],
          }

          const params = {
            ...(filters.conditions?.length ? { filters } : {}),
            ...pageParams,
            ...extraSearch.value,
          }

          const response = await getMaterialInfoAndVariantInfo(params)
          return response
        },
      },
      slots: {
        'table-left': 'materialLeft',
      },
    },
  ],
  async submit({ type }) {
    const materialTableApi = api.getApi('material')
    emits(
      'submit',
      unref(unref(materialTableApi.checked)),
      props.record,
      originRef.value
    )
    emits(
      'update:modelValue',
      unref(unref(materialTableApi.checked)),
      props.record,
      originRef.value
    )
    return undefined
  },
})

const getMaterialsTypeData = async () => {
  treeMaterialsTypeData.value = await getMaterialTypeTree()
}

// 物料类型选择
const handleMaterialCheckboxChange = (data: Recordable) => {
  const tableApi = api.getApi('material')
  materialCheckedKeys.value = data.allCheckedKeys
  tableApi.reload()
}

const open = async (obj?: {
  extraSearchParams?: Recordable
  origin?: Recordable
}) => {
  extraSearch.value = obj?.extraSearchParams || props.extraSearch
  originRef.value = obj?.origin
  api?.open()
  await nextTick()
}

const getBindParams = computed(() => {
  return {
    modalValue: newValue.value,
    record: props.record,
    api: {
      ...api,
      open,
    },
  }
})

onMounted(() => {
  getMaterialsTypeData()
})

defineExpose({
  ...api,
  open,
})
</script>
