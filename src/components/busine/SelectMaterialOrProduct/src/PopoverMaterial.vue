<template>
    <Popover v-model:open="open">
        <PopoverTrigger as-child>
            <!-- <Button variant="outline" size="sm" class="w-[150px] justify-start">1
            </Button> -->
            <span></span>
        </PopoverTrigger>
        <PopoverContent class="p-0 w-[580px]" side="right" align="start">
            <VTable @register="register" />
        </PopoverContent>
    </Popover>
</template>

<script lang="ts" setup>
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { VTable, useTable } from '@/components';
import { nextTick, ref, unref } from 'vue';
import { getMaterialInfoAndVariantInfo } from '@/api/ops/material_list';

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();

const open = ref(false);

const [register, api] = useTable({
    showSearch: false,
    // showPagination: false,
    border: false,
    checkType: undefined,
    height: 350,
    // columnsApi: getPageMetaData,
    fetchApi: async (searchParams: Recordable, pageParams) => {
        const baseConditions = [
            { field: 'is_variant', op: 'eq', value: false },
        ];

        const searchConditions = searchParams?.filters?.conditions || [];

        const extraSearchConditions =
            extraSearch.value?.filters?.conditions || [];

        const filters = {
            conditions: [
                ...baseConditions,
                ...searchConditions,
                ...extraSearchConditions,
            ],
        };

        const params = {
            ...(filters.conditions?.length ? { filters } : {}),
            ...pageParams,
            ...extraSearch.value,
        };

        // @ts-ignore
        const response = await getMaterialInfoAndVariantInfo(params);
        return response;
    },
    columns: [
        {
            field: 'code',
            title: '物料编码',
        },
        {
            field: 'name',
            title: '物料名称',
        },
        {
            width: undefined,
        },
    ],
    on: {
        cellDblclick({ row }) {
            console.log('cellDblclick', row);
            emits(
                'update:modelValue',
                unref(row),
                props.record,
                originRef.value
            );
            emits('submit', unref([row]), props.record, originRef.value);

            open.value = false;
        },
    },
});

async function handleOpen(obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    open.value = true;
    await nextTick();
}

defineExpose({
    ...api,
    open: handleOpen,
});
</script>
