<!-- 选择附加费 -->
<template>
    <slot name="target" v-bind="getBindParams" />
    <div
        v-if="showPlaceholder"
        class="items-center truncate flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-80 disabled:bg-disabledBg"
        @click="() => open()"
    >
        {{ getFormatter }}
    </div>

    <DialogForm @register="register"> </DialogForm>
</template>

<script lang="ts" setup>
import {
    DialogForm,
    useDialogForm,
    createPluginManager,
    PluginPresets,
} from '@/components';
import { isFunction } from 'lodash-es';
import { computed, nextTick, ref, unref } from 'vue';
import {
    getSurchargeMetadata,
    getSurchargeList,
} from '@/api/bas/surcharge/main';
import { commonTableProps } from '../../SelectOrderItems/src/config';

const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();

const newValue = computed({
    get() {
        try {
            return props.modelValue;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    },
    set(value) {
        emits('update:modelValue', value);
    },
});

const getFormatter = computed(() => {
    if (isFunction(props.formatter)) {
        return props.formatter(newValue.value, props.record);
    } else {
        return newValue.value;
    }
});

const [register, api] = useDialogForm({
    dialogProps: {
        title: '选择附加费',
        size: 'middle',
        height: 380,
    },
    cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
    },
    fields: [
        {
            field: 'list',
            component: 'table',
            span: true,
            componentProps: {
                ...commonTableProps,
                showOverflow: true,
                // checkType: 'radio',
                columnsApi: getSurchargeMetadata,
                showFields: [
                    {
                        field: 'valid',
                        title: '状态',
                    },
                    {
                        field: 'code',
                        title: '费用编码',
                    },
                    {
                        field: 'name',
                        title: '费用名称',
                    },
                    {
                        field: 'uom.name',
                        title: '单位',
                    },

                    column.userMapping('created_by', '创建者'),
                    column.date('created_at', '创建时间'),
                    {
                        field: 'notes',
                        title: '备注',
                    },
                ],
                fetchApi: (pagination, search, extraParams, reloadParams) => {
                    const params = {
                        filters: {
                            couple: 'and',
                            conditions: [
                                {
                                    field: 'valid',
                                    value: true,
                                    op: 'eq',
                                },
                                ...(search?.filters?.conditions
                                    ? search?.filters?.conditions
                                    : []),
                            ],
                        },
                        ...pagination,
                        ...reloadParams,
                    };
                    return getSurchargeList(params);
                },
            },
        },
    ],
    submit() {
        const tableApi = api.getApi('list');

        emits(
            'submit',
            unref(unref(tableApi.checked)),
            props.record,
            originRef.value
        );
        emits(
            'update:modelValue',
            unref(unref(tableApi.checked)),
            props.record,
            originRef.value
        );

        api.close();

        return undefined;
    },
});

const open = async (obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) => {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    api?.open();
    await nextTick();
};

const getBindParams = computed(() => {
    return {
        modalValue: newValue.value,
        api: {
            ...api,
            open,
        },
    };
});

defineExpose({
    ...api,
    open,
});
</script>
