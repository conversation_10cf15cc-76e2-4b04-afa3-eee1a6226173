import type { FormFieldConfig } from '@/components/common/FormEngine/types/formEngine';
import { z } from 'zod';
// 表单项配置
const fieldsConfig: FormFieldConfig[] = [
    {
        field: 'code',
        label: '费用编码',
        component: 'input',
        validation: z
            .string({ required_error: '费用编码不能为空' })
            .nonempty('费用编码不能为空'),
    },
    {
        field: 'name',
        label: '费用名称',
        component: 'input',
        validation: z
            .string({ required_error: '费用名称不能为空' })
            .nonempty('费用名称不能为空'),
    },
    {
        field: 'uom_id',
        label: '单位',
        component: 'select',
        dict: 'UOM', // 字典类型
        validation: z
            .number()
            .nullable()
            .refine((val) => val !== null, {
                message: '单位不能为空',
            }),
    },
    {
        field: 'notes',
        label: '备注',
        component: 'textarea',
        validation: z.string().optional(),
    },
];

// 表格配置项
export const columns = [
    // {
    //     // headerType: 'checkbox', //指定表头单元格显示为复选框
    //     cellType: 'radio', //指定body单元格显示为复选框
    //     field: 'id',
    //     width: 30,
    //     fieldFormat: () => '',
    // },
    {
        field: 'seq_no',
        title: '序号',
        sort: true,
        width: 40,
    },
    {
        field: 'valid',
        title: '状态',
        // cellType: 'switch',
        // fieldFormat: (row: CurrentItem) => row.valid === true,
        // cellConfig: {
        //     checkedIcon: 'on',
        //     uncheckedIcon: 'off',
        // },
        sort: true,
    },
    {
        field: 'code',
        title: '费用编码',
        sort: true,
    },
    {
        field: 'name',
        title: '费用名称',
        sort: true,
        width: 140,
    },
    {
        field: 'uom.name',
        title: '单位',
        sort: true,
    },

    {
        field: 'created_by',
        title: '创建人',
        sort: true,
    },
    {
        field: 'created_at',
        title: '创建时间',
        sort: true,
    },
    {
        field: 'notes',
        title: '备注',
    },
];

// 默认数据
class DefaultCurrent {
    code?: string = '';
    name?: string = '';
    uom_id?: number | null;
    notes?: string = '';

    constructor(data?: DefaultCurrent) {
        Object.assign(this, data || {});
    }
}

interface CurrentItem extends DefaultCurrent {
    id?: number;
    uom?: {
        id?: number;
        name?: string;
    };
    uom_id?: number;
    uom_name?: string;
    created_by?: string;
    created_at?: string;
}
export { fieldsConfig, DefaultCurrent };
export type { CurrentItem };
