<!-- 选择订单明细 -->
<template>
    <slot name="target" v-bind="getBindParams" />
    <div
        v-if="showPlaceholder"
        class="items-center truncate flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-80 disabled:bg-disabledBg"
        @click="() => open()"
    >
        {{ getFormatter }}
    </div>
    <DialogForm @register="register">
        <template #main>
            <VTable @register="registerOrder">
                <template #table-right>
                    <div class="h-full">
                        <VTable @register="registerItems" />
                    </div>
                </template>
            </VTable>
        </template>
    </DialogForm>
</template>

<script lang="ts" setup>
import { DialogForm, useDialogForm, VTable, useTable } from '@/components';
import { isFunction } from 'lodash-es';
import { computed, nextTick, ref, unref } from 'vue';
import { getOrderList, getMetadata } from '@/api/sal/order';
import { getUnProducedOrderItems } from '@/api/sal/order';
import { commonTableProps } from '../../SelectOrderItems/src/config';

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
    getItemsApi: {
        type: String,
        default: 'getDeliveryOrderItems',
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();

const getFormatter = computed(() => {
    if (isFunction(props.formatter)) {
        return props.formatter(newValue.value, props.record);
    } else {
        return newValue.value;
    }
});

const newValue = computed({
    get() {
        try {
            return props.modelValue;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    },
    set(value) {
        emits('update:modelValue', value);
    },
});

let originRow: Recordable | undefined = props.record;

let selectedOrderRecord = ref();

const [register, api] = useDialogForm({
    dialogProps: {
        title: '选择订单明细',
        size: 'middle',
        height: 380,
    },
    cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
    },
    fields: [
        {
            layout: 'Card',
            layoutProps: {
                mode: 'empty',
            },
            schemas: [
                {
                    field: 'list',
                    component: 'table',
                    span: 4,
                    componentProps: {
                        commonTableProps,
                        columns: [
                            {
                                field: 'order_no',
                                title: '销售订单号',
                                // width: undefined,
                            },
                        ],
                    },
                },
            ],
            // slots: {
            //     default: 'bomTablex',
            // },
        },
    ],
    submit() {
        const checked = itemsApi.getCheckboxRecords(true);
        const o = {
            order: selectedOrderRecord.value,
            product: checked,
        };

        emits('submit', o, props.record, originRef.value);
        emits('update:modelValue', o, props.record, originRef.value);

        api.close();

        return undefined;
    },
});

const [registerOrder, orderApi] = useTable({
    height: 400,
    checkType: undefined,
    columns: [],
    data: [{}],
    tableWrapConfig: {
        wrapProps: {
            class: 'flex',
        },
        rightProps: {
            class: 'flex-1 pl-[10px]',
            style: {
                width: 'calc(100% - 210px)',
            },
        },
        mainProps: {
            class: 'basis-[200px]',
            style: {
                flex: '0 0 200px',
            },
        },
    },
    columnsApi: getMetadata,
    showFields: [
        {
            field: 'order_no',
            title: '销售订单号',
            minWidth: 200,
            width: 200,
        },
    ],
    fetchApi: async (pageParams, searchParams, otherParams, reloadParams) => {
        const searchConditions = searchParams?.filters?.conditions || [];

        const extraSearchConditions =
            extraSearch.value?.filters?.conditions || [];

        const filters = {
            conditions: [...searchConditions, ...extraSearchConditions],
        };

        const params = {
            ...(filters.conditions?.length ? { filters } : {}),
            ...pageParams,
        };

        // @ts-ignore
        const res = await getOrderList(params);

        nextTick(() => {
            const row = res.items?.[0];
            if (!row) return;
            orderApi.setCurrentRow(res.items[0]);
            itemsApi?.reload(row);
            itemsApi?.clearCheckboxRow();
            selectedOrderRecord.value = row;
        });

        return res;
    },
    on: {
        cellClick(record) {
            console.log('itemsApi', record);
            itemsApi?.reload(record.row);
            itemsApi?.clearCheckboxRow();
            selectedOrderRecord.value = record.row;
        },
    },
});
const [registerItems, itemsApi] = useTable({
    height: 400,
    showSearch: false,
    showPagination: false,
    // @ts-ignore
    fetchApi: async (pagination, search, extraParams, reloadParams) => {
        const data = await getUnProducedOrderItems(reloadParams.id);
        console.log('getUnProducedOrderItems');

        return {
            items: data,
        };
    },
    columns: [
        {
            field: 'seq_no',
            title: '产品序号',
            sortable: true,
            width: 100,
        },
        {
            field: 'code',
            title: '产品编码',
            sortable: true,
        },
        {
            field: 'name',
            title: '产品名称',
        },
        {
            field: 'alias',
            title: '产品别名',
        },
        {
            field: 'specs',
            title: '产品规格',
        },
        {
            field: 'width',
            title: '宽(mm)',
        },
        {
            field: 'height',
            title: '高(mm)',
        },
        {
            field: 'un_produced_qty',
            title: '未生产数量',
        },
        {
            field: 'workmanship_route',
            title: '工艺路线',
            showTooltip: true,
        },
        {
            field: 'notes',
            title: '备注',
            showTooltip: true,
        },
    ],
});

const open = async (obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) => {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    api?.open();
    await nextTick();
    orderApi?.reload();
};

const getBindParams = computed(() => {
    return {
        modalValue: newValue.value,
        record: originRow,
        api: {
            ...api,
            open,
        },
    };
});

defineExpose({
    ...api,
    open,
});
</script>
