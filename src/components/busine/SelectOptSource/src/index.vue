<!-- 选择附加费 -->
<template>
    <slot name="target" v-bind="getBindParams" />
    <div
        v-if="showPlaceholder"
        class="items-center truncate flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-80 disabled:bg-disabledBg"
        @click="() => open()"
    >
        {{ getFormatter }}
    </div>

    <DialogForm @register="register">
        <template #materialLeft>
            <div class="absolute top-0">
                <Tabs v-model="activeId" class="w-[400px]">
                    <TabsList>
                        <TabsTrigger
                            @click="() => handleSelectType(item)"
                            v-for="item in options"
                            :value="item"
                            :key="item"
                        >
                            {{ item }}
                        </TabsTrigger>
                    </TabsList>
                </Tabs>
            </div>
        </template>
    </DialogForm>
</template>

<script lang="ts" setup>
import {
    DialogForm,
    useDialogForm,
    createPluginManager,
    PluginPresets,
} from '@/components';
import { isFunction } from 'lodash-es';
import { computed, nextTick, ref, unref } from 'vue';
import {
    getOptSourceMetadata,
    getOptSourceList,
    getOptSource,
} from '@/api/mes/opt_source';
import { commonTableProps } from '../../SelectOrderItems/src/config';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const activeId = ref('全部');
const options = ['全部', '销售订单', '生产通知单', '流程卡'];
const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();

const newValue = computed({
    get() {
        try {
            return props.modelValue;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    },
    set(value) {
        emits('update:modelValue', value);
    },
});

const getFormatter = computed(() => {
    if (isFunction(props.formatter)) {
        return props.formatter(newValue.value, props.record);
    } else {
        return newValue.value;
    }
});

const [register, api] = useDialogForm({
    dialogProps: {
        title: '切割优化数据源',
        size: 'middle',
        height: 380,
    },
    cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
    },
    fields: [
        {
            field: 'list',
            component: 'table',
            span: true,
            slots: {
                'table-left': 'materialLeft',
            },
            componentProps: {
                ...commonTableProps,
                showOverflow: true,
                // checkType: 'radio',
                columnsApi: getOptSourceMetadata,
                fetchApi: (
                    pageParams,
                    searchParams,
                    otherParams,
                    reloadParams
                ) => {
                    const searchConditions =
                        searchParams?.filters?.conditions || [];

                    const extraSearchConditions =
                        extraSearch.value?.filters?.conditions || [];

                    const reloadConditions =
                        reloadParams?.filters?.conditions || [];

                    const filters = {
                        conditions: [
                            ...searchConditions,
                            ...extraSearchConditions,
                            ...reloadConditions,
                        ],
                    };

                    const params = {
                        ...(filters.conditions?.length ? { filters } : {}),
                        ...pageParams,
                    };
                    return getOptSourceList(params);
                },
            },
        },
    ],
    submit() {
        const tableApi = api.getApi('list');

        emits(
            'submit',
            unref(unref(tableApi.checked)),
            props.record,
            originRef.value
        );
        emits(
            'update:modelValue',
            unref(unref(tableApi.checked)),
            props.record,
            originRef.value
        );

        api.close();

        return undefined;
    },
});

function handleSelectType(type) {
    const tableApi = api.getApi('list');
    const params =
        type == '全部'
            ? {}
            : {
                  filters: {
                      conditions: [
                          {
                              field: 'org_type',
                              op: 'eq',
                              value: type,
                          },
                      ],
                  },
              };
    tableApi.reload(params);
}

const open = async (obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) => {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    api?.open();
    await nextTick();
};

const getBindParams = computed(() => {
    return {
        modalValue: newValue.value,
        api: {
            ...api,
            open,
        },
    };
});

defineExpose({
    ...api,
    open,
});
</script>
