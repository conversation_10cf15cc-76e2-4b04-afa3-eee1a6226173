import type { FormFieldConfig } from '@/components';
import { z } from 'zod';

export const materialsInfoColumns = [
    {
        field: 'order_no',
        title: '销售订单号',
    },
];

export const materialsInfoFormFieldsConfig: FormFieldConfig[] = [
    {
        field: 'notes',
        label: '备注',
        component: 'textarea',
        validation: z.string().optional(),
    },
];

export const materialsTypeColumnsConfig: Recordable[] = [
    {
        field: 'name',
        title: '全部',
        tree: true,
        headerStyle: { textAlign: 'left' },
        style: { textAlign: 'left' },
    },
];

export const deptColumns: any[] = [
    {
        field: 'name',
        title: '找玻网',
        tree: true,
        headerStyle: { textAlign: 'left' },
        style: { textAlign: 'left' },
        // 表头图标配置
        //headerIcon: ['headerMore'],
        // 单元格图标配置
        // icon: 'more',
        // 为表头图标配置下拉菜单
        // dropDownMenu: [
        //     {
        //         text: '新增子类',
        //         menuKey: 'header-add',
        //     },
        //     // {
        //     //     text: '编辑',
        //     //     menuKey: 'header-edit',
        //     // },
        //     // {
        //     //     text: $t('common.button.delete'),
        //     //     menuKey: 'header-delete',
        //     // },
        // ],
        // 是否显示排序
        //showSort: false,
    },
];

export const columns: any[] = [
    {
        headerType: 'checkbox', //指定表头单元格显示为复选框
        cellType: 'checkbox', //指定body单元格显示为复选框
        field: 'id',
        width: 30,
        minWidth: 30,
        fieldFormat: () => '',
    },
    {
        field: 'employee_no',
        title: '工号',
        sort: true, // 添加排序功能
        cellType: 'text', // 指定单元格类型为文本类型
        width: 240,
    },
    {
        field: 'nick_name',
        title: '姓名',
        sort: true, // 添加排序功能
        // icon: ['edit', 'delete'],
        sortIconPosition: 'right', // 设置排序图标靠右显示
        width: 240,
    },
    {
        field: 'employee_state.name',
        title: '人员状态',
        sort: true,
        width: 140,
        minWidth: 80,
    },
    {
        field: 'dept.name',
        title: '所属部门',
        sort: true,
        width: 240,
    },
    {
        field: 'post.name',
        title: '所属岗位',
        sort: true,
        width: 180,
    },
    {
        field: 'employee_type.name',
        title: '用工类型',
        sort: true,
        width: 180,
    },
    {
        field: 'account_address',
        title: '地址',
        sort: true,
        width: 240,
    },
    {
        field: 'phone_number',
        title: '联系电话',
        width: 180,
    },
    {
        field: 'entry_date',
        title: '入职时间',
        sort: true,
        width: 180,
    },
    {
        field: 'entry_date',
        title: '在职时长',
        fieldFormat: (args: any) => {
            if (!args.entry_date) return '';
            if (args.term_date) {
                const entryDate = new Date(args.entry_date);
                const termDate = new Date(args.term_date);
                const diffTime = Math.abs(
                    termDate.getTime() - entryDate.getTime()
                );
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                return `${diffDays}天`;
            } else {
                const entryDate = new Date(args.entry_date);
                const today = new Date();
                const diffTime = Math.abs(
                    today.getTime() - entryDate.getTime()
                );
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                return `${diffDays}天`;
            }
        },
        sort: true,
        width: '180px',
    },
    {
        field: 'term_date',
        title: '离职时间',
        sort: true,
        width: '180px',
    },
    {
        field: 'creator',
        title: '创建人',
        width: 140,
    },
    {
        field: 'created_at',
        title: '创建时间',
        width: 140,
    },
];
