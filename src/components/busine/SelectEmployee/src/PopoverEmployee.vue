<template>
    <Popover v-model:open="open">
        <PopoverTrigger as-child>
            <!-- <Button variant="outline" size="sm" class="w-[150px] justify-start">1
            </Button> -->
            <span></span>
        </PopoverTrigger>
        <PopoverContent class="p-0 w-[580px]" side="right" align="start">
            <VTable @register="register" />
        </PopoverContent>
    </Popover>
</template>

<script lang="ts" setup>
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { VTable, useTable } from '@/components';
import { nextTick, ref, unref } from 'vue';
import { getPageMetaData, getEmployeeList } from '@/api/hr/employee';
const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();

const open = ref(false);

const [register, api] = useTable({
    showSearch: false,
    // showPagination: false,
    border: false,
    checkType: undefined,
    height: 350,
    // columnsApi: getPageMetaData,
    async fetchApi(pageParams, searchParams, otherParams, reloadParams) {
        if (pageParams.offset < 0) return;

        const searchConditions = searchParams?.filters?.conditions || [];

        const extraSearchConditions =
            extraSearch.value?.filters?.conditions || [];

        const filters = {
            conditions: [...searchConditions, ...extraSearchConditions],
        };

        const params = {
            ...(filters.conditions?.length ? { filters } : {}),
            ...pageParams,
        };

        return getEmployeeList(params);
    },
    columns: [
        {
            field: 'employee_no',
            title: '工号',
        },
        {
            field: 'nick_name',
            title: '姓名',
        },
        {
            field: 'employee_state.name',
            title: '人员状态',
        },
        // {
        //     field: 'dept.name',
        //     title: '所属部门',
        //     width: 100,
        // },
        // {
        //     field: 'post.name',
        //     title: '所属岗位',
        //     width: 100,
        // },
        // {
        //     field: 'employee_type.name',
        //     title: '用工类型',
        //     width: 100,
        // },
    ],
    on: {
        cellDblclick({ row }) {
            console.log('cellDblclick', row);
            emits('submit', row);
            open.value = false;
        },
    },
});

async function handleOpen(obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    open.value = true;
    await nextTick();
}

defineExpose({
    ...api,
    open: handleOpen,
});
</script>
