<!-- 选择产品 -->
<template>
    <div class="relative w-full items-center">
        <!-- @input="debouncedOpen" -->
        <Input
            v-model="inputValue"
            class="pr-10"
            id="search"
            type="text"
            @keydown.enter="openPopover"
            ref="InputRef"
        />
        <span
            class="absolute end-0 inset-y-0 flex items-center justify-center px-2 cursor-pointer"
            @click="() => openDialog({})"
        >
            <Icon icon="ant-design:search-outlined" />
        </span>
    </div>

    <CustomerPopover
        ref="CustomerPopoverRef"
        @submit="handleSelect"
        :extraSearch="extraSearch"
        :tableProps="tableProps"
    />
    <CustomerDlalog
        ref="CustomerDlalogRef"
        @submit="handleSelect"
        :extraSearch="extraSearch"
        :tableProps="tableProps"
    />
</template>

<script lang="ts" setup>
import { debounce, isFunction } from 'lodash-es';
import { nextTick, ref, unref, watch } from 'vue';
import { Input } from '@/components/ui/input';
import { Icon } from '@iconify/vue';
import CustomerPopover from './PopoverEmployee.vue';
import CustomerDlalog from './DialogEmployee.vue';

defineOptions({
    name: 'SelectWorkmanship',
});

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
    afterOpen: {
        type: Function,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const InputRef = ref();
const CustomerPopoverRef = ref();
const CustomerDlalogRef = ref();

const inputValue = ref(props.modelValue);
const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();

watch(
    () => props.modelValue,
    (n) => {
        inputValue.value = n;
    }
);

function openPopover(obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) {
    extraSearch.value = obj?.extraSearchParams;
    originRef.value = obj?.origin;

    unref(unref(InputRef).el)?.blur();
    // unref(CustomerPopoverRef).open({ value: inputValue.value });
    const extraSearchConditions = extraSearch.value?.filters?.conditions || [];

    const filters = {
        conditions: [
            ...extraSearchConditions,
            {
                field: 'nick_name',
                op: 'ilike',
                value: inputValue.value,
            },
        ],
    };

    const extraSearchParams = {
        ...(filters.conditions?.length ? { filters } : {}),
    };
    unref(CustomerPopoverRef).open({
        origin: obj.origin,
        // extraSearchParams: obj.extraSearchParams,
        extraSearchParams,
    });

    if (isFunction(props.afterOpen)) props.afterOpen(unref(CustomerPopoverRef));
}

function openDialog(obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) {
    extraSearch.value = obj?.extraSearchParams;
    originRef.value = obj?.origin;

    const extraSearchConditions = extraSearch.value?.filters?.conditions || [];

    const filters = {
        conditions: [
            ...extraSearchConditions,
            // {
            //     field: 'name',
            //     op: 'eq',
            //     value: inputValue.value,
            // },
        ],
    };

    const extraSearchParams = {
        ...(filters.conditions?.length ? { filters } : {}),
    };

    unref(CustomerDlalogRef).open({
        origin: obj.origin,
        extraSearchParams,
    });

    if (isFunction(props.afterOpen)) props.afterOpen(unref(CustomerDlalogRef));
}

const debouncedOpen = debounce((event: Event) => {
    const value = (event.target as HTMLInputElement).value;
    openPopover({});
}, 500);

function handleSelect(val) {
    emits('update:modelValue', val);
}

defineExpose({
    openPopover,
    openDialog,
});
</script>
