<template>
    <DialogForm @register="register">
        <template #table-left>
            <div class="h-[350px] overflow-auto">
                <NTree
                    :data="deptData"
                    @checkbox-change="handleCheckboxChange"
                />
            </div>
        </template>
    </DialogForm>
</template>

<script lang="ts" setup>
import {
    DialogForm,
    useDialogForm,
    BaseTableProps,
    createPluginManager,
    PluginPresets,
} from '@/components';
import { debounce, isEmpty, isFunction } from 'lodash-es';
import { nextTick, ref, unref, watch } from 'vue';
import { getPageMetaData, getEmployeeList } from '@/api/hr/employee';
import { NTree } from '@/components/common/NTree';
import { getDeptList } from '@/api/hr/dept';
import { useRequest } from 'alova/client';

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();
const deptData = ref([]);

const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();
const employeeColumns = [
    column.composite('nick_name', '员工信息', {
        main: {
            field: 'nick_name',
            formatter: (value) => value || '未命名员工',
            style: {
                fontWeight: 'bold',
                fontSize: '14px',
                color: '#1f2937',
            },
        },
        subs: [
            {
                field: 'employee_no',
                template: '工号: ${value}',
                condition: (record) => !!record.employee_no,
            },
            {
                field: 'dept.name',
                template: '部门: ${value}',
                condition: (record) => !!record.dept.name,
            },
            {
                field: 'post.name',
                template: '岗位: ${value}',
                condition: (record) => !!record.post.name,
            },
        ],

        width: '25%',
    }),
    column.manyToOne('employee_type', '员工类型', {
        displayField: 'name',
    }),
    column.manyToOne('employee_state', '状态', {
        displayField: 'name',
    }),
    column.datetime('entry_date', '入职时间'),
    column.datetime('term_date', '离职时间'),
    column.text('worker_date', '在职时长', {
        formatter: ({ row }) => {
            if (!row.entry_date) return '';
            if (row.term_date) {
                const entryDate = new Date(row.entry_date);
                const termDate = new Date(row.term_date);
                const diffTime = Math.abs(
                    termDate.getTime() - entryDate.getTime()
                );
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                return `${diffDays}天`;
            } else {
                const entryDate = new Date(row.entry_date);
                const today = new Date();
                const diffTime = Math.abs(
                    today.getTime() - entryDate.getTime()
                );
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                return `${diffDays}天`;
            }
        },
    }),
    column.text('account_address', '户籍地址'),
    column.text('phone_number', '联系电话'),
    column.userMapping('created_by', '创建者'),
    column.dateOnly('created_at', '创建时间'),
];

const [register, api] = useDialogForm({
    dialogProps: {
        title: '选择员工',
        size: 'middle',
        height: 380,
    },
    cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
    },
    fields: [
        {
            layout: 'Card',
            layoutProps: {
                mode: 'empty',
            },
            slots: {
                default: 'bomTable',
            },
        },
        {
            field: 'list',
            component: 'table',
            span: 3,
            slots: {
                'table-left': 'table-left',
            },
            componentProps: {
                // layoutHeight: 500,
                slots: {
                    'table-left': 'table-left',
                },
                tableWrapConfig: {
                    wrapProps: {
                        class: 'flex gap-[10px]',
                    },
                    leftProps: {
                        class: 'flex-1 mr-[10px] w-[350px]',
                    },
                    mainProps: {
                        style: {
                            width: 'calc(100% - 350px) !important',
                        },
                    },
                },
                // 新增：表格样式配置
                tableStyle: {
                    striped: true, // 斑马纹效果
                    border: false, // 只显示水平边框，不显示竖向边框
                    compact: true, // 紧凑布局
                    headerStyle: 'gray', // 灰色表头
                    rowHeight: 'medium', // 中等行高
                    rounded: 'md', // 中等圆角
                    shadow: 'sm', // 小阴影
                    responsive: true, // 响应式设计
                    theme: 'light', // 亮色主题
                },
                // 启用默认样式
                enableDefaultStyle: true,
                // VxeTable自适应行高配置
                showOverflow: false,
                height: 350,
                columnsApi: getPageMetaData,
                fetchApi(pageParams, searchParams, otherParams, reloadParams) {
                    if (isEmpty(pageParams)) return;

                    const searchConditions =
                        searchParams?.filters?.conditions || [];

                    const extraSearchConditions =
                        extraSearch.value?.filters?.conditions || [];

                    const filters = {
                        conditions: [
                            ...searchConditions,
                            ...extraSearchConditions,
                        ],
                    };

                    const params = {
                        ...(filters.conditions?.length ? { filters } : {}),
                        ...pageParams,
                    };
                    return getEmployeeList(params);
                },
                columns: employeeColumns,
                on: {
                    cellDblclick: ({ row }) => {
                        emits(
                            'update:modelValue',
                            unref([row]),
                            props.record,
                            originRef.value
                        );
                        emits(
                            'submit',
                            unref([row]),
                            props.record,
                            originRef.value
                        );

                        api?.close();
                    },
                },
                ...props.tableProps,
            } as unknown as BaseTableProps,
        },
    ],
    async submit(...args) {
        await nextTick();
        const tableApi = api?.getApi('list');

        emits(
            'submit',
            unref(unref(tableApi.checked)),
            props.record,
            originRef.value
        );
        emits(
            'update:modelValue',
            unref(unref(tableApi.checked)),
            props.record,
            originRef.value
        );
        return undefined;
    },
});

const getDeptLists = async (params?: any) => {
    const { onSuccess } = useRequest(getDeptList(params), {
        immediate: true, // 立即发送请求
    });
    onSuccess((res) => {
        // 处理部门数据，将其转换为树形结构
        if (res.data && res.data.items) {
            // 创建一个映射表，用于快速查找部门
            const deptMap = new Map();
            const rootDepts: any[] = [];

            // 第一步：将所有部门添加到映射表中
            res.data.items.forEach((dept) => {
                // 确保每个部门有一个children数组
                dept.children = [];
                deptMap.set(dept.id, dept);
            });

            // 第二步：构建树形结构
            res.data.items.forEach((dept) => {
                // 如果有父部门，将当前部门添加到父部门的children中
                if (dept.parent_id && deptMap.has(dept.parent_id)) {
                    const parentDept = deptMap.get(dept.parent_id);
                    parentDept.children.push(dept);
                } else {
                    // 没有父部门或父部门不存在，作为根节点
                    rootDepts.push(dept);
                }
            });

            // 将处理后的树形结构赋值给deptData
            res.data.items = rootDepts;
        }
        deptData.value = res.data.items || [];
        console.log('部门数据：', deptData.value);
    });
};

const handleCheckboxChange = (data: any) => {
    const tableApi = api.getApi('list');
    const { node, checked, timestamp, allCheckedKeys, selectAll } = data;
    if (selectAll === undefined) {
        tableApi.reload({
            filters: {
                conditions: [
                    {
                        field: 'dept_id',
                        op: 'in',
                        value: allCheckedKeys,
                    },
                ],
            },
            offset: 0,
            limit: 1000,
        });
        return;
    }
    if (selectAll === true) {
        tableApi.reload({});
    } else {
        tableApi.reload({
            filters: {
                conditions: [
                    {
                        field: 'dept_id',
                        op: 'in',
                        value: [],
                    },
                ],
            },
            offset: 0,
            limit: 1000,
        });
    }
};

async function open(obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    api?.open();
    getDeptLists({ offset: 0, limit: 1000 });
    await nextTick();
}

defineExpose({
    ...api,
    open,
});
</script>
