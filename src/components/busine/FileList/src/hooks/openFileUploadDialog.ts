import { useGlobDialogForm } from '@/components';

export function openFileUploadDialog(opt: {
    relatedModel: string;
    relatedId: string | number;
}) {
    return new Promise((resolve, reject) => {
        const { openDialogForm } = useGlobDialogForm();

        openDialogForm({
            dialogProps: {
                size: 'mini',
                title: '文件上传',
                showFooter: false,
            },
            fields: [
                {
                    field: 'files',
                    component: 'upload',
                    span: true,
                    componentProps: {
                        relatedModel: opt.relatedModel,
                        relatedId: opt.relatedId,
                        class: 'w-full',
                        onSuccess(val) {
                            resolve(val);
                        },
                    },
                },
            ],
            submit({ files }) {
                if (!files.data) return;

                resolve(files);
                // tableApi.reload();
                return undefined;
            },
        });
    });
}
