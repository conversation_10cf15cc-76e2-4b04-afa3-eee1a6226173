<script setup lang="ts">
import {
    VTable,
    useTable,
    createPluginManager,
    PluginPresets,
} from '@/components/common/Table';
import { unref } from 'vue';
import { deleteAttachment, getByRelation } from '@/api/sys/attachment';
import { downLoadURL } from '@/utils/down';
import { openFileUploadDialog } from './hooks/openFileUploadDialog';
import { useToast } from '@/components/ui/toast/use-toast';

const props = defineProps({
    relatedModel: String,
    relatedId: [String, Number],
});

const toast = useToast();

// 使用插件化架构
const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

// 定义表格列配置

// 使用 useTable 配置表格
const [register, tableApi] = useTable({
    // @ts-ignore
    async fetchApi() {
        const result = await getByRelation({
            related_model: props.relatedModel,
            related_ids: props.relatedId ? [props.relatedId] : [],
        });
        return {
            items: result,
            total: result?.length || 0,
        };
    },
    buttonList: [
        {
            name: '上传',
            props: {
                type: 'button',
            },
            on: {
                async click() {
                    if (!props.relatedId) {
                        toast.toast({
                            title: '',
                            description: '没有相关模块id 不能上传！',
                            variant: 'destructive',
                            duration: 2000,
                        });
                        return;
                    }
                    // @ts-ignore
                    await openFileUploadDialog(props);
                    tableApi.reload();
                },
            },
        },

        {
            name: '下载',
            props: {
                type: 'button',
            },
            on: {
                click() {
                    const checked = unref(unref(tableApi.checked));
                    const paths = [];
                    checked?.forEach((row) => {
                        if (!row.file_path) return;
                        const fileUrl =
                            import.meta.env.VITE_GLOB_FILE_URL + row.file_path;
                        paths.push(fileUrl);
                        downLoadURL(fileUrl, row.file_name);
                    });
                },
            },
        },
    ],
    columns: [
        column.composite('customer', '文件名称', {
            layout: 'horizontal',
            width: undefined,
            main: {
                field: 'file_name',
            },
            actions: [
                {
                    icon: 'ant-design:delete-outlined',
                    tooltip: '删除文件',
                    onClick: async ({ row }) => {
                        await deleteAttachment(row?.id);
                        toast.toast({
                            title: '',
                            description: '删除成功！',
                            // variant: 'destructive',
                            duration: 2000,
                        });
                        tableApi.reload();
                    },
                },
            ],
        }),
        {
            field: 'file_size',
            title: '文件大小',
            width: undefined,
        },

        column.userMapping('created_by', '上传人', {
            width: undefined,
        }),
        column.date('created_at', '上传时间', {
            width: undefined,
        }),
    ],
    height: 400,
    checkType: 'checkbox',
    showSearch: false,
    showPagination: false,
    toolbarConfig: {
        isReload: true,
    },

    // 表格样式配置
    tableStyle: {
        striped: true,
        border: false,
        compact: true,
        headerStyle: 'gray',
        rowHeight: 'medium',
        rounded: 'md',
        shadow: 'sm',
        responsive: true,
        theme: 'light',
    },

    // 启用默认样式
    enableDefaultStyle: true,

    // VxeTable自适应行高配置
    showOverflow: false,
});
</script>

<template>
    <VTable @register="register" />
</template>

<style scoped>
/* 自定义样式可以在这里添加 */
</style>
