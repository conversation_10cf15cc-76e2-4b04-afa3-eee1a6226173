<template>
    <slot name="target" v-bind="getBindParams" />
    <div
        v-if="showPlaceholder"
        class="items-center backdrop:truncate flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-80 disabled:bg-disabledBg"
        @click="() => open()"
    >
        {{ getFormatter }}
    </div>

    <DialogForm @register="register"> </DialogForm>
</template>

<script lang="ts" setup>
import { DialogForm, useDialogForm } from '@/components';
import { debounce, isEmpty, isFunction } from 'lodash-es';
import { computed, nextTick, onMounted, PropType, ref, unref } from 'vue';
import { TreeNode } from '@/components/common/NTree/types';
import { commonTableProps, columns } from './config';

import { getBOMList, getPageMetaData } from '@/api/ops/bom';

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    mode: {
        type: String as PropType<'quotation' | ''>,
    },
    extraSearch: {
        type: Object,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const extraSearch = ref<Recordable>({});
const originRef = ref<Recordable>();

const newValue = computed({
    get() {
        try {
            return props.modelValue;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    },
    set(value) {
        emits('update:modelValue', value);
    },
});

const getFormatter = computed(() => {
    if (isFunction(props.formatter)) {
        return props.formatter(newValue.value, props.record);
    } else {
        return newValue.value;
    }
});

const [register, api] = useDialogForm({
    dialogProps: {
        title: '选择BOM',
        size: 'middle',
        height: 380,
    },
    cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
    },

    fields: [
        // 物料
        {
            field: 'material',
            component: 'table',
            componentProps: {
                ...commonTableProps,
                ...props.tableProps,
                columns: columns,
                columnsApi: getPageMetaData,
                fetchApi: async (searchParams: Recordable, pageParams) => {
                    const searchConditions =
                        searchParams?.filters?.conditions || [];

                    const extraSearchConditions =
                        extraSearch.value?.filters?.conditions || [];

                    const filters = {
                        conditions: [
                            ...searchConditions,
                            ...extraSearchConditions,
                        ],
                    };

                    const params = {
                        ...(filters.conditions?.length ? { filters } : {}),
                        ...pageParams,
                        ...extraSearch.value,
                    };

                    const response = await getBOMList(params);
                    return response;
                },
            },
            slots: {
                'table-left': 'materialLeft',
            },
        },
    ],
    async submit({ type }) {
        const materialTableApi = api.getApi('material');

        emits(
            'submit',
            unref(unref(materialTableApi.checked)),
            props.record,
            originRef.value
        );
        emits(
            'update:modelValue',
            unref(unref(materialTableApi.checked)),
            props.record,
            originRef.value
        );
        return undefined;
    },
});

const open = async (obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) => {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    api?.open();
    await nextTick();
};

const getBindParams = computed(() => {
    return {
        modalValue: newValue.value,
        record: props.record,
        api: {
            ...api,
            open,
        },
    };
});

defineExpose({
    ...api,
    open,
});
</script>
