import { createPluginManager, PluginPresets } from '@/components/common/Table';
import { $t } from '@/locales';

const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

export const commonTableProps = {
    checkType: 'radio',
    height: 350,
    // tableWrapConfig: {
    //     wrapProps: {
    //         class: 'flex gap-[10px]',
    //     },
    //     leftProps: {
    //         class: 'flex-1 mr-[10px] w-[350px]',
    //     },
    //     mainProps: {
    //         style: {
    //             width: 'calc(100% - 350px) !important',
    //         },
    //     },
    // },
    // // 表格样式配置
    tableStyle: {
        striped: true,
        border: false,
        compact: true,
        headerStyle: 'gray',
        rowHeight: 'medium',
        rounded: 'md',
        shadow: 'sm',
        responsive: true,
        theme: 'light',
    },

    // 启用默认样式
    enableDefaultStyle: true,

    // VxeTable自适应行高配置
    showOverflow: false,
    rowConfig: {
        isHover: true,
    },
};

export const columns = [
    column.composite('bom_info', 'BOM信息', {
        main: {
            field: 'name',
            formatter: (value) => value || '-',
            props: {
                style: {
                    color: '#3b82f6',
                    cursor: 'pointer',
                },
                onClick: ({ row }) => {
                    currentId.value = row?.id;
                    showDetail.value = true;
                },
            },
        },
        subs: [
            {
                field: 'code',
                formatter: (value) => {
                    return value ? `🔤编码:${value}` : '';
                },
            },
        ],
        layout: 'horizontal',
        actions: [
            {
                icon: 'mdi:pencil',
                permission: ['ops:bom:update'],
                tooltip: '编辑BOM',
                onClick: (row) => {
                    console.log('编辑BOM:', row.row);
                    router.push(`/ops/bom/edit?id=${row?.row.id}`);
                },
            },
            {
                icon: 'mdi:delete',
                tooltip: '删除BOM',
                permission: ['ops:bom:delete'],
                onClick: (row) => {
                    console.log('删除BOM:', row);
                    openModal('delete', row.row);
                },
            },
        ],
        moreActions: {
            type: 'dropdown',
            items: [
                {
                    text: '详情',
                    icon: 'mdi:eye',
                    onClick: ({ row }) => {
                        currentId.value = row?.id;
                        showDetail.value = true;
                    },
                },
                {
                    text: '复制',
                    icon: 'mdi:content-copy',
                    onClick: ({ row }) => {
                        router.push(`/ops/bom/add?id=${row?.id}`);
                    },
                },
                {
                    text: '编辑',
                    icon: 'mdi:pencil',
                    permission: ['ops:bom:update'],
                    onClick: (row) => {
                        router.push(`/ops/bom/edit?id=${row?.row.id}`);
                    },
                },
                {
                    text: '删除',
                    icon: 'mdi:delete',
                    permission: ['ops:bom:delete'],
                    danger: true,
                    onClick: (row) => {
                        console.log('删除BOM', row);
                        openModal('delete', row.row);
                    },
                },
            ],
        },
        fixed: 'left',
    }),
    column.composite('product_info', '产品信息', {
        main: {
            field: 'product.name',
            formatter: (value) => value || '-',
        },
        subs: [
            {
                field: 'product.code',
                formatter: (value) => {
                    return value ? `🔤编码:${value}` : '';
                },
            },
        ],
        layout: 'horizontal',
        fixed: 'left',
    }),
    column.number('height', '长/高(mm)', { width: 150 }),
    column.number('width', '宽(mm)', { width: 150 }),
    column.number('deep', '厚度(mm)', { width: 150 }),
    column.text('bom_version', 'BOM版本'),
    column.boolean('is_quotation', '报价BOM', {
        style: 'tag', //text,tag,switch,badge
        trueText: '是',
        falseText: '否',
        width: 120,
    }),
    column.boolean('is_production', '生产BOM', {
        style: 'tag', //text,tag,switch,badge
        trueText: '是',
        falseText: '否',
        width: 120,
    }),
    column.userMapping('created_by', '创建者'),

    column.userMapping('updated_by', '更新者'),

    column.dateOnly('created_at', '创建时间'),

    column.text('notes', $t('product.table.notes'), { width: undefined, minWidth: 200 }),
];
