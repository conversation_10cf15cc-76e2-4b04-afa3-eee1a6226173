<!-- 选择产品 -->
<template>
    <div class="relative w-full items-center" v-loading="loading">
        <Input
            v-model="inputValue"
            class="pr-10"
            id="search"
            type="text"
            ref="InputRef"
            v-bind="getBind"
        />
        <span
            v-if="isNeedReload"
            title="获取系统单号"
            class="absolute end-0 inset-y-0 flex items-center justify-center px-2 cursor-pointer bg-[#fff] rounded-md shadow-sm top-[1px] right-[2px] bottom-[2px]"
            @click="() => reload()"
        >
            <Icon icon="ant-design:reload-outlined" />
        </span>
    </div>
</template>

<script lang="ts" setup>
import {
    computed,
    nextTick,
    ref,
    unref,
    watch,
    getCurrentInstance,
    onMounted,
} from 'vue';
import { Input } from '@/components/ui/input';
import { Icon } from '@iconify/vue';
import { generateOrderCode } from '@/api/bas/order-code';

defineOptions({
    name: 'SelectWorkmanship',
});

const props = defineProps({
    modelValue: {
        type: String,
    },
    doc_type: {
        type: String,
    },
    immediate: {
        type: Boolean,
        default: true,
    },
    isNeedReload: {
        type: Boolean,
        default: true,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const i = getCurrentInstance();
const inputValue = ref(props.modelValue);
const loading = ref(false);

watch(
    () => props.modelValue,
    (n) => {
        inputValue.value = n;
    }
);

const getBind = computed(() => {
    const attrs = i?.attrs;
    return {
        ...attrs,
        disabled: true,
    };
});

async function reload() {
    if (!props.doc_type) {
        console.error('请配置单据类型 doc_type');
        return;
    }

    if (loading.value) return;

    loading.value = true;

    try {
        const codeRes = await generateOrderCode({ doc_type: props.doc_type });
        emits('update:modelValue', codeRes.code);
        loading.value = false;
    } catch (error) {
        loading.value = false;
    }
}

onMounted(() => {
    if (props.immediate) reload();
});
</script>
