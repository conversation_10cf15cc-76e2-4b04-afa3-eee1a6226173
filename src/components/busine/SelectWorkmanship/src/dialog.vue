<template>
    <div>
        <DialogForm @register="register"> </DialogForm>
    </div>
</template>

<script lang="ts" setup>
import {
    DialogForm,
    useDialogForm,
    BaseTableProps,
    createPluginManager,
    PluginPresets,
    showWorkmanshipItems,
} from '@/components';
import { debounce, isEmpty, isFunction } from 'lodash-es';
import { nextTick, ref, unref } from 'vue';
import {
    getWorkmanshipMetadata,
    getWorkmanshipList,
} from '@/api/bas/workmanship/index';

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    showPlaceholder: {
        type: Boolean,
    },
    extraSearch: {
        type: Object,
    },
});
const emits = defineEmits(['update:modelValue', 'submit', 'update']);

const extraSearch = ref<Recordable>(props.extraSearch);
const originRef = ref<Recordable>();

const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();
const columns = [
    column.text('name', '工序名称', {}),
    column.text('code', '工序编号', {}),

    column.text('factory', '机构', {
        formatter: ({ row }) => {
            return row?.factory?.factory_name || '';
        },
    }),

    column.boolean('valid', '启用', {
        // style: 'switch',
        trueText: '启用',
        falseText: '禁用',
    }),

    column.boolean('is_check', '检验否', {}),
    column.boolean('is_out', '委外否', {}),
    column.boolean('is_handover', '交接否', {}),
    column.boolean('is_multiple', '多品种并行加工否', { width: 160 }),

    column.oneToMany('items', '工艺参数', {
        itemName: '条',
        clickable: true,
        onItemClick: (items, row) => {
            showWorkmanshipItems(row);
        },
        width: 100,
    }),

    column.text('notes', $t('product.table.notes'), { width: undefined }),
];

const [register, api] = useDialogForm({
    dialogProps: {
        title: '选择工序',
        size: 'middle',
        height: 380,
    },
    cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
    },
    fields: [
        {
            layout: 'Card',
            layoutProps: {
                mode: 'empty',
            },
            slots: {
                default: 'bomTable',
            },
        },
        {
            field: 'list',
            component: 'table',
            span: true,
            slots: {
                'table-left': 'table-left',
            },
            componentProps: {
                // 新增：表格样式配置
                tableStyle: {
                    striped: true, // 斑马纹效果
                    border: false, // 只显示水平边框，不显示竖向边框
                    compact: true, // 紧凑布局
                    headerStyle: 'gray', // 灰色表头
                    rowHeight: 'medium', // 中等行高
                    rounded: 'md', // 中等圆角
                    shadow: 'sm', // 小阴影
                    responsive: true, // 响应式设计
                    theme: 'light', // 亮色主题
                },
                // 启用默认样式
                enableDefaultStyle: true,
                // VxeTable自适应行高配置
                showOverflow: false,
                height: 350,
                columnsApi: getWorkmanshipMetadata,

                fetchApi: (
                    pageParams,
                    searchParams,
                    otherParams,
                    reloadParams
                ) => {
                    if (isEmpty(pageParams)) return;

                    const searchConditions =
                        searchParams?.filters?.conditions || [];

                    const extraSearchConditions =
                        extraSearch.value?.filters?.conditions || [];

                    const filters = {
                        conditions: [
                            ...searchConditions,
                            ...extraSearchConditions,
                        ],
                    };

                    const params = {
                        ...(filters.conditions?.length ? { filters } : {}),
                        ...pageParams,
                    };

                    return getWorkmanshipList(params);
                },
                columns: columns,
                on: {
                    cellDblclick: ({ row }) => {
                        emits(
                            'update:modelValue',
                            unref([row]),
                            props.record,
                            originRef.value
                        );
                        emits(
                            'submit',
                            unref([row]),
                            props.record,
                            originRef.value
                        );

                        api?.close();
                    },
                },
                ...props.tableProps,
            } as unknown as BaseTableProps,
        },
    ],
    async submit(...args) {
        const productTableApi = api.getApi('list');

        emits(
            'submit',
            unref(unref(productTableApi.checked)),
            props.record,
            originRef.value
        );
        emits(
            'update:modelValue',
            unref(unref(productTableApi.checked)),
            props.record,
            originRef.value
        );
        return undefined;
    },
});

async function open(obj?: {
    extraSearchParams?: Recordable;
    origin?: Recordable;
}) {
    extraSearch.value = obj?.extraSearchParams || props.extraSearch;
    originRef.value = obj?.origin;
    api?.open();
    await nextTick();
}

defineExpose({
    ...api,
    open,
});
</script>
