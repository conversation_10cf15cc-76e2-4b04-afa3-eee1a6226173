<!-- 选择产品 -->
<template>
    <div class="relative w-full items-center">
        <Input
            v-model="inputValue"
            class="pr-10"
            id="search"
            type="text"
            @input="debouncedOpen"
            ref="InputRef"
        />
        <span
            class="absolute end-0 inset-y-0 flex items-center justify-center px-2 cursor-pointer"
            @click="openDialog"
        >
            <Icon icon="ant-design:search-outlined" />
        </span>
    </div>

    <PopoverEmployee
        ref="PopoverEmployeeRef"
        @submit="handleSelectEmployee"
        :code="code"
        :searchValue="inputValue"
    />
    <DialogEmployee
        ref="DialogEmployeeRef"
        @submit="handleSelectEmployee"
        :code="code"
        :searchValue="inputValue"
        :title="title"
    />
</template>

<script lang="ts" setup>
import { debounce, isFunction } from 'lodash-es';
import { ref, unref, watch } from 'vue';
import { Input } from '@/components/ui/input';
import { Icon } from '@iconify/vue';
import PopoverEmployee from './PopoverEmployee.vue';
import DialogEmployee from './DialogEmployee.vue';

defineOptions({
    name: 'SelectEmployee',
});

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    code: {
        type: String,
    },
    title: {
        type: String,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const InputRef = ref();
const PopoverEmployeeRef = ref();
const DialogEmployeeRef = ref();

const inputValue = ref(props.modelValue);

watch(
    () => props.modelValue,
    (n) => {
        inputValue.value = n;
    }
);

function openPopover() {
    console.log('InputRef', InputRef);
    unref(unref(InputRef).el)?.blur();
    unref(PopoverEmployeeRef).open({ value: inputValue.value });
}

function openDialog() {
    unref(DialogEmployeeRef).open({ value: inputValue.value });
}

const debouncedOpen = debounce((event: Event) => {
    const value = (event.target as HTMLInputElement).value;
    openPopover(); // 传递结构化参数
}, 500);

function handleSelectEmployee(val) {
    emits('update:modelValue', val);
}

defineExpose({
    open,
});
</script>
