<template>
    <Popover v-model:open="open">
        <PopoverTrigger as-child>
            <!-- <Button variant="outline" size="sm" class="w-[150px] justify-start">1
            </Button> -->
            <span></span>
        </PopoverTrigger>
        <PopoverContent class="p-0 w-[580px]" side="right" align="start">
            <VTable @register="register" />
        </PopoverContent>
    </Popover>
</template>

<script lang="ts" setup>
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { VTable, useTable } from '@/components';
import { nextTick, ref, unref } from 'vue';
import { getPageMetaData, getEmployeeList } from '@/api/hr/employee';
import {
    getDictTypeInfoMetadata,
    getDictDataList,
    getDictByCode,
} from '@/api/bas/dict';

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    code: {
        type: String,
    },
    searchValue: {
        type: String,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const searchParams = ref<Recordable>();

const open = ref(false);

const [register, api] = useTable({
    showSearch: false,
    showPagination: false,
    border: false,
    checkType: undefined,
    height: 350,
    columnsApi: getDictTypeInfoMetadata,
    async fetchApi(pagination, search, extraParams, reloadParams) {
        const result = await getDictByCode(props.code);

        return {
            items: result?.filter((item) =>
                item.name.includes(props.searchValue)
            ),
            total: result?.length || 0,
        };
    },
    columns: [
        {
            field: 'name',
            title: '字典数据名称',
        },
        {
            field: 'value',
            title: '字典数据值',
        },
    ],
    on: {
        cellDblclick({ row }) {
            console.log('cellDblclick', row);
            emits('submit', row);
            open.value = false;
        },
    },
});

async function handleOpen(val) {
    searchParams.value = val;
    open.value = true;
    // await nextTick();
    // api.reload();
}

defineExpose({
    ...api,
    open: handleOpen,
});
</script>
