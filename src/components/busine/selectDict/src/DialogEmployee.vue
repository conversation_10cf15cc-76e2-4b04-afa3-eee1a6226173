<template>
    <DialogForm @register="register"> </DialogForm>
</template>

<script lang="ts" setup>
import {
    DialogForm,
    useDialogForm,
    BaseTableProps,
    createPluginManager,
    PluginPresets,
} from '@/components';
import { debounce, isEmpty, isFunction, isNil } from 'lodash-es';
import { nextTick, ref, unref } from 'vue';
import { getPageMetaData, getEmployeeList } from '@/api/hr/employee';
import { NTree } from '@/components/common/NTree';
import { getDeptList } from '@/api/hr/dept';
import { useRequest } from 'alova/client';
import { $t } from '@/locales';
import {
    getDictByCode,
    getDictDataList,
    getDictDataMetadata,
} from '@/api/bas/dict';
import { addDict } from '@/components/common/Select/src/capacity/addDict';

const props = defineProps({
    submit: {
        type: Function,
    },
    getFormData: {
        type: Function,
    },
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
    },
    formatter: {
        type: Function,
    },
    record: {
        type: Object,
    },
    tableProps: {
        type: Object,
    },
    code: {
        type: String,
    },
    searchValue: {
        type: String,
    },
    title: {
        type: String,
    },
});
const emits = defineEmits(['update:modelValue', 'submit']);

const deptData = ref([]);

let originRow: Recordable | undefined = props.record;

const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();
const employeeColumns = [
    {
        field: 'name',
        title: '字典数据名',
        width: '20%',
    },
    {
        field: 'value',
        title: '字典数据值',
        width: '15%',
    },
    {
        field: 'sort',
        title: '排序',
        width: '10%',
    },
    {
        field: 'valid',
        title: '状态',
        formatter: ({ row }) => {
            return row.valid ? '启用' : '禁用';
        },
        width: '10%',
    },
    {
        field: 'notes',
        title: '备注',
        width: '10%',
    },
    column.userMapping('created_by', '创建人', {
        width: '10%',
    }),
    {
        field: 'created_at',
        title: '创建时间',
        width: undefined,
    },
];

const [register, api] = useDialogForm({
    dialogProps: {
        title: props.title,
        size: 'middle',
        height: 380,
        zIndex: 44,
    },
    cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
    },
    fields: [
        {
            layout: 'Card',
            layoutProps: {
                mode: 'empty',
            },
            slots: {
                default: 'bomTable',
            },
        },
        {
            field: 'list',
            component: 'table',
            span: 3,
            slots: {
                'table-left': 'table-left',
            },
            componentProps: {
                // 新增：表格样式配置
                tableStyle: {
                    striped: true, // 斑马纹效果
                    border: false, // 只显示水平边框，不显示竖向边框
                    compact: true, // 紧凑布局
                    headerStyle: 'gray', // 灰色表头
                    rowHeight: 'medium', // 中等行高
                    rounded: 'md', // 中等圆角
                    shadow: 'sm', // 小阴影
                    responsive: true, // 响应式设计
                    theme: 'light', // 亮色主题
                },
                showSearch: false,
                showPagination: false,
                // 启用默认样式
                enableDefaultStyle: true,
                // VxeTable自适应行高配置
                showOverflow: false,
                height: 350,
                toolbarConfig: {
                    isFull: true,
                    isReload: true,
                    isZoom: true,
                },
                checkType: undefined,
                buttonList: [
                    {
                        name: $t('menu.bas.dict.btn.addData'),
                        permission: ['bas:currency:delete'],
                        type: 'button',
                        on: {
                            click: async () => {
                                await addDict(
                                    { code: props.code },
                                    undefined,
                                    {}
                                );

                                const tableApi = api?.getApi('list');
                                tableApi.reload();
                            },
                        },
                    },
                ],
                columnsApi: getDictDataMetadata,
                async fetchApi(pagination, search, extraParams, reloadParams) {
                    const result = await getDictByCode(props.code);
                    const items = result?.filter((item) => {
                        return (
                            isNil(props.searchValue) ||
                            item.name.includes(props.searchValue)
                        );
                    });
                    return {
                        items,
                        total: items?.length || 0,
                    };
                },
                columns: employeeColumns,
                on: {
                    cellDblclick: ({ row }) => {
                        // const tableApi = api?.getApi('list');
                        // const checked = tableApi.checked;

                        emits('update:modelValue', unref([row]), originRow);
                        emits('submit', unref([row]), originRow);

                        if (props.submit && isFunction(props.submit))
                            props.submit(unref([row]), originRow);

                        api?.close();
                    },
                },
                ...props.tableProps,
            } as unknown as BaseTableProps,
        },
    ],
    async submit(...args) {
        await nextTick();
        const tableApi = api?.getApi('list');
        const checked = tableApi.checked;

        emits('update:modelValue', unref(checked), originRow);
        emits('submit', unref(checked), originRow);

        if (props.submit && isFunction(props.submit))
            props.submit(unref(checked), originRow);

        api?.close();
    },
});

function open(params: Recordable) {
    originRow = params;
    api.open();
}

defineExpose({
    ...api,
    open,
});
</script>
