<!-- 选择订单明细 -->
<template>
  <slot name="target" v-bind="getBindParams" />
  <div
    v-if="showPlaceholder"
    class="items-center truncate flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-80 disabled:bg-disabledBg"
    @click="() => open()"
  >
    {{ getFormatter }}
  </div>
  <DialogForm @register="register">
    <template #main>
      <VTable @register="registerOrder">
        <template #table-right>
          <div class="h-full">
            <VTable @register="registerItems" />
          </div>
        </template>
      </VTable>
    </template>
  </DialogForm>
</template>

<script lang="ts" setup>
import { DialogForm, useDialogForm, VTable, useTable } from '@/components'
import { isFunction } from 'lodash-es'
import { computed, nextTick, ref, unref } from 'vue'
import {
  getProductionNoticeMetadata,
  getProductionNoticeList,
  getProductionNotice,
} from '@/api/mes/production_notice'
import { commonTableProps } from '../../SelectOrderItems/src/config'
import { getProductionNoticeDetail } from '@/api/report/production-notice'

const props = defineProps({
  submit: {
    type: Function,
  },
  getFormData: {
    type: Function,
  },
  modelValue: {
    type: String,
  },
  disabled: {
    type: Boolean,
  },
  formatter: {
    type: Function,
  },
  record: {
    type: Object,
  },
  tableProps: {
    type: Object,
  },
  showPlaceholder: {
    type: Boolean,
  },
  extraSearch: {
    type: Object,
  },
})
const emits = defineEmits(['update:modelValue', 'submit'])

const extraSearch = ref<Recordable>(props.extraSearch)
const originRef = ref<Recordable>()

const getFormatter = computed(() => {
  if (isFunction(props.formatter)) {
    return props.formatter(newValue.value, props.record)
  } else {
    return newValue.value
  }
})

const newValue = computed({
  get() {
    try {
      return props.modelValue
    } catch (error) {
      console.error(error)
      return undefined
    }
  },
  set(value) {
    emits('update:modelValue', value)
  },
})

let originRow: Recordable | undefined = props.record

let selectedOrderRecord = ref()

const [register, api] = useDialogForm({
  dialogProps: {
    title: '选择生产通知单物料清单',
    size: 'middle',
    height: 380,
  },
  cols: {
    sm: 1,
    md: 1,
    lg: 1,
    xl: 1,
    '2xl': 1,
  },
  fields: [],
  submit() {
    const checked = itemsApi.getCheckboxRecords(true)
    const o = {
      order: selectedOrderRecord.value,
      product: checked,
    }

    emits('submit', o, props.record, originRef.value)
    emits('update:modelValue', o, props.record, originRef.value)

    api.close()

    return undefined
  },
})

const [registerOrder, orderApi] = useTable({
  height: 400,
  checkType: undefined,
  columns: [],
  data: [{}],
  tableWrapConfig: {
    wrapProps: {
      class: 'flex',
    },
    rightProps: {
      class: 'flex-1 pl-[10px]',
      style: {
        width: 'calc(100% - 200px)',
        overflowY: 'auto',
      },
    },
    mainProps: {
      class: 'basis-[200px]',
      style: {
        flex: '0 0 200px',
      },
    },
  },
  columnsApi: getProductionNoticeMetadata,
  showFields: [
    {
      field: 'notice_no',
      title: '生产单号',
      width: undefined,
    },
  ],
  fetchApi: async (pageParams, searchParams, otherParams, reloadParams) => {
    const searchConditions = searchParams?.filters?.conditions || []

    const extraSearchConditions = extraSearch.value?.filters?.conditions || []

    const filters = {
      conditions: [...searchConditions, ...extraSearchConditions],
    }

    const params = {
      ...(filters.conditions?.length ? { filters } : {}),
      ...pageParams,
    }

    // @ts-ignore
    const res = await getProductionNoticeList(params)

    nextTick(() => {
      const row = res.items?.[0]
      if (!row) return
      orderApi.setCurrentRow(res.items[0])
      itemsApi?.reload(row)
      itemsApi?.clearCheckboxRow()
      selectedOrderRecord.value = { ...row, org_type: '生产通知单' }
    })

    return res
  },
  on: {
    cellClick(record) {
      console.log('itemsApi', record)
      itemsApi?.reload(record.row)
      itemsApi?.clearCheckboxRow()
      selectedOrderRecord.value = {
        ...record.row,
        org_type: '生产通知单',
      }
    },
  },
})
const [registerItems, itemsApi] = useTable({
  ...commonTableProps,
  checkType: 'checkbox',
  height: 400,
  columns: [],
  showSearch: false,
  showPagination: false,
  columnsApi: getProductionNoticeMetadata,
  // @ts-ignore
  fetchApi: async (pagination, search, extraParams, reloadParams) => {
    const params = {
      filters: {
        conditions: [
          {
            field: 'production_notice_id',
            op: 'eq',
            value: reloadParams.id,
          },
        ],
      },

      offset: 0,
      limit: 1000,
    }
    const data = await getProductionNoticeDetail(params)

    return {
      items: data?.items || [],
    }
  },
  showFields: [
    {
      field: 'code',
      title: '产品编码',
      minWidth: 120,
      sortable: true,
    },
    {
      field: 'name',
      title: '产品名称',
      sortable: true,
      minWidth: 120,
      showTooltip: true,
    },
    {
      field: 'specs',
      title: '规格型号',
      sortable: true,
      minWidth: 120,
      showTooltip: true,
    },
    {
      field: 'uom.name',
      title: '单位',
      width: 'auto',
      minWidth: 120,
    },
    {
      field: 'produced_qty',
      title: '数量',
      sortable: true,
      minWidth: 120,
    },
    {
      field: 'width',
      title: '宽度',
      sortable: true,
      minWidth: 120,
    },
    {
      field: 'height',
      title: '高度',
      sortable: true,
      minWidth: 120,
    },
    {
      field: 'deep',
      title: '厚度',
      sortable: true,
      minWidth: 120,
    },
    // {
    //     field: 'width',
    //     title: '宽',
    //     sortable: true,
    //     width: 'auto',
    //     minWidth: 120,
    // },
    // {
    //     field: 'height',
    //     title: '高/长',
    //     sortable: true,
    //     width: 'auto',
    //     minWidth: 120,
    // },
    // {
    //     field: 'deep',
    //     title: '厚度',
    //     sortable: true,
    //     width: 'auto',
    //     minWidth: 120,
    // },
    // {
    //     field: 'color.name',
    //     title: '颜色',
    //     sortable: true,
    //     width: 'auto',
    //     minWidth: 120,
    // },
  ],
})

const open = async (obj?: {
  extraSearchParams?: Recordable
  origin?: Recordable
}) => {
  extraSearch.value = obj?.extraSearchParams || props.extraSearch
  originRef.value = obj?.origin
  api?.open()
  await nextTick()
  orderApi?.reload()
}

const getBindParams = computed(() => {
  return {
    modalValue: newValue.value,
    record: originRow,
    api: {
      ...api,
      open,
    },
  }
})

defineExpose({
  ...api,
  open,
})
</script>
