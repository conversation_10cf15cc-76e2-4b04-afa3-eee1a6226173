import type { FormFieldConfig } from '@/components';
import { z } from 'zod';

export const materialsInfoColumns = [
    {
        field: 'order_no',
        title: '销售订单号',
    },
];

export const materialsInfoFormFieldsConfig: FormFieldConfig[] = [
    {
        field: 'notes',
        label: '备注',
        component: 'textarea',
        validation: z.string().optional(),
    },
];

export const materialsTypeColumnsConfig: Recordable[] = [
    {
        field: 'name',
        title: '全部',
        tree: true,
        headerStyle: { textAlign: 'left' },
        style: { textAlign: 'left' },
    },
];

export const productColumns: Recordable[] = [
    {
        headerType: 'checkbox', //指定表头单元格显示为复选框
        cellType: 'checkbox', //指定body单元格显示为复选框
        field: 'id',
        width: 40,
        minWidth: 40,
    },
    {
        field: 'code',
        title: '产品编码',
        width: 'auto',
        minWidth: 120,
        sort: true,
    },
    {
        field: 'name',
        title: '产品名称',
        sort: true,
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'type.name',
        title: '类别',
        sort: true,
        editor: 'type_editor',
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'valid',
        title: '状态',
        sort: true,
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'pricing_mode',
        title: '计价方式',
        width: 'auto',
        minWidth: 120,
        editor: 'pricing_editor',
    },
    {
        field: 'unit_price',
        title: '单价',
        sort: true,
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'base_unit.name',
        title: '单位',
        sort: true,
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'width',
        title: '宽',
        sort: true,
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'height',
        title: '高/长',
        sort: true,
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'deep',
        title: '厚度',
        sort: true,
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'color.name',
        title: '颜色',
        sort: true,
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'process_route_id',
        title: '工艺路线',
        width: 'auto',
        minWidth: 120,
        sort: true,
    },
    {
        field: 'created_by',
        title: '创建人',
        sort: true,
        width: 'auto',
        minWidth: 120,
    },
    {
        field: 'created_at',
        title: '创建时间',
        width: 'auto',
        minWidth: 120,
    },
];
