<!-- 搜索栏 -->
<template>
    <div
        class="hidden lg:flex flex-shrink-0 flex-grow-0 mr-2 text-title items-center"
    >
        {{ $route.meta.title || '未知页面' }}
    </div>
    <div class="flex justify-between items-center w-full">
        <div class="flex-1">
            <ButtonList
                v-if="props.showButtons && props.btnList.length > 0"
                :btnList="props.btnList"
                :subBtns="props.subBtns"
            />
        </div>
        <div class="flex-1 flex justify-center">
            <Search
                v-if="props.showSearch"
                :fieldsConfig="props.fieldsConfig"
                :metaDataApi="metaDataApi"
                @search="handleSearch"
            />
        </div>
        <div class="flex-1 flex justify-end">
            <Patigation
                v-if="props.showPagination"
                :pagination="paginationData"
                @queryPage="handleQueryPage"
            />
        </div>
    </div>
</template>
<script setup lang="ts">
import Patigation from './Patigation.vue';
import { type PropType, ref, computed, watch } from 'vue';
import ButtonList from './ButtonList.vue';
import Search from './Search/Search.vue';

defineOptions({
    name: 'ToolsBar',
});

// 存储搜索结果
const searchResults = ref<any[]>([]);
// 当前分页信息
const currentPage = ref({
    start: 1,
    end: 20,
});

const props = defineProps({
    btnList: {
        type: Array as PropType<any[]>,
        default: () => [],
    },
    subBtns: {
        type: Array as PropType<any[]>,
        default: () => [],
    },
    pagination: {
        type: Object as PropType<any>,
        default: () => ({
            start: 1,
            end: 20,
            total: 0,
        }),
    },
    /** 列表列配置 */
    fieldsConfig: {
        type: Array as PropType<any[]>,
        default: () => [],
    },
    // 控制各个子组件显示隐藏的属性
    showButtons: {
        type: Boolean,
        default: true,
    },
    showSearch: {
        type: Boolean,
        default: true,
    },
    showPagination: {
        type: Boolean,
        default: true,
    },
    metaDataApi: { type: Function },
});

// 计算分页数据，结合搜索结果和父组件传入的分页信息
const paginationData = computed(() => {
    return {
        start: props.pagination.start,
        end: props.pagination.end,
        total: props.pagination.total,
    };
});

const emit = defineEmits([
    'search',
    'btnClick', // 点击按钮回调
    'queryPage', // 分页查询回调
]);

const filterData = ref<any>({});

// 处理搜索事件
const handleSearch = (searchData: any) => {
    filterData.value = searchData;
    // 重置分页到第一页
    currentPage.value = {
        start: 1,
        end: 20,
    };
    // 将搜索信息抛出
    emitPaginatedResults();
};

// 触发分页查询事件
const handleQueryPage = (newPage: any) => {
    currentPage.value = newPage;
    emitPaginatedResults();
};

// 发送分页后的搜索结果给父组件
const emitPaginatedResults = () => {
    emit('search', {
        pagination: {
            start: currentPage.value.start,
            end: currentPage.value.end,
        },
        // 如果filterData对象为空，则赋值为 undefined，空对象给后端会报错
        filterData:
            Object.keys(filterData.value).length === 0
                ? undefined
                : filterData.value,
    });
};
</script>
