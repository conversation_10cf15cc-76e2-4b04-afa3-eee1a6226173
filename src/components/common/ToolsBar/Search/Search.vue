<template>
    <div ref="searchInputRef" class="relative w-full z-100">
        <div class="flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
            @click="handleClick()">
            <div class="flex flex-wrap flex-1 gap-1">
                <div v-for="(checkedRule, i) in checkedFilters" :key="i + checkedRule.text"
                    class="group hover:bg-white flex items-center rounded bg-muted text-sm">
                    <div class="h-full w-8 bg-[#02a7f0] rounded-md rounded-r-none flex items-center justify-center cursor-pointer hover:bg-[#0283bd]"
                        @click.stop="viewRule(checkedRule, i)">
                        <Filter class="group-hover:hidden h-4 w-4 text-white" />
                        <Settings class="group-hover:block hidden h-4 w-4 text-white" />
                    </div>
                    <div
                        class="group-hover:border group-hover:border-l-0 group-hover:pr-1 h-[26px] pr-[5px] flex-1 flex items-center gap-1 px-1 rounded rounded-l-none">
                        <div class="py-1 truncate max-w-[15rem] text-content" :title="checkedRule.text">
                            {{ checkedRule.text }}
                        </div>
                        <X class="h-3 w-3 cursor-pointer" @click.stop="removeTag(i)" />
                    </div>
                </div>
                <input ref="inputRef" v-model="inputValue"
                    class="flex-1 bg-transparent outline-none min-w-[50px] text-xs"
                    :placeholder="$t('common.placeholder.search')" @focus="handleClick('input')" @keydown="keydownInput"
                    @click.stop />
            </div>
            <ChevronsUpDown class="h-4 w-4 opacity-50" />
        </div>
        <!-- 输入后的下拉 -->
        <Command v-model:open="inputCommandVisible"
            class="absolute top-full z-50 mt-[2px] w-full rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 h-[auto] max-h-[300px] overflow-y-auto"
            :class="{ 'opacity-0': !inputCommandVisible }" @click.stop>
            <CommandInput :hideInput="true" id="input-command-input" :placeholder="$t('common.placeholder.filter')" />
            <CommandList>
                <CommandGroup>
                    <CommandEmpty>
                        <div class="p-6 text-sm text-muted-foreground">
                            {{ $t('common.common.noData') }}
                        </div>
                    </CommandEmpty>
                    <CommandItem v-for="option in columnsSearchConfig" :key="option.name" :value="option.name"
                        @select="toggleInputValue(option)">
                        <div class="flex w-full truncate cursor-pointer">
                            <span>{{ $t('common.button.search') }}</span>
                            <span class="px-1 font-bold">{{
                                option.comment || option.name
                            }}</span>
                            <span>{{ $t('common.search.for') }}</span>
                            <span class="text-[#71639e] font-bold">{{
                                inputValue
                            }}</span>
                        </div>
                    </CommandItem>
                    <CommandItem value="custom" @select="toggleValue('custom')">
                        <div class="w-full cursor-pointer">
                            {{ $t('common.search.addCustomFilter') }}
                        </div>
                    </CommandItem>
                </CommandGroup>
            </CommandList>
        </Command>
        <!-- 规则下拉 -->
        <Command v-model:open="ruleDropdownVisible"
            class="absolute top-full z-50 mt-[2px] w-[120%] left-[-10%] rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 h-[auto] max-h-screen overflow-y-auto"
            :class="{ 'opacity-0': !ruleDropdownVisible }" @click.stop>
            <CommandInput :hideInput="true" id="rule-command-input" :placeholder="$t('common.placeholder.filter')" />
            <CommandList class="max-h-full">
                <div class="flex py-4 pb-1">
                    <div class="flex-1 px-2 border-r">
                        <CommandGroup>
                            <div class="flex items-center gap-2 mb-2">
                                <Filter class="h-4 w-4" />
                                <span>{{
                                    $t('common.placeholder.filter')
                                }}</span>
                            </div>
                            <CommandEmpty>
                                <div class="p-6 text-sm text-muted-foreground">
                                    {{ $t('common.common.noData') }}
                                </div>
                            </CommandEmpty>
                            <div class="max-h-[13rem] overflow-y-auto">
                                <CommandItem class="cursor-pointer" v-for="option in columnsSearchConfig"
                                    :key="option.name" :value="option.name" @select="toggleValue(option)">
                                    <span class="flex-1 truncate">{{
                                        option.comment || option.name
                                    }}</span>
                                </CommandItem>
                            </div>
                        </CommandGroup>
                        <CommandGroup class="border-t">
                            <CommandItem class="cursor-pointer" value="custom" @select="toggleValue('custom')">{{
                                $t('common.search.addCustomFilter') }}
                            </CommandItem>
                        </CommandGroup>
                    </div>
                    <div class="flex-1"></div>
                    <div class="flex-1"></div>
                </div>
            </CommandList>
        </Command>
        <Dialog v-model:visible="editRuleDialog" :title="$t('common.search.filterCriteria')" size="small" showClose
            @close="closeDialog">
            <div class="w-full border-t pt-2 row-1fr">
                <RuleItem :filterData="filterData" :isTop="true" />
                <Button v-if="filterData[0].conditions.length === 0" variant="outline" @click="resetFilterData">{{
                    $t('common.search.addRule') }}</Button>
            </div>

            <template #footer>
                <Button variant="outline" @click="closeDialog">{{
                    $t('common.button.cancel')
                }}</Button>
                <Button :disabled="filterData[0].conditions.length === 0" @click="saveCondition">{{
                    $t('common.button.save') }}</Button>
            </template>
        </Dialog>
    </div>
</template>

<script setup lang="ts">
import {
    computed,
    onMounted,
    onUnmounted,
    provide,
    ref,
    shallowRef,
    watch,
    type PropType,
} from 'vue';
import { ChevronsUpDown, X, Filter, Settings } from 'lucide-vue-next';
import {
    Command,
    CommandEmpty,
    CommandInput,
    CommandGroup,
    CommandItem,
    CommandList,
} from '@/components/ui/command';
import Dialog from '@/components/common/Dialog.vue';
import { useRoute } from 'vue-router';
import { getMetaData } from '@/api/common';
import type { FilterOperatorKey } from '@/types/api/queryParams.ts';
import { FilterOperatorMap } from '@/types/api/queryParams';
import { Button } from '@/components/ui/button';
import RuleItem from '@/components/common/ToolsBar/Search/RuleItem.vue';
import {
    convertConditionsToReadable,
    defaultFilterData,
    findFieldWithParent,
    flattenConditions,
} from './search-contarols';
import { deepClone } from '@/utils/util';
import { useMetaDataStore } from '@/store/metaData';
import { isFunction } from 'lodash-es';

defineOptions({
    name: 'Search',
});

const { fieldsConfig, metaDataPath, metaDataApi } = defineProps({
    fieldsConfig: {
        type: Array as PropType<any[]>,
        default: () => [],
    },
    metaDataPath: {
        type: String,
        default: '',
    },
    metaDataApi: {
        type: Function,
    },
});

const emit = defineEmits(['search']);
// 缓存已选中的 filterData
const checkedFilters = ref<any[]>([]);
const ruleDropdownVisible = ref(false);
// 输入的值
const inputValue = ref('');
// 输入下拉是否显示
const inputCommandVisible = ref(false);
const inputRef = ref<HTMLInputElement>();
// 当前修改的规则索引
const currentEditRuleIndex = ref<null | number>(null);
// 搜索配置
const metaDataConfig = shallowRef<any[]>([]);
// 根据 fieldsConfig 配置生成的搜索配置
const columnsSearchConfig = computed(() => {
    let res = metaDataConfig.value.filter((item) =>
        fieldsConfig.find((col) => col.field === item.name)
    );
    return res;
});

// 筛选弹窗
const editRuleDialog = ref(false);

const filterData = ref<any>(deepClone(defaultFilterData));

const { setMetaData } = useMetaDataStore();

// 点击搜索字段，获取该字段的Sql规则下拉
function toggleValue(data: 'custom' | any) {
    console.log("🚀 ~ toggleValue ~ data:", data)
    if (data === 'custom') {
        ruleDropdownVisible.value = false;
        editRuleDialog.value = true;
        return;
    }
    // 因为是第一次打开，则直接附默认值
    filterData.value[0].conditions[0].field = data.name;
    filterData.value[0].conditions[0].type = data.type;
    setOperatorByField({
        field: data.name,
        id: '0-0',
    });
    ruleDropdownVisible.value = false;
    editRuleDialog.value = true;
}

// 点击输入框中已有规则，进行回显
function viewRule(rule: any, index: number) {
    filterData.value = deepClone(rule.filterData);
    currentEditRuleIndex.value = index;
    editRuleDialog.value = true;
}

// 删除输入框中已选中的规则
function removeTag(index: number) {
    checkedFilters.value.splice(index, 1);
    ruleDropdownVisible.value = false;
    emitSearch();
}

// 点击输入下拉，直接生成点击字段的 ilike（模糊匹配） 输入内容规则
function toggleInputValue(option: any) {
    checkedFilters.value.push({
        filterData: [
            {
                couple: 'and',
                id: '0',
                conditions: [
                    {
                        id: '0-0',
                        field: option.name,
                        operator: 'ilike',
                        value: inputValue.value,
                        operatorList: option.operators.map(
                            (item: FilterOperatorKey) => ({
                                label: FilterOperatorMap[item],
                                value: item,
                            })
                        ),
                        type: option.type,
                        children: [],
                    },
                ],
            },
        ],
        text: `${option.comment} 模糊匹配 ${inputValue.value}`,
    });
    inputValue.value = '';
    inputCommandVisible.value = false;
    emitSearch();
}

function getSearchData() {
    return {};
}

// 打开下拉
function handleClick(type?: string) {
    if (inputCommandVisible.value) return;
    if (type === 'input') {
        if (ruleDropdownVisible.value) return;
        ruleDropdownVisible.value = true;
        return;
    }
    ruleDropdownVisible.value = !ruleDropdownVisible.value;
}
// 监听键盘事件
function keydownInput(e: KeyboardEvent) {
    if (e.key === 'ArrowDown') {
        focusDropdown();
    }
}
function focusDropdown() {
    if (ruleDropdownVisible.value) {
        const commandInput = document.getElementById(
            'rule-command-input'
        ) as HTMLInputElement;
        commandInput.focus();
    } else if (inputCommandVisible.value) {
        const commandInput = document.getElementById(
            'input-command-input'
        ) as HTMLInputElement;
        commandInput.focus();
    }
}

// 根据 field 获取字段配置
function getDataByField(field: string) {
    return metaDataConfig.value.find((item) => item.name === field) || {};
}

// 根据 id 获取当前字段支持的Sql规则下拉
function setOperatorByField(ruleItem: any) {
    const data = getDataByField(ruleItem.field);
    // 赋值该字段的数值类型，方便后期识别转换
    ruleItem.type = data.type;
    // 将 Sql 规则转换为下拉选项
    const operatorsOptions = data.operators.map((item: FilterOperatorKey) => ({
        label: FilterOperatorMap[item],
        value: item,
    }));
    const res = findFieldWithParent(ruleItem.id, filterData.value);
    if (res?.target) {
        res.target.operatorList = operatorsOptions;
        // 默认 SQL 规则为第一个
        res.target.operator = operatorsOptions[0].value;
    }
}

/** 字段改变，如果field为空，则不进行操作 */
function fieldChange(ruleItem: any) {
    if (!ruleItem.field) return;
    setOperatorByField(ruleItem);
}

/** 关闭弹窗 */
const closeDialog = () => {
    editRuleDialog.value = false;
    currentEditRuleIndex.value = null;
    resetFilterData();
};

/** 重置 filterData */
const resetFilterData = () => {
    filterData.value = deepClone(defaultFilterData);
};

/** 保存筛选条件 */
function saveCondition() {
    let resText = convertConditionsToReadable(
        filterData.value,
        metaDataConfig.value
    );
    resText = resText.replace(/^\(|\)$/g, ''); // 去除首尾的括号
    // 如果已选中的规则，则修改已选中的规则
    if (currentEditRuleIndex.value !== null) {
        checkedFilters.value[currentEditRuleIndex.value] = {
            filterData: deepClone(filterData.value),
            text: resText,
        };
        currentEditRuleIndex.value = null;
    } else {
        checkedFilters.value.push({
            filterData: deepClone(filterData.value),
            text: resText,
        });
    }
    editRuleDialog.value = false;
    emitSearch();
}

const searchData = ref();

function emitSearch() {
    let arr: any[] = [];
    checkedFilters.value.forEach((item) => {
        arr.push(flattenConditions(item.filterData)[0]);
    });
    if (arr.length > 0) {
        emit('search', {
            couple: 'and',
            conditions: arr,
        });
        searchData.value = arr;
    } else {
        emit('search', {});
        searchData.value = {};
    }

    resetFilterData();
}

/** == 规则的增删 =================================================== */
/** 新增规则 */
const addRule = (curRule: any, index: number) => {
    let res = findFieldWithParent(curRule.id, filterData.value);
    if (res && res.parent && res.target) {
        let newCondition = {
            ...res.target,
            id: `${res.parent.id}-${index + 1}`,
            value: '',
            children: [],
        };
        res.parent.conditions.push(newCondition);
    }
};
/** 新增子集规则 */
const addChildRule = (curRule: any) => {
    let res = findFieldWithParent(curRule.id, filterData.value);
    if (res && res.target) {
        let newCondition = {
            id: '',
            field: curRule.field,
            operator: curRule.operator,
            value: '',
            operatorList: curRule.operatorList,
            type: '',
            children: [],
        };
        // 如果子集已经有值了，则向子集的 conditions 中添加新的规则
        if (res.target.children.length > 0) {
            newCondition.id = `${res.target.id}-0-${res.target.children[0].conditions.length}`;
            res.target.children[0].conditions.push(newCondition);
        } else {
            newCondition.id = `${res.target.id}-0-0`;
            res.target.children.push({
                couple: 'and',
                id: `${res.target.id}-0`,
                conditions: [newCondition],
            });
        }
    }
};
/** 删除规则 */
const deleteRule = (curRule: any) => {
    let res = findFieldWithParent(curRule.id, filterData.value);
    if (res && res.parent && res.target) {
        res.parent.conditions.splice(
            res.parent.conditions.indexOf(res.target),
            1
        );
    }
};
// 设置规则逻辑
const setCouple = (curRule: any, couple: 'and' | 'or') => {
    const res = findFieldWithParent(curRule.id, filterData.value);
    if (res && res.target) {
        res.target.couple = couple;
    }
};
provide('ruleHandles', {
    addRule,
    addChildRule,
    deleteRule,
    fieldChange,
    setCouple,
});
provide('metaDataConfig', metaDataConfig);
provide('fieldsConfig', fieldsConfig);

/** == 输入下拉逻辑 =================================================== */
// 输入下拉是否显示
watch(
    () => inputValue.value,
    (newVal: string) => {
        if (newVal) {
            inputCommandVisible.value = true;
            ruleDropdownVisible.value = false;
        } else {
            inputCommandVisible.value = false;
        }
    }
);

/** 页面路径映射 */
const pathMap = {
    '/bas/order-code': '/sys/code_setting',
    '/bas/currency': '/bas/currency',
    '/bas/dict': '/bas/dict_type',
    '/ops/materials-list': '/ops/material_variant',
};

/** == 获取页面搜索配置 =================================================== */
const route = useRoute();
const getSearchConfig = async () => {
    let urlPre = metaDataPath || pathMap[route.path as keyof typeof pathMap];
    let data;
    if (metaDataApi && isFunction(metaDataApi)) {
        data = await metaDataApi();
    } else {
        data = await getMetaData(urlPre || route.path);
    }
    setMetaData(urlPre || route.path, data);
    data.fields.forEach((item: any) => {
        if (!item.comment) {
            item.comment = fieldsConfig.find(
                (col) => col.field === item.name
            )?.label;
        }
        item.label = item.comment;
        item.value = item.name;
    });
    metaDataConfig.value = data.fields.filter((item: any) => item.comment);
};

/** == 点击组件外关闭下拉显示 =================================================== */
const searchInputRef = ref<HTMLDivElement>();
const handleClickOutside = (event: MouseEvent) => {
    if (!searchInputRef.value) return;
    if (!searchInputRef.value.contains(event.target as Node)) {
        ruleDropdownVisible.value = false;
        inputCommandVisible.value = false;
        inputValue.value = '';
    }
};
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    getSearchConfig();
});
onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>
