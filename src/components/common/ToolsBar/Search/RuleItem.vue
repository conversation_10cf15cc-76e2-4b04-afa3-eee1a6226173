<template>
    <div
        class="w-full"
        :class="{ 'pl-3': !isTop }"
        v-for="(filterItem, filterIndex) in filterData"
        :key="filterIndex + 'filter-item'"
    >
        <div class="flex items-center" v-if="filterItem.conditions.length > 0">
            <span v-if="isTop">{{ $t('common.search.rule.label') }}</span>
            <Popover v-model:open="popoverOpen">
                <PopoverTrigger as-child>
                    <div
                        class="inline-block flex items-center gap-1 cursor-pointer hover:bg-gray-100 px-1 color-[#6c757d]"
                    >
                        <span>{{
                            coupleMap[
                                filterItem.couple as keyof typeof coupleMap
                            ]
                        }}</span>
                        <div
                            class="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-[#6c757d] transition-transform duration-200"
                            :class="{ 'rotate-[180deg]': popoverOpen }"
                        ></div>
                    </div>
                </PopoverTrigger>
                <PopoverContent class="w-26 p-2">
                    <div class="flex flex-col gap-1">
                        <Button
                            variant="link"
                            @click="handleSetCouple(filterItem, 'and')"
                            >{{ $t('common.search.rule.options.all') }}</Button
                        >
                        <Button
                            variant="link"
                            @click="handleSetCouple(filterItem, 'or')"
                            >{{ $t('common.search.rule.options.any') }}</Button
                        >
                    </div>
                </PopoverContent>
            </Popover>
            <span>{{ $t('common.search.rule.ruleGroup') }}</span>
        </div>
        <div
            v-for="(rule, ruleIndex) in filterItem.conditions"
            :key="ruleIndex + 'rule-item'"
        >
            <div class="flex items-center gap-2 py-2">
                <div class="flex-1">
                    <Select
                        v-model="rule.field"
                        :options="metaDataConfig"
                        showFilter
                        :customField="{ value: 'name', label: 'comment' }"
                        @change="ruleHandles.fieldChange(rule)"
                    />
                </div>
                <div class="flex-1">
                    <Select
                        v-model="rule.operator"
                        :options="rule.operatorList"
                    />
                </div>
                <div class="flex-1" v-if="rule.field">
                    <template v-if="renderComponentNum === 1">
                        <component
                            :is="resolveComponent(rule)"
                            v-model="rule.value"
                            v-bind="resolveComponentProps(rule)"
                        />
                    </template>
                    <template v-if="renderComponentNum === 2">
                        <div class="flex items-center gap-1">
                            <component
                                :is="resolveComponent(rule)"
                                v-model="rule.value[0]"
                                v-bind="resolveComponentProps(rule)"
                            />
                            -
                            <component
                                :is="resolveComponent(rule)"
                                v-model="rule.value[1]"
                                v-bind="resolveComponentProps(rule)"
                            />
                        </div>
                    </template>
                </div>
                <div class="flex items-center gap-2 py-2">
                    <Plus
                        class="cursor-pointer h-5 w-5"
                        @click="ruleHandles.addRule(rule, ruleIndex)"
                    />
                    <Network
                        class="cursor-pointer h-4 w-4"
                        @click="ruleHandles.addChildRule(rule, ruleIndex)"
                    />
                    <Trash
                        class="cursor-pointer h-4 w-4"
                        @click="ruleHandles.deleteRule(rule, ruleIndex)"
                    />
                </div>
            </div>
            <template v-if="rule.children && rule.children.length > 0">
                <RuleItem :filterData="rule.children" />
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { inject, ref, type PropType } from 'vue';
import { Plus, Trash, Network } from 'lucide-vue-next';
import Select from '@/components/common/FormEngine/FormComponents/Select.vue';
import {
    searchControls,
    type SearchControlsKeys,
} from '@/components/common/ToolsBar/Search/search-contarols';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { $t } from '@/locales';

defineOptions({
    name: 'RuleItem',
});

const { filterData, isTop } = defineProps({
    filterData: {
        type: Array as PropType<any>,
        default: () => [],
    },
    isTop: {
        type: Boolean,
        default: false,
    },
});

// 事件注入
const ruleHandles: any = inject('ruleHandles');
const metaDataConfig: any = inject('metaDataConfig');
const fieldsConfig: any = inject('fieldsConfig');
const popoverOpen = ref(false);
// 弹窗数据
const coupleMap = {
    and: $t('common.search.rule.options.all'),
    or: $t('common.search.rule.options.any'),
};
const handleSetCouple = (curRule: any, couple: 'and' | 'or') => {
    ruleHandles.setCouple(curRule, couple);
    popoverOpen.value = false;
};
const typeToComponentMap = {
    string: 'input',
    integer: 'numberInput',
    float: 'numberInput',
    boolean: 'select',
    enum: 'select',
    date: 'datePicker',
    datetime: 'datePicker',
};
const renderComponentNum = ref(1);
// 一个输入框的 operator
const oneComponentOperators = ['eq', 'ne', 'gt', 'ge', 'lt', 'le', 'in'];
// 两个输入框的 operator
const twoComponentOperators = ['between'];
// 没有输入框的 operator
const noneComponentOperators = ['is_null', 'not_null'];

// 存放上次的条件值类型用于比较，防止不同类型组件的 value 赋值报错
const oldType = ref('');
// 根据传入的组件名称，动态解析组件
const resolveComponent = (ruleItem: any) => {
    if (!ruleItem.field) return;
    // 如果上次的条件值类型和当前的类型不一致而且 value 有值，则置空 value，防止不同类型组件的 value 赋值报错
    if (oldType.value && ruleItem.value && oldType.value !== ruleItem.type) {
        ruleItem.value = '';
    }
    oldType.value = ruleItem.type;
    // 如果 field 或 operator 发生变化，则置空 value，防止不同类型组件的 value 赋值报错
    const cur =
        metaDataConfig.value.find(
            (item: any) => item.name === ruleItem.field
        ) || {};
    // 根据 operator 确定渲染几个输入框
    if (oneComponentOperators.includes(ruleItem.operator)) {
        if (Array.isArray(ruleItem.value)) {
            ruleItem.value = '';
        }
        renderComponentNum.value = 1;
    } else if (twoComponentOperators.includes(ruleItem.operator)) {
        if (!Array.isArray(ruleItem.value)) {
            ruleItem.value = [null, null];
        }
        // 如果是日期选框，则渲染一个日期范围选择器
        if (cur.type === 'datetime' || cur.type === 'date') {
            renderComponentNum.value = 1;
            return searchControls.dateRangerPicker;
        } else {
            renderComponentNum.value = 2;
        }
    } else if (noneComponentOperators.includes(ruleItem.operator)) {
        renderComponentNum.value = 0;
        ruleItem.value = null;
    }
    return searchControls[
        typeToComponentMap[
            cur.type as keyof typeof typeToComponentMap
        ] as SearchControlsKeys
    ];
};

// 根据 field 获取组件的 props
const resolveComponentProps = (ruleItem: any) => {
    const cur =
        metaDataConfig.value.find(
            (item: any) => item.name === ruleItem.field
        ) || {};
    const curFieldConfig =
        fieldsConfig.find((item: any) => item.field === ruleItem.field) || {};
    if (
        typeToComponentMap[cur.type as keyof typeof typeToComponentMap] ===
        'input'
    ) {
        return {
            ...curFieldConfig.componentProps,
            placeholder: `${$t('common.placeholder.enter')} ${cur.comment}`,
        };
    }
    if (
        typeToComponentMap[cur.type as keyof typeof typeToComponentMap] ===
        'numberInput'
    ) {
        return {
            ...curFieldConfig.componentProps,
            min: 0,
            placeholder: `${$t('common.placeholder.enter')} ${cur.comment}`,
        };
    }
    return {
        ...curFieldConfig.componentProps,
        placeholder: `${$t('common.placeholder.select')} ${cur.comment}`,
    };
};
</script>
