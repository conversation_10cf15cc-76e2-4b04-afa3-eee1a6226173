<template>
    <div class="flex">
        <!-- PC端按钮布局 - 在md及以上屏幕显示 -->
        <div class="hidden lg:flex flex-1 flex-wrap items-center">
            <Button
                v-for="btnItem in btnList"
                :key="btnItem.eventName"
                :variant="
                    btnItem.variant === 'primary' ? 'default' : btnItem.variant
                "
                @click="handleBtnClick(btnItem.eventName)"
                v-permission="btnItem.permission"
                class="mr-2 px-2 text-[12px] flex items-center gap-1"
                :disabled="btnDisabled(btnItem.disabled)"
            >
                <!-- 渲染图标 -->
                <img
                    v-if="btnItem.icon"
                    :src="btnItem.icon"
                    width="14"
                    height="14"
                />
                {{ btnItem.name }}
            </Button>
            <DropdownMenu v-if="subBtns?.length">
                <DropdownMenuTrigger as-child>
                    <Button variant="outline" class="px-3 py-2 text-[12px]">
                        {{ $t('common.button.more') }}
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent class="min-w-[100px]">
                    <DropdownMenuItem
                        class="cursor-pointer hover:bg-gray-100 px-3 py-2 text-[12px]"
                        v-for="subBtn in subBtns"
                        :key="subBtn.eventName"
                        @click="handleBtnClick(subBtn.eventName)"
                        v-permission="subBtn.permission"
                    >
                        {{ subBtn.name }}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>

        <!-- 移动端按钮布局 - 只在小于md屏幕显示 -->
        <div class="lg:hidden flex-1 flex items-center">
            <DropdownMenu v-if="allButtons.length > 0">
                <DropdownMenuTrigger as-child>
                    <Button
                        variant="outline"
                        class="px-3 py-2 text-[12px] flex items-center gap-1"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-4 w-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
                            />
                        </svg>
                        {{ $t('common.button.more') }}
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                    class="min-w-[120px] max-h-[300px] overflow-y-auto"
                >
                    <DropdownMenuItem
                        class="cursor-pointer hover:bg-gray-100 px-3 py-2 text-[12px] flex items-center gap-2"
                        v-for="btn in allButtons"
                        :key="btn.eventName"
                        @click="handleBtnClick(btn.eventName)"
                        v-permission="btn.permission"
                        :disabled="btnDisabled(btn.disabled)"
                    >
                        <img
                            v-if="btn.icon"
                            :src="btn.icon"
                            width="14"
                            height="14"
                        />
                        {{ btn.name }}
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { inject, computed } from 'vue';
import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

// 获取父组件提供的actionHandler
export type BtnActionHandler = {
    handleAction: (action: string, payload?: any) => void;
};
const btnActionHandler = inject<BtnActionHandler>('btnActionHandler');

// 按钮展示风格
type VARIANT =
    | 'default'
    | 'primary'
    | 'secondary'
    | 'outline'
    | 'ghost'
    | 'link'
    | 'destructive';
// 按钮配置
export interface ButtonItem {
    /** 按钮名称 */
    name?: string;
    /** 按钮点击事件 */
    eventName?: string;
    /** 按钮展示风格 */
    variant?: VARIANT;
    /** 按钮权限控制 */
    permission?: string[];
    /** 按钮是否禁用 */
    disabled?: boolean | ((...args: any[]) => boolean);
    /** 按钮图标组件 */
    icon?: any;
}

const { btnList, subBtns } = defineProps({
    btnList: {
        type: Array as PropType<ButtonItem[]>,
        default: () => [],
    },
    subBtns: {
        type: Array as PropType<ButtonItem[]>,
        default: () => [],
    },
});

// 判断按钮是否禁用
const btnDisabled = (
    disabled: boolean | ((...args: any[]) => boolean) | undefined
) => {
    if (!disabled) return false;
    if (typeof disabled === 'function') {
        return disabled();
    }
    return disabled;
};

// 触发按钮点击事件 - 优化版
const handleBtnClick = (eventName: string | undefined) => {
    if (eventName && btnActionHandler) {
        btnActionHandler.handleAction(eventName);
    } else {
        console.warn('按钮事件名称未定义或actionHandler未注入');
    }
};

// 计算所有按钮
const allButtons = computed(() => [...btnList, ...(subBtns || [])]);
</script>
