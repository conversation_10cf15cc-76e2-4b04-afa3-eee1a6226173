<template>
    <div class="flex h-full items-center">
        <input
            v-model.number="newPage.start"
            @blur="handleBlur($event, 'start')"
            class="outline-none bg-transparent text-center border-b border-slate-200 w-fit min-w-[20px] max-w-[40px]"
        />
        -
        <input
            v-model.number="newPage.end"
            @blur="handleBlur($event, 'end')"
            class="outline-none bg-transparent text-center border-b border-slate-200 w-fit min-w-[20px] max-w-[40px]"
        />
        <div class="px-1 flex items-center">
            {{ $t('common.pagination.total') }}
            <span class="text-blue-500 px-1">{{ pagination.total }}</span>
            {{ $t('common.pagination.records') }}
        </div>
        <!-- 上一页 -->
        <div
            class="ml-2 cursor-pointer py-1 px-[3px] bg-[#006be6] border border-slate-200"
            :class="[
                newPage.start <= 1
                    ? 'bg-black/10 cursor-not-allowed text-gray-500'
                    : '',
            ]"
            @click="handlePrePage"
        >
            <span>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                >
                    <path
                        fill="none"
                        :stroke="newPage.start <= 1 ? '#717171' : '#FFFFFF'"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="m15 18l-6-6l6-6"
                    />
                </svg>
            </span>
        </div>
        <!-- 下一页 -->
        <div
            class="ml-1 cursor-pointer py-1 px-[3px] bg-[#006be6] border border-slate-200"
            :class="[
                newPage.end >= pagination.total
                    ? 'bg-black/10 cursor-not-allowed text-gray-500'
                    : '',
            ]"
            @click="handleNextPage"
        >
            <span>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                >
                    <path
                        fill="none"
                        :stroke="
                            newPage.end >= pagination.total
                                ? '#717171'
                                : '#FFFFFF'
                        "
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="m9 18l6-6l-6-6"
                    />
                </svg>
            </span>
        </div>
    </div>
</template>

<script setup lang="ts">
/**
 * 分页工具组件 (Pagination Tools)
 *
 * 这个组件实现了一个自定义的分页控制器，允许用户:
 * 1. 直接输入起始和结束记录编号
 * 2. 使用上一页/下一页按钮进行导航
 * 3. 显示总记录数
 *
 * 特点:
 * - 支持范围分页（显示从第x条到第y条）而不是传统的页码分页
 * - 自动验证输入值的合法性
 * - 保持分页区间大小一致（除非到达边界）
 * - 在输入无效时自动恢复到上一个有效值
 */
import { computed, reactive, ref, watch, type PropType } from 'vue';

defineOptions({
    name: 'PaginationTools',
});

// 定义分页相关的接口
export interface Page {
    start: number; // 当前显示的起始记录编号
    end: number; // 当前显示的结束记录编号
}
export interface Pagination extends Page {
    total: number; // 总记录数
}
export type PageKeyType = keyof Page;

// 组件属性定义
const { pagination } = defineProps({
    pagination: {
        type: Object as PropType<Pagination>,
        default: () => ({
            start: 1, // 默认从第1条开始
            end: 20, // 默认显示到第10条
            total: 1000, // 默认总共1000条记录
        }),
    },
});

// 当前分页状态，用于UI显示和交互
const newPage = ref<Page>({
    start: pagination.start,
    end: pagination.total < pagination.end ? pagination.total : pagination.end,
});

// 用于存放计算后的分页值
const countModelPage = reactive<Page>({
    start: pagination.start,
    end: pagination.end,
});

// 存储上一次有效的分页值，用于在输入无效时恢复
const oldPage = ref<Page>({
    start: pagination.start,
    end: pagination.end,
});

// 监听pagination属性变化，同步更新内部状态
watch(
    () => pagination,
    (newVal: Pagination) => {
        newPage.value.start = newVal.start;
        newPage.value.end =
            newVal.total < newVal.end ? newVal.total : newVal.end;
    },
    { deep: true }
);

// 计算当前分页区间大小（显示多少条记录），用于保持翻页时区间一致
const diffNumber = ref(pagination.end - pagination.start + 1);

// 定义事件
const emit = defineEmits(['queryPage']);

/**
 * 处理输入框失去焦点事件
 * 验证输入值的合法性，并在必要时恢复到上一个有效值
 */
const handleBlur = (event: any, type: PageKeyType) => {
    // 输入框内容为空时返回上一次的值
    if (event.target.value == '') {
        newPage.value.end = oldPage.value.end;
        newPage.value.start = oldPage.value.start;
    }

    let number = Number(event.target.value);
    // 如果start大于end，则返回上一次的值
    if (type === 'start' && number > newPage.value.end) {
        newPage.value.start = oldPage.value.start;
    }
    // 非数字、数字小于1、数字大于总条数均返回上一次的值
    if (Number.isNaN(number) || number < 1 || number > pagination.total) {
        event.target.value = oldPage.value[type];
    }
    diffNumber.value = newPage.value.end - newPage.value.start + 1;
    countModelPage.start = newPage.value.start;
    countModelPage.end = newPage.value.end;
    // 如果数值有变动，则触发父组件的回调
    if (
        newPage.value.start !== oldPage.value.start ||
        newPage.value.end !== oldPage.value.end
    ) {
        emit('queryPage', newPage.value);
    }
    oldPage.value[type] = number;
};

/**
 * 处理点击上一页按钮
 * 保持分页区间大小一致，除非到达边界（第1条记录）
 */
const handlePrePage = () => {
    if (newPage.value.start === 1) return;
    countModelPage.start = countModelPage.start - diffNumber.value;
    countModelPage.end = countModelPage.end - diffNumber.value;
    // 如果上一页的数量小于差值diffNumber，则展示 1 到 (start - 1)
    if (newPage.value.start < diffNumber.value) {
        newPage.value.end = newPage.value.start - 1;
        newPage.value.start = 1;
    } else {
        newPage.value.start = countModelPage.start;
        newPage.value.end = countModelPage.end;
    }
    oldPage.value.start = newPage.value.start;
    oldPage.value.end = newPage.value.end;
    emit('queryPage', {
        start: Math.max(newPage.value.start, 1),
        end: newPage.value.end,
    });
};

/**
 * 处理点击下一页按钮
 * 保持分页区间大小一致，除非到达边界（最后一条记录）
 */
const handleNextPage = () => {
    if (newPage.value.end === pagination.total) return;
    countModelPage.start = countModelPage.start + diffNumber.value;
    countModelPage.end = countModelPage.end + diffNumber.value;
    // 如果下一页的数量小于差值diffNumber，则展示 (end + 1) 到 total
    if (pagination.total - newPage.value.end < diffNumber.value) {
        newPage.value.start = newPage.value.end + 1;
        newPage.value.end = pagination.total;
    } else {
        newPage.value.start = countModelPage.start;
        newPage.value.end = countModelPage.end;
    }
    oldPage.value.start = newPage.value.start;
    oldPage.value.end = newPage.value.end;
    emit('queryPage', {
        start: newPage.value.start,
        end: Math.min(newPage.value.end, pagination.total),
    });
};
</script>
