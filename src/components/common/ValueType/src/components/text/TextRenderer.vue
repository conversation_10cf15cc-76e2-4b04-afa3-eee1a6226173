<template>
    <span
        class="text-renderer"
        :class="textClass"
        :title="showTooltip ? fullText : undefined"
        @click="handleClick"
    >
        {{ displayText }}
    </span>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes';
import { computed, PropType } from 'vue';
import { TextProps } from './type';

const props = defineProps({
    modelValue: {
        type: [Number, String],
    },
    record: {
        type: Object,
    },

    maxLength: propTypes.number, // 最大长度
    ellipsis: propTypes.bool, // 是否显示省略号
    showTooltip: propTypes.bool, // 是否显示完整内容的提示
    nullDisplay: propTypes.string, // null值显示
    placeholder: propTypes.string, // 占位符
    copyable: propTypes.bool, // 是否可复制
    searchable: propTypes.bool, // 是否可搜索
    highlight: {
        type: [String, Object] as PropType<TextProps['highlight']>,
    }, // 高亮文本
    case: {
        type: String as PropType<TextProps['case']>,
    }, // 文本大小写转换
    formatter: {
        type: Function as PropType<TextProps['formatter']>,
    },
    onClick: {
        type: Function as PropType<TextProps['onClick']>,
    },
});

// 获取原始文本值
const rawValue = computed(() => {
    let value = props.modelValue;

    // 处理 null/undefined 值
    if (value === null || value === undefined || value === '') {
        return props.nullDisplay || props.placeholder || '';
    }

    return String(value);
});

// 应用大小写转换
const caseTransformed = computed(() => {
    const text = rawValue.value;
    const caseType = props.case;

    switch (caseType) {
        case 'upper':
            return text.toUpperCase();
        case 'lower':
            return text.toLowerCase();
        case 'capitalize':
            return text.replace(/\b\w/g, (l) => l.toUpperCase());
        default:
            return text;
    }
});

// 完整文本
const fullText = computed(() => {
    return caseTransformed.value;
});

// 显示文本（处理长度限制）
const displayText = computed(() => {
    const text = fullText.value;
    const maxLength = props.maxLength;

    if (maxLength && text.length > maxLength) {
        return props.ellipsis !== false
            ? text.slice(0, maxLength) + '...'
            : text.slice(0, maxLength);
    }

    // 如果有自定义格式化函数
    if (props.formatter && typeof props.formatter === 'function') {
        return props.formatter(props.record);
    }

    return text;
});

// 是否显示提示
const showTooltip = computed(() => {
    if (props.showTooltip === false) return false;

    const text = fullText.value;
    const maxLength = props.maxLength;

    // 当文本被截断时显示完整内容
    return maxLength && text.length > maxLength;
});

// 样式类
const textClass = computed(() => {
    const value = rawValue.value;

    return {
        'null-value': !value,
        copyable: props.copyable,
        searchable: props.searchable,
        highlighted: !!props.highlight,
        truncated: showTooltip.value,
    };
});

// 点击处理
const handleClick = (event: Event) => {
    // 如果可复制
    if (props.copyable) {
        navigator.clipboard?.writeText(fullText.value);
    }

    // 自定义点击处理
    if (props.onClick && typeof props.onClick === 'function') {
        props.onClick(props.record, event);
    }
};
</script>

<style scoped>
.text-renderer {
    display: inline-block;
    word-break: break-word;
    line-height: 1.4;
}

.text-renderer.null-value {
    color: #9ca3af;
    font-style: italic;
}

.text-renderer.copyable {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.text-renderer.copyable:hover {
    background-color: #f3f4f6;
    border-radius: 4px;
    padding: 2px 4px;
    margin: -2px -4px;
}

.text-renderer.searchable {
    position: relative;
}

.text-renderer.highlighted {
    background-color: #fef3c7;
    padding: 1px 2px;
    border-radius: 2px;
}

.text-renderer.truncated {
    cursor: help;
}
</style>
