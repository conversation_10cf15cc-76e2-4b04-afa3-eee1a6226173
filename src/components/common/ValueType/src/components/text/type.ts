export interface TextProps {
    maxLength?: number; // 最大长度
    ellipsis?: boolean; // 是否显示省略号
    showTooltip?: boolean; // 是否显示完整内容的提示
    nullDisplay?: string; // null值显示
    placeholder?: string; // 占位符
    copyable?: boolean; // 是否可复制
    searchable?: boolean; // 是否可搜索
    highlight?: string | RegExp; // 高亮文本
    case?: 'upper' | 'lower' | 'capitalize'; // 文本大小写转换
    formatter?: (record: Recordable) => void;
    onClick?: (record: Recordable, event: Event) => void;
}
