<script setup lang="ts">
import { propTypes } from '@/utils/propTypes';
import { computed, PropType } from 'vue';
import { BooleanProps } from './type';

const props = defineProps({
    modelValue: {
        type: [Number, String, Boolean],
    },
    record: {
        type: Object,
    },

    trueText: propTypes.string,
    falseText: propTypes.string,
    nullText: propTypes.string,
    showIcon: propTypes.bool,
    style: {
        type: String as PropType<BooleanProps['style']>,
    },
    onChange: {
        type: Function as PropType<BooleanProps['onChange']>,
    },
});

const emit = defineEmits<{
    (e: 'change', value: boolean): void;
}>();

// 计算布尔值
const boolValue = computed(() => {
    const val = props.modelValue;
    if (val === null || val === undefined) {
        return null;
    }
    return val === true || val === 1 || val === '1' || val === 'true';
});

// 获取显示文本
const displayText = computed(() => {
    if (boolValue.value === null) {
        return props.nullText || '-';
    }
    return boolValue.value ? props.trueText || '是' : props.falseText || '否';
});

// 获取显示样式
const displayStyle = computed(() => {
    return props.style || 'text';
});

// 处理值变化
const handleChange = (newValue: boolean) => {
    emit('change', newValue);

    if (props.onChange && typeof props.onChange === 'function') {
        props.onChange(newValue, props.record);
    }
};

// 计算样式类
const textClass = computed(() => {
    if (boolValue.value === null) {
        return 'text-gray-500';
    }
    return boolValue.value ? 'text-green-600' : 'text-red-600';
});

const tagClass = computed(() => {
    if (boolValue.value === null) {
        return 'bg-gray-100 text-gray-600';
    }
    return boolValue.value
        ? 'bg-green-100 text-green-800'
        : 'bg-red-100 text-red-800';
});
</script>

<template>
    <div class="boolean-renderer">
        <!-- 标签样式 -->
        <span
            v-if="displayStyle === 'tag'"
            :class="[
                'inline-flex  px-2 py-1 rounded-full text-xs font-medium',
                tagClass,
            ]"
        >
            {{ displayText }}
        </span>

        <!-- 开关样式 -->
        <input
            v-else-if="displayStyle === 'switch'"
            type="checkbox"
            :checked="boolValue === true"
            :disabled="boolValue === null"
            @change="handleChange(($event.target as any).checked)"
            class="toggle-switch"
        />

        <!-- 纯文本样式 -->
        <span v-else :class="['font-medium', textClass]">
            {{ displayText }}
        </span>
    </div>
</template>

<style scoped>
.boolean-renderer {
    display: inline-flex;
    align-items: center;
    width: 100%;
}

.toggle-switch {
    width: 40px;
    height: 20px;
    appearance: none;
    background-color: #ccc;
    border-radius: 10px;
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s;
}

.toggle-switch:checked {
    background-color: #4ade80;
}

.toggle-switch::before {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    left: 2px;
    transition: transform 0.2s;
}

.toggle-switch:checked::before {
    transform: translateX(20px);
}

.toggle-switch:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
</style>
