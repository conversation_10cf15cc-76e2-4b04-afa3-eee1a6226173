export interface StatusConfig {
    value: any; // 状态值
    label: string; // 显示文本
    color?: string; // 颜色
    bgColor?: string; // 背景色
    icon?: string; // 图标
    dot?: boolean; // 是否显示圆点
}

export interface StatusProps {
    statusMap?: Record<string | number, StatusConfig>; // 状态映射
    defaultStatus?: StatusConfig; // 默认状态
    size?: 'small' | 'medium' | 'large'; // 尺寸
    variant?: 'filled' | 'outlined' | 'text' | 'dot'; // 变体样式
    shape?: 'rounded' | 'square' | 'pill'; // 形状
    showIcon?: boolean; // 是否显示图标
    showDot?: boolean; // 是否显示状态点
}
