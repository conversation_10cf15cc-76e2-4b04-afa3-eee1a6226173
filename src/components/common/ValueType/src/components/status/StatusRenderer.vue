<template>
    <span
        v-if="statusConfig"
        class="status-renderer"
        :class="statusClass"
        :style="statusStyle"
    >
        <!-- 状态点 -->
        <span v-if="showStatusDot" class="status-dot" :style="dotStyle"></span>

        <!-- 图标 -->
        <i
            v-if="showStatusIcon && statusConfig.icon"
            :class="statusConfig.icon"
            class="status-icon"
        ></i>

        <!-- 文本 -->
        <span class="status-text">{{ statusConfig.label }}</span>
    </span>

    <!-- 未找到状态配置时的默认显示 -->
    <span v-else class="status-renderer status-unknown">
        {{ rawValue || '-' }}
    </span>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes';
import { get } from 'lodash-es';
import { computed, PropType } from 'vue';
import { StatusProps } from './type';

interface StatusConfig {
    value: any;
    label: string;
    color?: string;
    bgColor?: string;
    icon?: string;
    dot: boolean;
}

const props = defineProps({
    modelValue: {},
    record: {
        type: Object,
    },

    statusMap: {
        type: Object as PropType<StatusProps['statusMap']>,
    }, // 状态映射
    defaultStatus: {
        type: Object as PropType<StatusProps['defaultStatus']>,
    }, // 默认状态
    size: {
        type: String as PropType<StatusProps['size']>,
    }, // 尺寸
    variant: {
        type: String as PropType<StatusProps['variant']>,
    }, // 变体样式
    shape: {
        type: String as PropType<StatusProps['shape']>,
    }, // 形状
    showIcon: propTypes.bool, // 是否显示图标
    showDot: propTypes.bool, // 是否显示状态点
});

// 获取原始值
const rawValue = computed(() => {
    return props.modelValue;
});

// 获取状态配置
const statusConfig = computed((): StatusConfig | null => {
    const value = rawValue.value;
    const statusMap = props.statusMap || {};

    // 直接匹配
    // @ts-ignore
    if (statusMap[value]) {
        // @ts-ignore
        return statusMap[value];
    }

    // 字符串化匹配
    if (statusMap[String(value)]) {
        // @ts-ignore
        return statusMap[String(value)];
    }

    // 返回默认状态
    if (props.defaultStatus) {
        // @ts-ignore
        return props.defaultStatus;
    }

    return null;
});

// 是否显示状态点
const showStatusDot = computed(() => {
    if (props.variant === 'dot') return true;
    if (props.showDot) return true;
    return statusConfig.value?.dot || false;
});

// 是否显示图标
const showStatusIcon = computed(() => {
    if (props.showIcon === false) return false;
    return !!statusConfig.value?.icon;
});

// 状态样式类
const statusClass = computed(() => {
    const size = props.size || 'medium';
    const variant = props.variant || 'filled';
    const shape = props.shape || 'rounded';

    return {
        [`status-size-${size}`]: true,
        [`status-variant-${variant}`]: true,
        [`status-shape-${shape}`]: true,
        'has-icon': showStatusIcon.value,
        'has-dot': showStatusDot.value,
    };
});

// 状态样式
const statusStyle = computed(() => {
    const config = statusConfig.value;
    if (!config) return {};

    const style: any = {};

    if (config.color) {
        style.color = config.color;
    }

    if (config.bgColor) {
        const variant = props.variant || 'filled';
        if (variant === 'filled') {
            style.backgroundColor = config.bgColor;
        } else if (variant === 'outlined') {
            style.borderColor = config.bgColor;
            style.color = config.bgColor;
        }
    }

    return style;
});

// 状态点样式
const dotStyle = computed(() => {
    const config = statusConfig.value;
    if (!config?.bgColor) return {};

    return {
        backgroundColor: config.bgColor,
    };
});
</script>

<style scoped>
.status-renderer {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    white-space: nowrap;
    transition: all 0.2s;
}

/* 尺寸 */
.status-size-small {
    padding: 2px 6px;
    font-size: 11px;
    gap: 3px;
}

.status-size-medium {
    padding: 3px 8px;
    font-size: 12px;
    gap: 4px;
}

.status-size-large {
    padding: 4px 12px;
    font-size: 13px;
    gap: 5px;
}

/* 变体 */
.status-variant-filled {
    color: white;
    border: 1px solid transparent;
}

.status-variant-outlined {
    background-color: transparent;
    border: 1px solid #d1d5db;
}

.status-variant-text {
    background-color: transparent;
    border: none;
    padding: 0;
}

.status-variant-dot {
    background-color: transparent;
    border: none;
    padding: 0;
    color: #374151;
}

/* 形状 */
.status-shape-rounded {
    border-radius: 4px;
}

.status-shape-square {
    border-radius: 0;
}

.status-shape-pill {
    border-radius: 999px;
}

/* 状态点 */
.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-size-small .status-dot {
    width: 5px;
    height: 5px;
}

.status-size-large .status-dot {
    width: 7px;
    height: 7px;
}

/* 图标 */
.status-icon {
    font-size: 1em;
    flex-shrink: 0;
}

/* 文本 */
.status-text {
    line-height: 1;
}

/* 未知状态 */
.status-unknown {
    color: #9ca3af;
    font-style: italic;
    font-weight: normal;
}

/* 默认状态颜色 */
.status-variant-filled:not([style*='background-color']) {
    background-color: #6b7280;
}

.status-variant-outlined:not([style*='border-color']) {
    border-color: #6b7280;
    color: #6b7280;
}
</style>
