export interface AvatarProps {
    nameField?: string; // 姓名字段，用于生成初始字母
    emailField?: string; // 邮箱字段，用于生成 Gravatar
    sizeField?: string; // 尺寸字段
    size?: number | 'small' | 'medium' | 'large'; // 尺寸
    shape?: 'circle' | 'square' | 'rounded'; // 形状
    defaultSrc?: string; // 默认头像
    fallbackType?: 'letter' | 'icon' | 'image'; // 回退类型
    fallbackIcon?: string; // 回退图标
    fallbackBg?: string; // 回退背景色
    showName?: boolean; // 是否显示姓名
    namePosition?: 'right' | 'bottom'; // 姓名位置
    borderColor?: string; // 边框颜色
    borderWidth?: number; // 边框宽度
    clickable?: boolean; // 是否可点击
    onClick?: (row: any) => void; // 点击事件
}
