<template>
    <div class="group-renderer" @click="handleClick">
        <template v-if="!modelValue"></template>
        <template v-else>
            <Tag v-bind="getBind" />
        </template>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, ref } from 'vue';
import Tag from '@/components/common/Tag/src/index.vue';
import { getDict, getDictAsync } from '@/utils/dict';
import { isFunction } from 'lodash-es';

const props = defineProps<{
    modelValue: {
        type: String;
    };
    record: {
        type: Object;
    };
}>();

const dictOptions = ref<any[]>([]); // 用于存储字典数据

onMounted(() => {
    const { dict } = currentInstance.attrs || {};

    // 假设getDictAsync返回一个Promise
    getDict(dict).then((ops) => {
        dictOptions.value = ops;
    });
});

const currentInstance = getCurrentInstance();

const getBind = computed(() => {
    const { dict, effect, round, size } = currentInstance.attrs || {};
    const ops = dictOptions.value; // 使用当前存储的字典数据
    const cell = props?.modelValue;
    if (!cell) return;

    const item = ops?.find((item) => item.name == cell);
    if (!item) {
        return {
            label: cell,
            type: 'info',
        };
    }

    return {
        label: item.name,
        type: item.type || item.list_class,
        effect,
        round,
        size,
    };
});

const handleClick = () => {
    const { onClick } = column;
    if (isFunction(onClick)) {
        onClick(props.params);
    }
};
</script>

<style scoped>
.avatar-renderer {
    display: flex;
    align-items: center;
    gap: 8px;
}

.avatar-renderer.name-position-bottom {
    flex-direction: column;
    gap: 4px;
}

.avatar-renderer.avatar-clickable {
    cursor: pointer;
}

.avatar-renderer.avatar-clickable:hover .avatar-container {
    transform: scale(1.05);
}

.avatar-container {
    position: relative;
    overflow: hidden;
    transition: transform 0.2s ease;
    flex-shrink: 0;
}

.avatar-shape-circle .avatar-container {
    border-radius: 50%;
}

.avatar-shape-square .avatar-container {
    border-radius: 0;
}

.avatar-shape-rounded .avatar-container {
    border-radius: 8px;
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.avatar-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.avatar-letter {
    line-height: 1;
    font-size: 0.75em;
}

.avatar-icon {
    font-size: 0.6em;
}

.avatar-name {
    font-size: 13px;
    color: #374151;
    font-weight: 500;
    line-height: 1.2;
}

.avatar-name.name-right {
    text-align: left;
}

.avatar-name.name-bottom {
    text-align: center;
    font-size: 11px;
}

/* 小尺寸调整 */
.avatar-renderer:has(.avatar-container[style*='32px']) .avatar-fallback {
    font-size: 12px;
}

.avatar-renderer:has(.avatar-container[style*='32px']) .avatar-letter {
    font-size: 10px;
}

.avatar-renderer:has(.avatar-container[style*='32px']) .avatar-icon {
    font-size: 14px;
}

/* 大尺寸调整 */
.avatar-renderer:has(.avatar-container[style*='48px']) .avatar-fallback {
    font-size: 16px;
}

.avatar-renderer:has(.avatar-container[style*='48px']) .avatar-letter {
    font-size: 18px;
}

.avatar-renderer:has(.avatar-container[style*='48px']) .avatar-icon {
    font-size: 20px;
}
</style>
