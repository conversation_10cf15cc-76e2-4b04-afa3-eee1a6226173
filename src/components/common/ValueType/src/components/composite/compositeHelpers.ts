// 导入核心工具函数
import {
    getFieldValue,
    formatFieldValue,
} from '@/components/common/Table/core/column';
// 导入类型定义
import type { CompositeSubConfig } from './types';

/**
 * 智能字段解析器
 */
function smartFieldParser(
    record: any,
    fieldPath: string,
    metadata?: {
        type?: string;
        enum_info?: { enum_values?: Record<string, string> };
        relation_info?: { related_model?: string; direction?: string };
    },
    options: any = {}
): string {
    const value = getFieldValue(record, fieldPath);

    if (!metadata) {
        return formatFieldValue(value, 'string', options);
    }

    return formatFieldValue(value, metadata.type, {
        ...options,
        enumMap: metadata.enum_info?.enum_values,
    });
}

/**
 * 关系数据显示函数
 */
function relationDisplay(
    record: any,
    relationPath: string,
    options: {
        displayField?: string;
        template?: string;
        fallbackFields?: string[];
        showCount?: boolean;
        nullText?: string;
    } = {}
): string {
    const {
        displayField = 'name',
        template,
        fallbackFields = ['title', 'code', 'id'],
        showCount = false,
        nullText = '-',
    } = options;

    const relationData = getFieldValue(record, relationPath);

    if (!relationData) {
        return nullText;
    }

    // 处理数组关系
    if (Array.isArray(relationData)) {
        if (showCount) {
            return `${relationData.length}项`;
        }
        return relationData
            .map((item) => getDisplayText(item, displayField, fallbackFields))
            .join(', ');
    }

    // 处理单个关系对象
    const displayText = getDisplayText(
        relationData,
        displayField,
        fallbackFields
    );

    if (template) {
        return template.replace(
            /\$\{(\w+)\}/g,
            (_, key) => relationData[key] || ''
        );
    }

    return displayText;
}

/**
 * 获取显示文本
 */
function getDisplayText(
    obj: any,
    displayField: string,
    fallbackFields: string[]
): string {
    if (!obj) return '';

    // 优先使用指定的显示字段
    if (obj[displayField]) {
        return obj[displayField];
    }

    // 使用备选字段
    for (const field of fallbackFields) {
        if (obj[field]) {
            return obj[field];
        }
    }

    return obj.toString();
}

/**
 * 枚举数据显示函数
 */
function enumDisplay(
    value: any,
    enumMap: Record<string, string>,
    options: {
        showOriginal?: boolean;
        template?: string;
        nullText?: string;
    } = {}
): string {
    const { showOriginal = false, template, nullText = '-' } = options;

    if (value === null || value === undefined) {
        return nullText;
    }

    const text = enumMap[value] || value;

    if (template) {
        return template.replace('${text}', text).replace('${value}', value);
    }

    if (showOriginal && enumMap[value]) {
        return `${text} (${value})`;
    }

    return text;
}

/**
 * 创建计算字段配置
 */
function computedField(
    fields: string[],
    formula: (values: any[], record: any) => any,
    options: Partial<CompositeSubConfig> = {}
): CompositeSubConfig {
    return {
        ...options,
        computed: {
            fields,
            formula,
            cache: options.computed?.cache ?? true,
        },
    };
}

/**
 * 创建模板字段配置
 */
function templateField(
    template: string,
    field?: string,
    options: Partial<CompositeSubConfig> = {}
): CompositeSubConfig {
    return {
        ...options,
        field,
        template,
    };
}

/**
 * 创建条件显示字段配置
 */
function conditionalField(
    condition: (record: any) => boolean,
    config: CompositeSubConfig
): CompositeSubConfig {
    return {
        ...config,
        condition,
    };
}

/**
 * 百分比计算辅助函数
 */
function percentage(
    numeratorField: string,
    denominatorField: string,
    options: {
        template?: string;
        precision?: number;
        style?: any;
    } = {}
): CompositeSubConfig {
    const { template = '占比: ${value}%', precision = 2, style } = options;

    return computedField(
        [numeratorField, denominatorField],
        (values) => {
            const [numerator, denominator] = values.map((v) => Number(v) || 0);
            if (denominator === 0) return '0.00';
            return ((numerator / denominator) * 100).toFixed(precision);
        },
        {
            template,
            style: { color: '#52c41a', ...style },
        }
    );
}

/**
 * 日期差计算辅助函数
 */
function dateDiff(
    startField: string,
    endField: string,
    options: {
        template?: string;
        unit?: 'days' | 'hours' | 'minutes';
        style?: any;
    } = {}
): CompositeSubConfig {
    const { template = '${value}${unit}', unit = 'days', style } = options;

    const unitMap = {
        days: '天',
        hours: '小时',
        minutes: '分钟',
    };

    return computedField(
        [startField, endField],
        (values) => {
            const [start, end] = values.map((v) => new Date(v));
            if (isNaN(start.getTime()) || isNaN(end.getTime())) return '0';

            const diff = end.getTime() - start.getTime();
            let result: number;

            switch (unit) {
                case 'hours':
                    result = Math.floor(diff / (1000 * 60 * 60));
                    break;
                case 'minutes':
                    result = Math.floor(diff / (1000 * 60));
                    break;
                default: // days
                    result = Math.floor(diff / (1000 * 60 * 60 * 24));
            }

            return result.toString();
        },
        {
            template: template.replace('${unit}', unitMap[unit]),
            style: { color: '#fa8c16', ...style },
        }
    );
}

/**
 * 基于元数据的智能字段配置
 */
function smartField(
    fieldPath: string,
    metadata?: {
        type?: string;
        enum_info?: { enum_values?: Record<string, string> };
        relation_info?: { related_model?: string; direction?: string };
        comment?: string;
    },
    options: Partial<CompositeSubConfig> = {}
): CompositeSubConfig {
    const config: CompositeSubConfig = {
        field: fieldPath,
        smartParse: {
            metadata,
            options: {
                nullText: '-',
                ...options.smartParse?.options,
            },
        },
        ...options,
    };

    // 根据元数据类型设置默认配置
    if (metadata?.type) {
        switch (metadata.type) {
            case 'enum':
                config.enum = {
                    enumMap: metadata.enum_info?.enum_values || {},
                    ...options.enum,
                };
                break;

            case 'relation':
                config.relation = {
                    showCount:
                        metadata.relation_info?.direction ===
                        'RelationshipDirection.ONETOMANY',
                    ...options.relation,
                };
                break;

            case 'datetime':
            case 'date':
                config.smartParse!.options!.dateFormat =
                    metadata.type === 'datetime'
                        ? 'YYYY-MM-DD HH:mm'
                        : 'YYYY-MM-DD';
                break;

            case 'float':
            case 'decimal':
                config.smartParse!.options!.precision = 2;
                break;
        }
    }

    return config;
}

/**
 * 关系字段配置
 */
function relationField(
    fieldPath: string,
    displayField?: string,
    options: {
        template?: string;
        fallbackFields?: string[];
        showCount?: boolean;
        metadata?: any;
        style?: any;
    } = {}
): CompositeSubConfig {
    return {
        field: fieldPath,
        relation: {
            displayField,
            template: options.template,
            fallbackFields: options.fallbackFields,
            showCount: options.showCount,
        },
        smartParse: {
            metadata: { type: 'relation', ...options.metadata },
        },
        style: options.style,
    };
}

// 导出复合列工具对象
export const compositeHelpers = {
    // 基础工具
    percentage,
    dateDiff,

    // 数据解析工具
    getFieldValue,
    formatFieldValue,
    smartFieldParser,
    relationDisplay,
    enumDisplay,

    // 智能字段配置
    smartField,
    relationField,
    computedField,
    templateField,
    conditionalField,
};
