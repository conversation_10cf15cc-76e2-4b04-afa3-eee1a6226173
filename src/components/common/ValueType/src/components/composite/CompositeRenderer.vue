<template>
    <div
        class="composite-cell"
        :class="{
            'composite-horizontal': isHorizontal,
            'composite-vertical': isVertical,
        }"
    >
        <!-- 图标部分 -->
        <div v-if="hasIcon" class="composite-icon">
            <Icon
                v-if="icon?.type === 'icon'"
                :icon="icon.iconName"
                class="composite-icon-item"
            />
        </div>

        <!-- 文本内容部分 -->
        <div class="composite-content">
            <!-- 主数据 -->
            <div class="composite-main" v-bind="itemBind(main)">
                {{ getMainText() }}
            </div>

            <!-- 辅助数据 -->
            <div v-if="shouldShowSubs" class="composite-subs">
                <span
                    v-for="(sub, index) in getDisplayedSubs()"
                    :key="index"
                    class="composite-sub-item"
                    :style="sub.style"
                    v-bind="itemBind(sub)"
                >
                    {{ getSubText(sub, index) }}
                </span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue';
import { Icon } from '@iconify/vue';
import { compositeHelpers } from './compositeHelpers';
import {
    CompositeColumnOptions,
    CompositeMainConfig,
    CompositeSubConfig,
    CompositeIconConfig,
} from './types';
import { get, isFunction } from 'lodash-es';

// Props 定义
const props = defineProps({
    modelValue: {
        type: Object,
    },
    record: {
        type: Object,
    },
    main: {
        type: Object as PropType<CompositeMainConfig>,
    },
    subs: {
        type: Object as PropType<CompositeSubConfig[]>,
    },
    layout: {
        type: String as PropType<CompositeColumnOptions['layout']>,
    },
    separator: {
        type: Object as PropType<CompositeColumnOptions['separator']>,
    },
    icon: {
        type: Object as PropType<CompositeIconConfig>,
    },
    subLayout: {
        type: Object as PropType<CompositeColumnOptions['subLayout']>,
    },
    containerStyle: {
        type: Object as PropType<CompositeColumnOptions['containerStyle']>,
    },
    interactive: {
        type: Object as PropType<CompositeColumnOptions['interactive']>,
    },
});

// 计算属性
const isHorizontal = computed(() => props.layout !== 'vertical');
const isVertical = computed(() => props.layout === 'vertical');
const hasIcon = computed(() => !!props.icon);
const shouldShowSubs = computed(() => props.subs && props.subs.length > 0);

const itemBind = computed(() => {
    return (item) => {
        if (!item) return {};

        return Object.entries(item).reduce((acc, [key, value]) => {
            if (key.startsWith('on') && isFunction(value)) {
                // 包装事件处理函数，传入 props.params
                acc[key] = () => value(props.record);
            } else {
                // 普通属性直接赋值
                acc[key] = value;
            }
            return acc;
        }, {});
    };
});

// 获取主文本
function getMainText(): string {
    if (!props.main?.field) return '';

    const value = get(props.modelValue || {}, props.main.field);

    if (props.main.formatter) {
        return props.main.formatter(value, props.record);
    }

    return compositeHelpers.formatFieldValue(value);
}

// 获取显示的辅助数据
function getDisplayedSubs() {
    if (!props.subs) return [];

    return props.subs.filter((sub) => {
        if (sub.condition) {
            return sub.condition(props.modelValue);
        }
        return true;
    });
}

// 获取辅助文本
function getSubText(sub: any, index: number): string {
    // 计算字段
    if (sub.computed) {
        const values = sub.computed.fields.map((field) =>
            compositeHelpers.getFieldValue(props.modelValue, field)
        );
        const result = sub.computed.formula(values, props.modelValue);

        if (sub.template) {
            return sub.template.replace('${value}', result);
        }
        return result;
    }

    // 普通字段
    if (sub.field) {
        const value = compositeHelpers.getFieldValue(
            props.modelValue,
            sub.field
        );

        if (sub.formatter) {
            return sub.formatter(value, props.record);
        }

        if (sub.template) {
            return sub.template.replace('${value}', value);
        }

        // 智能解析
        if (sub.smartParse?.metadata) {
            return compositeHelpers.smartFieldParser(
                props.modelValue,
                sub.field,
                sub.smartParse.metadata,
                sub.smartParse.options || {}
            );
        }

        // 关系字段
        if (sub.relation) {
            return compositeHelpers.relationDisplay(
                props.modelValue,
                sub.field,
                sub.relation
            );
        }

        // 枚举字段
        if (sub.enum?.enumMap) {
            return compositeHelpers.enumDisplay(value, sub.enum.enumMap, {
                showOriginal: sub.enum.showOriginal,
                template: sub.enum.template,
            });
        }

        return compositeHelpers.formatFieldValue(value);
    }

    // 模板字段
    if (sub.template) {
        return sub.template;
    }

    return '';
}
</script>

<style scoped>
.composite-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.composite-vertical {
    flex-direction: column;
    align-items: flex-start;
}

.composite-icon {
    flex-shrink: 0;
}

.composite-icon-item {
    width: 16px;
    height: 16px;
}

.composite-content {
    flex: 1;
    min-width: 0;
}

.composite-main {
    font-weight: 500;
    color: var(--primary-text-color, #333);
}

.composite-subs {
    display: flex;
    gap: 8px;
    margin-top: 2px;
    flex-wrap: wrap;
}

.composite-vertical .composite-subs {
    flex-direction: column;
    gap: 2px;
}

.composite-sub-item {
    font-size: 12px;
    color: var(--secondary-text-color, #666);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
