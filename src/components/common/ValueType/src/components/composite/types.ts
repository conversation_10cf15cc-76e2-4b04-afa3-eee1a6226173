import type { BaseColumn } from '../../src/types';

export interface CompositeSubConfig {
  field?: string;
  computed?: {
    fields: string[];
    formula: (values: any[], record: any) => any;
    cache?: boolean;
  };
  template?: string;
  formatter?: (value: any, record: any) => string;
  smartParse?: {
    metadata?: {
      type?: string;
      enum_info?: { enum_values?: Record<string, string> };
      relation_info?: { related_model?: string; direction?: string };
    };
    options?: {
      dateFormat?: string;
      precision?: number;
      currencyCode?: string;
      nullText?: string;
      showOriginal?: boolean;
    };
  };
  relation?: {
    displayField?: string;
    template?: string;
    fallbackFields?: string[];
    showCount?: boolean;
  };
  enum?: {
    enumMap?: Record<string, string>;
    showOriginal?: boolean;
    template?: string;
  };
  style?: any;
  condition?: (record: any) => boolean;
  onClick?: (value: any, record: any) => void;
  tooltip?: boolean | string | ((value: any, record: any) => string);
}

export interface CompositeMainConfig {
  field?: string;
  formatter?: (value: any, record: any) => string;
  style?: any;
  tooltip?: boolean | string | ((value: any, record: any) => string);
}

export interface CompositeIconConfig {
  field?: string;
  type?: 'icon' | 'image' | 'avatar';
  iconName?: string;
  avatarField?: string;
  imageField?: string;
  style?: any;
  size?: number | string;
  position?: 'left' | 'right' | 'top' | 'bottom';
}

export interface CompositeColumnOptions extends BaseColumn {
  main?: CompositeMainConfig;
  subs?: CompositeSubConfig[];
  separator?: {
    main?: string;
    subs?: string;
    vertical?: string;
  };
  icon?: CompositeIconConfig;
  layout?: 'horizontal' | 'vertical' | 'mixed';
  subLayout?: {
    main?: 'horizontal' | 'vertical';
    subs?: 'horizontal' | 'vertical';
  };
  containerStyle?: any;
  interactive?: {
    hover?: {
      showAll?: boolean;
      highlight?: boolean;
    };
    click?: {
      expandable?: boolean;
      onExpand?: (record: any) => void;
    };
  };
  actions?: Array<{
    icon?: string;
    tooltip?: string;
    condition?: (row: any) => boolean;
    onClick?: (row: any) => void;
  }>;
  moreActions?: {
    type?: 'group';
    items?: Array<{
      text: string;
      icon?: string;
      danger?: boolean;
      condition?: (row: any) => boolean;
      onClick?: (row: any) => void;
    }>;
  };
} 