export interface LinkProps {
    href?: string | ((row: any) => string); // 链接地址
    target?: '_blank' | '_self' | '_parent' | '_top'; // 打开方式
    textField?: string; // 显示文本字段
    iconField?: string; // 图标字段
    icon?: string; // 固定图标
    iconPosition?: 'left' | 'right'; // 图标位置
    underline?: boolean; // 是否显示下划线
    color?: string; // 链接颜色
    hoverColor?: string; // 悬停颜色
    disabled?: boolean | ((row: any) => boolean); // 是否禁用
    onClick?: (row: any, event: Event) => void; // 点击事件
    onHover?: (row: any, event: Event) => void; // 悬停事件
    maxLength?: number; // 最大显示长度
    ellipsis?: boolean; // 超长省略
    tooltip?: boolean; // 是否显示提示
}
