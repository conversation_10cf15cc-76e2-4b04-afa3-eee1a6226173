<template>
    <div class="link-renderer">
        <!-- 当数据为空时，不显示任何内容 -->
        <template v-if="rawText">
            <a
                v-if="linkHref && !isDisabled"
                :href="linkHref"
                :target="target"
                class="link-content"
                :class="linkClass"
                :style="linkStyle"
                :title="showTooltip ? fullText : undefined"
                @click="handleClick"
                @mouseenter="handleHover"
            >
                <!-- 左侧图标 -->
                <Icon
                    v-if="showIcon && iconPosition === 'left'"
                    :icon="linkIcon"
                    class="link-icon left"
                />

                <!-- 文本内容 -->
                <span class="link-text">{{ displayText }}</span>

                <!-- 右侧图标 -->
                <Icon
                    v-if="showIcon && iconPosition === 'right'"
                    :icon="linkIcon"
                    class="link-icon right"
                />
            </a>

            <!-- 禁用状态 -->
            <span
                v-else
                class="link-disabled"
                :title="showTooltip ? fullText : undefined"
            >
                <!-- 左侧图标 -->
                <Icon
                    v-if="showIcon && iconPosition === 'left'"
                    :icon="linkIcon"
                    class="link-icon left disabled"
                />

                <!-- 文本内容 -->
                <span class="link-text">{{ displayText }}</span>

                <!-- 右侧图标 -->
                <Icon
                    v-if="showIcon && iconPosition === 'right'"
                    :icon="linkIcon"
                    class="link-icon right disabled"
                />
            </span>
        </template>

        <!-- 数据为空时显示占位符 -->
        <span v-else class="link-empty">-</span>
    </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue';
import { Icon } from '@iconify/vue';
import { LinkProps } from './type';

const props = defineProps({
    modelValue: {
        type: String,
    },
    record: {
        type: Object,
    },

    href: {
        type: [String, Function] as PropType<LinkProps['href']>,
    }, // 链接地址
    target: {
        type: String as PropType<LinkProps['target']>,
    }, // 打开方式
    textField: {
        type: String as PropType<LinkProps['textField']>,
    }, // 显示文本字段
    iconField: {
        type: String as PropType<LinkProps['iconField']>,
    }, // 图标字段
    icon: {
        type: String as PropType<LinkProps['icon']>,
    }, // 固定图标
    iconPosition: {
        type: String as PropType<LinkProps['iconPosition']>,
    }, // 图标位置
    underline: {
        type: Boolean,
    }, // 是否显示下划线
    color: {
        type: String,
    }, // 链接颜色
    hoverColor: {
        type: String,
    }, // 悬停颜色
    disabled: {
        type: [Boolean, Function] as PropType<LinkProps['disabled']>,
    }, // 是否禁用
    onClick: {
        type: Function as PropType<LinkProps['onClick']>,
    }, // 点击事件
    onHover: {
        type: Function as PropType<LinkProps['onClick']>,
    }, // 悬停事件
    maxLength: {
        type: Number,
    }, // 最大显示长度
    ellipsis: {
        type: Boolean,
    }, // 超长省略
    tooltip: {
        type: Boolean,
    }, // 是否显示提示
});

// 原始文本
const rawText = computed(() => {
    return props.modelValue || '';
});

// 完整文本
const fullText = computed(() => {
    return String(rawText.value);
});

// 显示文本
const displayText = computed(() => {
    const text = fullText.value;
    const maxLength = props.maxLength;

    if (maxLength && text.length > maxLength) {
        return text.slice(0, maxLength) + '...';
    }

    return text;
});

// 链接地址
const linkHref = computed(() => {
    const href = props.href;

    if (typeof href === 'function') {
        return href(props.record);
    }

    if (typeof href === 'string') {
        return href;
    }

    // 默认使用字段值
    return rawText.value;
});

// 目标窗口
const target = computed(() => {
    return props.target || '_blank';
});

// 是否禁用
const isDisabled = computed(() => {
    const disabled = props.disabled;

    if (typeof disabled === 'function') {
        return disabled(props.record);
    }

    return disabled === true || !linkHref.value;
});

// 是否显示图标
const showIcon = computed(() => {
    // 如果没有数据，则不显示图标
    if (!rawText.value) {
        return false;
    }

    // 如果链接被禁用或没有有效链接，则不显示图标
    if (isDisabled.value || !linkHref.value) {
        return false;
    }

    return !!(props.icon || props.iconField);
});

// 图标
const linkIcon = computed(() => {
    return props.icon || '';
});

// 图标位置
const iconPosition = computed(() => {
    return props.iconPosition || 'left';
});

// 是否显示下划线
const showUnderline = computed(() => {
    return props.underline !== false;
});

// 是否显示提示
const showTooltip = computed(() => {
    return (
        props.tooltip !== false &&
        ((props.maxLength && fullText.value.length > props.maxLength) ||
            fullText.value !== displayText.value)
    );
});

// 样式类
const linkClass = computed(() => {
    return {
        'has-underline': showUnderline.value,
        'no-underline': !showUnderline.value,
        'has-icon': showIcon.value,
        [`icon-${iconPosition.value}`]: showIcon.value,
    };
});

// 样式
const linkStyle = computed(() => {
    const style: any = {};

    if (props.color) {
        style.color = props.color;
    }

    return style;
});

// 方法
function handleClick(event: Event) {
    if (props.onClick) {
        event.preventDefault();
        props.onClick(props.record, event);
    }
}

function handleHover(event: Event) {
    if (props.onHover) {
        props.onHover(props.record, event);
    }
}
</script>

<style scoped>
.link-renderer {
    display: inline-block;
}

.link-content {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    color: #3b82f6;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: inherit;
    line-height: inherit;
}

.link-content.has-underline {
    text-decoration: underline;
}

.link-content.no-underline {
    text-decoration: none;
}

.link-content:hover {
    color: #2563eb;
    text-decoration: underline;
}

.link-content:active {
    color: #1d4ed8;
}

.link-disabled {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    color: #9ca3af;
    cursor: not-allowed;
    text-decoration: none;
}

.link-empty {
    color: #9ca3af;
    font-style: italic;
}

.link-icon {
    font-size: 0.875em;
    flex-shrink: 0;
}

.link-icon.disabled {
    color: #d1d5db;
}

.link-text {
    word-break: break-all;
}

/* 图标位置样式 */
.link-content.icon-left {
    flex-direction: row;
}

.link-content.icon-right {
    flex-direction: row-reverse;
}

/* 不同链接类型的颜色 */
.link-content[href^='mailto:'] {
    color: #059669;
}

.link-content[href^='tel:'] {
    color: #7c3aed;
}

.link-content[href^='http'] {
    color: #3b82f6;
}

/* 悬停效果 */
.link-content[href^='mailto:']:hover {
    color: #047857;
}

.link-content[href^='tel:']:hover {
    color: #6d28d9;
}

.link-content[href^='http']:hover {
    color: #2563eb;
}

/* 响应式 */
@media (max-width: 640px) {
    .link-text {
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>
