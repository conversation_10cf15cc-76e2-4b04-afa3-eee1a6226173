<template>
  <div class="image-renderer">
    <template v-if="!cellValue"></template>
    <template v-else>
      <ElImage
        style="width: 40px; height: 40px"
        :src="cellValue.url"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :z-index="999"
        :hide-on-click-modal="true"
        :preview-src-list="cellValue.srcList"
        show-progress
        preview-teleported
        fit="cover"
      />
    </template>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue'
import { ElImage } from 'element-plus'
import { get, set, isFunction, isString, isArray } from 'lodash-es'

export default defineComponent({
  components: {
    ElImage,
  },
  props: {
    modelValue: {
      type: [Array, String],
    },
    record: {
      type: Object,
    },
  },
  setup(props, { attrs }) {
    const cellValue = computed(() => {
      const params = {
        url: '',
        srcList: [],
      }
      const tempVal = props.modelValue
      const preUrl = import.meta.env.VITE_GLOB_API_URL.replace(/api\/?$/, '')
      let val = preUrl + tempVal
      if (isString(val)) {
        set(params, 'url', val)
        set(params, 'srcList', [val])
      } else if (isArray(val)) {
        set(params, 'url', val?.[0])
        set(params, 'srcList', val)
      }

      return params
    })

    return {
      cellValue,
    }
  },
})
</script>

<style scoped></style>
