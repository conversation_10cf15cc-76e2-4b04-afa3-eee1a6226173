export interface RelationProps {
    relationType?: 'oneToOne' | 'oneToMany' | 'manyToOne' | 'manyToMany';
    displayField?: string; // 显示字段名，如 'name', 'title', 'nick_name'
    fallbackFields?: string[]; // 备选字段，默认 ['name', 'title', 'nick_name', 'code', 'id']
    template?: string; // 显示模板，如 '${name} (${code})'
    itemName?: string; // 项目名称，如 '订单', '商品', '条'
    showEmpty?: boolean; // 是否显示空值（如 0项）
    showCount?: boolean; // 是否显示数量
    clickable?: boolean; // 是否可点击
    maxDisplay?: number; // 最多显示几个项目（对数组关系）
    separator?: string; // 分隔符，默认 ', '
    nullDisplay?: string; // 空值显示文本

    // 事件处理
    onItemClick?: (data: any, row: any) => void; // 点击项目事件
    onMoreClick?: (allData: any[], row: any) => void; // 点击更多事件

    // 样式配置
    itemStyle?: any; // 单个项目样式
    countStyle?: any; // 数量样式
    linkStyle?: any; // 链接样式
}
