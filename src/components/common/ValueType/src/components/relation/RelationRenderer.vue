<template>
    <div class="relation-renderer" :class="relationClass">
        <!-- 空值显示 -->
        <span v-if="isEmpty" class="relation-empty">
            {{ displayText }}
        </span>

        <!-- 数量显示（一对多、多对多） -->
        <span
            v-else-if="shouldShowCount"
            class="relation-count"
            :class="{ clickable: isClickable }"
            :style="countStyle"
            @click="handleCountClick"
        >
            {{ displayText }}
        </span>

        <!-- 单个对象显示（一对一、多对一） -->
        <span
            v-else-if="isSingleObject"
            class="relation-single"
            :class="{ clickable: isClickable }"
            :style="itemStyle"
            @click="handleItemClick(relationData)"
        >
            {{ displayText }}
        </span>

        <!-- 多个对象列表显示 -->
        <div v-else class="relation-list">
            <span
                v-for="(item, index) in displayItems"
                :key="getItemKey(item, index)"
                class="relation-item"
                :class="{ clickable: isClickable }"
                :style="itemStyle"
                @click="handleItemClick(item)"
            >
                {{ getItemDisplayText(item) }}
            </span>

            <!-- 更多项目指示器 -->
            <span
                v-if="hasMoreItems"
                class="relation-more"
                :class="{ clickable: isClickable }"
                @click="handleMoreClick"
            >
                ...等{{ totalCount - maxDisplay }}项
            </span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue';
import { RelationProps } from './type';
import { propTypes } from '@/utils/propTypes';

const props = defineProps({
    modelValue: {},
    record: {
        type: Object,
    },

    relationType: {
        type: String as PropType<RelationProps['relationType']>,
    },
    displayField: propTypes.string, // 显示字段名，如 'name', 'title', 'nick_name'
    fallbackFields: {
        type: Array as PropType<RelationProps['fallbackFields']>,
    }, // 备选字段，默认 ['name', 'title', 'nick_name', 'code', 'id']
    template: propTypes.string, // 显示模板，如 '${name} (${code})'
    itemName: propTypes.string, // 项目名称，如 '订单', '商品', '条'
    showEmpty: propTypes.bool, // 是否显示空值（如 0项）
    showCount: propTypes.bool, // 是否显示数量
    clickable: propTypes.bool, // 是否可点击
    maxDisplay: propTypes.number, // 最多显示几个项目（对数组关系）
    separator: propTypes.string, // 分隔符，默认 ', '
    nullDisplay: propTypes.string, // 空值显示文本

    // 事件处理
    onItemClick: {
        type: Function as PropType<RelationProps['onItemClick']>,
    }, // 点击项目事件
    onMoreClick: {
        type: Function as PropType<RelationProps['onMoreClick']>,
    }, // 点击更多事件

    // 样式配置
    itemStyle: {
        type: Object,
    }, // 单个项目样式
    countStyle: {
        type: Object,
    }, // 数量样式
    linkStyle: {
        type: Object,
    }, // 链接样式
});

// 获取关系数据
const relationData = computed(() => {
    return props.modelValue;
});

// 关系类型
const relationType = computed(() => {
    return props.relationType || 'manyToOne';
});

// 是否为空
const isEmpty = computed(() => {
    const data = relationData.value;
    return !data || (Array.isArray(data) && data.length === 0);
});

// 是否显示数量
const shouldShowCount = computed(() => {
    if (isEmpty.value) return false;

    const isArrayType =
        relationType.value === 'oneToMany' ||
        relationType.value === 'manyToMany';
    return isArrayType && props.showCount !== false;
});

// 是否为单个对象
const isSingleObject = computed(() => {
    if (isEmpty.value) return false;

    const data = relationData.value;
    const isSingleType =
        relationType.value === 'oneToOne' || relationType.value === 'manyToOne';
    return isSingleType && !Array.isArray(data);
});

// 是否可点击
const isClickable = computed(() => {
    return props.clickable && !!props.onItemClick;
});

// 总数量
const totalCount = computed(() => {
    const data = relationData.value;
    return Array.isArray(data) ? data.length : data ? 1 : 0;
});

// 最大显示数量
const maxDisplay = computed(() => {
    return props.maxDisplay || 3;
});

// 显示的项目列表
const displayItems = computed(() => {
    const data = relationData.value;
    if (!Array.isArray(data)) return [];

    return maxDisplay.value > 0 ? data.slice(0, maxDisplay.value) : data;
});

// 是否有更多项目
const hasMoreItems = computed(() => {
    const data = relationData.value;
    return Array.isArray(data) && data.length > maxDisplay.value;
});

// 主要显示文本
const displayText = computed(() => {
    if (isEmpty.value) {
        if (shouldShowEmptyCount.value) {
            const itemName = props.itemName || '项';
            return `0${itemName}`;
        }
        return props.nullDisplay || '-';
    }

    if (shouldShowCount.value) {
        const count = totalCount.value;
        const itemName = props.itemName || '项';
        return `${count}${itemName}`;
    }

    if (isSingleObject.value) {
        return getItemDisplayText(relationData.value);
    }

    return '';
});

// 是否显示空数量
const shouldShowEmptyCount = computed(() => {
    const isArrayType =
        relationType.value === 'oneToMany' ||
        relationType.value === 'manyToMany';
    return isArrayType && props.showEmpty !== false;
});

// 获取项目显示文本
const getItemDisplayText = (item: any): string => {
    if (!item) return props.nullDisplay || '-';

    // 如果有模板
    if (props.template) {
        return props.template.replace(/\$\{(\w+)\}/g, (match, fieldName) => {
            const fieldValue = getFieldValue(item, fieldName);
            return fieldValue !== null && fieldValue !== undefined
                ? String(fieldValue)
                : '';
        });
    }

    // 如果指定了显示字段
    if (props.displayField) {
        const value = getFieldValue(item, props.displayField);
        if (value !== null && value !== undefined) return String(value);
    }

    // 按备选字段优先级查找
    const fallbackFields = props.fallbackFields || [
        'name',
        'title',
        'nick_name',
        'code',
        'id',
    ];
    for (const field of fallbackFields) {
        const value = getFieldValue(item, field);
        if (value !== null && value !== undefined) {
            return String(value);
        }
    }

    return props.nullDisplay || '-';
};

// 获取字段值（支持嵌套路径）
const getFieldValue = (obj: any, path: string): any => {
    if (!obj || !path) return null;

    const keys = path.split('.');
    let result = obj;

    for (const key of keys) {
        if (result === null || result === undefined) return null;
        result = result[key];
    }

    return result;
};

// 获取项目key
const getItemKey = (item: any, index: number): string => {
    return item?.id || item?.code || index.toString();
};

// 样式计算
const relationClass = computed(() => {
    return {
        [`relation-${relationType.value}`]: true,
        'relation-clickable': isClickable.value,
        'relation-empty': isEmpty.value,
    };
});

const itemStyle = computed(() => {
    return props.itemStyle || {};
});

const countStyle = computed(() => {
    return props.countStyle || {};
});

// 事件处理
const handleItemClick = (item: any) => {
    if (isClickable.value && props.onItemClick) {
        props.onItemClick(item, props.record);
    }
};

const handleCountClick = () => {
    if (isClickable.value && props.onItemClick) {
        props.onItemClick(relationData.value, props.record);
    }
};

const handleMoreClick = () => {
    if (isClickable.value && props.onMoreClick) {
        // @ts-ignore
        props.onMoreClick(relationData.value, props.record);
    } else if (isClickable.value && props.onItemClick) {
        props.onItemClick(relationData.value, props.record);
    }
};
</script>

<style scoped>
.relation-renderer {
    display: inline-block;
    line-height: 1.4;
}

.relation-empty {
    color: #9ca3af;
    font-style: italic;
}

.relation-count {
    padding: 2px 8px;
    background-color: #e5e7eb;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
}

.relation-count.clickable {
    background-color: #dbeafe;
    color: #2563eb;
    cursor: pointer;
    transition: all 0.2s ease;
}

.relation-count.clickable:hover {
    background-color: #bfdbfe;
    color: #1d4ed8;
}

.relation-single {
    color: #374151;
}

.relation-single.clickable {
    color: #2563eb;
    cursor: pointer;
    text-decoration: underline;
    text-decoration-color: transparent;
    transition: all 0.2s ease;
}

.relation-single.clickable:hover {
    color: #1d4ed8;
    text-decoration-color: currentColor;
}

.relation-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
}

.relation-item {
    color: #374151;
    white-space: nowrap;
}

.relation-item:not(:last-child):after {
    content: ',';
    margin-left: 0;
    margin-right: 4px;
    color: #6b7280;
}

.relation-item.clickable {
    color: #2563eb;
    cursor: pointer;
    text-decoration: underline;
    text-decoration-color: transparent;
    transition: all 0.2s ease;
}

.relation-item.clickable:hover {
    color: #1d4ed8;
    text-decoration-color: currentColor;
}

.relation-more {
    color: #6b7280;
    font-size: 12px;
    font-style: italic;
}

.relation-more.clickable {
    color: #2563eb;
    cursor: pointer;
    text-decoration: underline;
    text-decoration-color: transparent;
    transition: all 0.2s ease;
}

.relation-more.clickable:hover {
    color: #1d4ed8;
    text-decoration-color: currentColor;
}

/* 不同关系类型的样式 */
.relation-oneToOne .relation-single,
.relation-manyToOne .relation-single {
    font-weight: 500;
}

.relation-oneToMany .relation-count,
.relation-manyToMany .relation-count {
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
}

.relation-oneToMany .relation-count.clickable,
.relation-manyToMany .relation-count.clickable {
    background-color: #dbeafe;
    border-color: #93c5fd;
}
</style>
