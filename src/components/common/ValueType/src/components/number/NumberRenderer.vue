<template>
    <span class="number-renderer" :class="numberClass" :style="numberStyle">
        {{ formattedNumber }}
    </span>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue';
import { NumberProps } from '../../../types/number';

const props = defineProps({
    modelValue: {
        type: [Number, String],
    },
    record: {
        type: Object,
    },
    precision: {
        type: Number,
    },
    thousandsSeparator: {
        type: Boolean,
    },
    currency: {
        type: String,
    },
    prefix: {
        type: String,
    },
    suffix: {
        type: String,
    },
    unit: {
        type: String,
    },

    format: {
        type: String as PropType<NumberProps['format']>,
    },

    color: {
        type: Object as PropType<NumberProps['color']>,
    },

    showPlusSign: {
        type: Boolean,
    },
    nullDisplay: {
        type: Boolean,
    },
});

// 获取原始数值
const rawValue = computed(() => {
    const value = props.modelValue;
    return value !== null && value !== undefined ? Number(value) : null;
});

// 格式化数字
const formattedNumber = computed(() => {
    const value = rawValue.value;

    // 处理 null/undefined 值
    if (value === null || value === undefined || isNaN(value)) {
        return props.nullDisplay || '-';
    }
    let formatted = '';
    const precision = props.precision || 2;
    const format = props.format || 'number';

    // 基础数字格式化
    let numberStr = '';

    if (format === 'percentage') {
        // 百分比格式：假设原始值是小数，转换为百分比
        numberStr = (value * 100).toFixed(precision);
    } else {
        numberStr = value.toFixed(precision);
    }

    // 千分位分隔符
    if (props.thousandsSeparator !== false) {
        const parts = numberStr.split('.');
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        numberStr = parts.join('.');
    }

    // 添加正号
    if (props.showPlusSign && value > 0) {
        numberStr = '+' + numberStr;
    }

    // 根据格式类型添加前缀后缀
    switch (format) {
        case 'currency':
            formatted = (props.currency || '¥') + numberStr;
            break;
        case 'percentage':
            formatted = numberStr + (props.suffix || '%');
            break;
        default:
            formatted = numberStr;
    }

    // 添加自定义前缀和后缀
    if (props.prefix) {
        formatted = props.prefix + formatted;
    }
    if (props.suffix && format !== 'percentage') {
        formatted = formatted + props.suffix;
    }
    if (props.unit) {
        formatted = formatted + ' ' + props.unit;
    }

    return formatted;
});

// 数字样式类
const numberClass = computed(() => {
    const value = rawValue.value;
    if (value === null || value === undefined || isNaN(value)) {
        return 'null-value';
    }

    return {
        positive: value > 0,
        negative: value < 0,
        zero: value === 0,
        currency: props.format === 'currency',
        percentage: props.format === 'percentage',
    };
});

// 数字颜色样式
const numberStyle = computed(() => {
    const value = rawValue.value;
    const colors = props.color || {};

    if (value === null || value === undefined || isNaN(value)) {
        return {};
    }

    if (value > 0 && colors.positive) {
        return { color: colors.positive };
    } else if (value < 0 && colors.negative) {
        return { color: colors.negative };
    } else if (value === 0 && colors.zero) {
        return { color: colors.zero };
    }

    return {};
});
</script>

<style scoped>
.number-renderer {
    font-variant-numeric: tabular-nums;
    font-family:
        'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Droid Sans Mono',
        'Source Code Pro', monospace;
}

.number-renderer.positive {
    color: #16a34a;
}

.number-renderer.negative {
    color: #dc2626;
}

.number-renderer.zero {
    color: #6b7280;
}

.number-renderer.null-value {
    color: #9ca3af;
    font-style: italic;
}

.number-renderer.currency {
    font-weight: 500;
}

.number-renderer.percentage {
    font-weight: 500;
}
</style>
