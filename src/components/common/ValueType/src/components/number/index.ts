export interface NumberProps {
    precision?: number; // 小数位数
    thousandsSeparator?: boolean; // 是否显示千分位分隔符
    currency?: string; // 货币符号，如 ¥, $, €
    prefix?: string; // 前缀
    suffix?: string; // 后缀
    unit?: string; // 单位
    format?: 'number' | 'currency' | 'percentage' | 'custom';
    color?: {
        positive?: string; // 正数颜色
        negative?: string; // 负数颜色
        zero?: string; // 零值颜色
    };
    showPlusSign?: boolean; // 是否显示正号
    nullDisplay?: string; // null值显示
}
