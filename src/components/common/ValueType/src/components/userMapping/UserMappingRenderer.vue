<template>
    <div class="user-mapping-cell">
        <!-- 加载状态 -->
        <span v-if="isLoading" class="text-gray-400 text-sm"> 加载中... </span>

        <!-- 正常显示 -->
        <span
            v-else-if="displayText"
            :class="[
                'user-name',
                clickable
                    ? 'cursor-pointer hover:text-blue-600 hover:underline'
                    : '',
                userStyle,
            ]"
            @click="handleClick"
        >
            {{ displayText }}
        </span>

        <!-- 空值显示 -->
        <span v-else class="text-gray-400 text-sm">
            {{ nullDisplay }}
        </span>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import type { UserMappingProps } from './index';

// 支持两种参数格式：直接props或者params格式
interface DirectProps extends UserMappingProps {
    cellValue?: any;
    row?: any;
    column?: any;
    cacheManager?: any;
}

interface ParamsProps {
    params: {
        row: any;
        column: any;
        rowIndex: number;
        cellValue: any;
    };
}

type Props = DirectProps | ParamsProps;

const props = defineProps({
    modelValue: {
        type: [Number, String],
    },
    record: {
        type: Object,
    },
    options: {
        type: Array,
    },
});

// 判断是否使用params格式
const isParamsFormat = computed(() => {
    return 'params' in props && !!props.params;
});

// 统一的数据访问器
const cellValue = computed(() => {
    if (isParamsFormat.value) {
        const p = props as ParamsProps;
        return p.params.cellValue;
    } else {
        const p = props as DirectProps;
        return p.cellValue;
    }
});

const row = computed(() => {
    if (isParamsFormat.value) {
        const p = props as ParamsProps;
        return p.params.row;
    } else {
        const p = props as DirectProps;
        return p.row;
    }
});

const column = computed(() => {
    if (isParamsFormat.value) {
        const p = props as ParamsProps;
        return p.params.column;
    } else {
        const p = props as DirectProps;
        return p.column;
    }
});

// 获取渲染器配置
const options = computed(() => {
    if (isParamsFormat.value) {
        const p = props as ParamsProps;
        return p.params.column?.cellRender?.props || {};
    } else {
        const p = props as DirectProps;
        return p;
    }
});

// 提取配置选项
const nullDisplay = computed(() => options.value.nullDisplay || '-');
const unknownDisplay = computed(
    () => options.value.unknownDisplay || '未知用户'
);
const showId = computed(() => options.value.showId || false);
const clickable = computed(() => options.value.clickable || false);
const template = computed(() => options.value.template || '');
const userStyle = computed(() => options.value.userStyle);
const cacheManager = computed(() => options.value.cacheManager);
const onUserClick = computed(() => options.value.onUserClick);

const isLoading = ref(false);

// 计算显示文本
const displayText = computed(() => {
    console.log('props', props);
    if (!props.modelValue && props.modelValue !== 0) {
        return '';
    }

    const options = props.options;

    console.log('options', options);
    const item = options?.find((item) => item.id == props.modelValue);

    return item?.username || '';
});

// 加载用户数据
const loadUserDataIfNeeded = async () => {
    if (!cacheManager.value || isLoading.value) {
        return;
    }

    try {
        isLoading.value = true;
        await cacheManager.value.loadUserData();
    } catch (error) {
        console.error('UserMappingRenderer: 加载用户数据失败', error);
    } finally {
        isLoading.value = false;
    }
};

// 处理点击事件
const handleClick = () => {
    if (!clickable.value || !onUserClick.value) {
        return;
    }

    const userId = Number(cellValue.value);
    const username = cacheManager.value?.getUsername?.(userId) || '';

    if (userId && !isNaN(userId)) {
        onUserClick.value(userId, username, row.value);
    }
};

// 监听cellValue变化
watch(
    cellValue,
    (newValue) => {
        if (
            newValue &&
            cacheManager.value &&
            cacheManager.value.hasCache &&
            !cacheManager.value.hasCache()
        ) {
            loadUserDataIfNeeded();
        }
    },
    { immediate: true }
);

// 组件挂载时检查是否需要加载数据
onMounted(() => {
    if (
        cellValue.value &&
        cacheManager.value &&
        cacheManager.value.hasCache &&
        !cacheManager.value.hasCache()
    ) {
        loadUserDataIfNeeded();
    }
});
</script>

<style scoped>
.user-mapping-cell {
    @apply inline-flex items-center;
}

.user-name {
    @apply truncate;
}
</style>
