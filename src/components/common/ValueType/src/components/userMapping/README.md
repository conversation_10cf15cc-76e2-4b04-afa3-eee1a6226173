# UserMappingColumn 插件

用户映射列插件用于将用户ID转换为用户名显示，特别适用于显示创建人、修改人等字段。

## 功能特性

- ✅ **自动缓存** - 第一次请求后将用户数据缓存到本地
- ✅ **页面刷新重载** - 页面刷新时重新获取用户数据
- ✅ **智能映射** - 自动将用户ID转换为用户名
- ✅ **多种显示模式** - 支持仅显示用户名、显示ID+用户名、自定义模板
- ✅ **异常处理** - 处理未知用户、空值等情况
- ✅ **点击事件** - 支持点击用户名触发自定义事件

## 快速使用

### 基础用法

```typescript
import { VTable, useTable, createPluginManager, PluginPresets } from '@/components/common/Table';

// 创建包含用户映射插件的插件管理器
const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

const columns = [
  column.text('name', '姓名'),
  column.createdBy('created_by', '创建人'),  // 快捷方法
  column.updatedBy('updated_by', '修改人'),  // 快捷方法
];

const [register, tableApi] = useTable({
  fetchApi: getUserList,
  columns,
  showPagination: true
});
```

### 完整配置

```typescript
const columns = [
  // 使用 userMapping 方法进行完整配置
  column.userMapping('created_by', '创建人', {
    nullDisplay: '暂无',
    unknownDisplay: '未知用户', 
    showId: false,
    clickable: true,
    onUserClick: (userId, username, row) => {
      console.log(`点击了用户: ${username} (ID: ${userId})`);
      // 可以跳转到用户详情页面
      // router.push(`/user/${userId}`);
    }
  }),
  
  // 显示用户名和ID
  column.userMapping('updated_by', '修改人', {
    showId: true,  // 显示为 "张三 (123)"
  }),
  
  // 使用自定义模板
  column.userMapping('assigned_to', '负责人', {
    template: '负责人: ${username}',  // 显示为 "负责人: 张三"
  }),
];
```

## API 参考

### UserMappingColumnOptions

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `nullDisplay` | `string` | `'-'` | 空值显示文本 |
| `unknownDisplay` | `string` | `'未知用户'` | 未知用户显示文本 |
| `showId` | `boolean` | `false` | 是否同时显示ID |
| `template` | `string` | - | 显示模板，支持 `${username}` 和 `${id}` 变量 |
| `clickable` | `boolean` | `false` | 是否可点击 |
| `onUserClick` | `function` | - | 点击用户回调函数 |
| `userStyle` | `any` | - | 用户名样式 |
| `idStyle` | `any` | - | ID样式 |

### 列助手方法

#### userMapping(field, title, options?)

通用的用户映射列方法。

```typescript
column.userMapping('user_id', '用户', {
  showId: true,
  clickable: true
})
```

#### createdBy(field?, title?, options?)

创建人列的快捷方法。

```typescript
// 使用默认字段和标题
column.createdBy()

// 自定义字段和标题
column.createdBy('creator_id', '创建者')

// 添加配置
column.createdBy('created_by', '创建人', {
  clickable: true
})
```

#### updatedBy(field?, title?, options?)

修改人列的快捷方法。

```typescript
// 使用默认字段和标题
column.updatedBy()

// 自定义字段和标题
column.updatedBy('modifier_id', '修改者')
```

## 缓存机制

插件内置了智能缓存机制：

1. **首次加载** - 第一次使用时自动从后端获取用户数据
2. **内存缓存** - 用户数据存储在内存中，页面内快速访问
3. **页面刷新** - 监听页面刷新事件，重新获取最新用户数据
4. **错误处理** - 网络错误时显示合适的错误状态

## 高级用法

### 手动管理缓存

```typescript
import { UserCacheManager } from '@/components/common/Table/plugins/userMappingColumn';

const cacheManager = UserCacheManager.getInstance();

// 手动加载用户数据
await cacheManager.loadUserData();

// 强制重新加载
await cacheManager.loadUserData(true);

// 获取用户名
const username = cacheManager.getUsername(123);

// 清除缓存
cacheManager.clearCache();

// 获取所有用户数据
const allUsers = cacheManager.getAllUsers();
```

### 自定义显示样式

```typescript
column.userMapping('created_by', '创建人', {
  userStyle: {
    color: '#1890ff',
    fontWeight: 'bold'
  },
  clickable: true,
  onUserClick: (userId, username, row) => {
    // 自定义点击逻辑
    showUserProfile(userId);
  }
})
```

## 集成到预设中

用户映射插件已经集成到以下预设中：

- `PluginPresets.basic` - 基础预设
- `PluginPresets.enhanced` - 增强预设
- `PluginPresets.full` - 完整预设
- `PluginPresets.cms` - 内容管理系统预设
- `PluginPresets.userManagement` - 用户管理预设

## 注意事项

1. **接口依赖** - 插件依赖 `getAllUsersName` 接口，确保接口可用
2. **数据格式** - 接口返回数据格式必须为 `{ id: number, username: string }[]`
3. **性能考虑** - 用户数据会在页面级别缓存，大量用户时注意内存使用
4. **错误处理** - 网络错误时会显示 "未知用户"，可自定义错误显示文本

## 示例场景

### 用户管理表格

```typescript
const userColumns = [
  column.selection(),
  column.text('name', '姓名'),
  column.text('email', '邮箱'), 
  column.status('status', '状态', statusMap),
  column.createdBy(),  // 创建人
  column.updatedBy(),  // 修改人
  column.datetime('created_at', '创建时间'),
  column.action('操作', { actions: [...] })
];
```

### 订单管理表格

```typescript
const orderColumns = [
  column.text('order_no', '订单号'),
  column.currency('amount', '金额', '¥'),
  column.userMapping('sales_id', '销售员', {
    clickable: true,
    onUserClick: (userId) => viewSalesProfile(userId)
  }),
  column.createdBy('creator', '创建人'),
  column.datetime('created_at', '下单时间'),
];
``` 