/**
 * 根据字段信息增强列配置
 * @param columnConfig 现有的列配置
 * @param fieldInfo 从API获取的字段信息
 * @returns 增强后的列配置
 */
function enhanceColumnWithFieldInfo(columnConfig: any, fieldInfo: any) {
    // 如果没有字段信息，直接返回原始配置
    if (!fieldInfo) {
        return columnConfig;
    }

    // console.log(
    //     `[列增强] 字段 ${fieldInfo.name} 类型为 ${fieldInfo.type}，开始处理...`
    // );
    const enhanced = { ...columnConfig };

    // 基础信息增强
    if (fieldInfo.comment && !enhanced.title) {
        enhanced.title = fieldInfo.comment;
        // console.log(`  - 设置标题: ${fieldInfo.comment}`);
    }

    if (fieldInfo.nullable === false && !enhanced.required) {
        enhanced.required = true;
        // console.log(`  - 设置为必填字段`);
    }

    // 根据字段类型进行增强
    if (fieldInfo.type === 'string') {
        if (!Reflect.has(enhanced, 'width')) {
            enhanced.width = 150;
            // console.log(`  - 设置字符串默认宽度: 150`);
        }
    } else if (fieldInfo.type === 'integer') {
        if (!enhanced.align) enhanced.align = 'right';
        if (!Reflect.has(enhanced, 'width')) enhanced.width = 100;
        // console.log(`  - 设置整数格式: 右对齐，宽度100`);
    } else if (fieldInfo.type === 'float') {
        // console.log(`  - 检测到浮点数类型，字段名: ${fieldInfo.name}`);

        // 智能识别字段用途
        const fieldName = fieldInfo.name.toLowerCase();
        let formatType = 'decimal'; // 默认格式

        if (
            fieldName.includes('rate') ||
            fieldName.includes('percent') ||
            fieldName.includes('ratio')
        ) {
            formatType = 'percentage';
            // console.log(`  - 识别为百分比字段: ${fieldName}`);
        } else if (
            fieldName.includes('price') ||
            fieldName.includes('amount') ||
            fieldName.includes('cost') ||
            fieldName.includes('fee')
        ) {
            formatType = 'currency';
            // console.log(`  - 识别为货币字段: ${fieldName}`);
        } else {
            // console.log(`  - 识别为普通浮点数字段: ${fieldName}`);
        }

        if (!enhanced.formatter) {
            if (formatType === 'percentage') {
                enhanced.formatter = ({ cellValue }: any) => {
                    if (cellValue == null) return '';
                    const numValue = Number(cellValue);
                    if (isNaN(numValue)) return cellValue;

                    // 智能判断是0-1小数还是0-100百分比
                    if (numValue <= 1) {
                        return `${(numValue * 100).toFixed(1)}%`;
                    } else {
                        return `${numValue.toFixed(1)}%`;
                    }
                };
                // console.log(`  - 设置百分比格式化器`);
            } else if (formatType === 'currency') {
                enhanced.formatter = ({ cellValue }: any) => {
                    if (cellValue == null) return '';
                    const numValue = Number(cellValue);
                    if (isNaN(numValue)) return cellValue;
                    return `¥${numValue.toFixed(2)}`;
                };
                // console.log(`  - 设置货币格式化器`);
            } else {
                enhanced.formatter = ({ cellValue }: any) => {
                    if (cellValue == null) return '';
                    const numValue = Number(cellValue);
                    if (isNaN(numValue)) return cellValue;
                    return numValue.toFixed(2);
                };
                // console.log(`  - 设置小数格式化器`);
            }
        }

        if (!enhanced.align) enhanced.align = 'right';
        if (!Reflect.has(enhanced, 'width')) enhanced.width = 120;
        // console.log(`  - 设置浮点数格式: 右对齐，宽度120`);
    } else if (fieldInfo.type === 'boolean') {
        if (!enhanced.formatter) {
            enhanced.formatter = ({ cellValue }: any) =>
                cellValue ? '是' : '否';
            // console.log(`  - 设置布尔值格式化器`);
        }
        if (!Reflect.has(enhanced, 'width')) enhanced.width = 80;
        if (!enhanced.align) enhanced.align = 'center';
        // console.log(`  - 设置布尔值格式: 居中对齐，宽度80`);
    } else if (fieldInfo.type === 'datetime') {
        if (!enhanced.formatter) {
            enhanced.formatter = ({ cellValue }: any) => {
                if (!cellValue) return '';
                const date = new Date(cellValue);
                return date.toLocaleString('zh-CN');
            };
            // console.log(`  - 设置日期时间格式化器`);
        }
        if (!Reflect.has(enhanced, 'width')) enhanced.width = 160;
        // console.log(`  - 设置日期时间宽度: 160`);
    } else if (fieldInfo.type === 'enum' && fieldInfo.enum_info) {
        // console.log(
        //     `  - 检测到枚举类型，枚举值:`,
        //     fieldInfo.enum_info.enum_values
        // );
        enhanced.enumInfo = fieldInfo.enum_info;
        enhanced.isEnum = true;

        if (!enhanced.formatter) {
            enhanced.formatter = ({ cellValue }: any) => {
                if (!cellValue) return '';
                return fieldInfo.enum_info.enum_values[cellValue] || cellValue;
            };
            // console.log(`  - 设置枚举格式化器`);
        }
        if (!Reflect.has(enhanced, 'width')) enhanced.width = 120;
        // console.log(`  - 设置枚举宽度: 120`);
    } else if (fieldInfo.type === 'relation' && fieldInfo.relation_info) {
        // console.log(`  - 检测到关系类型，关系信息:`, fieldInfo.relation_info);
        enhanced.relationInfo = fieldInfo.relation_info;
        enhanced.isRelation = true;

        // 只有在没有formatter时才添加关系格式化
        if (!enhanced.formatter) {
            const direction = fieldInfo.relation_info.direction;
            // console.log(`  - 关系方向: ${direction}`);

            if (direction === 'RelationshipDirection.ONETOMANY') {
                // 一对多关系：显示关联项目数量
                enhanced.formatter = ({ cellValue }: any) => {
                    if (!cellValue) return '0项';
                    if (Array.isArray(cellValue)) {
                        const count = cellValue.length;
                        return count > 0 ? `${count}项` : '0项';
                    }
                    return '0项';
                };
                // console.log(`  - 设置一对多关系格式化器`);
            } else if (direction === 'RelationshipDirection.MANYTOONE') {
                // 多对一关系：显示关联对象的名称
                enhanced.formatter = ({ cellValue }: any) => {
                    if (!cellValue) return '-';
                    if (typeof cellValue === 'object') {
                        // 优先显示 name，然后 title，最后 code
                        const displayValue =
                            cellValue.name ||
                            cellValue.title ||
                            cellValue.code ||
                            cellValue.id ||
                            '-';
                        // console.log(
                        //     `  - 多对一关系显示值: ${displayValue}`,
                        //     cellValue
                        // );
                        return displayValue;
                    }
                    return cellValue;
                };
                // console.log(`  - 设置多对一关系格式化器`);
            } else {
                // 其他关系类型的默认处理
                enhanced.formatter = ({ cellValue }: any) => {
                    if (!cellValue) return '-';
                    if (Array.isArray(cellValue)) {
                        return `${cellValue.length}项`;
                    }
                    if (typeof cellValue === 'object') {
                        return (
                            cellValue.name ||
                            cellValue.title ||
                            cellValue.code ||
                            cellValue.id ||
                            '-'
                        );
                    }
                    return cellValue;
                };
                // console.log(`  - 设置通用关系格式化器`);
            }
        }

        // 设置关系字段的默认宽度
        if (!Reflect.has(enhanced, 'width')) {
            if (
                fieldInfo.relation_info.direction ===
                'RelationshipDirection.ONETOMANY'
            ) {
                enhanced.width = 100; // 一对多显示数量，宽度较小
                // console.log(`  - 设置一对多关系宽度: 100`);
            } else {
                enhanced.width = 150; // 多对一显示名称，宽度较大
                // console.log(`  - 设置多对一关系宽度: 150`);
            }
        }

        // 设置对齐方式
        if (!enhanced.align) {
            if (
                fieldInfo.relation_info.direction ===
                'RelationshipDirection.ONETOMANY'
            ) {
                enhanced.align = 'center'; // 数量居中显示
                // console.log(`  - 设置一对多关系对齐: 居中`);
            } else {
                enhanced.align = 'left'; // 名称左对齐
                // console.log(`  - 设置多对一关系对齐: 左对齐`);
            }
        }
    }

    // console.log(
    //     `[列增强] 字段 ${fieldInfo.name} 处理完成，最终配置:`,
    //     enhanced
    // );
    return enhanced;
}
