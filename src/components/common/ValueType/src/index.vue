<template>
  <component :is="getCellRenderComponent" v-bind="getBind">
    <template v-for="item in Object.keys($slots)" #[item]="data">
      <slot :name="item" v-bind="data || {}" />
    </template>
  </component>
</template>

<script lang="ts">
import { computed, defineComponent, inject, PropType, ref } from 'vue'
import avatar from './components/avatar/AvatarRenderer.vue'
import boolean from './components/boolean/BooleanRenderer.vue'
import composite from './components/composite/CompositeRenderer.vue'
import date from './components/date/DateRenderer.vue'
import customerLink from './components/link/LinkRenderer.vue'
import number from './components/number/NumberRenderer.vue'
import relation from './components/relation/RelationRenderer.vue'
import status from './components/status/StatusRenderer.vue'
import dict from './components/dict/DictRenderer.vue'
import { Tag } from '@/components'
import customerText from './components/text/TextRenderer.vue'
import userMapping from './components/userMapping/UserMappingRenderer.vue'
import customerImage from './components/image/image.vue'

export default defineComponent({
  name: 'ValueType',
  components: {
    avatar,
    boolean,
    composite,
    date,
    customerLink,
    number,
    relation,
    status,
    customerText,
    userMapping,
    Tag,
    dict,
    customerImage,
  },
  props: {
    modelValue: {},
    record: {
      type: Object,
    },
    type: {
      type: String as PropType<
        | 'avatar'
        | 'boolean'
        | 'composite'
        | 'date'
        | 'link'
        | 'number'
        | 'relation'
        | 'status'
        | 'dict'
        | 'text'
        | 'userMapping'
        | 'image'
      >,
    },
  },
  emits: [],
  setup(props, { emit, attrs, slots }) {
    const tableRef = inject('__table')

    const getBind = computed(() => {
      return {
        ...props,
        ...attrs,
      }
    })

    const getCellRenderComponent = computed(() => {
      const renderName = props.type
      if (!renderName) return 'customerText'

      // 返回组件名称，Vue会从已注册的组件中找到对应的组件
      const rendererMap = {
        avatar: 'avatar',
        boolean: 'boolean',
        composite: 'composite',
        date: 'date',
        link: 'customerLink',
        number: 'number',
        relation: 'relation',
        status: 'status',
        dict: 'dict',
        text: 'customerText',
        userMapping: 'userMapping',
        image: 'customerImage',
      }
      return rendererMap[renderName] || 'customerText'
    })

    return {
      getBind,
      getCellRenderComponent,
    }
  },
})
</script>

<style scoped></style>
