<script setup lang="ts">
import { Button } from '@/components/ui/button'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  SheetHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from '@/components/ui/sheet'
import { isNumber } from 'lodash-es'
import { computed, nextTick, onMounted, ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  size: {
    type: [String, Number],
    default: 'small', // 'large' | 'middle' | 'small' | 'mini';
  },
  title: {
    type: String,
  },
  description: {
    type: String,
  },
  headerClass: {
    type: String,
    default: '',
  },
  bodyClass: {
    type: String,
    default: '',
  },
  footerClass: {
    type: String,
    default: '',
  },
  position: {
    type: String,
    default: 'right', // right|left|top|bottom
    validator: (value: string) =>
      ['right', 'left', 'top', 'bottom'].includes(value),
  },
  showFooter: {
    type: <PERSON>olean,
    default: true,
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  disableClickOutside: {
    type: Boolean,
    default: false,
  },
  cancelText: {
    type: String,
  },
  submitText: {
    type: String,
  },
  loading: {
    type: Boolean,
  },
  class: {
    type: String,
  },
})

const emit = defineEmits<{
  (e: 'update:modelValue', value?: Boolean): void
  (e: 'open'): void
  (e: 'close'): void
  (e: 'submit'): void
}>()

const show = ref(props.modelValue)

// 计算尺寸对应的类名
const sizeClasses = computed(() => {
  const isHorizontal = ['left', 'right'].includes(props.position)
  const sizes = {
    mini: isHorizontal ? 'w-[30%]' : 'h-[30%]',
    small: isHorizontal ? 'w-[50%]' : 'h-[50%]',
    middle: isHorizontal ? 'w-[70%]' : 'h-[70%]',
    large: isHorizontal ? 'w-[90%]' : 'h-[90%]',
  }

  if (isNumber(props.size)) {
    return isHorizontal ? `w-[${props.size}px]` : `h-[${props.size}px]`
  } else if (sizes[props.size]) {
    return sizes[props.size]
  } else if (props.size) {
    return props.size
  } else {
    return isHorizontal ? 'w-[50%]' : 'h-[50%]'
  }
})

// 计算位置对应的类名
const positionClasses = computed(() => {
  switch (props.position) {
    case 'left':
      return 'left-0 border-r'
    case 'right':
      return 'right-0 border-l'
    case 'top':
      return 'top-0 border-b'
    case 'bottom':
      return 'bottom-0 border-t'
    default:
      return 'right-0 border-l'
  }
})

// 处理v-model双向绑定
watch(
  () => props.modelValue,
  (val) => {
    show.value = val
  }
)

watch(show, (val) => {
  emit('update:modelValue', val)
  if (val) {
    emit('open')

    removeClass()
  } else {
    emit('close')
  }
})

// 处理表单提交
function handleSubmit() {
  emit('submit')
}

// 关闭抽屉
function closeDrawer() {
  show.value = false
}

const SheetContentRef = ref()

onMounted(() => {
  removeClass()
})

function removeClass() {
  nextTick(() => {
    const el = document.querySelector(`.sheet-content`)
    el?.classList.remove('sm:max-w-sm')
  })
}
</script>

<template>
  <Sheet v-model:open="show" :disableClickOutside="disableClickOutside">
    <SheetContent
      ref="SheetContentRef"
      class="p-0 flex flex-col sheet-content"
      :class="[positionClasses, sizeClasses, props.class]"
      :side="position"
    >
      <!-- 头部 - 固定 -->
      <SheetHeader
        class="sticky top-0 z-10 bg-background border-b p-4"
        :class="headerClass"
      >
        <SheetTitle>{{ title }}</SheetTitle>
        <SheetDescription v-if="description">{{
          description
        }}</SheetDescription>

        <!-- 关闭按钮 -->
        <button
          v-if="showClose"
          class="absolute top-4 right-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
          @click="closeDrawer"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
          <span class="sr-only">关闭</span>
        </button>
      </SheetHeader>

      <!-- 主体内容 - 自适应高度并带滚动条 -->
      <div class="flex-1 overflow-y-auto" :class="bodyClass">
        <div class="p-4">
          <!-- 默认插槽用于内容区域 -->
          <slot> </slot>
        </div>
      </div>

      <!-- 底部 - 固定 -->
      <SheetFooter
        v-if="showFooter !== false"
        class="sticky bottom-0 z-10 bg-background border-t p-4"
        :class="footerClass"
      >
        <div class="flex justify-end space-x-3">
          <Button variant="outline" @click="closeDrawer">
            {{ cancelText || $t?.('common.button.cancel') }}
          </Button>
          <Button variant="default" @click="handleSubmit" v-loading="loading">
            {{ submitText || $t?.('common.button.confirm') }}
          </Button>
        </div>

        <!-- 底部插槽 -->
        <slot name="footer"></slot>
      </SheetFooter>
    </SheetContent>
  </Sheet>
</template>

<style scoped>
/* 优化滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.5);
}

/* 抽屉动画优化 */
.sheet-content {
  transition: transform 0.1s ease-out;
}
</style>

<style>
/* 尺寸覆盖 */
.sheet-content.sm:max-w-sm {
  max-width: unset !important;
  width: unset !important;
  min-width: unset !important;
  width: 100% !important;
}
</style>
