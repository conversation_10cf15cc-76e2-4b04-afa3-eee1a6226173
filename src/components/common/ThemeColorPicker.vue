<template>
    <div class="relative" data-menu>
        <button
            @click.stop="toggleColorPicker"
            class="inline-flex items-center justify-center rounded-full text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 w-9"
        >
            <Icon
                icon="lucide:palette"
                class="h-5 w-5 text-white hover:text-theme-accent"
                :stroke-width="1.5"
            />
            <span class="sr-only">切换主题颜色</span>
        </button>

        <!-- 颜色选择下拉菜单 -->
        <div
            v-if="isColorPickerOpen"
            class="absolute right-0 mt-2 w-[140px] bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-50"
        >
            <div class="p-3">
                <div
                    class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                    选择主题颜色
                </div>
                <div class="grid grid-cols-3 gap-2">
                    <!-- 蓝色主题 -->
                    <button
                        @click="selectColor('blue')"
                        class="group relative flex flex-col items-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        :class="{
                            'bg-blue-50 dark:bg-blue-900 ring-2 ring-blue-500':
                                currentColor === 'blue',
                        }"
                    >
                        <div
                            class="w-6 h-6 rounded-full bg-[#001d85] mb-1 group-hover:scale-110 transition-transform"
                        ></div>
                        <span class="text-xs text-gray-600 dark:text-gray-300"
                            >蓝色</span
                        >
                        <Icon
                            v-if="currentColor === 'blue'"
                            icon="lucide:check"
                            class="absolute top-1 right-1 h-3 w-3 text-blue-600"
                            :stroke-width="2"
                        />
                    </button>

                    <!-- 绿色主题 -->
                    <button
                        @click="selectColor('green')"
                        class="group relative flex flex-col items-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        :class="{
                            'bg-green-50 dark:bg-green-900 ring-2 ring-green-500':
                                currentColor === 'green',
                        }"
                    >
                        <div
                            class="w-6 h-6 rounded-full bg-green-500 mb-1 group-hover:scale-110 transition-transform"
                        ></div>
                        <span class="text-xs text-gray-600 dark:text-gray-300"
                            >绿色</span
                        >
                        <Icon
                            v-if="currentColor === 'green'"
                            icon="lucide:check"
                            class="absolute top-1 right-1 h-3 w-3 text-green-600"
                            :stroke-width="2"
                        />
                    </button>

                    <!-- 黑色主题 -->
                    <button
                        @click="selectColor('black')"
                        class="group relative flex flex-col items-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        :class="{
                            'bg-gray-50 dark:bg-gray-900 ring-2 ring-gray-500':
                                currentColor === 'black',
                        }"
                    >
                        <div
                            class="w-6 h-6 rounded-full bg-gray-800 mb-1 group-hover:scale-110 transition-transform"
                        ></div>
                        <span class="text-xs text-gray-600 dark:text-gray-300"
                            >黑色</span
                        >
                        <Icon
                            v-if="currentColor === 'black'"
                            icon="lucide:check"
                            class="absolute top-1 right-1 h-3 w-3 text-gray-600"
                            :stroke-width="2"
                        />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Icon } from '@iconify/vue';
import { useThemeStore, type ThemeColor } from '@/store/theme';

const themeStore = useThemeStore();
const isColorPickerOpen = ref(false);

const currentColor = computed(() => themeStore.themeColor);

const toggleColorPicker = () => {
    isColorPickerOpen.value = !isColorPickerOpen.value;
};

const selectColor = (color: ThemeColor) => {
    themeStore.setThemeColor(color);
    isColorPickerOpen.value = false;
};

// 全局点击关闭
const handleOutsideClick = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target.closest('[data-menu]')) {
        isColorPickerOpen.value = false;
    }
};

onMounted(() => {
    document.addEventListener('click', handleOutsideClick);
});

onUnmounted(() => {
    document.removeEventListener('click', handleOutsideClick);
});
</script>
