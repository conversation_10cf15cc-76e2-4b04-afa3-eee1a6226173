<template>
    <div class="modal">
        <div class="modal-content">
            <!-- file upload -->
            <div class="upload-container mb-2" @click="openFile" @dragover.prevent="handleDragOver"
                @dragleave="handleDragLeave" @drop.prevent="handleDrop" :class="{ 'is-dragging': isDragging }">
                <img v-if="fileUpload" :src="fileUpload.url" alt="上传文件" />
                <div v-else class="upload-icon">+ 上传文件</div>
            </div>
            <div>{{ fileContent }}</div>
        </div>
        <div class="modal-footer">
            <Button type="button" class="mr-[12px]" @click="upload">上传</Button>
            <Button @click="$emit('closeModal')" variant="outline">关闭</Button>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { Button } from '@/components/ui/button';
import { defineEmits } from 'vue';

const emit = defineEmits(['closeModal', 'success']);
interface FileData {
    fileContent: string;
    fileUpload: any;
}
const fileContent = ref('');
const _formData = ref<FileData>({
    fileContent: '',
    fileUpload: null,
});
const fileUpload = ref<any>(null);
const isDragging = ref(false);

const upload = () => {
    console.log('上传文件', _formData.value);
    _formData.value.fileContent = fileContent.value;
    emit('success', _formData.value);
};

const handleFile = (file: File) => {
    // 如果是图片，则显示图片
    if (/^image\//.test(file.type)) {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e: any) => {
            fileUpload.value = {
                url: e.target.result,
                name: file.name,
                size: file.size,
                type: file.type,
            };
        };
    }

    _formData.value = {
        fileUpload: {
            file: file,
        },
        fileContent: file.name,
    };
    fileContent.value = file.name;
};

const openFile = () => {
    // 打开文件选择框
    const input = document.createElement('input');
    input.type = 'file';
    input.onchange = (e: any) => {
        console.log('打开文件选择框', e);
        const file = e.target.files[0];
        if (file) {
            handleFile(file);
        }
    };
    input.click();
};

const handleDragOver = () => {
    isDragging.value = true;
};

const handleDragLeave = () => {
    isDragging.value = false;
};

const handleDrop = (e: DragEvent) => {
    isDragging.value = false;
    const file = e.dataTransfer?.files[0];
    if (file) {
        handleFile(file);
    }
};
</script>
<style scoped>
.upload-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    border: 1px dashed #ccc;
    border-radius: 5px;
    background-color: #f5f5f5;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-container.is-dragging {
    border-color: #409eff;
    background-color: #e6f7ff;
}

.upload-icon {
    font-size: 30px;
    color: #999;
}

.upload-container img {
    max-width: 100%;
    max-height: 100%;
}
</style>
