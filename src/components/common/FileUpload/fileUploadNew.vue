<template>
    <div class="upload-container max-w-[100%] mt-2 mb-2 py-2 px-2 overflow-auto max-h-[100%] overflow-y-auto">
        <!-- 拖拽区域 -->
        <div class="drag-zone border-[2px] border-dashed border-gray-300 hover:border-blue-500 rounded-md cursor-pointer duration-300 ease-in iscurrent:border-green-500 iscurrent:bg-[#f5faff]"
            :class="{ iscurrent: isDragging }" @dragenter="handleDragEnter" @dragleave="handleDragLeave"
            @dragover="handleDragOver" @drop="handleDrop">
            <!-- 上传按钮 -->
            <div class="px-4 py-20 text-center" @click="triggerFileInput">
                <div class="flex items-center justify-center">
                    <svg t="1748240187890" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="11173" width="32" height="32">
                        <path
                            d="M663.466667 526.933333L541.866667 405.333333c-17.066667-17.066667-42.666667-17.066667-59.733334 0l-121.6 121.6c-17.066667 17.066667-17.066667 42.666667 0 59.733334 8.533333 8.533333 19.2 12.8 29.866667 12.8s21.333333-4.266667 29.866667-12.8l46.933333-46.933334V853.333333c0 23.466667 19.2 42.666667 42.666667 42.666667s42.666667-19.2 42.666666-42.666667V539.733333l46.933334 46.933334c17.066667 17.066667 42.666667 17.066667 59.733333 0s21.333333-42.666667 4.266667-59.733334z"
                            fill="#80B8F8" p-id="11174"></path>
                        <path
                            d="M808.533333 388.266667C789.333333 241.066667 663.466667 128 512 128c-151.466667 0-277.333333 113.066667-296.533333 260.266667-102.4 19.2-179.2 113.066667-172.8 221.866666 6.4 113.066667 106.666667 200.533333 219.733333 200.533334H405.333333c12.8 0 21.333333-8.533333 21.333334-21.333334v-128c0-12.8-10.666667-23.466667-23.466667-21.333333-42.666667 6.4-87.466667-19.2-93.866667-72.533333-4.266667-27.733333 8.533333-57.6 27.733334-76.8l113.066666-113.066667c14.933333-14.933333 32-23.466667 51.2-25.6 27.733333-4.266667 53.333333 6.4 70.4 23.466667l117.333334 117.333333c32 32 36.266667 85.333333 6.4 119.466667-19.2 23.466667-49.066667 32-76.8 27.733333-10.666667 0-21.333333 10.666667-21.333334 21.333333v128c0 12.8 8.533333 21.333333 21.333334 21.333334h142.933333c115.2 0 213.333333-87.466667 219.733333-200.533334 6.4-108.8-70.4-202.666667-172.8-221.866666z"
                            fill="#80B8F8" p-id="11175"></path>
                    </svg>
                </div>
                <div class="text-[16px] text-gray-400">
                    点击、拖拽、粘贴截图
                </div>
            </div>

            <!-- 截图粘贴提示 -->
            <div v-show="isDragging || showClipboardTip"
                class="clipboard-tip text-[14px] text-center text-green-500 mb-[20px]">
                或按下 <strong>Ctrl+V</strong> 粘贴截图
            </div>

            <!-- 预览列表 -->

        </div>
        <div class="preview-list flex flex-wrap gap-4 py-2 px-2">
            <div v-for="(file, index) in filesList" :key="index"
                class="preview-item group w-[120px] flex justify-center h-[150px] rounded-[2px] relative mt-2 shadow-[0_0_10px_rgba(0,0,0,0.1)]">
                <img :src="file.preview" alt="预览" class="preview-img max-h-[100%] w-[120px] h-[120px] object-cover" />
                <span @click="deleteFun(file.id, index)"
                    class="absolute rounded-full text-center cursor-pointer w-[20px] h-[20px] bg-white right-0 top-0 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200"><svg
                        t="1747983690245" class="icon" viewBox="0 0 1024 1024" version="1.1"
                        xmlns="http://www.w3.org/2000/svg" p-id="8860" width="20" height="20">
                        <path
                            d="M512 1024C229.248 1024 0 794.752 0 512S229.248 0 512 0s512 229.248 512 512-229.248 512-512 512z m0-572.330667L300.629333 240.213333a42.538667 42.538667 0 0 0-60.16 0.213334 42.410667 42.410667 0 0 0-0.213333 60.16L451.669333 512 240.213333 723.370667a42.538667 42.538667 0 0 0 0.213334 60.16 42.410667 42.410667 0 0 0 60.16 0.213333L512 572.330667l211.370667 211.413333a42.538667 42.538667 0 0 0 60.16-0.213333 42.410667 42.410667 0 0 0 0.213333-60.16L572.330667 512l211.413333-211.370667a42.538667 42.538667 0 0 0-0.213333-60.16 42.410667 42.410667 0 0 0-60.16-0.213333L512 451.669333z"
                            fill="#333333" p-id="8861"></path>
                    </svg>
                </span>
                <div class="absolute bottom-[6px] left-0 w-[100%] text-center">{{ file.file.name }}</div>
            </div>
        </div>
        <!-- 隐藏文件输入框 -->
        <input type="file" ref="fileInput" @change="handleFileChange" style="display: none" />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRequest } from 'alova/client';
import { uploadAttachment, deleteAttachment } from '@/api/sys/attachment';
import { useToast } from '@/components/ui/toast/use-toast';
import { useUserStore } from '@/store/user';

// 定义文件项接口
interface FileItem {
    file: File;
    preview?: string;
    id: number;
}

// 定义props类型
interface Props {
    relatedId?: number;
    relatedModel?: string;
    modelValue?: any;
}

// 定义组件接收的props
const props = defineProps<Props>();
const emit = defineEmits<{
    (e: 'closeModal'): void;
    (e: 'success', res: any): void;
    (e: 'update:modelValue', res: any): void;
    (e: 'delete', id: number): void;
}>();

const fileInput = ref<HTMLInputElement | null>(null);
const filesList = ref<FileItem[]>([]); // 存储文件列表
const isDragging = ref<boolean>(false);
const showClipboardTip = ref<boolean>(false);
const toast = useToast();
const user = useUserStore();

// 初始化剪贴板监听
onMounted(() => {
    if (navigator.clipboard) {
        initClipboardListener();
    } else {
        console.warn('当前浏览器不支持剪贴板粘贴');
    }
});

// 触发文件选择
const triggerFileInput = (): void => {
    fileInput.value?.click();
};

// 删除文件
const deleteFun = async (id: number, index: number): Promise<void> => {
    filesList.value.splice(index, 1);
    if (props.relatedId) {
        await deleteAttachment(id)
    }
    emit('delete', id);
};

// 处理文件选择/拖拽/粘贴
const handleFiles = (filesList: FileList): void => {
    Array.from(filesList).forEach((file) => {
        if (file.type.startsWith('image/')) {
            // 生成预览地址
            const reader = new FileReader();
            reader.onload = (e) => {
                addFileToQueue(file, e.target?.result as string);
            };
            reader.readAsDataURL(file);
        } else {
            addFileToQueue(file);
        }
    });
};

// 添加文件到队列并上传
const addFileToQueue = (file: File, preview?: string): void => {
    const newFile: FileItem = {
        file,
        preview,
        id: Date.now() + Math.random(),
    };
    filesList.value = [...filesList.value, newFile];
    if (props.relatedId) {
        uploadFile(newFile)
    } else {
        const emitData = filesList.value.map(fileItem => {
            return {
                id: fileItem.id,
                file_name: fileItem.file.name,
                file_size: fileItem.file.size,
                file_path: fileItem.preview,
                created_by: user.$state.userInfo.id,
                created_at: new Date(),
                file: fileItem.file,
            }
        })
        emit('success', emitData);
    }
};

// 上传文件
const uploadFile = (file: FileItem): void => {
    const formData = new FormData();
    formData.append('file', file.file);
    const { onSuccess: onSuccessFile } = useRequest(
        uploadAttachment(formData, props.relatedId, props.relatedModel),
        { immediate: true }
    );
    onSuccessFile(async (res: any) => {
        const htmlValue = filesList;
        const index = filesList.value.findIndex((f) => f.id === file.id);
        if (index !== -1) {
            const updatedFile: FileItem = { ...htmlValue.value[index], id: res.data.id };
            htmlValue.value = [...htmlValue.value.slice(0, index), updatedFile, ...htmlValue.value.slice(index + 1)];
        }
        toast.toast({ title: '成功', description: '上传成功', duration: 2000 });
        emit('success', [res.data]);
        emit('update:modelValue', res);
    });
};

// 拖拽事件处理
const handleDragEnter = (e: DragEvent): void => {
    e.preventDefault();
    isDragging.value = true;
    showClipboardTip.value = true;
};

const handleDragLeave = (e: DragEvent): void => {
    e.preventDefault();
    isDragging.value = false;
    showClipboardTip.value = false;
};

const handleDragOver = (e: DragEvent): void => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    isDragging.value = true;
    showClipboardTip.value = true;
};

const handleDrop = (e: DragEvent): void => {
    e.preventDefault();
    isDragging.value = false;
    showClipboardTip.value = false;
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
        handleFiles(droppedFiles);
    }
};

// 剪贴板粘贴监听
const initClipboardListener = (): void => {
    document.addEventListener('paste', async (e: ClipboardEvent) => {
        const items = e.clipboardData?.items;
        if (!items) return;
        for (const item of Array.from(items)) {
            const file = await item.getAsFile();
            if (file) {
                // 创建包含单个文件的FileList
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                handleFiles(dataTransfer.files);
            }
        }
    });
};

// 文件选择处理
const handleFileChange = (e: Event): void => {
    const input = e.target as HTMLInputElement;
    const filesList = input.files;
    if (filesList && filesList.length > 0) {
        handleFiles(filesList);
    }
    input.value = ''; // 清空输入框
};
</script>
