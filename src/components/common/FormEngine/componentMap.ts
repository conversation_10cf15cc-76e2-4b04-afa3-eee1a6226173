import type { Component } from 'vue'
import { Input } from '@/components/ui/input'
import Radio from './FormComponents/radio.vue'
// import Select from '@/components/common/FormEngine/FormComponents/Select.vue';
import { Select } from '@/components/common/Select'
import { Checkbox } from '@/components/ui/checkbox'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import MultipleSelect from './FormComponents/MultipleSelect.vue'
import NumberInput from './FormComponents/NumberInput.vue'
import DatePicker from '@/components/common/FormEngine/FormComponents/DatePicker.vue'
import DateRangerPicker from '@/components/common/FormEngine/FormComponents/DateRangerPicker.vue'
import Placeholder from './FormComponents/Placeholder.vue'
import VTable from '@/components/common/Table/src/index.vue'
import CheckGroup from '@/components/common/FormEngine/FormComponents/CheckGroup.vue'
import { ElTimePicker } from 'element-plus'
import Tags from '@/components/common/FormEngine/FormComponents/Tags.vue'
import { FileUpload } from '@/components/common/FileUpload'
import { Text } from '@/components/common/Text'
import { ValueType } from '@/components/common/ValueType'
import Tabs from '@/components/common/Tabs/src/index.vue'
import TreeSelect from '@/components/common/FormEngine/FormComponents/TreeSelect.vue'
import { SysCode } from '@/components/busine/SysCode'
import ValidResult from './FormComponents/ValidResult.vue'
import { ButtonGroup } from '@/components/common/ButtonGroup'

export type FormControlsKeys =
  | 'input'
  | 'numberInput'
  | 'radio'
  | 'select'
  | 'checkbox'
  | 'switch'
  | 'textarea'
  | 'datePicker'
  | 'multipleSelect'
  | 'placeholder'
  | 'table'
  | 'checkGroup'
  | 'ElTimePicker'
  | 'Tags'
  | 'upload'
  | 'text'
  | 'valueType'
  | 'TreeSelect'
  | 'tabs'
  | 'sysCode'
  | 'validResult'
  | 'buttonGroup'
  | string

const componentMap = new Map<FormControlsKeys, Component>()

componentMap.set('input', Input)
componentMap.set('numberInput', NumberInput)
componentMap.set('radio', Radio)
componentMap.set('select', Select)
componentMap.set('checkbox', Checkbox)
componentMap.set('switch', Switch)
componentMap.set('textarea', Textarea)
componentMap.set('datePicker', DatePicker)
componentMap.set('dateRangerPicker', DateRangerPicker)
componentMap.set('multipleSelect', MultipleSelect)
componentMap.set('placeholder', Placeholder)
componentMap.set('table', VTable)
componentMap.set('checkGroup', CheckGroup)
componentMap.set('ElTimePicker', ElTimePicker)
componentMap.set('Tags', Tags)
componentMap.set('upload', FileUpload)
componentMap.set('text', Text)
componentMap.set('valueType', ValueType)
componentMap.set('TreeSelect', TreeSelect)
componentMap.set('tabs', Tabs)
componentMap.set('sysCode', SysCode)
componentMap.set('validResult', ValidResult)
componentMap.set('buttonGroup', ButtonGroup)

export function add(compName: FormControlsKeys, component: Component) {
  if (compName == undefined) return
  componentMap.set(compName, component)
}

export function del(compName: FormControlsKeys) {
  componentMap.delete(compName)
}

export { componentMap }
