import type { Component } from 'vue'
import type { FormControlsKeys } from '../componentMap'

export interface FormFieldConfig {
  /** 字段名 */
  field?: string
  /** 标签文本 */
  label?: string
  /** 占位符 */
  placeholder?: string
  /** 字段描述 */
  description?: string
  /** 组件 */
  component?: Component | FormControlsKeys // 组件名或导入的组件
  /** 组件属性 */
  componentProps?: Record<string, any>
  /** 标签类名 */
  labelClass?: string
  /** 校验规则 */
  validation?: any
  /** 默认值 */
  defaultValue?: any
  /** 是否隐藏 */
  // hidden?: boolean | ((values: Record<string, any>) => boolean);
  /** 是否只读 */
  readonly?: boolean | ((values: Record<string, any>) => boolean)
  /** 字典 */
  dict?: string
  /** 字典赋值路径 */
  dictPath?: string
  /** 字典赋值映射别名 */
  dictAlias?: IDictAlias
  /** 完成设置字典值的回调 */
  afterSetDict?: (options: Recordable[]) => void
  /** 字典设置值前的回调 */
  beforeSetDict?: (options: Recordable[]) => Recordable[]
  /** schema 动态查询数据赋值给 schema */
  api?: (schema: FormFieldConfig) => Promise<FormFieldConfig>
  /** 表单布局组件 */
  layout?: 'Card'
  /** 表单布局组件属性 */
  layoutProps?: Recordable
  schemas?: FormFieldConfig[]
  /** 自定义插槽 */
  slots?: Recordable
  /** 动态判断当前组件是否显示，css 控制，不会删除 dom */
  show?: boolean | ((values: Recordable) => boolean)
  /** 动态判断当前组件是否显示，js 控制，会删除 dom */
  ifShow?: boolean | ((values: Recordable) => boolean)
  /** 当前schema占据的宽度 grid-column: span n  span=true:会跟随form.cols item占满一行  */
  span?:
    | boolean
    | number
    | {
        sm?: number
        md?: number
        lg?: number
        xl?: number
        '2xl'?: number
      }
  /** 表单项属性 */
  formItemProps?: Recordable
  /** 是否必填 - 只展示必填符号 */
  required?: boolean
  labelPosition?: 'top' | 'left'
  labelPrefix?: string
  /** 文字提示 */
  tooltip?: string
}

export interface FormEngineProps {
  /** 字段配置 */
  fields?: FormFieldConfig[]
  /** 初始值 */
  initValues?: Record<string, any>
  /** 提交按钮文本 */
  submitText?: string
  /** 关闭按钮文本 */
  closeText?: string
  /** 全局 schema 校验（可选） */
  schema?: any
  /** 列数 响应式布局 */
  cols?: {
    sm?: number
    md?: number
    lg?: number
    xl?: number
    '2xl'?: number
  }
  size?: '' | 'large' | 'default' | 'small'
  /** 使用查询的metaData的值匹配fields下的label 用于国际化 */
  metaData?: string
  formProps?: Recordable
  record?: Recordable
  labelPosition?: 'top' | 'left' | 'right'
  columnsApi?: Function
  fetchApi?: () => Promise<Recordable | false>
  mode?: 'detail' | 'default'
  labelPrefix?: string
  /** 表单校验错误提示模式  默认dialogAndDefault, 如果配置其他任何值都不会走统一的错误提示 */
  validateModel?: 'default' | 'dialog' | 'dialogAndDefault' | string
}

export interface IDictAlias {
  label?: string
  value?: string
  isDefault?: string
}

export interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'register', methods: any): void
}
