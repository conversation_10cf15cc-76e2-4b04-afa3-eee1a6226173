<template>
    <form :class="formLayoutClass" v-bind="propsRef.formProps">
        <template v-for="fieldItem in getFields || []" :key="fieldItem.field">
            <ZbCard
                v-if="fieldItem.layout === 'Card'"
                v-bind="fieldItem.layoutProps"
                :class="[
                    {
                        hidden: !computedShow(fieldItem),
                        [formItemSpans(fieldItem.span || true)]: true,
                    },
                ]"
            >
                <div :class="formLayoutClass">
                    <template
                        v-for="schema in fieldItem.schemas || []"
                        :key="schema.field"
                    >
                        <DynamicFormField
                            :fieldItem="schema"
                            :formApi="formApi"
                            :labelPosition="
                                propsRef?.labelPosition || schema?.labelPosition
                            "
                            :labelPrefix="
                                schema?.labelPrefix || propsRef?.labelPrefix
                            "
                            :size="propsRef?.size"
                            :formItemClass="formItemSpans(schema.span)"
                            @registerCom="registerCom"
                        >
                            <template
                                class="flex-1"
                                v-for="item in getComponentSlots(schema)"
                                #[item]="data"
                            >
                                <slot
                                    :name="schema?.slots?.[item]"
                                    v-bind="data || {}"
                                />
                            </template>
                        </DynamicFormField>
                    </template>
                </div>
                <!-- ZbCard 的插槽 -->
                <template
                    v-for="item in getComponentSlots(fieldItem)"
                    #[item]="data"
                    :key="item"
                >
                    <slot
                        :name="fieldItem?.slots?.[item]"
                        v-bind="data || {}"
                    />
                </template>
            </ZbCard>
            <template v-else>
                <DynamicFormField
                    :fieldItem="fieldItem"
                    :formApi="formApi"
                    :labelPosition="
                        fieldItem?.labelPosition || propsRef?.labelPosition
                    "
                    :labelPrefix="
                        fieldItem?.labelPrefix || propsRef?.labelPrefix
                    "
                    :size="propsRef?.size"
                    :formItemClass="formItemSpans(fieldItem.span)"
                    @registerCom="registerCom"
                >
                    <template
                        class="flex-1"
                        v-for="item in getComponentSlots(fieldItem)"
                        #[item]="data"
                    >
                        <slot
                            :name="fieldItem?.slots?.[item]"
                            v-bind="data || {}"
                        />
                    </template>
                </DynamicFormField>
            </template>
        </template>
    </form>
</template>

<script setup lang="ts">
import {
    computed,
    type Component,
    watch,
    ref,
    unref,
    type ComputedRef,
    onMounted,
    onUnmounted,
    getCurrentInstance,
    inject,
    provide,
    nextTick,
} from 'vue';
import { useForm } from 'vee-validate';
import { toTypedSchema } from '@vee-validate/zod';
import * as z from 'zod';
import DynamicFormField from './FormComponents/DynamicFormField.vue';
import type { FormEngineProps, FormFieldConfig } from './types/formEngine';
import type { AnyZodObject } from 'zod';
// import formControls, { type FormControlsKeys } from './form-contarols';
import { componentMap } from './componentMap';
import { deepMerge } from '@/utils/object';
import {
    cloneDeep,
    get,
    isArray,
    isBoolean,
    isEmpty,
    isFunction,
    isNil,
    isNumber,
    isObject,
    uniqBy,
} from 'lodash-es';
import { useDict } from './hooks/useDict';
import { useSchemaApi } from './hooks/useSchemaApi';
import { useMetaData } from './hooks/useMetaData';
import { useFetchData } from './hooks/useFetchData';
import ZbCard from '@/components/common/ZbCard/ZbCard.vue';
import { cn } from '@/lib/utils';
import { PartialDeep } from 'type-fest';

const FORM_CUSTOMER_COM_ACTIONS_KEY = Symbol();

const props = withDefaults(
    defineProps<FormEngineProps & { modelValue?: Record<string, any> }>(),
    {
        modelValue: () => {
            // 使用函数返回一个新的空对象，确保每次组件实例化时都创建一个全新的引用
            // 这样可以避免多个组件实例共享同一个对象引用导致的数据污染问题
            return {};
        },
    }
);

const customerComActions = new Map();

const currentInstance = getCurrentInstance();

const propsRef = ref<Partial<FormEngineProps>>();

const getBind = computed(() => {
    return unref(propsRef);
}) as ComputedRef<FormEngineProps>;

const schemaRef = ref<FormFieldConfig[] | null>();

init();

// 布局列数
const formLayoutClass = computed(() => {
    const sm = unref(propsRef)?.cols?.sm || 1;
    const md = unref(propsRef)?.cols?.md || 2;
    const lg = unref(propsRef)?.cols?.lg || 3;
    const xl = unref(propsRef)?.cols?.xl || 4;
    const txl = unref(propsRef)?.cols?.['2xl'] || 5;

    let size = 4;
    switch (unref(propsRef)?.size) {
        case 'small':
            size = 2;
            break;
        case 'large':
            size = 4;
            break;
    }

    return `grid gap-${size} sm:grid-cols-${sm} md:grid-cols-${md} lg:grid-cols-${lg} xl:grid-cols-${xl} 2xl:grid-cols-${txl}`;
});

const formItemSpans = computed(() => {
    return (span) => {
        if (!span) return '';

        let sm;
        let md;
        let lg;
        let xl;
        let txl;

        if (span === true) {
            sm = unref(propsRef)?.cols?.sm || 1;
            md = unref(propsRef)?.cols?.md || 2;
            lg = unref(propsRef)?.cols?.lg || 3;
            xl = unref(propsRef)?.cols?.xl || 4;
            txl = unref(propsRef)?.cols?.['2xl'] || 5;
        } else {
            sm = isNumber(span) ? span : span?.sm;
            md = isNumber(span) ? span : span?.md;
            lg = isNumber(span) ? span : span?.lg;
            xl = isNumber(span) ? span : span?.xl;
            txl = isNumber(span) ? span : span?.['2xl'];
        }

        return `sm:col-span-${sm} md:col-span-${md} lg:col-span-${lg} xl:col-span-${xl} 2xl:col-span-${txl}`;
    };
});

const formApi = ref();
// 包含 layout=Card 的schemas
const getFields = computed(() => {
    const schemas = unref(schemaRef);
    if (!schemas) return [];

    const checkIfShow = (ifShow, values) => {
        if (isBoolean(ifShow)) return ifShow;
        if (isFunction(ifShow)) return ifShow(values);
        return true; // 默认显示
    };

    return schemas
        .map((schema) => {
            const isIfShow = checkIfShow(schema.ifShow, formApi?.value?.values);
            if (!isIfShow) return null;

            // 处理 Card 布局的 schema
            if (schema.layout === 'Card' && schema.schemas?.length) {
                const filteredSchemas = schema.schemas.filter((s) =>
                    checkIfShow(s.ifShow, formApi?.value?.values)
                );
                return { ...schema, schemas: filteredSchemas };
            }

            return schema;
        })
        .filter(Boolean); // 过滤掉 null 和 undefined
}) as ComputedRef<FormFieldConfig[]>;

// 计算 Show
const computedShow = computed(() => {
    const checkShow = (show, values) => {
        if (isBoolean(show)) return show;
        if (isFunction(show)) return show(values);
        return true; // 默认显示
    };

    return (schema) => {
        return checkShow(schema?.show, formApi.value.values);
    };
});

console.log('getFields', getFields);

// 不包含 layout=Card 的schemas
const getSchema = computed(() => {
    return (
        (unref(getFields) ?? []).reduce<FormFieldConfig[]>((pre, next) => {
            return next.layout
                ? [...pre, ...(next.schemas || [])]
                : [...pre, next];
        }, []) || []
    );
}) as ComputedRef<FormFieldConfig[]>;

// 生成合并后的 Schema 用于校验
const fullSchema = computed(() => {
    // 确保 fields 存在，如果为 undefined 则使用空数组
    // const fieldsArray = unref(getBind).fields || [];
    const fieldsArray = getSchema.value;

    const fieldSchemas = fieldsArray.reduce(
        (acc, fieldItem) => {
            if (fieldItem.validation && fieldItem.field) {
                acc[fieldItem.field] = fieldItem.validation;
            }
            return acc;
        },
        {} as Record<string, AnyZodObject>
    );

    return unref(getBind).schema
        ? z.object(fieldSchemas).merge(unref(getBind).schema)
        : z.object(fieldSchemas);
});

// 初始化表单内容
const defaultInitValues = computed(() => {
    // 确保 fields 存在，如果为 undefined 则使用空数组

    const fieldsArray = getSchema.value || [];
    let obj = {
        ...fieldsArray.reduce(
            (acc, fieldItem) => {
                if (fieldItem.defaultValue !== undefined && fieldItem.field) {
                    acc[fieldItem.field] = fieldItem.defaultValue;
                }
                return acc;
            },
            {} as Record<string, any>
        ),
    };
    if (
        unref(getBind).initValues &&
        // @ts-ignore
        Object.keys(unref(getBind).initValues).length > 0
    ) {
        obj = {
            ...obj,
            ...unref(getBind).initValues,
        };
    }
    if (props.modelValue && Object.keys(props.modelValue).length > 0) {
        obj = {
            ...obj,
            ...props.modelValue,
        };
    }
    return obj;
});

// 初始化表单
formApi.value = useForm({
    validationSchema: computed(() => toTypedSchema(fullSchema.value)),
    initialValues: defaultInitValues.value,
});

console.log('formApi.value', formApi.value.values);

/** 提交前校验 */
const validateForm = async () => {
    const isValid = await formApi.value.validate();
    if (isValid.valid) {
        return true;
    }
    return false;
};

// 初始化表单
// 1.通过useForm方式使用表单 formProps 必然有值
// 2.常规方式使用表单 formProps 必然无值，直接取值props
function init(formProps?: Partial<FormEngineProps>) {
    if (!formProps) {
        propsRef.value = props;
        schemaRef.value = props.fields;
    } else {
        propsRef.value = deepMerge(unref(propsRef) || {}, formProps);

        schemaRef.value = unref(getBind)?.fields;
    }
}

function setProps(formProps?: Partial<FormEngineProps>) {
    init(formProps);
    initDict();
    initSchemaApi();
    initMetaData();
    initDefaultValues();
}

function getApi(field) {
    return customerComActions?.get(field);
}

function registerCom(api, field: string) {
    if (!field) return;
    customerComActions.set(field as string, api);
}

const methods = {
    ...formApi.value,
    submit,
    updateSchema,
    updateAllSchema,
    init,
    setProps,
    getApi,
    getValues: () => {
        return cloneDeep(formApi.value.values);
    },
    setValues: (fields: PartialDeep<Recordable>, shouldValidate?: boolean) => {
        formApi.value.setValues(fields, shouldValidate);

        customerComActions.forEach((com, field) => {
            const val = get(fields, field);
            !isNil(val) && com?.setValues(val);
        });
    },
};

provide('$form', methods);
provide('__formPropsRef', propsRef);

/** 暴露给父组件使用校验 */
defineExpose({
    validateForm,
    methods,
});

// 操作处理
const emit = defineEmits(['update:modelValue', 'register']);

// 监听表单值变化，实时向父组件发送更新
watch(
    formApi.value.values,
    (newValues) => {
        /** 每次值更新时，触发useForm的setValues保持同步更新，第二个参数 false 表示不触发校验 */
        formApi.value.setValues(
            {
                ...defaultInitValues.value,
                ...newValues,
            },
            false
        );
        emit('update:modelValue', newValues);
    },
    { deep: true }
);

// 根据传入的组件名称，动态解析组件
const resolveComponent = (component: Component | string) => {
    if (typeof component === 'string') {
        // return formControls[component as FormControlsKeys];
        return componentMap.get(component);
    }
    return component;
};

function updateSchema(data: FormFieldConfig | FormFieldConfig[]) {
    // 初始化待更新数据
    let updateData = [];
    if (isObject(data)) {
        updateData.push(data as FormFieldConfig);
    }
    if (isArray(data)) {
        updateData = [...data];
    }

    // 字段校验
    if (!validateFields(updateData)) return;

    // 合并配置
    const schemaList = unref(getFields) || [];
    const mergedSchema = mergeSchemas(schemaList, updateData);

    // 更新响应式引用
    schemaRef.value = mergedSchema;
}

function updateAllSchema(data: FormFieldConfig) {
    let updateData = getSchema.value?.map((schema) => {
        return {
            ...schema,
            ...data,
        };
    });
    // 合并配置
    const schemaList = unref(getFields) || [];
    const mergedSchema = mergeSchemas(schemaList, updateData);

    // 更新响应式引用
    schemaRef.value = mergedSchema;
}

const validateFields = (data: Partial<FormFieldConfig>[]) => {
    const isValid = data.every(
        (item) => Reflect.has(item, 'field') && item.field
    );
    if (!isValid) console.error("Missing 'field' in schema items");
    return isValid;
};

const mergeSchemas = (
    originSchemas: FormFieldConfig[],
    updateData: Partial<FormFieldConfig>[]
) => {
    return originSchemas.map((schema) => {
        if (schema.layout === 'Card' && schema.schemas?.length) {
            return {
                ...schema,
                schemas: schema.schemas.map((s) =>
                    deepMerge(
                        s,
                        updateData.find((u) => u.field === s.field) || {}
                    )
                ),
            };
        }
        return deepMerge(
            schema,
            updateData.find((u) => u.field === schema.field) || {}
        );
    });
};

async function submit(): Promise<Recordable | undefined> {
    const isValid = await formApi.value.validate();
    if (isValid.valid) {
        return formApi.value.values;
    } else {
        throw isValid;
    }
}

function getComponentSlots(schema: FormFieldConfig) {
    const { slots: comSlots } = schema;
    const slots = currentInstance?.slots || {};
    let allSlotsKey = Object.keys(slots);
    if (!comSlots || isEmpty(slots)) return [];

    const compSlot: Recordable = {};

    Object.entries(comSlots).forEach(([originName, slotName]) => {
        if (slots?.[slotName]) {
            compSlot[originName] = slots[slotName];
            allSlotsKey = allSlotsKey.filter((key) => key !== slotName);
        }
    });

    return Object.keys(compSlot);
}

const { initDict } = useDict({ getSchema, updateSchema });
const { initSchemaApi } = useSchemaApi({ getSchema, updateSchema });
const { initMetaData } = useMetaData({ propsRef, getSchema, updateSchema });
const { initFetchData } = useFetchData({
    propsRef,
    setValues: formApi.value.setValues,
});

function initDefaultValues() {
    formApi.value.setValues(unref(defaultInitValues), false);
}

onMounted(() => {
    initDict();
    initSchemaApi();
    initMetaData();
    initDefaultValues();

    nextTick(() => {
        initFetchData();
    });
});

// 在组件卸载时清空modelValue
onUnmounted(() => {
    // 通过emit事件将modelValue设置为空对象，实现清空效果
    emit('update:modelValue', {});
});

emit('register', methods);
</script>

<style scoped>
:deep(.is-required) {
    > label {
        &::before {
            content: '*';
            color: hsl(var(--destructive));
            margin-right: 4px;
        }
    }
}

.col-span-2 {
    grid-column: span 2;
}

.col-span-3 {
    grid-column: span 3;
}

.col-span-4 {
    grid-column: span 4;
}

.col-span-5 {
    grid-column: span 5;
}

.col-span-6 {
    grid-column: span 6;
}

.col-span-7 {
    grid-column: span 7;
}

.col-span-9 {
    grid-column: span 8;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
}
</style>
