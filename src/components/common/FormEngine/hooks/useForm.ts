import { computed, ref, unref } from 'vue'
import type { FormEngineProps, FormFieldConfig } from '../types/formEngine'
import type {
  FieldState,
  FlattenAndSetPathsType,
  // FormActions,
  FormState,
  Path,
  ResetFormOpts,
} from 'vee-validate'
import type { PartialDeep } from 'type-fest'

export interface FormActions {
  getApi(field: string): Recordable
  setFieldValue(
    field: string,
    value: Recordable,
    shouldValidate?: boolean
  ): void
  setFieldError(field: string, message: string | string[] | undefined): void
  setErrors(
    fields: Partial<
      FlattenAndSetPathsType<Recordable, string | string[] | undefined>
    >
  ): void
  setValues(fields: PartialDeep<Recordable>, shouldValidate?: boolean): void
  getValues(): Recordable
  setFieldTouched(field: Path<Recordable>, isTouched: boolean): void
  setTouched(fields: Partial<Record<Path<Recordable>, boolean>> | boolean): void
  resetForm(
    state?: Partial<FormState<Recordable>>,
    opts?: Partial<ResetFormOpts>
  ): void
  resetField(field: Path<Recordable>, state?: Partial<FieldState>): void
  updateSchema(data: FormFieldConfig | FormFieldConfig[]): void
  appendSchema(schema: FormFieldConfig, field?: string): void
  appendSchemas(schemas: FormFieldConfig[], field?: string): void
  deleteSchema(field: string): void
  updateAllSchema(data: FormFieldConfig): void
  submit(): Promise<Recordable | undefined>
  setProps(props: Partial<FormEngineProps>): void
}

type IUseForm = [
  register: (api: FormActions) => void,
  formAction: Omit<FormActions, 'init'>,
]

export function useForm(props: FormEngineProps): IUseForm {
  const methods = ref<Recordable>()

  const register = (api: Recordable): any => {
    methods.value = api
    props && init(props)
  }

  function init(props: FormEngineProps) {
    unref(methods)!.init(props)
  }

  function setProps(props: FormEngineProps) {
    unref(methods)?.setProps(props)
  }

  function getApi(field: string) {
    return unref(methods)!.getApi(field)
  }

  // const controlledValues = computed(() => unref(methods)!.controlledValues);

  // function createPathState(path, config) {
  //   return unref(methods)!.createPathState(path, config)
  // }

  function setFieldValue(
    field: string,
    value: Recordable,
    shouldValidate?: boolean
  ) {
    unref(methods)!.setFieldValue(field, value, shouldValidate)
  }

  function setFieldError(
    field: string,
    message: string | string[] | undefined
  ) {
    unref(methods)!.setFieldError(field, message)
  }

  function setErrors(
    fields: Partial<
      FlattenAndSetPathsType<Recordable, string | string[] | undefined>
    >
  ) {
    unref(methods)!.setErrors(fields)
  }

  function setValues(
    fields: PartialDeep<Recordable>,
    shouldValidate?: boolean
  ) {
    unref(methods)?.setValues(fields, shouldValidate)
  }

  function getValues() {
    return unref(methods)!.getValues()
  }

  function setFieldTouched(field: Path<Recordable>, isTouched: boolean) {
    unref(methods)!.setValues(field, isTouched)
  }

  function setTouched(
    fields: Partial<Record<Path<Recordable>, boolean>> | boolean
  ) {
    unref(methods)!.setTouched(fields)
  }

  function resetForm(
    state?: Partial<FormState<Recordable>>,
    opts?: Partial<ResetFormOpts>
  ) {
    unref(methods)!.resetForm(state, opts)
  }

  function resetField(field: Path<Recordable>, state?: Partial<FieldState>) {
    unref(methods)!.resetField(field, state)
  }

  function updateSchema(data: FormFieldConfig | FormFieldConfig[]) {
    unref(methods)!.updateSchema(data)
  }

  function updateAllSchema(data: FormFieldConfig) {
    unref(methods)!.updateAllSchema(data)
  }

  function appendSchema(schema: FormFieldConfig, field?: string) {
    unref(methods)!.appendSchema(schema, field)
  }

  function appendSchemas(schemas: FormFieldConfig[], field?: string) {
    unref(methods)!.appendSchema(schemas, field)
  }

  function deleteSchema(field: string) {
    unref(methods)!.deleteSchema(field)
  }

  function submit() {
    return unref(methods)?.submit()
  }

  return [
    register,
    {
      getApi,
      setFieldValue,
      setFieldError,
      setErrors,
      setValues,
      getValues,
      setFieldTouched,
      setTouched,
      resetForm,
      resetField,
      updateSchema,
      updateAllSchema,
      appendSchema,
      appendSchemas,
      deleteSchema,
      submit,
      setProps,
    },
  ]
}
