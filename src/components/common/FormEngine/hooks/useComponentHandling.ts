import { Component, ComponentInternalInstance, ref } from 'vue';
import { componentMap } from '../componentMap';
import { isEmpty } from 'lodash-es';
import { FormFieldConfig } from '../types/formEngine';

export function useComponentHandling(
    currentInstance: ComponentInternalInstance
) {
    const customerComActions = new Map<string, Recordable>();

    function registerCom(api: Recordable, field: string) {
        if (!field) return;
        customerComActions.set(field, api);
    }

    function getApi(field: string) {
        return customerComActions.get(field);
    }

    function resolveComponent(component: Component | string) {
        if (typeof component === 'string') {
            return componentMap.get(component);
        }
        return component;
    }

    function getComponentSlots(schema: FormFieldConfig) {
        const { slots: comSlots } = schema;
        const slots = currentInstance?.slots || {};
        let allSlotsKey = Object.keys(slots);
        if (!comSlots || isEmpty(slots)) return [];

        const compSlot: Recordable = {};

        Object.entries(comSlots).forEach(([originName, slotName]) => {
            if (slots?.[slotName]) {
                compSlot[originName] = slots[slotName];
                allSlotsKey = allSlotsKey.filter((key) => key !== slotName);
            }
        });

        return Object.keys(compSlot);
    }

    return {
        customerComActions,
        registerCom,
        getApi,
        resolveComponent,
        getComponentSlots,
    };
}
