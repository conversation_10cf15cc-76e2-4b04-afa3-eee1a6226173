import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { computed, ComputedRef, ref, Ref, unref, watch } from 'vue'
import { Emits, FormEngineProps, FormFieldConfig } from '../types/formEngine'
import { PartialDeep } from 'type-fest'
import {
  cloneDeep,
  get,
  isArray,
  isBoolean,
  isEmpty,
  isFunction,
  isNil,
  isObject,
  set,
} from 'lodash-es'
import { AnyZodObject } from 'zod'
import { deepMerge } from '@/utils/object'
import { useGlobDialogForm } from '../../DialogForm'

export function useFormCore(
  propsRef: Ref<Partial<FormEngineProps>>,
  getBind: Ref<Partial<FormEngineProps>>,
  schemaRef: Ref<FormFieldConfig[] | null>,
  defaultInitValues: ComputedRef<Record<string, any>>,
  customerComActions: Map<string, Recordable<any, string>>,
  emit: Emits
) {
  const formApi = ref()
  // 包含 layout=Card 的schemas
  const getFields = computed(() => {
    const schemas = unref(schemaRef)
    if (!schemas) return []

    const checkIfShow = (ifShow, values) => {
      if (isBoolean(ifShow)) return ifShow
      if (isFunction(ifShow)) return ifShow(values)
      return true // 默认显示
    }

    return schemas
      .map((schema) => {
        const isIfShow = checkIfShow(schema.ifShow, formApi?.value?.values)
        if (!isIfShow) return null

        // 处理 Card 布局的 schema
        if (schema.layout === 'Card' && schema.schemas?.length) {
          const filteredSchemas = schema.schemas.filter((s) =>
            checkIfShow(s.ifShow, formApi?.value?.values)
          )
          return { ...schema, schemas: filteredSchemas }
        }

        return schema
      })
      .filter(Boolean) // 过滤掉 null 和 undefined
  }) as ComputedRef<FormFieldConfig[]>

  // 不包含 layout=Card 的schemas
  const getSchema = computed(() => {
    return (
      (unref(getFields) ?? []).reduce<FormFieldConfig[]>((pre, next) => {
        return next.layout ? [...pre, ...(next.schemas || [])] : [...pre, next]
      }, []) || []
    )
  }) as ComputedRef<FormFieldConfig[]>

  // 生成合并后的 Schema 用于校验
  const fullSchema = computed(() => {
    // 确保 fields 存在，如果为 undefined 则使用空数组
    // const fieldsArray = unref(getBind).fields || [];
    const fieldsArray = getSchema.value

    const fieldSchemas = fieldsArray.reduce(
      (acc, fieldItem) => {
        if (fieldItem.validation && fieldItem.field) {
          acc[fieldItem.field] = fieldItem.validation
        }
        return acc
      },
      {} as Record<string, AnyZodObject>
    )

    return unref(getBind).schema
      ? z.object(fieldSchemas).merge(unref(getBind).schema)
      : z.object(fieldSchemas)
  })

  formApi.value = useForm({
    validationSchema: computed(() => toTypedSchema(fullSchema.value)),
    initialValues: defaultInitValues.value,
  })

  const computedShow = computed(() => {
    const checkShow = (show, values) => {
      if (isBoolean(show)) return show
      if (isFunction(show)) return show(values)
      return true // 默认显示
    }

    return (schema) => {
      return checkShow(schema?.show, formApi.value.values)
    }
  })

  async function submit(): Promise<Recordable | undefined> {
    try {
      const isValid = await formApi.value.validate()
      let customerComValid: Record<string, any> | undefined

      // 将 customerComActions 转换为 Promise 数组
      const promises = Array.from(customerComActions).map(
        async ([field, com]) => {
          if (isNil(field)) return

          try {
            const v = await com?._validate()
            if (!isNil(v)) {
              if (isNil(customerComValid)) customerComValid = {}
              set(customerComValid, `errors.${field}`, v)
            }
          } catch (error) {
            console.error(error)
          }
        }
      )

      // 等待所有异步验证完成
      await Promise.all(promises)

      // 此时所有异步操作已完成
      if (!isNil(customerComValid)) {
        set(isValid, 'valid', false)
        Object.assign(isValid.errors, customerComValid.errors)
      }

      if (isValid.valid) {
        return formApi.value.values
      } else {
        validateModel(isValid)

        throw isValid
      }
    } catch (error) {
      throw error
    }
  }

  function validateModel(isValid) {
    const { validateModel = 'dialogAndDefault' } = unref(propsRef)
    if (validateModel === 'dialog') {
      dialogValid(isValid)
    } else if (validateModel === 'default') {
      defaultValid(isValid)
    } else if (validateModel === 'dialogAndDefault') {
      defaultValid(isValid)
      dialogValid(isValid)
    }
  }

  function dialogValid(isValid) {
    const { openDialogForm } = useGlobDialogForm()
    const errorsValues = {}
    for (const schema of unref(getFields)) {
      if (schema.schemas) {
        schema.schemas.forEach((child) => {
          const val = get(isValid.errors, child.field)
          if (!isNil(val)) {
            set(errorsValues, child.label || child.field, val)
          }
        })
      } else {
        const val = get(isValid.errors, schema.field)
        if (!isNil(val)) {
          set(errorsValues, schema.label || schema.field, val)
        }
      }
    }
    openDialogForm({
      dialogProps: {
        title: '表单验证',
        size: 'mini',
      },
      cols: {
        sm: 1,
        md: 1,
        lg: 1,
        xl: 1,
        '2xl': 1,
      },
      fields: [
        {
          field: 'err',
          span: true,
          defaultValue: errorsValues,
          component: 'validResult',
        },
      ],
    })
  }

  function defaultValid(isValid) {
    const tabs = unref(getFields).find((schema) => schema.component === 'tabs')
    const TAB_FIELDS_MAP = {}
    tabs?.componentProps?.options?.forEach((item) => {
      const card = unref(getFields).find(
        (schema) => schema.layoutProps?.id === item.value
      )
      const fields = card?.schemas?.map((schema) => schema.field)
      if (!fields) return
      set(TAB_FIELDS_MAP, item.value, fields)
    })

    if (isEmpty(TAB_FIELDS_MAP)) {
      console.error(`请配置 layoutProps.id`)
      return
    }

    // 统一错误处理逻辑
    for (const key in isValid.errors) {
      if (Object.prototype.hasOwnProperty.call(isValid.errors, key)) {
        // 定位错误所在tab页签
        for (const [tabKey, fields] of Object.entries(TAB_FIELDS_MAP)) {
          // @ts-ignore
          if (fields?.includes(key)) {
            setValues({ _tabs: tabKey }, false)
            break
          }
        }
      }
    }
  }

  function setValues(
    fields: PartialDeep<Recordable>,
    shouldValidate?: boolean
  ) {
    formApi.value.setValues(fields, shouldValidate)
  }

  function getValues(): Recordable {
    return cloneDeep(formApi.value.values)
  }

  async function validateForm() {
    const isValid = await formApi.value.validate()
    return isValid.valid
  }

  function updateSchema(data: FormFieldConfig | FormFieldConfig[]) {
    // 初始化待更新数据
    let updateData = []
    if (isObject(data)) {
      updateData.push(data as FormFieldConfig)
    }
    if (isArray(data)) {
      updateData = [...data]
    }

    // 字段校验
    if (!validateFields(updateData)) return

    // 合并配置
    const schemaList = unref(getFields) || []
    const mergedSchema = mergeSchemas(schemaList, updateData)

    // 更新响应式引用
    schemaRef.value = mergedSchema
  }

  function updateAllSchema(data: FormFieldConfig) {
    let updateData = getSchema.value?.map((schema) => {
      return {
        ...schema,
        ...data,
      }
    })
    // 合并配置
    const schemaList = unref(getFields) || []
    const mergedSchema = mergeSchemas(schemaList, updateData)

    // 更新响应式引用
    schemaRef.value = mergedSchema
  }

  function appendSchema(schema: FormFieldConfig, field?: string) {
    if (!field) {
      // 无指定字段时直接添加到末尾
      schemaRef.value.push(schema)
      return
    }

    // 查找插入位置
    const result = findInsertionPosition(schemaRef.value, field)

    if (result) {
      const { array, index } = result
      // 插入到指定字段之后
      array.splice(index + 1, 0, schema)
    } else {
      // 未找到目标字段时添加到末尾（或根据需求抛出错误）
      console.warn(`[Schema Warning] Field "${field}" not found in schema`)
      schemaRef.value.push(schema)
    }
  }

  function appendSchemas(schemas: FormFieldConfig[], field?: string) {
    schemas?.forEach((schema) => {
      appendSchema(schema, field)
    })
  }

  function deleteSchema(field: string) {
    function removeField(elements: FormFieldConfig[]): boolean {
      for (let i = 0; i < elements.length; i++) {
        // 直接匹配到目标字段
        if (elements[i].field === field) {
          elements.splice(i, 1)
          return true
        }

        // 支持其他嵌套结构（如 tabs、cards 等）
        if (elements[i].schemas?.length && removeField(elements[i].schemas)) {
          return true
        }
      }
      return false
    }

    // 执行删除操作
    const deleted = removeField(schemaRef.value)

    if (!deleted) {
      console.warn(`[Schema Warning] Field "${field}" not found in schema`)
    }
  }

  function findInsertionPosition(
    elements: FormFieldConfig[],
    targetField: string
  ): { array: FormFieldConfig[]; index: number } | undefined {
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i]

      // 直接匹配到目标字段
      if (element.field === targetField) {
        return { array: elements, index: i }
      }

      // 支持其他嵌套结构（如 tabs、cards 等）
      if (element.schemas?.length) {
        const result = findInsertionPosition(element.schemas, targetField)
        if (result) return result
      }
    }
    return undefined
  }

  const validateFields = (data: Partial<FormFieldConfig>[]) => {
    const isValid = data.every(
      (item) => Reflect.has(item, 'field') && item.field
    )
    if (!isValid) console.error("Missing 'field' in schema items")
    return isValid
  }

  const mergeSchemas = (
    originSchemas: FormFieldConfig[],
    updateData: Partial<FormFieldConfig>[]
  ) => {
    return originSchemas.map((schema) => {
      if (schema.layout === 'Card' && schema.schemas?.length) {
        return {
          ...schema,
          schemas: schema.schemas.map((s) =>
            deepMerge(s, updateData.find((u) => u.field === s.field) || {})
          ),
        }
      }
      return deepMerge(
        schema,
        updateData.find((u) => u.field === schema.field) || {}
      )
    })
  }

  return {
    formApi,
    getFields,
    getSchema,
    fullSchema,
    computedShow,
    submit,
    setValues,
    getValues,
    validateForm,
    updateSchema,
    updateAllSchema,
    appendSchema,
    appendSchemas,
    deleteSchema,
    validateFields,
    mergeSchemas,
  }
}
