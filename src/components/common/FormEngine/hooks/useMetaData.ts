import { ref, unref, type ComputedRef, type Ref } from 'vue';
import type { FormEngineProps, FormFieldConfig } from '../types/formEngine';
import { get, isFunction, set } from 'lodash-es';

interface IUseMetaData {
    propsRef: Ref<FormEngineProps>;
    getSchema: ComputedRef<FormFieldConfig[]> | null;
    updateSchema: (data: FormFieldConfig | FormFieldConfig[]) => void;
}
export function useMetaData({
    propsRef,
    getSchema,
    updateSchema,
}: IUseMetaData) {
    const originMetaData = ref();

    async function initMetaData() {
        // unref(getSchema)?.forEach((schema) => {
        //     const item = fields?.find((s) => s.name == schema.field);
        //     set(schema, 'label', item?.comment);
        //     updateSchema(schema);
        // });

        fetchColumns();
    }

    async function fetchColumns(opt?: Recordable) {
        const props = unref(propsRef);
        const { columnsApi, mode } = props;

        if (!columnsApi || !isFunction(columnsApi)) return;

        try {
            // 获取字段元数据
            const result = await columnsApi(opt);

            // 处理不同的返回格式，获取字段信息
            let fields;
            if (Array.isArray(result)) {
                // 如果直接返回数组
                fields = result;
            } else if (result && result.data && result.data.fields) {
                // 如果返回的是 { code, msg, data: { fields: [...] } } 格式（后端标准格式）
                fields = result.data.fields;
            } else if (result && result.fields) {
                // 如果返回的是 { fields: [...] } 格式
                fields = result.fields;
            } else if (result && result.data) {
                // 如果返回的是 { data: [...] } 格式
                fields = Array.isArray(result.data)
                    ? result.data
                    : [result.data];
            } else {
                // 其他格式
                fields = result ? [result] : [];
            }

            // 如果 fields 不存在，直接使用传入的columns
            if (!fields || !Array.isArray(fields)) {
                console.warn('columnsApi 返回的数据格式不正确:', result);
                return;
            }

            // 保存元数据
            originMetaData.value = fields;

            // 创建字段信息映射表，便于快速查找
            const fieldMap = new Map<string, any>();
            fields.forEach((field) => {
                const fieldName = field.field || field.name;
                if (fieldName) {
                    fieldMap.set(fieldName, field);
                }
            });

            unref(getSchema)?.forEach((schema) => {
                if (mode === 'detail' && !get(schema, 'component'))
                    set(schema, 'component', 'valueType');

                const item = fields?.find((s) => s.name == schema.field);
                const enhance = enhanceColumnWithFieldInfo(schema, item);

                enhance && updateSchema(enhance);
            });
            console.log('unref(getSchema)', unref(getSchema));
        } catch (error) {
            console.error('Failed to fetch or process columns:', error);
        }
    }

    return {
        initMetaData,
    };
}

/**
 * 根据字段信息增强列配置
 * @param columnConfig 现有的列配置
 * @param fieldInfo 从API获取的字段信息
 * @returns 增强后的列配置
 */
function enhanceColumnWithFieldInfo(columnConfig: any, fieldInfo: any) {
    // 如果没有字段信息，直接返回原始配置
    if (!fieldInfo) {
        return columnConfig;
    }

    console.log(
        `[列增强] 字段 ${fieldInfo.name} 类型为 ${fieldInfo.type}，开始处理...`
    );
    const enhanced = { ...columnConfig };

    // 基础信息增强
    if (fieldInfo.comment && !enhanced.label) {
        enhanced.label = fieldInfo.comment;
        console.log(`  - 设置标题: ${fieldInfo.comment}`);
    }

    // if (fieldInfo.nullable === false && !enhanced.required) {
    //     enhanced.required = true;
    //     console.log(`  - 设置为必填字段`);
    // }

    // 根据字段类型进行增强
    if (fieldInfo.type === 'string') {
        if (get(enhanced, 'component') === 'valueType') {
            set(
                enhanced,
                'componentProps.type',
                get(enhanced, 'componentProps.type') || 'text'
            );
        }
    } else if (fieldInfo.type === 'integer') {
        if (get(enhanced, 'component') === 'valueType') {
            set(
                enhanced,
                'componentProps.type',
                get(enhanced, 'componentProps.type') || 'number'
            );
        }
        console.log(`  - 设置整数格式`);
    } else if (fieldInfo.type === 'float') {
        console.log(`  - 检测到浮点数类型，字段名: ${fieldInfo.name}`);

        // 智能识别字段用途
        const fieldName = fieldInfo.name.toLowerCase();
        let formatType = 'number'; // 默认格式

        //  format?: 'number' | 'currency' | 'percentage' | 'custom';
        if (
            fieldName.includes('rate') ||
            fieldName.includes('percent') ||
            fieldName.includes('ratio')
        ) {
            formatType = 'percentage';
            console.log(`  - 识别为百分比字段: ${fieldName}`);
        } else if (
            fieldName.includes('price') ||
            fieldName.includes('amount') ||
            fieldName.includes('cost') ||
            fieldName.includes('fee')
        ) {
            formatType = 'currency';
            console.log(`  - 识别为货币字段: ${fieldName}`);
        } else {
            console.log(`  - 识别为普通浮点数字段: ${fieldName}`);
        }

        if (!enhanced.formatter) {
            if (formatType === 'percentage') {
                enhanced.formatter = ({ cellValue }: any) => {
                    if (cellValue == null) return '';
                    const numValue = Number(cellValue);
                    if (isNaN(numValue)) return cellValue;

                    // 智能判断是0-1小数还是0-100百分比
                    if (numValue <= 1) {
                        return `${(numValue * 100).toFixed(1)}%`;
                    } else {
                        return `${numValue.toFixed(1)}%`;
                    }
                };
                console.log(`  - 设置百分比格式化器`);
            } else if (formatType === 'currency') {
                enhanced.formatter = ({ cellValue }: any) => {
                    if (cellValue == null) return '';
                    const numValue = Number(cellValue);
                    if (isNaN(numValue)) return cellValue;
                    return `¥${numValue.toFixed(2)}`;
                };
                console.log(`  - 设置货币格式化器`);
            } else {
                enhanced.formatter = ({ cellValue }: any) => {
                    if (cellValue == null) return '';
                    const numValue = Number(cellValue);
                    if (isNaN(numValue)) return cellValue;
                    return numValue.toFixed(2);
                };
                console.log(`  - 设置小数格式化器`);
            }
        }
        if (get(enhanced, 'component') === 'valueType') {
            set(
                enhanced,
                'componentProps.type',
                get(enhanced, 'componentProps.type') || 'number'
            );
            set(
                enhanced,
                'componentProps.format',
                get(enhanced, 'componentProps.format') || formatType
            );
        }

        console.log(`  - 设置浮点数格式: 右对齐，宽度120`);
    } else if (fieldInfo.type === 'boolean') {
        if (!enhanced.formatter) {
            enhanced.formatter = ({ cellValue }: any) =>
                cellValue ? '是' : '否';
            console.log(`  - 设置布尔值格式化器`);
        }

        if (get(enhanced, 'component') === 'valueType') {
            set(
                enhanced,
                'componentProps.type',
                get(enhanced, 'componentProps.type') || 'boolean'
            );
        }

        console.log(`  - 设置布尔值格式`);
    } else if (fieldInfo.type === 'datetime') {
        if (!enhanced.formatter) {
            enhanced.formatter = ({ cellValue }: any) => {
                if (!cellValue) return '';
                const date = new Date(cellValue);
                return date.toLocaleString('zh-CN');
            };
            console.log(`  - 设置日期时间格式化器`);
        }

        if (get(enhanced, 'component') === 'valueType') {
            set(
                enhanced,
                'componentProps.type',
                get(enhanced, 'componentProps.type') || 'date'
            );
        }

        console.log(`  - 设置日期时间宽度: 160`);
    } else if (fieldInfo.type === 'enum' && fieldInfo.enum_info) {
        console.log(
            `  - 检测到枚举类型，枚举值:`,
            fieldInfo.enum_info.enum_values
        );
        enhanced.enumInfo = fieldInfo.enum_info;
        enhanced.isEnum = true;

        if (!enhanced.formatter) {
            enhanced.formatter = ({ cellValue }: any) => {
                if (!cellValue) return '';
                return fieldInfo.enum_info.enum_values[cellValue] || cellValue;
            };
            console.log(`  - 设置枚举格式化器`);
        }

        if (get(enhanced, 'component') === 'valueType') {
            set(
                enhanced,
                'componentProps.type',
                get(enhanced, 'componentProps.type') || 'status'
            );
        }

        console.log(`  - 设置枚举`);
    } else if (fieldInfo.type === 'relation' && fieldInfo.relation_info) {
        console.log(`  - 检测到关系类型，关系信息:`, fieldInfo.relation_info);
        enhanced.relationInfo = fieldInfo.relation_info;
        enhanced.isRelation = true;

        // 只有在没有formatter时才添加关系格式化
        if (!enhanced.formatter) {
            const direction = fieldInfo.relation_info.direction;
            console.log(`  - 关系方向: ${direction}`);

            if (direction === 'RelationshipDirection.ONETOMANY') {
                // 一对多关系：显示关联项目数量
                enhanced.formatter = ({ cellValue }: any) => {
                    if (!cellValue) return '0项';
                    if (Array.isArray(cellValue)) {
                        const count = cellValue.length;
                        return count > 0 ? `${count}项` : '0项';
                    }
                    return '0项';
                };
                console.log(`  - 设置一对多关系格式化器`);
            } else if (direction === 'RelationshipDirection.MANYTOONE') {
                // 多对一关系：显示关联对象的名称
                enhanced.formatter = ({ cellValue }: any) => {
                    if (!cellValue) return '-';
                    if (typeof cellValue === 'object') {
                        // 优先显示 name，然后 title，最后 code
                        const displayValue =
                            cellValue.name ||
                            cellValue.title ||
                            cellValue.code ||
                            cellValue.id ||
                            '-';
                        console.log(
                            `  - 多对一关系显示值: ${displayValue}`,
                            cellValue
                        );
                        return displayValue;
                    }
                    return cellValue;
                };
                console.log(`  - 设置多对一关系格式化器`);
            } else {
                // 其他关系类型的默认处理
                enhanced.formatter = ({ cellValue }: any) => {
                    if (!cellValue) return '-';
                    if (Array.isArray(cellValue)) {
                        return `${cellValue.length}项`;
                    }
                    if (typeof cellValue === 'object') {
                        return (
                            cellValue.name ||
                            cellValue.title ||
                            cellValue.code ||
                            cellValue.id ||
                            '-'
                        );
                    }
                    return cellValue;
                };
                console.log(`  - 设置通用关系格式化器`);
            }
        }
    }

    console.log(
        `[列增强] 字段 ${fieldInfo.name} 处理完成，最终配置:`,
        enhanced,
        columnConfig
    );
    return enhanced;
}
