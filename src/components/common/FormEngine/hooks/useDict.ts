import { nextTick, unref } from 'vue';
import type { ComputedRef } from 'vue';
import type { FormFieldConfig } from '../types/formEngine';
import { get, isArray, isFunction, isNil, set } from 'lodash-es';
import { getDict } from '@/utils/dict';
import { PartialDeep } from 'type-fest';
interface IUseDict {
    getSchema: ComputedRef<FormFieldConfig[]> | null;
    updateSchema: (data: FormFieldConfig | FormFieldConfig[]) => void;
    setValues: (
        fields: PartialDeep<Recordable>,
        shouldValidate?: boolean
    ) => void;
    getValues: () => Recordable;
}

export function useDict({
    getSchema,
    updateSchema,
    setValues,
    getValues,
}: IUseDict) {
    function initDict() {
        unref(getSchema)?.forEach((schema) => {
            _updateDict(schema);
        });
    }

    function updateDict(schema: FormFieldConfig | FormFieldConfig[]) {
        if (isArray(schema)) {
            schema?.forEach((s) => {
                s?.dict && _updateDict(s);
            });
        } else {
            schema?.dict && _updateDict(schema);
        }
    }

    function _updateDict(schema: FormFieldConfig) {
        const {
            field,
            dict,
            dictPath = 'componentProps.options',
            dictAlias = { label: 'name', value: 'id', isDefault: 'is_default' },
            afterSetDict,
            beforeSetDict,
            componentProps,
        } = schema;

        if (!dict) return;

        const item = unref(getSchema)?.find((s) => s.field === field);
        // options已经有值将不再通过dict赋值
        if (!item || get(item, dictPath)) return;

        getDict(dict).then((data) => {
            let options;

            if (isFunction(beforeSetDict)) {
                options = beforeSetDict(data);
            } else {
                options = data;
            }

            set(
                item,
                dictPath,
                options?.map((item) => {
                    return {
                        ...item,
                        label: get(item, dictAlias.label),
                        value: get(item, dictAlias.value),
                        isDefault: get(item, dictAlias.isDefault),
                    };
                })
            );
            updateSchema(item);
            const defaultDict = data?.find((d) => get(d, dictAlias.isDefault));
            if (isFunction(afterSetDict)) afterSetDict(data);

            if (!defaultDict) return;

            nextTick(() => {
                const values = getValues();
                const val = get(values, field);
                const dictVal = get(defaultDict, dictAlias.value);
                if (isNil(val)) {
                    if (isFunction(componentProps?.onUpdate)) {
                        componentProps?.onUpdate(dictVal, values);
                    }

                    if (isFunction(componentProps?.onSelect)) {
                        componentProps?.onSelect(dictVal, defaultDict);
                    }

                    setValues({ [field]: dictVal }, false);
                }
            });
        });
    }

    return {
        initDict,
        updateDict,
    };
}
