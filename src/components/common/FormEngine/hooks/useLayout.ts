import { computed, Ref, unref } from 'vue';
import { FormEngineProps } from '../types/formEngine';
import { isNumber } from 'lodash-es';

export function useLayout(propsRef: Ref<Partial<FormEngineProps>>) {
    const formLayoutClass = computed(() => {
        const sm = unref(propsRef)?.cols?.sm || 1;
        const md = unref(propsRef)?.cols?.md || 2;
        const lg = unref(propsRef)?.cols?.lg || 3;
        const xl = unref(propsRef)?.cols?.xl || 4;
        const txl = unref(propsRef)?.cols?.['2xl'] || 5;

        let size = 4;
        switch (unref(propsRef)?.size) {
            case 'small':
                size = 2;
                break;
            case 'large':
                size = 4;
                break;
        }

        return `grid gap-${size} sm:grid-cols-${sm} md:grid-cols-${md} lg:grid-cols-${lg} xl:grid-cols-${xl} 2xl:grid-cols-${txl}`;
    });

    const formItemSpans = computed(() => {
        return (span) => {
            if (!span) return '';

            let sm;
            let md;
            let lg;
            let xl;
            let txl;

            if (span === true) {
                sm = unref(propsRef)?.cols?.sm || 1;
                md = unref(propsRef)?.cols?.md || 2;
                lg = unref(propsRef)?.cols?.lg || 3;
                xl = unref(propsRef)?.cols?.xl || 4;
                txl = unref(propsRef)?.cols?.['2xl'] || 5;
            } else {
                sm = isNumber(span) ? span : span?.sm;
                md = isNumber(span) ? span : span?.md;
                lg = isNumber(span) ? span : span?.lg;
                xl = isNumber(span) ? span : span?.xl;
                txl = isNumber(span) ? span : span?.['2xl'];
            }

            return `sm:col-span-${sm} md:col-span-${md} lg:col-span-${lg} xl:col-span-${xl} 2xl:col-span-${txl}`;
        };
    });

    return {
        formLayoutClass,
        formItemSpans,
    };
}
