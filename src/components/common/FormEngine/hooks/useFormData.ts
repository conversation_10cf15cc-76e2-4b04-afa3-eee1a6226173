// hooks/useFormData.ts
import { ref, computed, watch, Ref, unref, ComputedRef } from 'vue';
import { FormEngineProps, FormFieldConfig } from '../types/formEngine';
import { deepMerge } from '@/utils/object';
import { isFunction } from 'lodash-es';
import { PartialDeep } from 'type-fest';

export function useFormData(
    props: any,
    propsRef: Ref<Partial<FormEngineProps>>,
    schemaRef: Ref<FormFieldConfig[] | null>,
    formApi: Ref<Recordable>
) {
    const loading = ref(false);
    const defaultInitValues = computed(() => {
        const fieldsArray = unref(schemaRef) || [];
        const propsRefVal = unref(propsRef);
        const initValues = propsRefVal?.initValues || {};
        const modelValue = props.modelValue || {};

        // 收集 schema 中的默认值
        const defaultValues = fieldsArray.reduce(
            (acc, fieldItem) => {
                if (fieldItem.defaultValue !== undefined && fieldItem.field) {
                    acc[fieldItem.field] = fieldItem.defaultValue;
                } else if (
                    fieldItem.layout === 'Card' &&
                    fieldItem.schemas?.length
                ) {
                    fieldItem.schemas.forEach((schema) => {
                        if (schema.defaultValue !== undefined && schema.field) {
                            acc[schema.field] = schema.defaultValue;
                        }
                    });
                }
                return acc;
            },
            {} as Record<string, any>
        );

        // 合并所有值，保持优先级：modelValue > initValues > defaultValues
        return {
            ...defaultValues,
            ...initValues,
            ...modelValue,
        };
    });

    // 初始化表单
    // 1.通过useForm方式使用表单 formProps 必然有值
    // 2.常规方式使用表单 formProps 必然无值，直接取值props
    function init(formProps?: Partial<FormEngineProps>) {
        if (!formProps) {
            propsRef.value = props;
            schemaRef.value = props.fields;
        } else {
            propsRef.value = deepMerge(unref(propsRef) || {}, formProps);

            schemaRef.value = unref(propsRef)?.fields;
        }
    }

    function initDefaultValues() {
        unref(formApi.value)?.setValues(unref(defaultInitValues), false);
    }

    async function fetchData() {
        const { fetchApi } = unref(propsRef);
        if (!fetchApi || !isFunction(fetchApi)) return;

        try {
            loading.value = true;

            const res = await fetchApi();

            if (res === false) return;

            unref(formApi.value)?.setValues(res, false);

            return res;
        } catch (error) {
            loading.value = false;
        } finally {
            loading.value = false;
        }
    }

    watch(
        () => props?.modelValue,
        (n) => {
            unref(formApi.value)?.setValues(n, false);
        },
        {
            deep: true,
        }
    );

    return {
        defaultInitValues,
        init,
        initDefaultValues,
        fetchData,
    };
}
