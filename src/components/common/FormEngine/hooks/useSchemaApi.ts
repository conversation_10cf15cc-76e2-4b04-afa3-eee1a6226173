import { unref } from "vue"
import type { ComputedRef } from "vue"
import type { FormFieldConfig } from "../types/formEngine"
import { isArray, isFunction } from "lodash-es"

interface IUseSchemaApi {
  getSchema: ComputedRef<FormFieldConfig[]> | null
  updateSchema: (data: FormFieldConfig | FormFieldConfig[]) => void
}

export function useSchemaApi({ getSchema, updateSchema }: IUseSchemaApi) {
  function initSchemaApi() {
    unref(getSchema)?.forEach((schema) => {
      _update(schema)
    })
  }

  function updateSchemaApi(schema: FormFieldConfig | FormFieldConfig[]) {
    if (isArray(schema)) {
      schema?.forEach((s) => {
        s?.dict && _update(s)
      })
    } else {
      schema?.dict && _update(schema)
    }
  }

  function _update(schema: FormFieldConfig) {
    const { field, api } = schema

    if (!api || !isFunction(api)) return

    const item = unref(getSchema)?.find((s) => s.field === field)
    // options已经有值将不再通过dict赋值
    if (!item) return

    api(schema).then((schema) => {
      updateSchema(schema)
    })
  }

  return {
    initSchemaApi,
    updateSchemaApi
  }
}
