<template>
    <Popover v-model:open="open">
        <PopoverTrigger as-child :disabled="isDisabled">
            <div
                :id="elId"
                :class="[
                    'customer-select flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-1 text-xs ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 relative',
                    isDisabled
                        ? 'pointer-events-none opacity-80 bg-disabledBg cursor-not-allowed'
                        : 'pointer-events-auto',
                    clearable && !isNil(modelValue) ? 'clearable' : '',
                ]"
            >
                <div class="flex flex-wrap flex-1 gap-1">
                    <template v-if="multiple">
                        <span
                            v-for="value in modelValue"
                            :key="value"
                            class="flex items-center gap-1 rounded bg-muted px-2 py-1 text-xs"
                        >
                            {{ options.find((o) => o.value === value)?.label }}
                            <X
                                class="h-3 w-3 cursor-pointer"
                                @click.stop="removeTag(value)"
                            />
                        </span>
                        <input
                            ref="inputRef"
                            v-model="searchValue"
                            class="flex-1 bg-transparent outline-none min-w-[50px] text-xs"
                            :placeholder="placeholder"
                        />
                    </template>
                    <template v-else>
                        <input
                            ref="inputRef"
                            v-model="selectedValue"
                            readonly
                            class="flex-1 bg-transparent outline-none min-w-[50px] text-xs"
                            :placeholder="placeholder"
                        />
                    </template>
                </div>
                <ChevronsUpDown class="h-4 w-4 opacity-50 text-xs down" />
                <X
                    @click.stop="clear"
                    class="h-4 w-4 opacity-50 text-xs cursor-pointer close"
                />
            </div>
        </PopoverTrigger>
        <PopoverContent :style="{ width: popoverWidth }" class="p-0">
            <Command @click.stop>
                <div class="flex border-b justify-center items-center">
                    <CommandInput
                        :hideInput="!showFilter"
                        :readOnly="!showFilter"
                        :placeholder="$t('common.placeholder.filter')"
                        customerClass="border-0 flex-auto text-xs"
                    />
                    <Button
                        v-if="showFilter && showAdd"
                        @click="onAdd"
                        class="mr-2"
                        >{{ addTitle || $t('common.button.create') }}</Button
                    >
                </div>
                <CommandList>
                    <CommandEmpty>{{
                        $t('common.common.noData')
                    }}</CommandEmpty>
                    <CommandGroup>
                        <CommandItem
                            class="flex justify-between"
                            v-for="option in options"
                            :key="
                                option[customField.value as keyof typeof option]
                            "
                            :value="
                                option[customField.label as keyof typeof option]
                            "
                            :disabled="!!option.disabled"
                            @select="toggleValue(option)"
                        >
                            <div class="flex gap-2">
                                <slot name="iconPrefix"></slot>
                                <span class="flex-1 truncate">{{
                                    option[
                                        customField.label as keyof typeof option
                                    ]
                                }}</span>
                            </div>
                            <template v-if="multiple">
                                <Check
                                    :class="[
                                        'mr-2 h-4 w-4',
                                        Array.isArray(modelValue) &&
                                        modelValue.includes(
                                            option[
                                                customField.value as keyof typeof option
                                            ]
                                        )
                                            ? 'opacity-100'
                                            : 'opacity-0',
                                    ]"
                                />
                            </template>
                            <template v-else>
                                <Check
                                    :class="[
                                        'mr-2 h-4 w-4',
                                        modelValue ===
                                        option[
                                            customField.value as keyof typeof option
                                        ]
                                            ? 'opacity-100'
                                            : 'opacity-0',
                                    ]"
                                />
                            </template>
                        </CommandItem>
                    </CommandGroup>
                </CommandList>
            </Command>
        </PopoverContent>
    </Popover>
</template>
<script setup lang="ts">
import { ref, watch, type PropType, getCurrentInstance, computed } from 'vue';
import { Check, ChevronsUpDown, X } from 'lucide-vue-next';
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command';
import {
    Popover,
    PopoverTrigger,
    PopoverContent,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { isFunction, isNil } from 'lodash-es';
import { $t } from '@/locales';
defineOptions({
    name: 'MultipleSelect',
});

const generateId = () => {
    return (
        Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15)
    );
};

const elId = generateId();

const {
    options,
    modelValue,
    placeholder,
    showFilter,
    multiple,
    customField,
    disabled,
    clearable,
} = defineProps({
    options: {
        type: Array as PropType<any[]>,
        default: () => [],
    },
    modelValue: {
        type: [Array, String, Number] as PropType<
            (string | number)[] | string | number
        >,
        default: () => [],
    },
    placeholder: {
        type: String,
        default: $t('common.placeholder.select'),
    },
    showFilter: {
        type: Boolean,
        default: false,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    customField: {
        type: Object as PropType<{ value: string; label: string }>,
        default: () => ({
            value: 'value',
            label: 'label',
        }),
    },
    showAdd: {
        type: Boolean,
        default: false,
    },
    addTitle: {
        type: String,
    },
    disabled: {
        type: [Boolean, Function] as PropType<
            boolean | ((value: any) => boolean)
        >,
        default: false,
    },
    clearable: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:modelValue', 'change']);

const searchValue = ref('');
const selectedValue = ref('');
const open = ref(false);
const inputRef = ref<HTMLInputElement>();

const currentInstance = getCurrentInstance();

const isDisabled = computed(() => {
    if (typeof disabled === 'function') {
        return disabled(modelValue);
    }
    return disabled;
});

function toggleValue(data: any) {
    if (isDisabled.value) return;

    let newValue: (string | number)[] | string | number = [];
    if (multiple) {
        const currentValue = modelValue as (string | number)[];
        if (currentValue.includes(data[customField.value])) {
            newValue = currentValue.filter(
                (v) => v !== data[customField.value]
            );
        } else {
            newValue = [...currentValue, data[customField.value]];
        }
    } else {
        newValue = data[customField.value];
        selectedValue.value = data[customField.label];
        open.value = false;
    }

    if (isFunction(currentInstance?.attrs?.onSelect))
        currentInstance?.attrs?.onSelect(newValue, data);
    emit('update:modelValue', newValue);
}
function removeTag(value: string | number) {
    if (isDisabled.value) return;

    // 确保modelValue是数组类型
    const currentValue = Array.isArray(modelValue) ? modelValue : [];
    const removedValue = currentValue.filter((v) => v !== value);
    if (isFunction(currentInstance?.attrs?.onSelect)) {
        currentInstance?.attrs?.onSelect(removedValue, value);
    }
    emit('update:modelValue', removedValue);
    if (removedValue.length === 0) {
        inputRef.value?.focus();
    }
}

function onAdd() {
    if (isFunction(currentInstance?.attrs?.onAdd))
        currentInstance?.attrs?.onAdd(currentInstance.props?.modelValue);
    open.value = false;
}

function clear() {
    emit('update:modelValue', undefined);
}

watch(
    () => modelValue,
    (newVal: (string | number)[] | string) => {
        if (!multiple) {
            selectedValue.value = options.find(
                (o) => o[customField.value] === newVal
            )?.[customField.label];
        }
        emit('change', newVal);
    }
);

watch(
    () => options,
    (newVal: any[]) => {
        if (!multiple) {
                selectedValue.value = newVal.find(
                    (o) => o[customField.value] === modelValue
                )?.[customField.label];
        }
    },
    { immediate: true }
);

/** 弹出框的宽度 */
const popoverWidth = ref('');

watch(open, (newVal) => {
    if (newVal) {
        // 在弹出框打开时获取触发元素的宽度
        const el = document.getElementById(elId);
        if (el) popoverWidth.value = `${el.offsetWidth}px`;
    }
});
</script>

<style lang="scss" scoped>
.customer-select {
    > div {
        width: calc(100% - 24px);
    }
}

.close {
    display: none;
}

.clearable {
    .close {
        display: none;
    }

    &:hover {
        .down {
            display: none;
        }

        .close {
            display: inline-flex;
        }
    }
}
</style>
