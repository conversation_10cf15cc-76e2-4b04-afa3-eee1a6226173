<script setup lang="ts">
import { cn } from '@/lib/utils';

import { Button } from '@/components/ui/button';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { RangeCalendar } from '@/components/ui/range-calendar';
import {
    CalendarDate,
    DateFormatter,
    getLocalTimeZone,
} from '@internationalized/date';
import { Calendar as CalendarIcon } from 'lucide-vue-next';
import { type PropType, type Ref, ref, watch } from 'vue';
import { getLocale } from '@/locales';

const props = defineProps({
    modelValue: {
        type: Array as PropType<any[]>,
        default: () => [null, null],
    },
});

const emit = defineEmits(['update:modelValue']);

let locale = 'zh-CN';

if (getLocale() === 'zh-CN') {
    locale = 'zh-CN';
} else {
    locale = 'en-US';
}
const defaultValue = new DateFormatter(locale, {
    dateStyle: 'medium',
});

const date = new Date();
const selfValue = ref({
    start: new CalendarDate(
        date.getFullYear(),
        date.getMonth() + 1,
        date.getDate()
    ),
    end: new CalendarDate(
        date.getFullYear(),
        date.getMonth() + 1,
        date.getDate()
    ).add({
        days: 20,
    }),
}) as Ref<any>;

watch(
    () => selfValue.value,
    (newVal) => {
        if (newVal.start && newVal.end) {
            emit('update:modelValue', [
                newVal.start.toDate(getLocalTimeZone()),
                newVal.end.toDate(getLocalTimeZone()),
            ]);
        }
    },
    { immediate: true, deep: true }
);

watch(
    props.modelValue,
    (newVal) => {
        const start = newVal[0] ? new Date(newVal[0]) : new Date();
        const end = newVal[1] ? new Date(newVal[1]) : new Date();
        selfValue.value.start = new CalendarDate(
            start.getFullYear(),
            start.getMonth() + 1,
            start.getDate()
        );
        selfValue.value.end = new CalendarDate(
            end.getFullYear(),
            end.getMonth() + 1,
            end.getDate()
        ).add({ days: 20 });
    },
    { immediate: true, deep: true }
);
</script>

<template>
    <Popover>
        <PopoverTrigger as-child>
            <Button variant="outline" :class="cn(
                'w-full h-10 justify-start text-left font-normal text-xs',
                !selfValue && 'text-muted-foreground'
            )
                ">
                <CalendarIcon class="mr-2 h-4 w-4" />
                <template v-if="selfValue.start">
                    <template v-if="selfValue.end">
                        {{
                            defaultValue.format(
                                selfValue.start.toDate(getLocalTimeZone())
                            )
                        }}
                        -
                        {{
                            defaultValue.format(
                                selfValue.end.toDate(getLocalTimeZone())
                            )
                        }}
                    </template>

                    <template v-else>
                        {{
                            defaultValue.format(
                                selfValue.start.toDate(getLocalTimeZone())
                            )
                        }}
                    </template>
                </template>
                <template v-else>
                    {{ $t('common.placeholder.select') }}
                </template>
            </Button>
        </PopoverTrigger>
        <PopoverContent class="w-auto p-0">
            <RangeCalendar v-model="selfValue" initial-focus :number-of-months="2" :locale="locale" />
        </PopoverContent>
    </Popover>
</template>
