<template>
    <FormField v-slot="{ componentField }" :name="fieldItem.field">
        <FormItem
            class="relative flex space-y-0 items-center"
            :class="[
                { hidden: !computedShow(fieldItem) },
                `col-span-1 lg:col-span-${fieldItem.span || 1}`,
                {
                    'flex-col items-start text-justify ':
                        labelPosition === 'top',
                    'is-required': fieldItem.required,
                },
                {
                    [`min-h-[${size}px]`]: true,
                    [formItemClass]: true,
                },
                {
                    'overflow-x-auto': fieldItem.component == 'table',
                },
            ]"
            v-bind="fieldItem.formItemProps"
        >
            <FormLabel
                v-if="fieldItem.label"
                class="pr-2 text-xs flex-shrink-0"
                :class="[
                    fieldItem.labelClass,
                    { 'text-justify pb-1 w-full': labelPosition === 'top' },
                    { 'text-left w-24': labelPosition == 'left' },
                    {
                        'text-right w-24':
                            labelPosition == 'right' || !labelPosition,
                    },
                ]"
            >
                {{ fieldItem.label }}{{ labelPrefix }}

                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger as-child>
                            <span
                                v-show="fieldItem.tooltip"
                                variant="outline"
                                type="button"
                                class="inline-block w-[12px] h-[12px] cursor-pointer p-[1px]"
                            >
                                <Icon
                                    icon="ant-design:question-circle-filled"
                                    width="12px"
                                    height="12px"
                                />
                            </span>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>{{ fieldItem.tooltip }}</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </FormLabel>

            <FormControl>
                <component
                    :is="resolveComponent(fieldItem?.component)"
                    :record="formApi.values"
                    v-bind="
                        getBind(
                            {
                                ...fieldItem.componentProps,
                                ...componentField,
                                class: cn(fieldItem.componentProps?.class),
                            },
                            fieldItem,
                            componentField
                        )
                    "
                    :placeholder="placeholder"
                    @register="register"
                >
                    <template
                        v-for="slot in Object.keys($slots)"
                        #[slot]="data"
                    >
                        <slot :name="slot" v-bind="data || {}" />
                    </template>
                </component>
            </FormControl>

            <FormDescription v-if="fieldItem.description">
                {{ fieldItem.description }}
            </FormDescription>

            <FormMessage
                class="absolute z-100 top-full text-content"
                :class="{
                    'left-24 bottom-[-14px]': labelPosition !== 'top',
                    'bottom-[-14px]': labelPosition === 'top',
                }"
            />
        </FormItem>
    </FormField>
</template>

<script lang="ts" setup>
import { computed, inject, isReadonly, ref, unref } from 'vue';
import type { Component } from 'vue';
import { componentMap } from '../componentMap';
import {
    FormField,
    FormItem,
    FormLabel,
    FormControl,
    FormMessage,
    FormDescription,
} from '@/components/ui/form';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { get, isBoolean, isFunction, set, omit, isNil } from 'lodash-es';
import { $t } from '@/locales';
import { Icon } from '@iconify/vue';

const check = ['switch', 'checkbox'];

// FormFieldConfig
const props = defineProps({
    fieldItem: { type: Object, required: true },
    formApi: { type: Object, required: true },
    labelPosition: { type: String, default: null },
    labelPrefix: { type: String, default: null },
    size: { type: String },
    formItemClass: { type: String },
});

const emits = defineEmits(['register', 'registerCom']);

const resolveComponent = (component: Component | string) => {
    if (typeof component === 'string') {
        // return formControls[component as FormControlsKeys];
        return componentMap.get(component);
    }
    return component;
};

const size = computed(() => {
    let size = 60;
    switch (props?.size) {
        case 'small':
            size = 42;
            break;
        case 'large':
            size = 72;
            break;
    }

    return size;
});

const placeholder = computed(() =>
    props?.fieldItem?.component === 'valueType'
        ? ''
        : !isNil(props.fieldItem.componentProps?.placeholder)
          ? props.fieldItem.componentProps?.placeholder
          : `${$t('common.placeholder.enter')}${props?.fieldItem?.label}`
);
// 计算 Show
const computedShow = computed(() => {
    const checkShow = (show, values) => {
        if (isBoolean(show)) return show;
        if (isFunction(show)) return show(values);
        return true; // 默认显示
    };

    return (schema) => {
        return checkShow(schema?.show, unref(props.formApi)?.values);
    };
});

const modelValue = ref(
    isNil(props.fieldItem.field)
        ? undefined
        : get(props.formApi.values, props.fieldItem.field)
);

const getBind = computed(() => {
    return (props, schema, componentField) => {
        const updataModelObj = getUpdataObj(
            unref(schema)?.component,
            schema.field
        );

        return {
            ...props,
            ...updataModelObj,
            ...(check.includes(unref(schema)?.component)
                ? { checked: componentField.modelValue }
                : {}),
        };
    };
});

function getUpdataObj(type: string, field) {
    if (isNil(field)) return {};
    const values = props.formApi?.values;

    let obj = {};

    if (check.includes(type)) {
        obj = {
            // checked: get(values, field),
            ['onUpdate:checked'](val) {
                modelValue.value = val;
                props.formApi.setValues(
                    {
                        [field]: val,
                    },
                    false
                );

                if (
                    isFunction(unref(props.fieldItem)?.componentProps?.onUpdate)
                ) {
                    unref(props.fieldItem)?.componentProps?.onUpdate(
                        val,
                        values
                    );
                }
            },
        };
    } else {
        obj = {
            // modelValue: get(values, field),
            ['onUpdate:modelValue'](val) {
                if (!isReadonly(values)) set(values, field, val);
                props.formApi.setValues(
                    {
                        [field]: val,
                    },
                    false
                );
                // modelValue.value = val;

                if (
                    isFunction(unref(props.fieldItem)?.componentProps?.onUpdate)
                ) {
                    unref(props.fieldItem)?.componentProps?.onUpdate(
                        val,
                        values
                    );
                }
            },
        };
    }

    return obj;
}

const register = (api) => {
    emits('registerCom', api, props.fieldItem.field);
};
</script>
