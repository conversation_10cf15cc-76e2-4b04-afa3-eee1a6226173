<script setup lang="ts">
import { ref, computed, watch, getCurrentInstance, nextTick } from 'vue'
import {
  <PERSON><PERSON><PERSON>,
  NumberFieldContent,
  NumberFieldDecrement,
  NumberFieldIncrement,
  NumberFieldInput,
} from '@/components/ui/number-field'
import { isFunction, isNil } from 'lodash-es'
import { toNum } from '@/utils/math'

defineOptions({
  name: 'NumberInput',
  // inheritAttrs: true,
})

interface Props {
  modelValue: number | string | undefined | null
  id?: string
  label?: string
  min?: number
  max?: number
  step?: number
  disabled?: boolean
  placeholder?: string
  width?: string | number
  height?: string | number
  showControls?: boolean
  readonly?: boolean
  required?: boolean
  error?: string
  name?: string
  blur?: Function
  focus?: Function
  change?: Function
  formatOptions?: Recordable
  stopKeyboardEvent?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  min: 0,
  max: Infinity,
  step: 1,
  disabled: false,
  showControls: false,
  readonly: false,
  required: false,
  width: '100%',
  height: '36px',
})

const emit = defineEmits<{
  'update:modelValue': [value: number | undefined]
  change: [value: number | undefined]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
}>()

const currentInstance = getCurrentInstance()

const innerValue = ref(
  isNil(props.modelValue) ? props.modelValue : toNum(props.modelValue)
)

const fieldId = computed(
  () => props.id || `number-input-${Math.random().toString(36).slice(2, 11)}`
)

watch(
  () => props.modelValue,
  (newVal) => {
    let _val = ''
    if (newVal !== undefined && newVal !== null) {
      _val = newVal.toString().replace(/,/gi, '')
      innerValue.value = Number(_val)
    }
  }
)
watch(
  () => innerValue.value,
  (newVal: number | string) => {
    if (isNaN(newVal) || isNil(newVal)) {
      emit('update:modelValue', undefined)
      emit('change', undefined)
    } else {
      let _val = ''
      if (newVal) _val = newVal.toString().replace(/,/gi, '')
      emit('update:modelValue', Number(_val))
      emit('change', Number(_val))
    }
  }
)
const handleChange = (event: Event) => {
  console.log('handleChange', event)
  let val = (event.target as HTMLInputElement).value
  if (val !== undefined && val !== null && val !== '') {
    val = val.replace(/,/gi, '')
    emit('update:modelValue', Number(val))
    emit('change', Number(val))
  } else {
    emit('update:modelValue', undefined)
    emit('change', undefined)
  }
  if (isFunction(currentInstance.attrs.change))
    currentInstance.attrs.change(Number(val.replace(/,/gi, '')))
}

const handleFocus = (event: FocusEvent) => {
  if (isFunction(props.focus)) props.focus(innerValue.value)
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  if (isFunction(props.blur)) props.blur(innerValue.value)
  emit('blur', event)
}

// 阻止键盘上下键的默认行为修改数值
const handleKeyDown = (e: KeyboardEvent) => {
  if (!props.stopKeyboardEvent) return
  const val = isNil(props.modelValue)
    ? props.modelValue
    : toNum(props.modelValue)
  if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
    nextTick(() => {
      innerValue.value = val
      emit('update:modelValue', innerValue.value)
      emit('change', innerValue.value)
    })
    e.preventDefault() // 阻止数值变化
    return
  }
}

const containerStyle = computed(() => {
  return {
    width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  }
})

const inputStyle = computed(() => {
  return {
    height:
      typeof props.height === 'number' ? `${props.height}px` : props.height,
  }
})
</script>

<template>
  <div :style="containerStyle" class="relative">
    <NumberField
      :value="innerValue"
      :default-value="innerValue"
      :min="min"
      :max="max"
      :step="step"
      :disabled="disabled"
      :formatOptions="formatOptions"
      v-model="innerValue"
      @change.stop="handleChange"
    >
      <NumberFieldContent class="relative flex items-center">
        <NumberFieldDecrement v-if="showControls"></NumberFieldDecrement>
        <NumberFieldInput
          :id="fieldId"
          :name="name"
          :placeholder="placeholder || '请输入'"
          :style="inputStyle"
          :disabled="disabled"
          :readonly="readonly"
          class="pr-1 text-xs"
          @focus.stop="handleFocus"
          @blur.stop="handleBlur"
          @keydown="handleKeyDown"
          @input.stop
        />
        <NumberFieldIncrement v-if="showControls"></NumberFieldIncrement>
      </NumberFieldContent>
    </NumberField>
    <p v-if="error" class="absolute top-full left-0 text-xs text-destructive">
      {{ error }}
    </p>
  </div>
</template>
