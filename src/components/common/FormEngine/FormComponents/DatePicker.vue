<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  DateFormatter,
  CalendarDate,
  getLocalTimeZone,
} from '@internationalized/date'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { $t, getLocale } from '@/locales'
import dayjs from 'dayjs'
import { toNumber } from 'lodash-es'

const props = defineProps<{
  modelValue?: Date | String
  change?: (value: Date | undefined, record?: Record<string, any>) => void
  record?: Record<string, any>
  disabled?: Boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value?: Date): void
}>()

// 语言代码映射
const locale = computed(() => (getLocale() === 'zh-CN' ? 'zh-CN' : 'en-US'))

// 日期值转换器
const newValue = computed<CalendarDate | undefined>({
  get() {
    try {
      return props.modelValue
        ? new CalendarDate(
            toNumber(dayjs(props.modelValue).format('YYYY')),
            toNumber(dayjs(props.modelValue).format('MM')),
            toNumber(dayjs(props.modelValue).format('DD'))
          )
        : undefined
    } catch (error) {
      console.error(error)
      return undefined
    }
  },
  set(value) {
    console.log('value', value)
    const dateValue = value?.toDate(getLocalTimeZone())
    console.log('dateValue', dateValue)
    emit('update:modelValue', dateValue)
    props.change?.(dateValue, props.record)
  },
})

// 响应式日期格式化器
const dateFormatter = computed(
  () => new DateFormatter(locale.value, { dateStyle: 'long' })
)

// 显示文本计算
const displayText = computed(() =>
  newValue.value
    ? dateFormatter.value.format(newValue.value.toDate(getLocalTimeZone()))
    : $t('common.placeholder.selectDate')
)
</script>

<template>
  <Popover>
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        :class="[
          cn(
            'h-10 w-full flex-1 justify-start text-left font-normal text-xs',
            !newValue && 'text-muted-foreground'
          ),
          props.disabled
            ? 'pointer-events-none opacity-80 bg-disabledBg cursor-not-allowed'
            : '',
        ]"
      >
        <CalendarIcon class="mr-1 h-4 w-4" />
        {{ displayText }}
      </Button>
    </PopoverTrigger>

    <PopoverContent class="w-auto p-0">
      <Calendar v-model="newValue" :locale="locale" :initial-focus="true" />
    </PopoverContent>
  </Popover>
</template>
