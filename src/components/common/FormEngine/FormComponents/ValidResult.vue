<template>
    <div class="valid-result">
        <div v-for="item in Object.keys(modelValue || {})">
            <template v-if="isArray(modelValue?.[item])">
                <div class="item flex">
                    <div class="label flex items-center">
                        <span
                            class="flex h-2 w-2 rounded-full bg-red-600 mr-2"
                        ></span>
                        <span class="mr-1">{{ item }}:</span>
                    </div>
                    <p
                        class="text-sm text-muted-foreground"
                        v-for="sitem in modelValue?.[item]"
                    >
                        {{ sitem }}
                    </p>
                </div>
            </template>
            <template v-else>
                <div class="item flex">
                    <div class="label flex items-center">
                        <span
                            class="flex h-2 w-2 rounded-full bg-red-600 mr-2"
                        ></span>
                        <span class="mr-1">{{ item }}:</span>
                    </div>
                    <p class="text-sm text-muted-foreground">
                        {{ modelValue[item] }}
                    </p>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, watch, type PropType } from 'vue';
import { isArray } from 'lodash-es';

defineOptions({
    name: 'ValidResult',
});

defineProps({
    modelValue: {
        type: Object,
        default: () => null,
    },
});

const emit = defineEmits(['update:modelValue']);
</script>
