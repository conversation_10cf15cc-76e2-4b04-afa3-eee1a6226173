<!-- 输入多选框 -->
<template>
    <TagsInput class="w-full min-h-10" v-model="innerValue" @update:modelValue="handleUpdate">
        <TagsInputItem v-for="item in innerValue" :key="item" :value="item">
            <TagsInputItemText />
            <TagsInputItemDelete />
        </TagsInputItem>

        <TagsInputInput :placeholder="placeholder" />
    </TagsInput>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { TagsInput, TagsInputInput, TagsInputItem, TagsInputItemDelete, TagsInputItemText } from '@/components/ui/tags-input'

defineOptions({
    name: 'TagsInput',
    inheritAttrs: false,
})

interface Props {
    modelValue: string[]
    placeholder?: string
    disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    placeholder: '请输入...',
    disabled: false,
})

const emit = defineEmits<{
    'update:modelValue': [value: string[]]
    change: [value: string[]]
}>()

const innerValue = ref<string[]>(props.modelValue)

// 监听外部值变化
watch(
    () => props.modelValue,
    (newVal) => {
        innerValue.value = newVal
    }
)

// 处理内部值变化
const handleUpdate = (value: string[]) => {
    innerValue.value = value
    emit('update:modelValue', value)
    emit('change', value)
}
</script>
