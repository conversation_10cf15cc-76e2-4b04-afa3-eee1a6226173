<template>
    <RadioGroup
        v-bind="$attrs"
        v-model="modelValue"
        class="flex space-x-2 ml-2 text-xs"
    >
        <div class="flex items-center gap-x-1" v-for="option in options">
            <RadioGroupItem
                :id="option.label"
                :key="option.value"
                :value="option.value"
                :disabled="option.disabled"
            >
            </RadioGroupItem>
            <Label :for="option.label" class="cursor-pointer text-xs">{{
                option.label
            }}</Label>
        </div>
    </RadioGroup>
</template>
<script setup lang="ts">
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

defineProps<{
    name: 'Radio';
    options: Array<{
        value: string;
        label: string;
        disabled?: boolean;
    }>;
}>();

const modelValue = defineModel<string>();
</script>
