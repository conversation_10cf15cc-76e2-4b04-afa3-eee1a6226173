<template>
    <div class="flex flex-col gap-2">
        <!-- 全选控制 -->
        <div v-if="isAll" class="flex items-center gap-2 mb-4">
            <Checkbox :checked="isAllSelected" @update:checked="toggleAll" />
            <Label class="font-medium">{{ allText }}</Label>
        </div>

        <div
            class="flex grid-cols-2 gap-4 flex-wrap"
            :class="[layout === 'vertical' ? 'flex-col' : '']"
        >
            <div
                v-for="option in options"
                :key="option?.value"
                class="flex items-center gap-2"
            >
                <Checkbox
                    :id="option?.value"
                    :checked="modelValue.includes(option?.value)"
                    @update:checked="
                        (checked) => handleoptionCheck(checked, option?.value)
                    "
                />
                <Label :for="option?.value">{{ option?.label }}</Label>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { PropType } from 'vue';

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => [],
    },
    options: {
        type: Array as PropType<Recordable[]>,
    },
    isAll: {
        type: Boolean,
        default: true,
    },
    allText: {
        type: String,
        default: '全选',
    },
    layout: {
        type: String as PropType<'vertical' | 'horizontal'>,
        default: '',
    },
});

const emit = defineEmits(['update:modelValue']);

// 计算是否全选
const isAllSelected = computed(() => {
    return props.modelValue.length === props.options.length;
});

// 处理单个选择
const handleoptionCheck = (checked, value) => {
    let newSelected = [...props.modelValue];
    if (checked) {
        if (!newSelected.includes(value)) {
            newSelected.push(value);
        }
    } else {
        newSelected = newSelected.filter((v) => v !== value);
    }
    emit('update:modelValue', newSelected);
};

// 处理全选切换
const toggleAll = (checked) => {
    if (checked) {
        emit(
            'update:modelValue',
            props.options.map((d) => d.value)
        );
    } else {
        emit('update:modelValue', []);
    }
};
</script>

<style lang="scss" scoped>
/* 默认水平布局 */
.grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
}
/* 竖屏或垂直模式 */
@media (orientation: portrait) {
    .grid-container {
        grid-template-rows: repeat(3, 1fr);
    }
}
</style>
