<template>
    <div v-if="modelValue != '' || modelValue != null">
        <template v-if="Array.isArray(type)">
            <div v-for="(item, index) in type">
                <span
                    class="px-4 py-1 rounded-xl text-xs"
                    :class="backClassFun(item)"
                    v-if="item.name === modelValue"
                    ><Icon
                        :icon="item.icon"
                        size="20"
                        class="inline-block w-[18px] h-[18px] mr-[5px]"
                        :class="item.color"
                        v-if="item.icon"
                    ></Icon
                    >{{ item.label ? item.label : modelValue }}</span
                >
            </div>
        </template>
        <template v-else>
            <span
                class="px-4 py-1 rounded-xl text-xs"
                :class="{
                    'bg-[rgba(255,0,0,0.1)] text-red-500':
                        typeList === 'danger',
                    'bg-[rgba(19,229,19,0.1)] text-green-500':
                        typeList === 'success',
                    'bg-[rgba(255,179,0,0.1)] text-[rgba(249,176,3,1)] ':
                        typeList === 'warn',
                }"
                v-if="type"
                >{{ modelValue }}</span
            >
            <span v-else class="px-4 py-1 rounded-xl text-xs" :class="typeList">
                <template v-if="typeof modelValue === 'boolean'">
                    {{ modelValueFattmer() }}
                </template>
                <template v-else>
                    {{ modelValue }}
                </template>
            </span>
        </template>
    </div>
</template>
<script setup lang="ts">
import { ref, watch, type PropType } from 'vue';
import { Icon } from '@iconify/vue';
defineOptions({
    name: 'Tags',
});

const { modelValue, mode, type, record, column } = defineProps({
    modelValue: {
        type: [Array, String, Number, Boolean] as PropType<
            (string | number | boolean)[] | string | number | boolean
        >,
        default: () => null,
    },
    mode: {
        type: [Array, String] as PropType<
            (string | number | boolean)[] | string | number | boolean
        >,
        default: 'normal',
    },
    type: {
        type: [Array, Object, String, Number, Boolean] as PropType<
            (string | number | boolean)[] | string | number | boolean
        >,
        default: () => [],
    },
    record: {
        type: Object as PropType<Record<string, any>>,
        default: () => ({}),
    },
    column: {
        type: Object as PropType<Record<string, any>>,
        default: () => ({}),
    },
});
const emit = defineEmits(['update:modelValue']);
//console.log('tags', record, column, modelValue);
const typeList = ref(null);
if (type) {
    typeList.value = type;
} else {
    if (modelValue === true) {
        typeList.value = 'bg-[rgba(19,229,19,0.1)] text-green-500';
    } else if (modelValue === false) {
        typeList.value = 'bg-[rgba(255,0,0,0.1)] text-red-500';
    } else {
        switch (record.$rowIndex) {
            case 0:
                typeList.value = 'bg-[rgba(19,229,19,0.1)] text-green-500';
                break;
            case 1:
                typeList.value = 'bg-[rgba(255,0,0,0.1)] text-red-500';
                break;
            case 2:
                typeList.value =
                    'bg-[rgba(255,179,0,0.1)] text-[rgba(249,176,3,1)]';
                break;
            case 3:
                typeList.value =
                    'bg-[rgba(0,112,255,0.1)] text-[rgba(3,147,249,1)]';
                break;
            default:
                typeList.value = 'text-[rgba(3,3,3,0.5)] bg-[rgba(0,0,0,0.1)]';
                break;
        }
    }
}
const modelValueFattmer = () => {
    switch (record.column.field) {
        case 'valid':
            return modelValue ? '启用' : '禁用';
        default:
            return modelValue ? '是' : '否';
    }
};

const backClassFun = (item) => {
    if (item.icon) {
        return (
            item.color +
            ' flex items-center w-[auto] max-w-[100px] overflow-hidden whitespace-nowrap text-ellipsis'
        );
    } else {
        return item.color;
    }
};
</script>
