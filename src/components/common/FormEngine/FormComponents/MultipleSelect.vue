<template>
    <div ref="multipleSelectRef" class="relative z-99">
        <div
            class="flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
            @click="openDropdown()"
        >
            <div class="flex flex-wrap flex-1 gap-1">
                <span
                    v-for="value in modelValue"
                    :key="value"
                    class="flex items-center gap-1 rounded bg-muted px-2 py-1 text-sm"
                >
                    {{ options.find((o) => o.value === value)?.label }}
                    <X
                        class="h-3 w-3 cursor-pointer"
                        @click.stop="removeTag(value)"
                    />
                </span>
                <input
                    ref="inputRef"
                    v-model="inputValue"
                    class="flex-1 bg-transparent outline-none min-w-[50px] text-xs"
                    :placeholder="placeholder"
                    @focus="openDropdown('input')"
                    @click.stop
                />
            </div>
            <ChevronsUpDown class="h-4 w-4 opacity-50" />
        </div>

        <Command
            v-if="open"
            class="absolute top-full z-50 mt-0 w-full rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 h-[auto]"
            @click.stop
        >
            <CommandInput
                v-if="showFilter"
                @input="inputValue = $event.target.value"
                placeholder="Search frameworks..."
            />
            <CommandList>
                <CommandEmpty>{{ $t('common.common.noData') }}</CommandEmpty>
                <CommandGroup>
                    <CommandItem
                        class="flex justify-between"
                        v-for="option in options"
                        :key="option.value"
                        :value="option.label"
                        @select="toggleValue(option.value)"
                    >
                        <div class="flex gap-2">
                            <slot name="iconPrefix"></slot>
                            <span class="flex-1 truncate">{{
                                option.label
                            }}</span>
                        </div>
                        <Check
                            :class="[
                                'mr-2 h-4 w-4',
                                modelValue.includes(option.value)
                                    ? 'opacity-100'
                                    : 'opacity-0',
                            ]"
                        />
                    </CommandItem>
                </CommandGroup>
            </CommandList>
        </Command>
    </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch, type PropType } from 'vue';
import { Check, ChevronsUpDown, X } from 'lucide-vue-next';
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command';
import { $t } from '@/locales';
defineOptions({
    name: 'MultipleSelect',
});

const { options, modelValue, placeholder, showFilter } = defineProps({
    options: {
        type: Array as PropType<{ value: string | number; label: string }[]>,
        default: () => [],
    },
    modelValue: {
        type: Array as PropType<(string | number)[]>,
        default: () => [],
    },
    placeholder: {
        type: String,
        default: $t('common.placeholder.select'),
    },
    showFilter: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:modelValue', 'change']);
const inputValue = ref('');
const open = ref(false);
const inputRef = ref<HTMLInputElement>();

function toggleValue(value: string | number) {
    let newValue: (string | number)[] = [];
    if (modelValue.includes(value)) {
        newValue = modelValue.filter((v) => v !== value);
    } else {
        newValue = [...modelValue, value];
    }
    emit('update:modelValue', newValue);
}

function removeTag(value: string | number) {
    const removedValue = modelValue.filter((v) => v !== value);
    emit('update:modelValue', removedValue);
    if (removedValue.length === 0) {
        inputRef.value?.focus();
    }
}

function openDropdown(type?: string) {
    if (type === 'input') {
        if (open.value) return;
        open.value = true;
        return;
    }
    open.value = !open.value;
    if (open.value && showFilter) {
        inputValue.value = '';
    }
}

watch(
    () => modelValue,
    (newVal: (string | number)[]) => {
        emit('change', newVal);
    }
);

/** == 点击组件外关闭下拉显示 =================================================== */
const multipleSelectRef = ref<HTMLDivElement>();
const handleClickOutside = (event: MouseEvent) => {
    if (!multipleSelectRef.value) return;
    if (!multipleSelectRef.value.contains(event.target as Node)) {
        open.value = false;
    }
};
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});
onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>
