<template>
    <Popover v-model:open="open">
        <PopoverTrigger as-child :disabled="isDisabled">
            <div
                :id="elId"
                :class="[
                    'flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-1 text-xs ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 relative',
                    isDisabled
                        ? 'pointer-events-none opacity-80 bg-disabledBg cursor-not-allowed'
                        : 'pointer-events-auto',
                ]"
            >
                <div class="flex flex-wrap flex-1 gap-1">
                    <input
                        ref="inputRef"
                        v-model="selectedLabel"
                        readonly
                        class="flex-1 bg-transparent outline-none min-w-[50px] text-xs"
                        :placeholder="placeholder"
                    />
                </div>
                <ChevronsUpDown class="h-4 w-4 opacity-50 text-xs" />
            </div>
        </PopoverTrigger>
        <PopoverContent :style="{ width: popoverWidth }" class="p-0">
            <Command @click.stop>
                <div class="flex border-b justify-center items-center">
                    <CommandInput
                        :hideInput="!showFilter"
                        :readOnly="!showFilter"
                        :placeholder="$t('common.placeholder.filter')"
                        v-model="filterKeyword"
                        customerClass="border-0 flex-auto text-xs"
                        @input="filterInput"
                    />
                </div>
                <CommandList>
                    <el-tree
                        :data="props.options"
                        :props="props.treeProps"
                        node-key="id"
                        ref="treeRef"
                        highlight-current
                        :current-node-key="currentNodeKey"
                        default-expand-all
                        @node-click="handleNodeClick"
                        :filter-node-method="filterNode"
                    />
                </CommandList>
                <div
                    class="customer-item cursor-pointer"
                    v-for="item in customItems"
                >
                    <div class="-mx-1 h-px bg-border"></div>
                    <div
                        class="flex p-2 bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors duration-200 items-center"
                        @click="() => generateType(item)?.fn.call($form)"
                    >
                        <Icon
                            v-if="generateType(item)?.icon"
                            :icon="generateType(item)?.icon"
                            class="h-6 w-6"
                        ></Icon>
                        <span>{{ generateType(item)?.title }}</span>
                    </div>
                </div>
            </Command>
        </PopoverContent>
    </Popover>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, watch, inject } from 'vue';
import { Check, ChevronsUpDown, X } from 'lucide-vue-next';
import { ElTree, TreeInstance } from 'element-plus';
import { Command, CommandInput, CommandList } from '@/components/ui/command';
import {
    Popover,
    PopoverTrigger,
    PopoverContent,
} from '@/components/ui/popover';
import { $t } from '@/locales';
import { generateType, IType } from '../../Select/src/capacity';
import { Icon } from '@iconify/vue';
import { isNil } from 'lodash-es';

const $form = inject('$form');

// 定义组件接收的属性
const props = defineProps<{
    modelValue: string | number | undefined;
    options?: Array<Record<string, any>>;
    treeProps: {
        children: string;
        label: string;
        value: string;
    };
    placeholder?: string;
    showFilter?: boolean;
    disabled?: boolean;
    customItems?: IType[];
    record?: Recordable;
}>();
const open = ref(false);

const isDisabled = computed(() => {
    return props.disabled;
});

// 定义组件触发的事件
const emit = defineEmits(['select', 'update:modelValue']);

// 选中节点的值
const selectedValue = ref(null);
const selectedLabel = ref('');
// 搜索关键词
const filterKeyword = ref('');
const treeRef = ref<TreeInstance>();
const currentNodeKey = ref<(string | number) | null>(null);

// 初始化选中节点
const initSelectedItem = () => {
    if (props.modelValue !== undefined && props.modelValue !== null) {
        const selectedItem = findSelectedItem(props.options, props.modelValue);
        currentNodeKey.value = props.modelValue;
        if (selectedItem) {
            selectedLabel.value = selectedItem[props.treeProps.label];
            selectedValue.value = selectedItem[props.treeProps.value];
        }
    }
};
// 查找选中节点
const findSelectedItem = (
    data: Array<Record<string, any>>,
    value: (string | number) | (string | number)[]
) => {
    if (isNil(data)) return null;
    for (const item of data) {
        if (
            Array.isArray(value)
                ? value.includes(item[props.treeProps.value])
                : value === item[props.treeProps.value]
        ) {
            return item;
        }
        if (item[props.treeProps.children]) {
            const found = findSelectedItem(
                item[props.treeProps.children],
                value
            );
            if (found) {
                return found;
            }
        }
    }
    return null;
};
watch(
    () => props.modelValue,
    (newVal) => {
        // console.log('props.modelValue', newVal);
        initSelectedItem();
    }
);

// 处理节点点击事件
const handleNodeClick = (data: Record<string, any>) => {
    selectedLabel.value = data[props.treeProps.label];
    selectedValue.value = data[props.treeProps.value];
    emit('update:modelValue', selectedValue.value);
    emit('select', selectedValue.value, data);
    open.value = false;
};

// 过滤树节点方法
const filterNode = (value: string, data: Record<string, any>) => {
    if (!value) return true;
    return data[props.treeProps.label]
        .toLowerCase()
        .includes(value.toLowerCase());
};

const filterInput = (e) => {
    filterKeyword.value = e.data;
    treeRef.value!.filter(filterKeyword.value);
    console.log('filterTree22222222', e, filterKeyword.value);
};
const generateId = () => {
    return (
        Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15)
    );
};

const elId = generateId();
/** 弹出框的宽度 */
const popoverWidth = ref('');

watch(open, (newVal) => {
    if (newVal) {
        // 在弹出框打开时获取触发元素的宽度
        const el = document.getElementById(elId);
        if (el) popoverWidth.value = `${el.offsetWidth}px`;
    }
});

onMounted(() => {
    initSelectedItem();
});
</script>
