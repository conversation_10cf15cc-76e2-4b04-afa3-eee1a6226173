<template>
  <form :class="formLayoutClass" v-bind="propsRef.formProps">
    <template v-for="fieldItem in getFields || []" :key="fieldItem.field">
      <ZbCard
        v-if="fieldItem.layout === 'Card'"
        v-bind="fieldItem.layoutProps"
        :class="[
          {
            hidden: !computedShow(fieldItem),
            [formItemSpans(fieldItem.span || true)]: true,
          },
        ]"
      >
        <div :class="formLayoutClass">
          <template
            v-for="schema in fieldItem.schemas || []"
            :key="schema.field"
          >
            <DynamicFormField
              :fieldItem="schema"
              :formApi="formApi"
              :labelPosition="propsRef?.labelPosition || schema?.labelPosition"
              :labelPrefix="schema?.labelPrefix || propsRef?.labelPrefix"
              :size="propsRef?.size"
              :formItemClass="formItemSpans(schema.span)"
              @registerCom="registerCom"
            >
              <template
                class="flex-1"
                v-for="item in getComponentSlots(schema)"
                #[item]="data"
              >
                <slot :name="schema?.slots?.[item]" v-bind="data || {}" />
              </template>
            </DynamicFormField>
          </template>
        </div>
        <!-- ZbCard 的插槽 -->
        <template
          v-for="item in getComponentSlots(fieldItem)"
          #[item]="data"
          :key="item"
        >
          <slot :name="fieldItem?.slots?.[item]" v-bind="data || {}" />
        </template>
      </ZbCard>
      <template v-else>
        <DynamicFormField
          :fieldItem="fieldItem"
          :formApi="formApi"
          :labelPosition="fieldItem?.labelPosition || propsRef?.labelPosition"
          :labelPrefix="fieldItem?.labelPrefix || propsRef?.labelPrefix"
          :size="propsRef?.size"
          :formItemClass="formItemSpans(fieldItem.span)"
          @registerCom="registerCom"
        >
          <template
            class="flex-1"
            v-for="item in getComponentSlots(fieldItem)"
            #[item]="data"
          >
            <slot :name="fieldItem?.slots?.[item]" v-bind="data || {}" />
          </template>
        </DynamicFormField>
      </template>
    </template>
  </form>
</template>

<script setup lang="ts">
import {
  computed,
  type Component,
  watch,
  ref,
  unref,
  type ComputedRef,
  onMounted,
  onUnmounted,
  getCurrentInstance,
  inject,
  provide,
  nextTick,
} from 'vue'
import DynamicFormField from './FormComponents/DynamicFormField.vue'
import type { FormEngineProps, FormFieldConfig } from './types/formEngine'
import { cloneDeep, get, isNil } from 'lodash-es'
import { useDict } from './hooks/useDict'
import { useSchemaApi } from './hooks/useSchemaApi'
import { useMetaData } from './hooks/useMetaData'
import { useFormCore } from './hooks/useFormCore'
import { useFormData } from './hooks/useFormData'
import { useComponentHandling } from './hooks/useComponentHandling'
import { useLayout } from './hooks/useLayout'
import ZbCard from '@/components/common/ZbCard/ZbCard.vue'
import { PartialDeep } from 'type-fest'

const props = withDefaults(
  defineProps<FormEngineProps & { modelValue?: Record<string, any> }>(),
  {
    modelValue: () => ({}),
  }
)
const emit = defineEmits(['update:modelValue', 'register'])

const propsRef = ref<Partial<FormEngineProps>>()
const schemaRef = ref<FormFieldConfig[] | null>()
const currentInstance = getCurrentInstance()

const getBind = computed(() => {
  return unref(propsRef)
}) as ComputedRef<FormEngineProps>

const { formLayoutClass, formItemSpans } = useLayout(propsRef)

const { defaultInitValues, init, initDefaultValues, fetchData } = useFormData(
  props,
  propsRef,
  schemaRef,
  computed(() => formApi)
)

init()

const {
  customerComActions,
  registerCom,
  getApi,
  resolveComponent,
  getComponentSlots,
} = useComponentHandling(currentInstance)

const {
  formApi,
  getFields,
  getSchema,
  fullSchema,
  computedShow,
  submit,
  setValues,
  getValues,
  validateForm,
  updateSchema,
  updateAllSchema,
  appendSchema,
  appendSchemas,
  deleteSchema,
  validateFields,
  mergeSchemas,
} = useFormCore(
  propsRef,
  getBind,
  schemaRef,
  defaultInitValues,
  customerComActions,
  emit
)

const { initDict } = useDict({ getSchema, updateSchema, setValues, getValues })
const { initSchemaApi } = useSchemaApi({ getSchema, updateSchema })
const { initMetaData } = useMetaData({ propsRef, getSchema, updateSchema })

function setProps(formProps?: Partial<FormEngineProps>) {
  init(formProps)
  initDict()
  initSchemaApi()
  initMetaData()
  initDefaultValues()
}

const methods = {
  ...formApi.value,
  submit,
  updateSchema,
  updateAllSchema,
  appendSchema,
  appendSchemas,
  deleteSchema,
  init,
  setProps,
  getApi,
  getValues,
  setValues: (fields: PartialDeep<Recordable>, shouldValidate?: boolean) => {
    formApi.value.setValues(fields, shouldValidate)

    customerComActions.forEach((com, field) => {
      if (isNil(field)) return
      const val = get(fields, field)
      !isNil(val) && com?.setValues(val)
    })
  },
}

provide('$form', methods)
provide('__formPropsRef', propsRef)

/** 暴露给父组件使用校验 */
defineExpose({
  validateForm,
  methods,
})

watch(
  formApi.value.values,
  (newValues) => {
    /** 每次值更新时，触发useForm的setValues保持同步更新，第二个参数 false 表示不触发校验 */
    formApi.value.setValues(
      {
        ...defaultInitValues.value,
        ...newValues,
      },
      false
    )
    emit('update:modelValue', newValues)
  },
  { deep: true }
)

onMounted(() => {
  initDict()
  initSchemaApi()
  initMetaData()
  initDefaultValues()

  nextTick(() => {
    fetchData()
  })
})

// 在组件卸载时清空modelValue
onUnmounted(() => {
  // 通过emit事件将modelValue设置为空对象，实现清空效果
  emit('update:modelValue', {})
})

emit('register', methods)
</script>

<style scoped>
:deep(.is-required) {
  > label {
    &::before {
      content: '*';
      color: hsl(var(--destructive));
      margin-right: 4px;
    }
  }
}

.col-span-2 {
  grid-column: span 2;
}

.col-span-3 {
  grid-column: span 3;
}

.col-span-4 {
  grid-column: span 4;
}

.col-span-5 {
  grid-column: span 5;
}

.col-span-6 {
  grid-column: span 6;
}

.col-span-7 {
  grid-column: span 7;
}

.col-span-9 {
  grid-column: span 8;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
</style>
