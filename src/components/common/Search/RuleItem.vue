<template>
    <div class="w-full" :class="{ 'pl-3': !isTop }" v-for="(filterItem, filterIndex) in props.filterData"
        :key="filterIndex + 'filter-item'">
        <div class="flex items-center" v-if="filterItem.conditions.length > 0">
            <span v-if="isTop">{{ $t('common.search.rule.label') }}</span>
            <Popover v-model:open="popoverOpen">
                <PopoverTrigger as-child>
                    <div
                        class="inline-block flex items-center gap-1 cursor-pointer hover:bg-gray-100 px-1 color-[#6c757d]">
                        <span>{{
                            coupleMap[
                            filterItem.couple as keyof typeof coupleMap
                            ]
                        }}</span>
                        <div class="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-[#6c757d] transition-transform duration-200"
                            :class="{ 'rotate-[180deg]': popoverOpen }"></div>
                    </div>
                </PopoverTrigger>
                <PopoverContent class="w-26 p-2">
                    <div class="flex flex-col gap-1">
                        <Button variant="link" @click="handleSetCouple(filterItem, 'and')">{{
                            $t('common.search.rule.options.all') }}</Button>
                        <Button variant="link" @click="handleSetCouple(filterItem, 'or')">{{
                            $t('common.search.rule.options.any') }}</Button>
                    </div>
                </PopoverContent>
            </Popover>
            <span>{{ $t('common.search.rule.ruleGroup') }}</span>
        </div>
        <div v-for="(rule, ruleIndex) in filterItem.conditions" :key="ruleIndex + 'rule-item'">
            <div class="flex items-center gap-2 py-2">
                <div class="flex-1">
                    <Select v-model="rule.field" :options="metaDataConfig"
                        :customField="{ value: 'name', label: 'comment' }" @change="fieldChange(rule)" />
                </div>
                <div class="flex-1">
                    <Select v-model="rule.operator" :options="rule.operatorList" />
                </div>

                <div class="flex-1" v-if="rule.field">
                    <template v-if="showThired(rule, 1)">
                        <component :is="componentsArr[ruleIndex]" v-model="rule.value" :key="rule.id"
                            v-bind="componentsPropsArr[ruleIndex]" />
                    </template>
                    <template v-if="showThired(rule, 2)">
                        <div class="flex items-center gap-1">
                            <component :is="componentsArr[ruleIndex]" v-model="rule.value[0]"
                                v-bind="componentsPropsArr[ruleIndex]" />
                            -
                            <component :is="componentsArr[ruleIndex]" v-model="rule.value[1]"
                                v-bind="componentsPropsArr[ruleIndex]" />
                        </div>
                    </template>
                    <template v-if="showThired(rule, 0)">
                    </template>
                </div>
                <div class="flex items-center gap-2 py-2">
                    <Plus class="cursor-pointer h-5 w-5" @click="ruleHandles.addRule(rule, ruleIndex)" />
                    <Network class="cursor-pointer h-4 w-4" @click="ruleHandles.addChildRule(rule, ruleIndex)" />
                    <Trash class="cursor-pointer h-4 w-4" @click="ruleHandles.deleteRule(rule, ruleIndex)" />
                </div>
            </div>
            <template v-if="rule.children && rule.children.length > 0">
                <RuleItem :filterData="rule.children" />
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { inject, Ref, ref, shallowRef, watch, type PropType } from 'vue';
import { Plus, Trash, Network } from 'lucide-vue-next';
import Select from '@/components/common/FormEngine/FormComponents/Select.vue';
import { Button } from '@/components/ui/button'
import {
    searchControls,
    type SearchControlsKeys,
} from './search-contarols';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import { $t } from '@/locales';
import { Condition, CoupleType, MetaData, SearchFilterData, SearchFilterDataItem } from './types';

defineOptions({
    name: 'RuleItem',
});

const props = defineProps({
    filterData: {
        type: Array as PropType<SearchFilterData>,
        default: () => [],
    },
    isTop: {
        type: Boolean,
        default: false,
    },
});

// 事件注入
const ruleHandles: Recordable = inject('ruleHandles');
const metaDataConfig: Ref<MetaData[]> = inject('metaDataConfig');
const popoverOpen = ref(false);
// 弹窗数据
const coupleMap = {
    and: $t('common.search.rule.options.all'),
    or: $t('common.search.rule.options.any'),
};
const handleSetCouple = (curRule: SearchFilterDataItem, couple: CoupleType) => {
    ruleHandles.setCouple(curRule, couple);
    popoverOpen.value = false;
};
const typeToComponentMap = {
    string: 'input',
    integer: 'numberInput',
    float: 'numberInput',
    boolean: 'select',
    enum: 'select',
    date: 'datePicker',
    datetime: 'datePicker',
};
// 一个输入框的 operator
const oneComponentOperators = ['eq', 'ne', 'gt', 'ge', 'ilike', 'lt', 'le', 'in'];
// 两个输入框的 operator
const twoComponentOperators = ['between'];
// 没有输入框的 operator
const noneComponentOperators = ['is_null', 'not_null'];

/** 渲染的输入组件 */
const componentsArr = shallowRef([])
const componentsPropsArr = shallowRef([])

/** 选择sql条件后渲染不同的输入组件 */
const fieldChange = (currentRule: Condition) => {
    ruleHandles.fieldChange(currentRule);
}

const showThired = (rule: Condition, number: number) => {
    if (!rule || !rule.operator) return false;

    if (number === 1) {
        if (oneComponentOperators.includes(rule.operator)) return true;
        if (twoComponentOperators.includes(rule.operator) && (rule.type === 'datetime' || rule.type === 'date')) return true;
    }
    if (number === 2) {
        if (twoComponentOperators.includes(rule.operator) && rule.type !== 'datetime' && rule.type !== 'date') return true;
    }
    if (number === 0) {
        if (noneComponentOperators.includes(rule.operator)) return true;
    }
    return false;
}

// 根据传入的组件名称，动态解析组件
function resolveComponent(ruleItem: Condition, index: number) {
    if (!ruleItem || !ruleItem.field) return;
    const cur = metaDataConfig.value.find(
        (item: MetaData) => item.name === ruleItem.field
    ) || {};

    if (ruleItem.operator === 'in') {
        if (!Array.isArray(ruleItem.value)) {
            ruleItem.value = [];
        }
        if (ruleItem.type === 'enum') {
            return searchControls.select;
        }
        return searchControls.tagsInput;
    }
    if (twoComponentOperators.includes(ruleItem.operator)) {
        if (!Array.isArray(ruleItem.value)) {
            ruleItem.value = [null, null];
        }
        // 如果是日期选框，则渲染一个日期范围选择器
        if (cur.type === 'datetime' || cur.type === 'date') {
            return searchControls.dateRangerPicker;
        }
    } else if (noneComponentOperators.includes(ruleItem.operator)) {
        ruleItem.value = null;
    }

    if (ruleItem.type)

        return searchControls[
            typeToComponentMap[
            cur.type as keyof typeof typeToComponentMap
            ] as SearchControlsKeys
        ];
};

function setComponentProps(ruleItem: Condition) {
    if (!ruleItem || !ruleItem.field) return;

    const cur = metaDataConfig.value.find(
        (item: MetaData) => item.name === ruleItem.field
    ) || {};

    //如果是布尔值类型，则给下拉赋值是否选项
    if (cur.type === 'boolean') {
        return {
            options: [
                { label: '是', value: true },
                { label: '否', value: false },
            ],
        };
    }
    // 如果是枚举类型，则给下拉赋值枚举选项
    if (cur.type === 'enum') {
        if (cur.enum_info?.enum_values) {
            let options = Object.values(cur.enum_info?.enum_values).map(item => ({
                label: item,
                value: item,
            }));
            return {
                multiple: ruleItem.operator === 'in',
                showFilter: true,
                options,
            };
        }
        return {
            options: [],
        };
    }
    return {}
};

watch(() => props.filterData, async (newVal) => {
    if (newVal[0]?.conditions) {
        newVal[0].conditions.forEach((element, index) => {
            if (element.field === '' || element.field === null) {
                return;
            }
            const props = setComponentProps(element)
            const com = resolveComponent(element, index)
            if (componentsArr.value[index]) {
                componentsArr.value.splice(index, 1, com)
            } else {
                componentsArr.value.splice(index, 0, com)
            }
            if (componentsPropsArr.value[index]) {
                componentsPropsArr.value.splice(index, 1, props)
            } else {
                componentsPropsArr.value.splice(index, 0, props)
            }
        });
    }
}, { deep: true, immediate: true })

</script>
