import { FilterOperatorKey } from '@/types/api/queryParams';

interface MetaData {
    name?: string;
    type?: string;
    nullable?: boolean;
    operators?: Array<string>;
    comment?: string;
    is_foreign?: boolean;
    relation_info?: Relation_Info;
    enum_info?: Enum_info;
    is_hybrid?: boolean;
}

interface Relation_Info {
    related_model?: string;
    foreign_keys?: Array<string>;
    direction?: string;
}

interface Enum_info {
    enum_class?: string;
    enum_values?: Recordable;
}

interface InitialFilterData {
    field: string;
    op: string;
    value: any;
}

/** 条件类型 */
export interface Condition {
    id: string;
    field: string;
    operator: FilterOperatorKey;
    value: string | number | boolean | Date | any[];
    operatorList: Recordable[];
    type: string;
    children: SearchFilterDataItem[];
}

/** 条件组类型 */
export type CoupleType = 'and' | 'or';

/** 规则类型 */
export interface SearchFilterDataItem {
    couple: CoupleType;
    id: string;
    conditions: Condition[];
}
/** 搜索条件数据类型 */
export type SearchFilterData = SearchFilterDataItem[];

export interface CheckedFilterData {
    filterData: SearchFilterData;
    text: string;
}

export type { MetaData, InitialFilterData };
