<template>
  <div
    ref="searchInputRef"
    class="relative min-w-[min(600px,33%)] max-w-[max(600px,50%)] z-100"
  >
    <div
      class="flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"
      @click="handleClick()"
    >
      <div class="flex flex-wrap flex-1 gap-1">
        <div
          v-for="(checkedRule, i) in checkedFilters"
          :key="i + checkedRule.text"
          class="group hover:bg-white flex items-center rounded bg-muted text-sm"
        >
          <div
            class="h-full w-8 bg-[#02a7f0] rounded-md rounded-r-none flex items-center justify-center cursor-pointer hover:bg-[#0283bd]"
            @click.stop="viewRule(checkedRule, i)"
          >
            <Filter class="group-hover:hidden h-4 w-4 text-white" />
            <Settings class="group-hover:block hidden h-4 w-4 text-white" />
          </div>
          <div
            class="group-hover:border group-hover:border-l-0 group-hover:pr-1 h-[26px] pr-[5px] flex-1 flex items-center gap-1 px-1 rounded rounded-l-none"
          >
            <div
              class="py-1 truncate max-w-[15rem] text-content"
              :title="checkedRule.text"
            >
              {{ checkedRule.text }}
            </div>
            <X class="h-3 w-3 cursor-pointer" @click.stop="removeTag(i)" />
          </div>
        </div>
        <input
          ref="inputRef"
          v-model="inputValue"
          class="flex-1 bg-transparent outline-none min-w-[50px]"
          :placeholder="$t('common.placeholder.search')"
          @focus="handleClick('input')"
          @keydown="keydownInput"
          @click.stop
        />
      </div>
      <ChevronsUpDown class="h-4 w-4 opacity-50" />
    </div>
    <!-- 输入后的下拉 -->
    <Command
      v-model:open="inputCommandVisible"
      class="absolute top-full z-50 mt-[2px] w-full rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 h-[auto] max-h-[300px] overflow-y-auto"
      :class="{ 'opacity-0': !inputCommandVisible }"
      @click.stop
    >
      <CommandInput
        :hideInput="true"
        id="input-command-input"
        :placeholder="$t('common.placeholder.filter')"
      />
      <CommandList>
        <CommandGroup>
          <CommandEmpty>
            <div class="p-6 text-sm text-muted-foreground">
              {{ $t('common.common.noData') }}
            </div>
          </CommandEmpty>
          <template v-for="option in columnsSearchConfig" :key="option.name">
            <CommandItem
              v-if="option.type !== 'enum'"
              :value="option.name"
              @select="toggleInputValue(option)"
            >
              <div class="flex w-full truncate cursor-pointer">
                <span>{{ $t('common.button.search') }}</span>
                <span class="px-1 font-bold">{{
                  option.comment || option.name
                }}</span>
                <span>{{ $t('common.search.for') }}</span>
                <span class="text-[#71639e] font-bold">{{ inputValue }}</span>
              </div>
            </CommandItem>
          </template>
          <CommandItem value="custom" @select="toggleValue('custom')">
            <div class="w-full cursor-pointer">
              {{ $t('common.search.addCustomFilter') }}
            </div>
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </Command>
    <!-- 规则下拉 -->
    <Command
      v-model:open="ruleDropdownVisible"
      class="absolute top-full z-50 mt-[2px] w-[120%] left-[-10%] rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 h-[auto] max-h-screen overflow-y-auto"
      :class="{ 'opacity-0': !ruleDropdownVisible }"
      @click.stop
    >
      <CommandInput
        :hideInput="true"
        id="rule-command-input"
        :placeholder="$t('common.placeholder.filter')"
      />
      <CommandList class="max-h-full">
        <div class="flex py-4 pb-1">
          <div class="flex-1 px-2 border-r">
            <CommandGroup>
              <div class="flex items-center gap-2 mb-2">
                <Filter class="h-4 w-4" />
                <span>{{ $t('common.placeholder.filter') }}</span>
              </div>
              <CommandEmpty>
                <div class="p-6 text-sm text-muted-foreground">
                  {{ $t('common.common.noData') }}
                </div>
              </CommandEmpty>
              <div class="max-h-[13rem] overflow-y-auto">
                <CommandItem
                  class="cursor-pointer"
                  v-for="option in columnsSearchConfig"
                  :key="option.name"
                  :value="option.name"
                  @select="toggleValue(option)"
                >
                  <span class="flex-1 truncate">{{
                    option.comment || option.name
                  }}</span>
                </CommandItem>
              </div>
            </CommandGroup>
            <CommandGroup class="border-t">
              <CommandItem
                class="cursor-pointer"
                value="custom"
                @select="toggleValue('custom')"
                >{{ $t('common.search.addCustomFilter') }}
              </CommandItem>
            </CommandGroup>
          </div>
          <div class="flex-1"></div>
          <div class="flex-1"></div>
        </div>
      </CommandList>
    </Command>
    <Dialog
      v-model:open="editRuleDialog"
      :title="$t('common.search.filterCriteria')"
      size="small"
    >
      <div class="w-full border-t pt-2">
        <RuleItem :filterData="filterData" :isTop="true" />
        <Button
          v-if="filterData[0].conditions.length === 0"
          variant="outline"
          @click="resetFilterData"
          >{{ $t('common.search.addRule') }}</Button
        >
      </div>
      <template #footer>
        <Button variant="outline" @click="closeDialog">{{
          $t('common.button.cancel')
        }}</Button>
        <Button
          :disabled="filterData[0].conditions.length === 0"
          @click="() => saveCondition()"
          >{{ $t('common.button.save') }}</Button
        >
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  provide,
  ref,
  shallowRef,
  watch,
  watchEffect,
  unref,
} from 'vue'
import { ChevronsUpDown, X, Filter, Settings } from 'lucide-vue-next'
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import Dialog from '@/components/common/Dialog.vue'
import type { FilterGroup, FilterOperatorKey } from '@/types/api/queryParams.ts'
import { FilterOperatorMap } from '@/types/api/queryParams'
import { Button } from '@/components/ui/button'
import RuleItem from './RuleItem.vue'
import {
  convertConditionsToReadable,
  defaultFilterData,
  findFieldWithParent,
  flattenConditions,
} from './search-contarols'
import { deepClone } from '@/utils/util'
import type {
  CheckedFilterData,
  Condition,
  InitialFilterData,
  MetaData,
  SearchFilterData,
} from './types'
import { cloneDeep, isArray, isFunction } from 'lodash-es'

defineOptions({
  name: 'Search',
})

interface Props {
  /** 根据列表展示项筛选过的搜索字段 */
  fieldsConfig?: string[]
  /** 所有的筛选字段 */
  metaData?: MetaData[]
  /** 初始传入的搜索条件 */
  initialFilterData?: InitialFilterData | InitialFilterData[]
}

const props = withDefaults(defineProps<Props>(), {
  fieldsConfig: () => [],
  metaData: () => [],
  initialFilterData: undefined,
})

const emit = defineEmits(['search'])
const valueRef = ref()
/** 缓存已选中的 filterData */
const checkedFilters = ref<CheckedFilterData[]>([])
/** 规则下拉是否显示 */
const ruleDropdownVisible = ref(false)
/** 输入的值 */
const inputValue = ref('')
/** 输入下拉是否显示 */
const inputCommandVisible = ref(false)
/** 输入框 */
const inputRef = ref<HTMLInputElement>()
/** 当前修改的规则索引 */
const currentEditRuleIndex = ref<null | number>(null)
/** 搜索配置 */
const metaDataConfig = shallowRef<MetaData[]>([])
/** 根据 fieldsConfig 配置生成的搜索配置 */
const columnsSearchConfig = shallowRef<MetaData[]>([])
/** 默认的搜索数据结构 */
const filterData = ref<SearchFilterData>(deepClone(defaultFilterData))
watchEffect(() => {
  if (props.metaData.length && props.fieldsConfig.length) {
    /** 筛选mateData，id和以_id结尾的不展示 */
    metaDataConfig.value = props.metaData.filter((item) => {
      if (item.name === 'id') return false
      return !/.*_id$/.test(item.name)
    })
    columnsSearchConfig.value = metaDataConfig.value.filter((item) =>
      props.fieldsConfig.find((field: string) => field === item.name)
    )
    setCheckedFilter(props.initialFilterData)
  }
})

/** 设置传入的搜索条件 */
function setCheckedFilter(data: InitialFilterData | InitialFilterData[]) {
  if (!data) return
  const mockData = deepClone(defaultFilterData)
  mockData[0].conditions = []
  if (Array.isArray(data)) {
    // 是数组
    data.forEach((item) => {
      const metaData = getDataByField(item.field)
      mockData[0].conditions.push({
        id: `0-${mockData[0].conditions.length}`,
        field: item.field,
        operator: item.op,
        value: item.value,
        operatorList: metaData.operators.map((item: FilterOperatorKey) => ({
          label: FilterOperatorMap[item],
          value: item,
        })),
        type: metaData.type,
        children: [],
      })
    })
    const resText = convertConditionsToReadable(mockData, metaDataConfig.value)
    checkedFilters.value.push({
      filterData: deepClone(mockData),
      text: resText,
    })
  } else if (data && typeof data === 'object') {
    const metaData = getDataByField(data.field)
    mockData[0].conditions.push({
      id: '0-0',
      field: data.field,
      operator: data.op,
      value: data.value,
      operatorList: metaData.operators.map((item: FilterOperatorKey) => ({
        label: FilterOperatorMap[item],
        value: item,
      })),
      type: metaData.type,
      children: [],
    })
    const resText = convertConditionsToReadable(mockData, metaDataConfig.value)
    checkedFilters.value.push({
      filterData: deepClone(mockData),
      text: resText,
    })
  }
}

/** 筛选弹窗 */
const editRuleDialog = ref(false)

/** 点击搜索字段，获取该字段的Sql规则下拉 */
function toggleValue(data: 'custom' | MetaData) {
  if (data === 'custom') {
    ruleDropdownVisible.value = false
    editRuleDialog.value = true
    return
  }
  // 因为是第一次打开，则直接附默认值
  filterData.value[0].conditions[0].field = data.name
  filterData.value[0].conditions[0].type = data.type
  setOperatorByField(filterData.value[0].conditions[0])
  ruleDropdownVisible.value = false
  editRuleDialog.value = true
}

/** 点击输入框中已有规则，进行回显 */
function viewRule(rule: CheckedFilterData, index: number) {
  filterData.value = deepClone(rule.filterData)
  currentEditRuleIndex.value = index
  editRuleDialog.value = true
}

/** 删除输入框中已选中的规则 */
function removeTag(index: number) {
  checkedFilters.value.splice(index, 1)
  ruleDropdownVisible.value = false
  emitSearch()
}

/** 点击输入下拉，直接生成点击字段的 ilike（模糊匹配） 输入内容规则 */
function toggleInputValue(option: MetaData) {
  checkedFilters.value.push({
    filterData: [
      {
        couple: 'and',
        id: '0',
        conditions: [
          {
            id: '0-0',
            field: option.name,
            operator: 'ilike',
            value: inputValue.value,
            operatorList: option.operators.map((item: FilterOperatorKey) => ({
              label: FilterOperatorMap[item],
              value: item,
            })),
            type: option.type,
            children: [],
          },
        ],
      },
    ],
    text: `${option.comment} 模糊匹配 ${inputValue.value}`,
  })
  inputValue.value = ''
  inputCommandVisible.value = false
  emitSearch()
}

/** 打开下拉 */
function handleClick(type?: string) {
  if (inputCommandVisible.value) return
  if (type === 'input') {
    if (ruleDropdownVisible.value) return
    ruleDropdownVisible.value = true
    return
  }
  ruleDropdownVisible.value = !ruleDropdownVisible.value
}
/** 监听键盘事件 */
function keydownInput(e: KeyboardEvent) {
  if (e.key === 'ArrowDown') {
    focusDropdown()
  }
}
function focusDropdown() {
  if (ruleDropdownVisible.value) {
    const commandInput = document.getElementById(
      'rule-command-input'
    ) as HTMLInputElement
    commandInput.focus()
  } else if (inputCommandVisible.value) {
    const commandInput = document.getElementById(
      'input-command-input'
    ) as HTMLInputElement
    commandInput.focus()
  }
}

/** 根据 field 获取字段配置 */
function getDataByField(field: string) {
  return metaDataConfig.value.find((item) => item.name === field) || {}
}

/** 根据 id 获取当前字段支持的Sql规则下拉 */
function setOperatorByField(ruleItem: Condition) {
  const data = getDataByField(ruleItem.field)
  // 将 Sql 规则转换为下拉选项
  const operatorsOptions = data.operators.map((item: FilterOperatorKey) => ({
    label: FilterOperatorMap[item],
    value: item,
  }))
  const res = findFieldWithParent(ruleItem.id, filterData.value)
  if (res?.target) {
    nextTick(() => {
      res.target.operatorList = operatorsOptions
      res.target.operator = operatorsOptions[0].value
      res.target.type = data.type
      res.target.value = ''
    })
  }
}

/** 字段改变，如果field为空，则不进行操作 */
function fieldChange(ruleItem: Condition) {
  if (!ruleItem.field) return
  setOperatorByField(ruleItem)
}

/** 关闭弹窗 */
const closeDialog = () => {
  editRuleDialog.value = false
  currentEditRuleIndex.value = null
  resetFilterData()
}

/** 重置 filterData */
const resetFilterData = () => {
  filterData.value = deepClone(defaultFilterData)
}

/** 保存筛选条件 */
function saveCondition(text?, filter?, index = null) {
  let resText =
    text || convertConditionsToReadable(filterData.value, metaDataConfig.value)
  resText = resText.replace(/^\(|\)$/g, '') // 去除首尾的括号
  // 如果已选中的规则，则修改已选中的规则
  currentEditRuleIndex.value = index
  if (currentEditRuleIndex.value !== null) {
    checkedFilters.value[currentEditRuleIndex.value] = {
      filterData: deepClone(filterData.value),
      text: resText,
    }
    currentEditRuleIndex.value = null
  } else {
    checkedFilters.value.push({
      filterData: deepClone(filterData.value),
      text: resText,
    })
  }
  editRuleDialog.value = false
  emitSearch((val) => {
    if (filter) {
      valueRef.value = filter
      emit('search', filter)
    } else {
      valueRef.value = val
      emit('search', val)
    }
  })
}

/** 将 checkedFilters 转换为请求参数格式 */
const convertRequestParams = () => {
  let arr: FilterGroup[] = []
  checkedFilters.value.forEach((item) => {
    arr.push(flattenConditions(item.filterData)[0])
  })
  return arr
}

/** 暴露方法获取请求参数 */
const getRequestParams = () => {
  //   const arr = convertRequestParams()
  //   if (arr.length > 0) {
  //     return {
  //       filters: {
  //         couple: 'and',
  //         conditions: arr,
  //       },
  //     }
  //   }
  return valueRef.value
}

function emitSearch(callBack?) {
  const arr = convertRequestParams() || []
  let val
  if (arr.length > 0) {
    val = {
      filters: {
        couple: 'and',
        conditions: arr,
      },
    }
  }

  valueRef.value = val

  if (isFunction(callBack)) {
    callBack(val)
  } else {
    emit('search', val)
  }
  resetFilterData()
}

/** == 规则的增删 =================================================== */
/** 新增规则 */
const addRule = (curRule: Condition, index: number) => {
  let res = findFieldWithParent(curRule.id, filterData.value)
  if (res && res.parent && res.target) {
    let newCondition = cloneDeep(unref(res.target))
    newCondition.id = `${res.parent.id}-${index + 1}`
    newCondition.value = ''
    newCondition.children = []
    res.parent.conditions.splice(index + 1, 0, newCondition)
  }
}
/** 新增子集规则 */
const addChildRule = (curRule: Condition) => {
  let res = findFieldWithParent(curRule.id, filterData.value)
  if (res && res.target) {
    let newCondition = {
      id: '',
      field: curRule.field,
      operator: curRule.operator,
      value: '',
      operatorList: curRule.operatorList,
      type: '',
      children: [],
    }
    // 如果子集已经有值了，则向子集的 conditions 中添加新的规则
    if (res.target.children.length > 0) {
      newCondition.id = `${res.target.id}-0-${res.target.children[0].conditions.length}`
      res.target.children[0].conditions.push(newCondition)
    } else {
      newCondition.id = `${res.target.id}-0-0`
      res.target.children.push({
        couple: 'and',
        id: `${res.target.id}-0`,
        conditions: [newCondition],
      })
    }
  }
}
/** 删除规则 */
const deleteRule = (curRule: Condition, index: number) => {
  let res = findFieldWithParent(curRule.id, filterData.value)
  if (res && res.parent && res.target) {
    res.parent.conditions.splice(index, 1)
  }
}
// 设置规则逻辑
const setCouple = (curRule: Condition, couple: 'and' | 'or') => {
  const res = findFieldWithParent(curRule.id, filterData.value)
  if (res && res.target) {
    res.target.couple = couple
  }
}
provide('ruleHandles', {
  addRule,
  addChildRule,
  deleteRule,
  fieldChange,
  setCouple,
})
provide('metaDataConfig', metaDataConfig)

/** == 输入下拉逻辑 =================================================== */
// 输入下拉是否显示
watch(
  () => inputValue.value,
  (newVal: string) => {
    if (newVal) {
      inputCommandVisible.value = true
      ruleDropdownVisible.value = false
    } else {
      inputCommandVisible.value = false
    }
  }
)

/** == 点击组件外关闭下拉显示 =================================================== */
const searchInputRef = ref<HTMLDivElement>()
const handleClickOutside = (event: MouseEvent) => {
  if (!searchInputRef.value) return
  if (!searchInputRef.value.contains(event.target as Node)) {
    ruleDropdownVisible.value = false
    inputCommandVisible.value = false
    inputValue.value = ''
  }
}

defineExpose({
  getRequestParams,
  saveCondition,
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  // getSearchConfig();
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
