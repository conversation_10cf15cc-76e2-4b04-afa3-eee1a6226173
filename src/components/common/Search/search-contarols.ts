import Select from '@/components/common/FormEngine/FormComponents/Select.vue';
import { Input } from '@/components/ui/input';
import NumberInput from '@/components/common/FormEngine/FormComponents/NumberInput.vue';
import { reactive } from 'vue';
import DatePicker from '@/components/common/FormEngine/FormComponents/DatePicker.vue';
import DateRangerPicker from '@/components/common/FormEngine/FormComponents/DateRangerPicker.vue';
import { $t } from '@/locales';
import TagsInput from '@/components/common/FormEngine/FormComponents/TagsInput.vue';
import {
    Condition,
    MetaData,
    SearchFilterData,
    SearchFilterDataItem,
} from './types';
const searchControls = {
    select: Select,
    input: Input,
    numberInput: NumberInput,
    datePicker: DatePicker,
    dateRangerPicker: DateRangerPicker,
    tagsInput: TagsInput,
};

export type SearchControlsKeys = keyof typeof searchControls;
export { searchControls };

const LANG = 'zh-CN';

/** 操作方法 */
// 根据 field 查找对象及其父级
type Rule = {
    couple: string;
    id: string;
    conditions: Condition[];
};

/** 根据 id 查找对象及其父级 */
function findFieldWithParent(
    id: string,
    data: any[],
    parent: Rule | null = null
): { target: any; parent: Rule } | null {
    // 遍历数组中的每个对象
    for (const item of data) {
        // 检查当前对象是否有conditions属性（表示它是父对象）
        if (item.conditions) {
            // 在当前对象的conditions中查找
            const result = findFieldWithParent(id, item.conditions, item);
            if (result) return result;
        }

        // 检查当前对象是否是我们要找的字段，如果是则返回响应式的对象和父级，这样直接改这些对象就能改对应位置的原数据
        if (item.id === id) {
            return {
                target: reactive(item),
                parent: reactive(parent as Rule),
            };
        }

        // 检查是否有子条件需要递归查找
        if (item.children && item.children.length > 0) {
            const result = findFieldWithParent(id, item.children, item);
            if (result) return result;
        }
    }

    // 如果没有找到返回null
    return null;
}

/** 将 filterData 转换为传给后端的格式 */
function flattenConditions(data: any) {
    return data.map(processNode).filter(Boolean);

    function processNode(node: any) {
        // 过滤条件
        if (node.couple && (!node.conditions || node.conditions.length === 0)) {
            return null;
        }

        const newNode: any = { id: node.id };

        if (node.couple) newNode.couple = node.couple;
        if (node.field) {
            newNode.field = node.field;
            newNode.op = node.operator;
            newNode.value = formatValue(node.value, node.type, node.operator);
        }

        const allConditions = [
            ...(node.conditions
                ?.filter((cond: any) => cond.field)
                ?.map(({ id, field, operator, value, type }: any) => ({
                    id,
                    field,
                    op: operator,
                    value: formatValue(value, type, operator),
                })) || []),
            ...(node.children?.map(processNode).filter(Boolean) || []),
            ...(node.conditions?.flatMap(
                (cond: any) =>
                    cond.children?.map(processNode).filter(Boolean) || []
            ) || []),
        ];

        if (allConditions.length > 0) {
            newNode.conditions = allConditions;
        }

        return newNode;
    }
    /** 根据类型格式化 value */
    function formatValue(value: any, type: string, operator: string) {
        if (operator === 'between') {
            return [value[0], value[1]];
        }
        if (type === 'date' || type === 'datetime') {
            return value;
        } else if (type === 'integer' || type === 'float') {
            return numberStringToNumber(value);
        }
        return value;
    }
    /** 将数字字符转换为数字 */
    function numberStringToNumber(value: any) {
        if (
            typeof value === 'string' &&
            !isNaN(Number(value)) &&
            !isNaN(parseFloat(value))
        ) {
            return Number(value);
        }
        return value;
    }
}

/** 默认的 filterData */
const defaultFilterData = [
    {
        couple: 'and',
        id: '0',
        conditions: [
            {
                id: '0-0',
                field: '',
                operator: '',
                value: '',
                operatorList: [],
                type: '',
                children: [],
            },
        ],
    },
];

/** 将 filterData 转换为可读的文字格式，用于输入框的显示 */
function convertConditionsToReadable(
    data: SearchFilterData,
    config: MetaData[]
) {
    // 创建字段名映射表
    const fieldMap: Recordable = {};
    config.forEach((item: MetaData) => {
        fieldMap[item.name] = item.comment;
    });

    // 操作符映射表
    const operatorMap: Recordable = {
        eq: $t('common.operator.eq'),
        ne: $t('common.operator.ne'),
        ilike: $t('common.operator.ilike'),
        in: $t('common.operator.in'),
        is_null: $t('common.operator.is_null'),
        not_null: $t('common.operator.not_null'),
        gt: $t('common.operator.gt'),
        ge: $t('common.operator.ge'),
        lt: $t('common.operator.lt'),
        le: $t('common.operator.le'),
        between: $t('common.operator.between'),
    };

    // 处理单个条件节点
    function processCondition(condition: Condition) {
        const fieldLabel = fieldMap[condition.field] || condition.field;
        const operatorText =
            operatorMap[condition.operator] || condition.operator;

        if (condition.operator === 'is_null') {
            return `${fieldLabel} 为空`;
        } else if (condition.operator === 'not_null') {
            return `${fieldLabel} 不为空`;
        } else {
            let valueText = '';
            if (condition.type === 'date' || condition.type === 'datetime') {
                // 日期类型
                if (Array.isArray(condition.value)) {
                    valueText = condition.value
                        .map((item) => {
                            return new Date(item).toLocaleDateString(LANG);
                        })
                        .join('至');
                } else {
                    try {
                        valueText = new Date(
                            condition.value as Date
                        ).toLocaleDateString(LANG);
                    } catch (error) {
                        valueText = condition.value as string;
                    }
                }
                return `${fieldLabel} ${operatorText} ${valueText}`;
            } else {
                // 其他类型
                if (Array.isArray(condition.value)) {
                    valueText = condition.value.join('-');
                } else {
                    valueText = condition.value as string;
                }
                return `${fieldLabel} ${operatorText} ${valueText}`;
            }
        }
    }

    // 递归处理条件组
    function processGroup(group: any) {
        if (!group.conditions || group.conditions.length === 0) {
            return '';
        }

        const parts = [];
        for (const condition of group.conditions) {
            if (condition.field) {
                // 普通条件
                parts.push(processCondition(condition));
            } else if (condition.couple) {
                // 子条件组
                const subParts: any = processGroup(condition);
                if (subParts) {
                    parts.push(subParts);
                }
            }

            // 处理children中的条件
            if (condition.children && condition.children.length > 0) {
                condition.children.forEach((child: any) => {
                    const childParts = processGroup(child);
                    if (childParts) {
                        parts.push(childParts);
                    }
                });
            }
        }

        if (parts.length === 0) {
            return '';
        } else if (parts.length === 1) {
            return parts[0];
        } else {
            const connector = group.couple === 'or' ? ' 或 ' : ' 并且 ';
            return `(${parts.join(connector)})`;
        }
    }

    // 处理顶层数据
    const result: string[] = [];
    data.forEach((group: SearchFilterDataItem) => {
        const groupText = processGroup(group);
        if (groupText) {
            result.push(groupText);
        }
    });

    return result.join(' 并且 ');
}

export {
    flattenConditions,
    findFieldWithParent,
    convertConditionsToReadable,
    defaultFilterData,
};
