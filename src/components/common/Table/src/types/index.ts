import { ButtonGroupProps, FormFieldConfig } from '@/components'
import { Ref } from 'vue'
import {
    VxeTableProps,
    VxeColumnProps,
    VxeColumnPropTypes,
    VxeTableDefines,
    VxeTablePropTypes,
    VxeToolbarConstructor,
    VxeToolbarInstance,
    ValueOf,
    VxeTableEmits,
} from 'vxe-table'
import type { SummaryColumnOptions } from '../../plugins/summaryColumn'

export interface BaseTableProps extends VxeTableProps {
    modelValue?: Recordable[] | undefined
    // 唯一标识符 只有设置了id的table才会保存表头
    id?: string
    // 模式 detail 模式下将移除schema配置
    mode?: 'detail' | string
    columns?: BaseColumn[]
    mergeColumns?: (BaseColumn & { code: string })[]
    columnsApi?: Function
    showFields?: BaseColumn[]
    // 是否使用序号
    showIndex?: boolean
    // 是否使用翻页
    usePagination?: boolean
    // 翻页配置
    pagination?: {
        start?: number
        end?: number
        total?: number
        queryParams?: Recordable
    }
    // 是否立即请求接口
    immediate?: boolean
    // 请求接口
    // 请求之前调用，用于修正请求参数和阻止继续查询 返回Recordable是请求参数，返回false会阻止后续的查询
    // searchParams: 列表查询框
    // pageParams： 分页
    // otherParams：fetchSetting.queryParams 一般用于一些固定参数传递进去
    // reloadParams：api.reload 查询参数
    fetchApi?: (
        searchParams: Recordable,
        pageParams: Recordable,
        otherParams?: Recordable,
        reloadParams?: Recordable
    ) => Promise<{ total: number; items: Recordable[] } | false>

    // fetchApi 请求之后
    afterFetch?: (tableApi) => void

    dataChange?: (data: Recordable[]) => void

    // 是否支持排序
    enableSort?: boolean

    buttonList?: ButtonGroupProps[]

    // 事件绑定
    on?: Recordable

    // 是否监听页面高度 动态设置table高度
    observeScrollY?: boolean

    // 额外的布局高度 参与计算页面高度
    layoutHeight?: number

    // 是否自动高度占满容器
    autoHeight?: boolean

    // 单选 多选
    checkType?: 'radio' | 'checkbox' | false

    showPagination?: boolean

    showSearch?: boolean

    toolbarConfig?: {
        isExport?: boolean
        isReload?: boolean
        isZoom?: boolean
        isFull?: boolean
    }

    // 键盘事件配置
    keyboardEventsConfig?: {
        fields?: string[]
        enterField?: string
        nextField?: string
        afterEnter?: (
            row: Recordable,
            nextRow: Recordable,
            column: BaseColumn
        ) => void
        afterDown?: (preRow: Recordable, row: Recordable, field: string) => void
    }

    tableWrapConfig?: {
        wrapProps?: Recordable
        mainProps?: Recordable
        leftProps?: Recordable
        rightProps?: Recordable
    }

    // 表格样式配置
    tableStyle?: TableStyleOptions

    // 自定义样式类
    customClass?: string

    // 是否启用默认样式
    enableDefaultStyle?: boolean

    // 合计列配置
    summaryConfig?: SummaryColumnOptions

    // 内置的鼠标右键事件配置
    rightMouseEventConfig?: {
        events?: string[]
        enable?: boolean
    }
}

export interface MoreConfigItem {
    text: string // 菜单项文本
    icon?: string // 菜单项图标
    danger?: boolean // 是否为危险操作（红色）
    disabled?: boolean // 是否禁用
    onClick?: (params: { row: any }) => void // 点击事件处理函数
}

interface MoreConfig {
    items?: MoreConfigItem[] // 菜单项列表
}

export interface BaseColumn extends VxeColumnProps {
    field?: string
    valueType?: string
    schema?: FormFieldConfig
    updateCellSchema?: (
        record: Recordable,
        schema: FormFieldConfig
    ) => FormFieldConfig
    auth?: string | string[]
    sort?: number
    // 默认隐藏，可在列配置显示
    defaultHidden?: boolean
    children?: BaseColumn[]
    slots?: Recordable
    footerSlot?: string // 自定义底部合计插槽
    editRender?: Recordable
    ifShow?: (column: BaseColumn) => boolean
    // formatter?: (value: any, record: Recordable) => string | number;
    // 单元格操作配置
    cellActionConfig?: {
        moreConfig?: MoreConfig
        actions?: ButtonGroupProps[]
    }
    // 列的额外配置选项
    options?: Recordable
    // 复合数据显示配置
    compositeConfig?: {
        // 主数据配置
        main: {
            field: string
            formatter?: (value: any, record: Recordable) => string
            style?: Recordable // 主数据样式
            tooltip?: boolean | string | ((value: any, record: Recordable) => string) // 工具提示
        }
        // 辅助数据配置（支持多个）
        subs?: Array<{
            field?: string // 数据字段
            computed?: {
                // 计算字段配置
                fields: string[] // 参与计算的字段
                formula: (values: any[], record: Recordable) => any // 计算公式
                cache?: boolean // 是否缓存计算结果
            }
            template?: string // 模板字符串，如 "总价: ${value}"
            formatter?: (value: any, record: Recordable) => string
            style?: Recordable // 自定义样式
            condition?: (record: Recordable) => boolean // 条件显示
            onClick?: (value: any, record: Recordable) => void // 点击事件
            tooltip?: boolean | string | ((value: any, record: Recordable) => string) // 工具提示
        }>
        // 分隔符配置
        separator?: {
            main?: string // 主数据与辅助数据之间的分隔符，默认为空
            subs?: string // 辅助数据之间的分隔符，默认为 " | "
            vertical?: string // 垂直布局时的分隔符，默认为换行
        }
        // 图标/图片配置（可选）
        icon?: {
            field?: string // 图标字段
            type?: 'icon' | 'image' | 'avatar' // 图标类型
            // iconName?: string // 固定图标名称
            // 支持字符串和函数类型，函数参数为 record，返回图标名称或组件
            iconName?: string | ((record: Recordable) => string | any)
            avatarField?: string // 头像字段
            imageField?: string // 图片字段
            style?: Recordable // 自定义样式
            size?: number | string // 图标大小
            position?: 'left' | 'right' | 'top' | 'bottom' // 图标位置
        }
        // 布局配置
        layout?: 'horizontal' | 'vertical' | 'mixed' // 布局方式
        // 子布局配置（仅在mixed模式下生效）
        subLayout?: {
            main?: 'horizontal' | 'vertical' // 主数据布局
            subs?: 'horizontal' | 'vertical' // 辅助数据布局
        }
        // 容器样式
        containerStyle?: Recordable
        // 交互配置
        interactive?: {
            hover?: {
                showAll?: boolean // 悬停时显示所有信息
                highlight?: boolean // 悬停时高亮显示
            }
            click?: {
                expandable?: boolean // 点击可展开详情
                onExpand?: (record: Recordable) => void
            }
        }
    }

    // 编辑器渲染模式
    schemaRenderType?: 'edit' | 'default'

    // 是否展示tooltip
    showTooltip?: boolean

    // 唯一key 用于刷新
    _key?: string
}

export interface VxeTableMethods {
    updateCellSchema: (record: Recordable, schema: Recordable) => void
    insert: (records: any) => Promise<{
        row: any
        rows: any[]
    }>

    setEditCell: (
        row: Recordable,
        fieldOrColumn: string | Recordable
    ) => Promise<any>

    setSelectCell: (
        row: Recordable,
        fieldOrColumn: string | Recordable
    ) => Promise<any>

    insertAt: (
        records: any,
        targetRow?: any | -1 | null
    ) => Promise<{
        row: any
        rows: any[]
    }>

    validate: (
        rows?:
            | boolean
            | object
            | any[]
            | ((errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void),
        callback?: (errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void
    ) => Promise<Recordable>

    validateField: (
        rows?: boolean | object | any[],
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ) => Promise<Recordable>
    fullValidate: (
        rows?:
            | boolean
            | object
            | any[]
            | ((errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void),
        callback?: (errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void
    ) => Promise<Recordable>

    fullValidateField: (
        rows?: boolean | object | any[],
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ) => Promise<Recordable>
    dispatchEvent: (
        type: ValueOf<VxeTableEmits>,
        params: Record<string, any>,
        evnt: Event | null
    ) => void
    getEl: () => HTMLElement | null
    clearAll: () => Promise<void>
    updateData: () => void
    loadData: (data: any[]) => Promise<any>
    reloadData: (data: any[]) => Promise<void>
    setRow: (rows: any | any[], record?: any) => Promise<void>
    reloadRow: (rows: any | any[], record?: any, field?: string) => Promise<void>
    getParams: () => any
    loadTreeChildren: (row: any, children: any[]) => Promise<any[]>
    loadColumn: (
        columns: (
            | VxeTableDefines.ColumnOptions<any>
            | VxeTableDefines.ColumnInfo<any>
        )[]
    ) => Promise<any>
    reloadColumn: (
        columns: (
            | VxeTableDefines.ColumnOptions<any>
            | VxeTableDefines.ColumnInfo<any>
        )[]
    ) => Promise<any>
    getRowNode: (trElem: HTMLElement) => {
        rowid: string
        item: any
        items: any[]
        index: number
        parent?: any
    } | null
    getColumnNode: (cellElem: HTMLElement) => {
        colid: string
        item: VxeTableDefines.ColumnInfo<any>
        items: VxeTableDefines.ColumnInfo<any>[]
        index: number
        parent: VxeTableDefines.ColumnInfo<any>
    }
    getRowSeq: (row: any) => string | number
    getRowIndex: (row: any) => number
    getVTRowIndex: (row: any) => number
    getVMRowIndex: (row: any) => number
    getColumnIndex: (row: any) => number
    getVTColumnIndex: (row: any) => number
    getVMColumnIndex: (row: any) => number
    createData: (records: any[]) => Promise<any[]>
    createRow: (records: any | any[]) => Promise<any | any[]>
    revertData: (rows?: any | any[], field?: string) => Promise<any>
    clearData: (rows?: any | any[], field?: string) => Promise<any>
    getCellElement: (
        row: any,
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ) => HTMLTableDataCellElement | null
    getCellLabel: (
        row: any,
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ) => string | number | null
    isInsertByRow: (row: any | null) => boolean
    isRemoveByRow: (row: any | null) => boolean
    removeInsertRow: () => Promise<{
        row: any
        rows: any[]
    }>
    isUpdateByRow: (row: any, field?: string | null) => boolean
    getColumns: (columnIndex?: number) => VxeTableDefines.ColumnInfo<any>[]
    getColid: (
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ) => string | null
    getColumnById: (colid: string | null) => VxeTableDefines.ColumnInfo<any>
    getColumnByField: (
        field: VxeColumnPropTypes.Field | null
    ) => VxeTableDefines.ColumnInfo<any>
    getParentColumn: (
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ) => VxeTableDefines.ColumnInfo<any>
    getTableColumn: () => VxeTableDefines.ColumnInfo<any>[]
    moveColumnTo: (
        fieldOrColumn: any,
        targetFieldOrColumn: any,
        options: any
    ) => Promise<{ status: boolean }>
    moveRowTo: (
        rowidOrRow: any,
        targetRowidOrRow: any,
        options?: {
            isCrossDrag?: boolean
            dragToChild?: boolean
            dragPos?: 'top' | 'bottom' | '' | null
        }
    ) => Promise<{ status: boolean }>
    getFullColumns: () => VxeTableDefines.ColumnInfo<any>[]
    getData: (rowIndex?: number) => any
    getCheckboxRecords: (isFull?: boolean) => any[]
    getTreeRowChildren: (rowOrRowid: any) => any[]
    getTreeParentRow: (rowOrRowid: any) => any
    getRowById: (rowid: string | number | null) => any
    getRowid: (row: any | null) => string
    getTableData: () => any
    getFullData: () => any[]
    setColumnFixed: (
        fieldOrColumns:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[],
        fixed: VxeColumnPropTypes.Fixed
    ) => Promise<void>
    clearColumnFixed: (
        fieldOrColumns:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ) => Promise<void>
    hideColumn: (
        fieldOrColumns:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ) => Promise<void>
    showColumn: (
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ) => Promise<void>
    setColumnWidth: (
        fieldOrColumns:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[],
        width: number | string
    ) => Promise<{ status: boolean }>
    getColumnWidth: (
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ) => number
    refreshColumn: (resiveOrder?: boolean) => Promise<void>
    setRowHeightConf: (
        heightConf: Record<string, number>
    ) => Promise<{ status: boolean }>
    getRowHeightConf: (isFull?: boolean) => Record<string, number>
    setRowHeight: (
        rowOrId: any | any[],
        height: number | string
    ) => Promise<{ status: boolean }>
    getRowHeight: (rowOrId: any) => number
    refreshScroll: () => Promise<void>
    recalculate: (refull?: boolean) => Promise<void>
    openTooltip: (target: HTMLElement, content: string | number) => Promise<any>
    closeTooltip: () => Promise<any>
    isAllCheckboxChecked: () => boolean
    isAllCheckboxIndeterminate: () => boolean
    getCheckboxIndeterminateRecords: (isFull?: boolean) => any[]
    setCheckboxRow: (rows: any | any[], checked: boolean) => Promise<any>
    setCheckboxRowKey: (
        keys: string | number | (string | number)[],
        checked: boolean
    ) => Promise<any>
    isCheckedByCheckboxRow: (row: any) => boolean
    isCheckedByCheckboxRowKey: (key: string | number) => boolean
    isIndeterminateByCheckboxRow: (row: any) => boolean
    isIndeterminateByCheckboxRowKey: (key: string | number) => boolean
    toggleCheckboxRow: (row: any) => Promise<any>
    setAllCheckboxRow: (checked: boolean) => Promise<any>
    getRadioReserveRecord: (isFull?: boolean) => any
    clearRadioReserve: () => Promise<any>
    getCheckboxReserveRecords: (isFull?: boolean) => any[]
    clearCheckboxReserve: () => Promise<any>
    toggleAllCheckboxRow: () => Promise<any>
    clearCheckboxRow: () => Promise<any>
    setCurrentRow: (row: any) => Promise<any>
    isCheckedByRadioRow: (row: any | null) => boolean
    isCheckedByRadioRowKey: (key: string | number) => boolean
    setRadioRow: (row: any) => Promise<any>
    setRadioRowKey: (key: string | number) => Promise<any>
    clearCurrentRow: () => Promise<any>
    clearRadioRow: () => Promise<any>
    getCurrentRecord: () => any
    getRadioRecord: (isFull?: boolean) => any
    getCurrentColumn: () => VxeTableDefines.ColumnInfo<any> | null
    setCurrentColumn: (
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ) => Promise<void>
    clearCurrentColumn: () => Promise<void>
    setPendingRow: (rows: any | any[], status: boolean) => Promise<any>
    togglePendingRow: (rows: any | any[]) => Promise<any>
    isPendingByRow: (row: any) => boolean
    getPendingRecords: () => any[]
    clearPendingRow: () => Promise<any>
    sort: (field: string, order?: VxeTablePropTypes.SortOrder) => Promise<void>
    setSort: (
        sortConfs: VxeTableDefines.SortConfs | VxeTableDefines.SortConfs[],
        update?: boolean
    ) => Promise<void>
    setSortByEvent: (
        event: Event,
        sortConfs: VxeTableDefines.SortConfs | VxeTableDefines.SortConfs[],
        update?: boolean
    ) => Promise<void>
    clearSort: (
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ) => Promise<void>
    clearSortByEvent: (
        event: Event,
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ) => Promise<void>
    isSort: (
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ) => boolean
    getSortColumns: () => VxeTableDefines.SortCheckedParams[]
    setFilterByEvent: (
        event: Event,
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>,
        options: VxeColumnPropTypes.FilterItem[],
        update?: boolean
    ) => Promise<void>
    closeFilter: () => Promise<any>
    isActiveFilterByColumn: (
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ) => boolean
    clearFilterByEvent: (
        event: Event,
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ) => Promise<void>
    isRowExpandLoaded: (row: any | null) => boolean
    clearRowExpandLoaded: (row: any) => Promise<void>
    reloadRowExpand: (row: any) => Promise<void>
    toggleRowExpand: (row: any) => Promise<void>
    setAllRowExpand: (expanded: boolean) => Promise<void>
    setRowExpand: (rows: any | any[], expanded: boolean) => Promise<void>
    isRowExpandByRow: (row: any | null) => boolean
    clearRowExpand: () => Promise<void>
    clearRowExpandReserve: () => Promise<void>
    getRowExpandRecords: () => any[]
    setRowGroups: (
        fieldOrColumns:
            | (VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>)[]
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ) => Promise<void>
    clearRowGroups: () => Promise<void>
    isRowGroupRecord: (row: any) => boolean
    isRowGroupExpandByRow: (row: any) => boolean
    setRowGroupExpand: (rows: any | any[], expanded: boolean) => Promise<void>
    setAllRowGroupExpand: (expanded: boolean) => Promise<void>
    clearRowGroupExpand: () => Promise<void>
    getTreeExpandRecords: () => any[]
    isTreeExpandLoaded: (row: any | null) => boolean
    clearTreeExpandLoaded: (rows: any | any[]) => Promise<any>
    reloadTreeExpand: (row: any) => Promise<any>
    toggleTreeExpand: (row: any) => Promise<any>
    setAllTreeExpand: (expanded: boolean) => Promise<void>
    setTreeExpand: (rows: any | any[], expanded: boolean) => Promise<void>
    isTreeExpandByRow: (row: any | null) => boolean
    clearTreeExpand: () => Promise<void>
    clearTreeExpandReserve: () => Promise<void>
    getScroll: () => {
        virtualX: boolean
        virtualY: boolean
        scrollTop: number
        scrollLeft: number
    }
    scrollTo: (
        scrollLeft: number | null,
        scrollTop?: number | null
    ) => Promise<void>
    scrollToRow: (
        row: any,
        fieldOrColumn?: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ) => Promise<any>
    scrollToColumn: (
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ) => Promise<any>
    clearScroll: () => Promise<any>
    updateFooter: () => Promise<any>
    updateStatus: (
        params: { row: any; column: VxeTableDefines.ColumnInfo<any> },
        cellValue?: any
    ) => Promise<any>
    setMergeCells: (
        merges:
            | VxeTableDefines.MergeOptions<any>
            | VxeTableDefines.MergeOptions<any>[]
    ) => Promise<any>
    removeMergeCells: (
        merges:
            | VxeTableDefines.MergeOptions<any>
            | VxeTableDefines.MergeOptions<any>[]
    ) => Promise<VxeTableDefines.MergeInfo[]>
    getMergeCells: () => VxeTableDefines.MergeInfo[]
    clearMergeCells: () => Promise<any>
    setMergeFooterItems: (
        merges:
            | VxeTableDefines.MergeOptions<any>
            | VxeTableDefines.MergeOptions<any>[]
    ) => Promise<any>
    removeMergeFooterItems: (
        merges:
            | VxeTableDefines.MergeOptions<any>
            | VxeTableDefines.MergeOptions<any>[]
    ) => Promise<VxeTableDefines.MergeInfo[]>
    getMergeFooterItems: () => VxeTableDefines.MergeInfo[]
    clearMergeFooterItems: () => Promise<any>
    updateCellAreas: () => Promise<any>
    getCustomStoreData: () => VxeTableDefines.CustomStoreData
    focus: () => Promise<void>
    blur: () => Promise<void>
    connect: (
        toolbar: VxeToolbarConstructor | VxeToolbarInstance
    ) => Promise<void>
}

export interface TableMethods extends VxeTableMethods {
    instance: Ref<Recordable>
    reload: (opt: Recordable, vxeTableMethods: VxeTableMethods) => void
    setTableData: (data: Recordable[]) => void
    checked: Ref<Recordable>
}

// 导入 TableStyleOptions 类型
export interface TableStyleOptions {
    // 斑马纹配置
    striped?: boolean
    // 边框配置
    border?: boolean | 'horizontal' | 'vertical' | 'all'
    // 紧凑模式
    compact?: boolean
    // 表头样式
    headerStyle?: 'default' | 'gray' | 'dark' | 'none'
    // 行高配置
    rowHeight?: 'small' | 'medium' | 'large' | number
    // 圆角边框
    rounded?: boolean | 'sm' | 'md' | 'lg'
    // 阴影
    shadow?: boolean | 'sm' | 'md' | 'lg'
    // 响应式配置
    responsive?: boolean
    // 自定义CSS类
    customClass?: string
    // 主题模式
    theme?: 'light' | 'dark' | 'auto'
}
