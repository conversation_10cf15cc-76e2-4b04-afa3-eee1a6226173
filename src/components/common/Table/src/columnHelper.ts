import type { BaseColumn } from './types';

/**
 * 数据字段解析和格式化工具
 */

/**
 * 安全地获取嵌套对象的值
 * @param obj 数据对象
 * @param path 字段路径，如 'user.name' 或 'order.items[0].name'
 * @param defaultValue 默认值
 */
export function getFieldValue(
    obj: any,
    path: string,
    defaultValue: any = null
): any {
    if (!obj || !path) return defaultValue;

    try {
        const keys = path.split('.');
        let value = obj;

        for (const key of keys) {
            // 处理数组索引，如 items[0]
            if (key.includes('[') && key.includes(']')) {
                const arrayKey = key.substring(0, key.indexOf('['));
                const indexStr = key.substring(
                    key.indexOf('[') + 1,
                    key.indexOf(']')
                );
                const index = parseInt(indexStr);

                value = value?.[arrayKey]?.[index];
            } else {
                value = value?.[key];
            }

            if (value === null || value === undefined) {
                return defaultValue;
            }
        }

        return value;
    } catch (error) {
        console.warn(`Failed to get field value for path: ${path}`, error);
        return defaultValue;
    }
}

/**
 * 根据字段类型格式化值
 * @param value 原始值
 * @param fieldType 字段类型（来自元数据）
 * @param options 格式化选项
 */
export function formatFieldValue(
    value: any,
    fieldType?: string,
    options: {
        dateFormat?: string;
        currencyCode?: string;
        precision?: number;
        enumMap?: Record<string, string>;
        nullText?: string;
    } = {}
): string {
    const {
        dateFormat = 'YYYY-MM-DD',
        currencyCode = 'CNY',
        precision = 2,
        enumMap = {},
        nullText = '-',
    } = options;

    if (value === null || value === undefined || value === '') {
        return nullText;
    }

    switch (fieldType) {
        case 'datetime':
        case 'date':
            if (!value) return options.nullText || '-';
            const date = new Date(value);
            if (isNaN(date.getTime())) return options.nullText || '-';
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                ...(fieldType === 'datetime'
                    ? {
                          hour: '2-digit',
                          minute: '2-digit',
                      }
                    : {}),
            });

        case 'float':
        case 'number':
            const num = Number(value);
            return isNaN(num) ? value.toString() : num.toFixed(precision);

        case 'boolean':
            return value ? '是' : '否';

        case 'enum':
            return enumMap[value] || value.toString();

        case 'relation':
            // 处理关系对象，通常显示 name 字段
            if (typeof value === 'object') {
                return (
                    value?.name ||
                    value?.title ||
                    value?.nick_name ||
                    value?.code ||
                    value?.id?.toString() ||
                    nullText
                );
            }
            return value?.toString() || nullText;

        default:
            return String(value);
    }
}

/**
 * 智能字段解析器 - 根据字段路径和元数据自动解析和格式化
 * @param record 数据记录
 * @param fieldPath 字段路径
 * @param metadata 字段元数据（可选）
 * @param options 格式化选项
 */
function smartFieldParser(
    record: any,
    fieldPath: string,
    metadata?: {
        type?: string;
        enum_info?: {
            enum_values?: Record<string, string>;
        };
        relation_info?: {
            related_model?: string;
            direction?: string;
        };
    },
    options: any = {}
): string {
    const value = getFieldValue(record, fieldPath);

    // 如果有元数据，使用元数据信息格式化
    if (metadata) {
        const formatOptions = {
            ...options,
            enumMap: metadata.enum_info?.enum_values || {},
        };

        return formatFieldValue(value, metadata.type, formatOptions);
    }

    // 没有元数据时的智能推断
    return formatFieldValue(value, undefined, options);
}

/**
 * 关系数据展示辅助函数
 */
function relationDisplay(
    record: any,
    relationPath: string,
    options: {
        displayField?: string; // 优先显示的字段
        template?: string; // 模板，如 "${name} (${code})"
        fallbackFields?: string[]; // 备选字段
        showCount?: boolean; // 对于数组关系，是否显示数量
        nullText?: string;
    } = {}
): string {
    const {
        displayField,
        template,
        fallbackFields = ['name', 'title', 'nick_name', 'code', 'id'],
        showCount = false,
        nullText = '-',
    } = options;

    const relationObj = getFieldValue(record, relationPath);

    if (!relationObj) return nullText;

    // 如果是数组（一对多关系）
    if (Array.isArray(relationObj)) {
        if (showCount || !template) {
            // 显示数量（默认行为或明确要求显示数量）
            return `${relationObj.length}条`;
        } else if (template) {
            // 使用模板显示数组信息
            return template
                .replace(/\$\{count\}/g, relationObj.length.toString())
                .replace(/\$\{length\}/g, relationObj.length.toString());
        }
        return `${relationObj.length}条`;
    }

    // 处理单个关系对象（多对一）
    if (typeof relationObj === 'object' && relationObj !== null) {
        // 如果有模板，优先使用模板
        if (template) {
            return template.replace(/\$\{(\w+)\}/g, (match, fieldName) => {
                const fieldValue = getFieldValue(relationObj, fieldName);
                return fieldValue !== null && fieldValue !== undefined
                    ? String(fieldValue)
                    : '';
            });
        }

        // 如果指定了显示字段
        if (displayField && displayField !== 'undefined') {
            const value = getFieldValue(relationObj, displayField);
            if (value !== null && value !== undefined) return String(value);
        }

        // 按优先级查找可显示的字段
        for (const field of fallbackFields) {
            const value = getFieldValue(relationObj, field);
            if (value !== null && value !== undefined) {
                return String(value);
            }
        }
    }

    // 如果是简单值（字符串、数字等）
    if (typeof relationObj === 'string' || typeof relationObj === 'number') {
        return String(relationObj);
    }

    return nullText;
}

/**
 * 枚举值显示辅助函数
 */
function enumDisplay(
    value: any,
    enumMap: Record<string, string>,
    options: {
        showOriginal?: boolean; // 是否同时显示原始值
        template?: string; // 模板，如 "${text} (${value})"
        nullText?: string;
    } = {}
): string {
    const { showOriginal = false, template, nullText = '-' } = options;

    if (value === null || value === undefined) return nullText;

    const text = enumMap[value] || value.toString();

    if (template) {
        return template
            .replace(/\$\{text\}/g, text)
            .replace(/\$\{value\}/g, value.toString());
    }

    if (showOriginal && enumMap[value]) {
        return `${text} (${value})`;
    }

    return text;
}

/**
 * 列配置助手函数
 * 简化列定义，提供常用的列类型快捷方法
 */

export interface ColumnHelperOptions {
    width?: number | string;
    minWidth?: number | string;
    fixed?: 'left' | 'right';
    align?: 'left' | 'center' | 'right';
    sortable?: boolean;
    resizable?: boolean;
    visible?: boolean;
    formatter?: (params: {
        cellValue: any;
        row: any;
        column: any;
    }) => string | number;
    auth?: string | string[];
}

/**
 * 复合列辅助数据配置接口
 */
export interface CompositeSubConfig {
    field?: string; // 数据字段路径，支持嵌套如 'user.name'
    computed?: {
        fields: string[]; // 参与计算的字段路径
        formula: (values: any[], record: any) => any; // 计算公式
        cache?: boolean; // 是否缓存计算结果
    };
    template?: string; // 模板字符串，如 "总价: ${value}"
    formatter?: (value: any, record: any) => string; // 自定义格式化器
    // 智能字段解析配置
    smartParse?: {
        metadata?: {
            type?: string; // 字段类型：string, integer, float, boolean, enum, relation, datetime等
            enum_info?: {
                enum_values?: Record<string, string>; // 枚举值映射
            };
            relation_info?: {
                related_model?: string;
                direction?: string; // MANYTOONE, ONETOMANY等
            };
        };
        options?: {
            dateFormat?: string; // 日期格式
            precision?: number; // 数字精度
            currencyCode?: string; // 货币代码
            nullText?: string; // 空值显示文本
            showOriginal?: boolean; // 是否显示原始值
        };
    };
    // 关系数据配置
    relation?: {
        displayField?: string; // 关系对象显示字段
        template?: string; // 关系对象显示模板
        fallbackFields?: string[]; // 备选字段
        showCount?: boolean; // 对于数组关系，是否显示数量
    };
    // 枚举数据配置
    enum?: {
        enumMap?: Record<string, string>; // 枚举映射
        showOriginal?: boolean; // 是否显示原始值
        template?: string; // 显示模板
    };
    style?: any; // 自定义样式，支持函数
    condition?: (record: any) => boolean; // 条件显示
    onClick?: (value: any, record: any) => void; // 点击事件
    tooltip?: boolean | string | ((value: any, record: any) => string); // 工具提示
}

/**
 * 复合列主配置接口
 */
export interface CompositeMainConfig {
    field?: string;
    formatter?: (value: any, record: any) => string;
    style?: any;
    tooltip?: boolean | string | ((value: any, record: any) => string);
}

/**
 * 复合列图标配置接口
 */
export interface CompositeIconConfig {
    field?: string;
    type?: 'icon' | 'image' | 'avatar';
    iconName?: string;
    avatarField?: string;
    imageField?: string;
    style?: any;
    size?: number | string;
    position?: 'left' | 'right' | 'top' | 'bottom';
}

/**
 * 创建文本列
 */
export function textColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions = {}
): BaseColumn {
    return {
        field,
        title,
        ...options,
    };
}

/**
 * 创建数字列
 */
export function numberColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & { precision?: number; unit?: string } = {}
): BaseColumn {
    const { precision, unit, ...rest } = options;

    return {
        field,
        title,
        formatter: ({ cellValue }: { cellValue: any }) => {
            if (cellValue == null) return '';
            let formatted =
                precision !== undefined
                    ? Number(cellValue).toFixed(precision)
                    : cellValue.toString();
            return unit ? `${formatted} ${unit}` : formatted;
        },
        align: 'right',
        ...rest,
    };
}

/**
 * 创建日期列
 */
export function dateColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & { format?: string } = {}
): BaseColumn {
    const { format = 'YYYY-MM-DD', ...rest } = options;

    return {
        field,
        title,
        formatter: ({ cellValue }: { cellValue: any }) => {
            if (!cellValue) return '-';
            // 这里可以集成项目的日期格式化库
            return new Date(cellValue).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
            });
        },
        align: 'center',
        width: 120,
        ...rest,
    };
}

/**
 * 创建状态列
 */
export function statusColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        statusMap?: Record<string | number, { text: string; color?: string }>;
        type?: 'tag' | 'badge' | 'text';
    } = {}
): BaseColumn {
    const { statusMap = {}, type = 'tag', ...rest } = options;

    return {
        field,
        title,
        align: 'center',
        width: 100,
        slots: { default: `${field}_status` },
        formatter: ({ cellValue }: { cellValue: any }) => {
            return statusMap[cellValue]?.text || cellValue;
        },
        ...rest,
    };
}

/**
 * 创建操作列
 */
export function actionColumn(
    title: string = '操作',
    options: ColumnHelperOptions & {
        actions?: Array<{
            text: string;
            icon?: string;
            color?: string;
            auth?: string | string[];
            onClick?: (row: any) => void;
        }>;
        width?: number;
    } = {}
): BaseColumn {
    const { actions = [], width = 120, ...rest } = options;

    return {
        field: 'actions',
        title,
        width,
        fixed: 'right',
        align: 'center',
        cellActionConfig: {
            actions: actions.map((action) => ({
                text: action.text,
                icon: action.icon,
                color: action.color,
                auth: action.auth,
                onClick: action.onClick,
            })),
        },
        slots: { default: 'actions' },
        ...rest,
    };
}

/**
 * 创建图片列
 */
export function imageColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        preview?: boolean;
        size?: number;
    } = {}
): BaseColumn {
    const { preview = true, size = 40, ...rest } = options;

    return {
        field,
        title,
        width: size + 20,
        align: 'center',
        slots: { default: `${field}_image` },
        ...rest,
    };
}

/**
 * 创建链接列
 */
export function linkColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        href?: string | ((row: any) => string);
        target?: '_blank' | '_self';
    } = {}
): BaseColumn {
    const { href, target = '_blank', ...rest } = options;

    return {
        field,
        title,
        cellRender: {
            name: 'LinkRenderer',
            props: { href, target, ...rest },
        },
        ...rest,
    };
}

/**
 * 创建选择列（枚举）
 */
export function selectColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        options?: Array<{ label: string; value: any; color?: string }>;
        multiple?: boolean;
    } = {}
): BaseColumn {
    const { options: selectOptions = [], multiple = false, ...rest } = options;

    return {
        field,
        title,
        formatter: ({ cellValue }: { cellValue: any }) => {
            if (multiple && Array.isArray(cellValue)) {
                return cellValue
                    .map((v) => {
                        const option = selectOptions.find(
                            (opt) => opt.value === v
                        );
                        return option?.label || v;
                    })
                    .join(', ');
            } else {
                const option = selectOptions.find(
                    (opt) => opt.value === cellValue
                );
                return option?.label || cellValue;
            }
        },
        ...rest,
    };
}

/**
 * 创建进度条列
 */
export function progressColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        max?: number;
        showText?: boolean;
        color?: string;
    } = {}
): BaseColumn {
    const { max = 100, showText = true, color = 'primary', ...rest } = options;

    return {
        field,
        title,
        width: 120,
        align: 'center',
        slots: { default: `${field}_progress` },
        ...rest,
    };
}

/**
 * 创建百分比列
 */
export function percentColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        precision?: number;
        isDecimal?: boolean; // 是否是0-1的小数形式
    } = {}
): BaseColumn {
    const { precision = 2, isDecimal = true, ...rest } = options;

    return {
        field,
        title,
        align: 'right',
        width: 100,
        formatter: ({ cellValue }: { cellValue: any }) => {
            if (cellValue == null) return '';
            const numValue = Number(cellValue);
            if (isNaN(numValue)) return cellValue;

            // 如果是小数形式（0-1），转换为百分比
            if (isDecimal && numValue >= 0 && numValue <= 1) {
                return `${(numValue * 100).toFixed(precision)}%`;
            }
            // 否则直接显示为百分比
            return `${numValue.toFixed(precision)}%`;
        },
        ...rest,
    };
}

/**
 * 创建货币列
 */
export function currencyColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        precision?: number;
        currency?: string;
        locale?: string;
    } = {}
): BaseColumn {
    const {
        precision = 2,
        currency = 'CNY',
        locale = 'zh-CN',
        ...rest
    } = options;

    // 货币符号映射，避免编码问题
    const currencySymbols: Record<string, string> = {
        CNY: '¥',
        USD: '$',
        EUR: '€',
        GBP: '£',
        JPY: '¥',
        HKD: 'HK$',
        TWD: 'NT$',
        KRW: '₩',
    };

    return {
        field,
        title,
        align: 'right',
        width: 120,
        formatter: ({ cellValue }: { cellValue: any }) => {
            if (cellValue == null) return '';
            const numValue = Number(cellValue);
            if (isNaN(numValue)) return cellValue;

            // 优先使用符号映射，避免编码问题
            if (currencySymbols[currency]) {
                return `${currencySymbols[currency]}${numValue.toFixed(precision)}`;
            }

            // 使用 Intl.NumberFormat，增加错误处理
            try {
                return new Intl.NumberFormat(locale, {
                    style: 'currency',
                    currency: currency,
                    minimumFractionDigits: precision,
                    maximumFractionDigits: precision,
                }).format(numValue);
            } catch (error) {
                console.warn(
                    `Invalid currency code: ${currency}, falling back to simple format`
                );
                // 降级处理：使用原货币代码作为前缀
                return `${currency} ${numValue.toFixed(precision)}`;
            }
        },
        ...rest,
    };
}

/**
 * 创建浮点数列
 */
export function floatColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        precision?: number;
        unit?: string;
        thousandSeparator?: boolean;
    } = {}
): BaseColumn {
    const { precision = 2, unit, thousandSeparator = false, ...rest } = options;

    return {
        field,
        title,
        align: 'right',
        width: 100,
        formatter: ({ cellValue }: { cellValue: any }) => {
            if (cellValue == null) return '';
            const numValue = Number(cellValue);
            if (isNaN(numValue)) return cellValue;

            let formatted = numValue.toFixed(precision);

            if (thousandSeparator) {
                formatted = Number(formatted).toLocaleString();
            }

            return unit ? `${formatted} ${unit}` : formatted;
        },
        ...rest,
    };
}

/**
 * 创建关系列（一对多）
 */
export function oneToManyColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        itemName?: string; // 项目名称，如 "订单", "商品"
        showEmpty?: boolean; // 是否显示0项
        clickable?: boolean; // 是否可点击查看详情
        onItemClick?: (items: any[], row: any) => void;
    } = {}
): BaseColumn {
    const {
        itemName = '项',
        showEmpty = true,
        clickable = false,
        onItemClick,
        ...rest
    } = options;

    return {
        field,
        title,
        width: 100,
        align: 'center',
        formatter: ({ cellValue }: { cellValue: any }) => {
            if (!cellValue || !Array.isArray(cellValue)) {
                return showEmpty ? `0 ${itemName}` : '-';
            }
            const count = cellValue.length;
            return count > 0
                ? `${count} ${itemName}`
                : showEmpty
                  ? `0 ${itemName}`
                  : '-';
        },
        ...(clickable && onItemClick
            ? {
                  cellStyle: { cursor: 'pointer', color: '#1890ff' },
                  slots: { default: `${field}_clickable` },
              }
            : {}),
        ...rest,
    };
}

/**
 * 创建关系列（多对一）
 */
export function manyToOneColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        displayField?: string; // 显示字段名，默认优先级: name > title > code > id
        clickable?: boolean; // 是否可点击查看详情
        onItemClick?: (item: any, row: any) => void;
    } = {}
): BaseColumn {
    const { displayField, clickable = false, onItemClick, ...rest } = options;

    return {
        field,
        title,
        width: 150,
        align: 'left',
        formatter: ({ cellValue }: { cellValue: any }) => {
            if (!cellValue) return '-';
            if (typeof cellValue === 'object') {
                if (displayField && cellValue[displayField]) {
                    return cellValue[displayField];
                }
                // 默认优先级: name > title > code > id
                return (
                    cellValue.name ||
                    cellValue.title ||
                    cellValue.code ||
                    cellValue.id ||
                    '-'
                );
            }
            return cellValue;
        },
        ...(clickable && onItemClick
            ? {
                  cellStyle: { cursor: 'pointer', color: '#1890ff' },
                  slots: { default: `${field}_clickable` },
              }
            : {}),
        ...rest,
    };
}

/**
 * 创建布尔列
 */
export function booleanColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        trueText?: string; // 真值显示文本，默认"是"
        falseText?: string; // 假值显示文本，默认"否"
        nullText?: string; // 空值显示文本，默认"-"
        showIcon?: boolean; // 是否显示图标
        style?: 'text' | 'tag' | 'switch'; // 显示样式
        onChange?: (value: boolean, row: any, field: string) => void; // 值变化时的回调函数
    } = {}
): BaseColumn {
    const {
        trueText = '是',
        falseText = '否',
        nullText = '-',
        showIcon = false,
        style = 'tag',
        onChange,
        ...rest
    } = options;

    return {
        field,
        title,
        width: 95,
        align: 'center',
        // 保存选项供 BooleanRenderer 使用
        options: {
            trueText,
            falseText,
            nullText,
            showIcon,
            style,
            onChange,
        },
        formatter: ({ cellValue }: { cellValue: any }) => {
            if (cellValue === null || cellValue === undefined) {
                return nullText;
            }
            // 将各种可能的值转换为布尔值
            const boolValue =
                cellValue === true ||
                cellValue === 1 ||
                cellValue === '1' ||
                cellValue === 'true';
            return boolValue ? trueText : falseText;
        },
        slots: { default: `${field}_boolean` },
        ...rest,
    };
}

/**
 * 创建通用关系列
 */
export function relationColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        relationType?: 'oneToMany' | 'manyToOne' | 'manyToMany';
        displayField?: string;
        itemName?: string;
        showEmpty?: boolean;
        clickable?: boolean;
        onItemClick?: (data: any, row: any) => void;
    } = {}
): BaseColumn {
    const {
        relationType = 'manyToOne',
        displayField,
        itemName = '项',
        showEmpty = true,
        clickable = false,
        onItemClick,
        ...rest
    } = options;

    if (relationType === 'oneToMany' || relationType === 'manyToMany') {
        return oneToManyColumn(field, title, {
            itemName,
            showEmpty,
            clickable,
            onItemClick,
            ...rest,
        });
    } else {
        return manyToOneColumn(field, title, {
            displayField,
            clickable,
            onItemClick,
            ...rest,
        });
    }
}

/**
 * 批量创建列的辅助函数
 */
export function createColumns(
    configs: Array<{
        type:
            | 'text'
            | 'number'
            | 'date'
            | 'status'
            | 'action'
            | 'image'
            | 'link'
            | 'select'
            | 'progress'
            | 'boolean';
        field: string;
        title: string;
        options?: any;
    }>
): BaseColumn[] {
    const columnCreators = {
        text: textColumn,
        number: numberColumn,
        date: dateColumn,
        status: statusColumn,
        action: (title: string, options: any) => actionColumn(title, options),
        image: imageColumn,
        link: linkColumn,
        select: selectColumn,
        progress: progressColumn,
        boolean: booleanColumn,
    };

    return configs.map((config) => {
        const creator = columnCreators[config.type];
        if (config.type === 'action') {
            return creator(config.title, config.options || {});
        }
        return creator(config.field, config.title, config.options || {});
    });
}

/**
 * 列配置快捷方法
 */
export const column = {
    text: textColumn,
    number: numberColumn,
    date: dateColumn,
    status: statusColumn,
    action: actionColumn,
    image: imageColumn,
    link: linkColumn,
    select: selectColumn,
    progress: progressColumn,
    percent: percentColumn,
    currency: currencyColumn,
    float: floatColumn,
    boolean: booleanColumn,
    relation: relationColumn,
    oneToMany: oneToManyColumn,
    manyToOne: manyToOneColumn,
    composite: composite,
    group: group,

    // 基于元数据的智能字段配置
    smartField: smartField,
    relationField: relationField,

    // 常用列快捷方法
    id: (title = 'ID') => textColumn('id', title, { width: 80, fixed: 'left' }),
    code: (title = '编码') =>
        textColumn('code', title, { minWidth: 100, fixed: 'left' }),
    name: (title = '名称') =>
        textColumn('name', title, { minWidth: 120, fixed: 'left' }),
    statusEnum: (title = '状态') => statusColumn('status', title),
    createAt: (title = '创建日期') => dateColumn('created_at', title),
    updateAt: (title = '更新日期') => dateColumn('updated_at', title),
    actions: (actions?: any[], title = '操作') =>
        actionColumn(title, { actions }),

    // 数值类型快捷方法
    price: (title = '价格') => currencyColumn('price', title),
    amount: (title = '金额') => currencyColumn('amount', title),
    rate: (title = '费率') => percentColumn('rate', title),
    ratio: (title = '比例') => percentColumn('ratio', title),

    // 布尔类型快捷方法
    valid: (title = '是否生效') =>
        booleanColumn('valid', title, {
            trueText: '生效',
            falseText: '失效',
            style: 'tag',
        }),
};

/**
 * 创建复合数据列（包含主数据、多个辅助数据和图标）
 * @param field 主字段
 * @param title 列标题
 * @param options 配置选项
 */
function composite(
    field: string,
    title: string,
    options: {
        // 主数据配置
        main?: CompositeMainConfig;
        // 辅助数据配置（支持多个）
        subs?: CompositeSubConfig[];
        // 分隔符配置
        separator?: {
            main?: string; // 主数据与辅助数据之间的分隔符
            subs?: string; // 辅助数据之间的分隔符
            vertical?: string; // 垂直布局时的分隔符
        };
        // 图标配置
        icon?: CompositeIconConfig;
        // 布局
        layout?: 'horizontal' | 'vertical' | 'mixed';
        subLayout?: {
            main?: 'horizontal' | 'vertical';
            subs?: 'horizontal' | 'vertical';
        };
        // 容器样式
        containerStyle?: any;
        // 交互配置
        interactive?: {
            hover?: {
                showAll?: boolean;
                highlight?: boolean;
            };
            click?: {
                expandable?: boolean;
                onExpand?: (record: any) => void;
            };
        };
        // 其他列配置
        width?: number | string;
        minWidth?: number | string;
        fixed?: 'left' | 'right';
        align?: 'left' | 'center' | 'right';
        // 操作配置
        actions?: any[];
        moreActions?: any;
    } = {}
): BaseColumn {
    const {
        main,
        subs,
        icon,
        layout,
        separator,
        actions,
        moreActions,
        interactive,
        ...rest
    } = options;
    return {
        field,
        title,
        width: 200,
        align: 'left',
        compositeConfig: {
            main: {
                field: main?.field || field,
                formatter: main?.formatter,
                style: main?.style,
                tooltip: main?.tooltip,
            },
            subs: subs || [],
            icon,
            layout: layout || 'horizontal',
            separator: separator || {
                main: ' · ',
                subs: ' | ',
                vertical: '\n',
            },
            interactive,
        },
        cellActionConfig:
            options.actions || options.moreActions
                ? {
                      actions: options.actions,
                      moreConfig: options.moreActions,
                  }
                : undefined,
        ...rest,
    };
}

/**
 * 创建计算字段辅助配置
 */
function computedField(
    fields: string[],
    formula: (values: any[], record: any) => any,
    options: Partial<CompositeSubConfig> = {}
): CompositeSubConfig {
    return {
        ...options,
        computed: {
            fields,
            formula,
            cache: options.computed?.cache ?? true,
        },
    };
}

/**
 * 创建模板字段辅助配置
 */
function templateField(
    template: string,
    field?: string,
    options: Partial<CompositeSubConfig> = {}
): CompositeSubConfig {
    return {
        ...options,
        field,
        template,
    };
}

/**
 * 创建条件显示字段配置
 */
function conditionalField(
    condition: (record: any) => boolean,
    config: CompositeSubConfig
): CompositeSubConfig {
    return {
        ...config,
        condition,
    };
}

/**
 * 百分比计算辅助函数
 */
function percentage(
    numeratorField: string,
    denominatorField: string,
    options: {
        template?: string;
        precision?: number;
        style?: any;
    } = {}
): CompositeSubConfig {
    const { template = '占比: ${value}%', precision = 2, style } = options;

    return computedField(
        [numeratorField, denominatorField],
        (values) => {
            const [numerator, denominator] = values.map((v) => Number(v) || 0);
            if (denominator === 0) return '0.00';
            return ((numerator / denominator) * 100).toFixed(precision);
        },
        {
            template,
            style: { color: '#52c41a', ...style },
        }
    );
}

/**
 * 日期差计算辅助函数
 */
function dateDiff(
    startField: string,
    endField: string,
    options: {
        template?: string;
        unit?: 'days' | 'hours' | 'minutes';
        style?: any;
    } = {}
): CompositeSubConfig {
    const { template = '${value}${unit}', unit = 'days', style } = options;

    const unitMap = {
        days: '天',
        hours: '小时',
        minutes: '分钟',
    };

    return computedField(
        [startField, endField],
        (values) => {
            const [start, end] = values.map((v) => new Date(v));
            if (isNaN(start.getTime()) || isNaN(end.getTime())) return '0';

            const diff = end.getTime() - start.getTime();
            let result: number;

            switch (unit) {
                case 'hours':
                    result = Math.floor(diff / (1000 * 60 * 60));
                    break;
                case 'minutes':
                    result = Math.floor(diff / (1000 * 60));
                    break;
                default: // days
                    result = Math.floor(diff / (1000 * 60 * 60 * 24));
            }

            return result.toString();
        },
        {
            template: template.replace('${unit}', unitMap[unit]),
            style: { color: '#fa8c16', ...style },
        }
    );
}

/**
 * 基于元数据的智能字段配置
 */
function smartField(
    fieldPath: string,
    metadata?: {
        type?: string;
        enum_info?: { enum_values?: Record<string, string> };
        relation_info?: { related_model?: string; direction?: string };
        comment?: string;
    },
    options: Partial<CompositeSubConfig> = {}
): CompositeSubConfig {
    const config: CompositeSubConfig = {
        field: fieldPath,
        smartParse: {
            metadata,
            options: {
                nullText: '-',
                ...options.smartParse?.options,
            },
        },
        ...options,
    };

    // 根据元数据类型设置默认配置
    if (metadata?.type) {
        switch (metadata.type) {
            case 'enum':
                config.enum = {
                    enumMap: metadata.enum_info?.enum_values || {},
                    ...options.enum,
                };
                break;

            case 'relation':
                config.relation = {
                    showCount:
                        metadata.relation_info?.direction ===
                        'RelationshipDirection.ONETOMANY',
                    ...options.relation,
                };
                break;

            case 'datetime':
            case 'date':
                config.smartParse!.options!.dateFormat =
                    metadata.type === 'datetime'
                        ? 'YYYY-MM-DD HH:mm'
                        : 'YYYY-MM-DD';
                break;

            case 'float':
            case 'decimal':
                config.smartParse!.options!.precision = 2;
                break;
        }
    }

    return config;
}

/**
 * 关系字段配置
 */
function relationField(
    fieldPath: string,
    displayField?: string,
    options: {
        template?: string;
        fallbackFields?: string[];
        showCount?: boolean;
        metadata?: any;
        style?: any;
    } = {}
): CompositeSubConfig {
    return {
        field: fieldPath,
        relation: {
            displayField,
            template: options.template,
            fallbackFields: options.fallbackFields,
            showCount: options.showCount,
        },
        smartParse: {
            metadata: { type: 'relation', ...options.metadata },
        },
        style: options.style,
    };
}

/**
 * 创建复合列 - 统一配置接口
 */
export function compositeColumn(
    field: string,
    title: string,
    options: ColumnHelperOptions & {
        main?: {
            field?: string;
            formatter?: (value: any, record: any) => string;
            style?: any;
            tooltip?: boolean | string | ((value: any, record: any) => string);
        };
        subs?: Array<{
            field?: string;
            template?: string;
            formatter?: (value: any, record: any) => string;
            style?: any;
            condition?: (record: any) => boolean;
            onClick?: (value: any, record: any) => void;
            tooltip?: boolean | string | ((value: any, record: any) => string);
        }>;
        icon?: {
            field?: string;
            type?: 'icon' | 'image' | 'avatar';
            iconName?: string;
            avatarField?: string;
            imageField?: string;
            style?: any;
            size?: number | string;
            position?: 'left' | 'right' | 'top' | 'bottom';
        };
        layout?: 'horizontal' | 'vertical' | 'mixed';
        separator?: {
            main?: string;
            subs?: string;
            vertical?: string;
        };
        actions?: Array<{
            icon?: string;
            tooltip?: string;
            condition?: (row: any) => boolean;
            onClick?: (row: any) => void;
        }>;
        moreActions?: {
            type?: 'group';
            items?: Array<{
                text: string;
                icon?: string;
                danger?: boolean;
                condition?: (row: any) => boolean;
                onClick?: (row: any) => void;
            }>;
        };
        interactive?: {
            hover?: {
                showAll?: boolean;
                highlight?: boolean;
            };
            click?: {
                expandable?: boolean;
                onExpand?: (record: any) => void;
            };
        };
    } = {}
): BaseColumn {
    const {
        main,
        subs,
        icon,
        layout,
        separator,
        actions,
        moreActions,
        interactive,
        ...rest
    } = options;

    return {
        field,
        title,
        width: 200,
        align: 'left',
        // 使用现有的compositeConfig机制
        compositeConfig: {
            main: {
                field: main?.field || field,
                formatter: main?.formatter,
                style: main?.style,
                tooltip: main?.tooltip,
            },
            subs: subs || [],
            icon,
            layout: layout || 'vertical',
            separator: separator || {
                main: '',
                subs: ' | ',
                vertical: '\n',
            },
            interactive,
        },
        // 如果有actions或moreActions，设置cellActionConfig
        cellActionConfig:
            actions || moreActions
                ? {
                      actions: actions,
                      moreConfig: moreActions,
                  }
                : undefined,
        ...rest,
    };
}

export function group(
    field: string,
    title: string,
    options: {} = {}
): BaseColumn {
    debugger;
    const { ...rest } = options;
    return {
        field,
        title,
        width: 200,
        align: 'left',

        ...rest,
    };
}

// 导出复合列工具对象，方便统一使用
export const compositeHelpers = {
    // 基础工具
    percentage,
    dateDiff,

    // 数据解析工具
    getFieldValue,
    formatFieldValue,
    smartFieldParser,
    relationDisplay,
    enumDisplay,

    // 智能字段配置
    smartField,
    relationField,
    computedField,
    templateField,
    conditionalField,
};
