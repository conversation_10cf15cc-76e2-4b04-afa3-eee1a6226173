import { math } from '@/utils/math';
import { get } from 'lodash-es';
import { bignumber } from 'mathjs';
import { BaseColumn } from './types';

/**
 * @param 需要统计的字段columnsSumm,   bool：true/存在返回（TablePluesGrid） footer-method需要的二维[]
 * @param {Function} formatSumFn 格式化合计
 * @returns 前端计算合计
 */
export const getSummaries = (columnsSumm, bool?, formatSumFn?) => {
    return (param: any) => {
        const { columns, data } = param;
        const sums: any[] = [];
        columns.forEach((column, index) => {
            if (index === 0) {
                sums[index] = '合计';
                return;
            }

            if (columnsSumm.includes(column.property)) {
                const values = data.map((item) =>
                    Number(get(item, column.property))
                );
                if (!values.every((value) => Number.isNaN(value))) {
                    sums[index] = `${values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!Number.isNaN(value)) {
                            return math.add(bignumber(prev), bignumber(curr));
                        } else {
                            return prev;
                        }
                    }, 0)}`;
                    // formatSumFn 格式化合计 例如数字+%
                    if (formatSumFn) {
                        sums[index] = formatSumFn(
                            sums[index],
                            index,
                            column.property
                        );
                    }
                } else {
                    sums[index] = '';
                }
            }
        });
        return bool ? [sums] : sums;
    };
};

interface SpanMethodProps {
    row: Recordable;
    column: BaseColumn;
    rowIndex: number;
    columnIndex: number;
}
export const spanMethod = ({
    row,
    column,
    rowIndex,
    columnIndex,
}: SpanMethodProps) => {
    if (rowIndex % 2 === 0) {
        if (columnIndex === 0) {
            return [1, 2];
        } else if (columnIndex === 1) {
            return [0, 0];
        }
    }
};

export function convertToOnEventName(str) {
    // 将字符串分割成数组
    const parts = str.split('-');
    // 将每个部分的首字母大写（除了第一部分），然后连接起来
    const camelCase = parts
        .map((part, index) =>
            index === 0 ? part : part.charAt(0).toUpperCase() + part.slice(1)
        )
        .join('');
    // 添加 "on" 前缀并返回
    return 'on' + camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
}

export function getItemByKey(dataSource, row, rowKey) {
    for (const item of dataSource) {
        if (item?.[rowKey] === row?.[rowKey]) {
            return item;
        }
        if (item.children && item.children.length) {
            const foundItem = getItemByKey(item.children, row, rowKey);
            if (foundItem) {
                return foundItem;
            }
        }
    }
    return null;
}

export function formatEmitEvent(emits) {
    Object.keys(emits)?.forEach((item) => {});
}
