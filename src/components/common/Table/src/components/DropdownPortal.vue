<template>
    <Teleport to="body">
        <div 
            v-if="visible" 
            ref="dropdownRef"
            class="dropdown-portal"
            :style="{ 
                position: 'fixed', 
                top: top + 'px', 
                left: left + 'px',
                zIndex: 9999 
            }"
        >
            <slot />
        </div>
    </Teleport>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue';

interface Props {
    visible: boolean;
    triggerRef: HTMLElement | null;
    offset?: { x: number; y: number };
    placement?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'auto';
}

const props = withDefaults(defineProps<Props>(), {
    offset: () => ({ x: 0, y: 4 }),
    placement: 'auto'
});

const top = ref(0);
const left = ref(0);
const dropdownRef = ref<HTMLElement>();

const updatePosition = async () => {
    if (!props.triggerRef || !props.visible) {
        return;
    }
    
    await nextTick();
    
    const triggerRect = props.triggerRef.getBoundingClientRect();
    const viewport = {
        width: window.innerWidth,
        height: window.innerHeight
    };
    
    // 获取下拉菜单的尺寸
    let dropdownWidth = 120; // 默认宽度
    let dropdownHeight = 100; // 默认高度
    
    if (dropdownRef.value) {
        const dropdownRect = dropdownRef.value.getBoundingClientRect();
        dropdownWidth = dropdownRect.width || 120;
        dropdownHeight = dropdownRect.height || 100;
    }
    
    let finalPlacement = props.placement;
    
    // 如果是auto模式，智能选择最佳位置
    if (props.placement === 'auto') {
        const spaces = {
            bottom: viewport.height - triggerRect.bottom,
            top: triggerRect.top,
            right: viewport.width - triggerRect.right,
            left: triggerRect.left
        };
        
        // 优先选择底部，如果空间不够则选择顶部
        const preferBottom = spaces.bottom >= dropdownHeight + Math.abs(props.offset.y) + 10;
        const preferTop = spaces.top >= dropdownHeight + Math.abs(props.offset.y) + 10;
        
        // 优先选择右侧，如果空间不够则选择左侧
        const preferRight = spaces.right >= dropdownWidth + Math.abs(props.offset.x) + 10;
        
        if (preferBottom) {
            finalPlacement = preferRight ? 'bottom-right' : 'bottom-left';
        } else if (preferTop) {
            finalPlacement = preferRight ? 'top-right' : 'top-left';
        } else {
            // 如果上下都没有足够空间，选择空间较大的一侧
            finalPlacement = spaces.bottom > spaces.top 
                ? (preferRight ? 'bottom-right' : 'bottom-left')
                : (preferRight ? 'top-right' : 'top-left');
        }
    }
    
    // 根据最终placement计算位置
    switch (finalPlacement) {
        case 'bottom-right':
            top.value = triggerRect.bottom + props.offset.y;
            left.value = triggerRect.right + props.offset.x;
            break;
        case 'bottom-left':
            top.value = triggerRect.bottom + props.offset.y;
            left.value = triggerRect.left + props.offset.x;
            break;
        case 'top-right':
            top.value = triggerRect.top - dropdownHeight - props.offset.y;
            left.value = triggerRect.right + props.offset.x;
            break;
        case 'top-left':
            top.value = triggerRect.top - dropdownHeight - props.offset.y;
            left.value = triggerRect.left + props.offset.x;
            break;
    }
    
    // 确保不会超出视窗边界
    const padding = 10;
    
    // 水平边界检查
    if (left.value + dropdownWidth > viewport.width - padding) {
        left.value = viewport.width - dropdownWidth - padding;
    }
    if (left.value < padding) {
        left.value = padding;
    }
    
    // 垂直边界检查
    if (top.value + dropdownHeight > viewport.height - padding) {
        top.value = viewport.height - dropdownHeight - padding;
    }
    if (top.value < padding) {
        top.value = padding;
    }
};

watch(() => props.visible, (visible) => {
    if (visible) {
        nextTick(() => {
            updatePosition();
        });
    }
});

// 监听窗口滚动和resize，更新位置
let resizeObserver: ResizeObserver | null = null;

watch(() => props.triggerRef, (triggerRef) => {
    if (resizeObserver) {
        resizeObserver.disconnect();
    }
    
    if (triggerRef) {
        resizeObserver = new ResizeObserver(() => {
            updatePosition();
        });
        resizeObserver.observe(triggerRef);
        
        // 监听滚动事件
        document.addEventListener('scroll', updatePosition, true);
        window.addEventListener('resize', updatePosition);
    }
});

// 组件卸载时清理
import { onBeforeUnmount } from 'vue';
onBeforeUnmount(() => {
    if (resizeObserver) {
        resizeObserver.disconnect();
    }
    document.removeEventListener('scroll', updatePosition, true);
    window.removeEventListener('resize', updatePosition);
});
</script> 