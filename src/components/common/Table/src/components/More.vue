<template>
    <div class="table_more-action" :class="{ 'has-actions': hasActions }">
        <div
            class="text-wrapper"
            :style="{
                width: hasActions
                    ? `calc(100% - ${28 * (moreConfig?.length || 0) + 36 * (actionsConfig?.length || 0)}px)`
                    : '100%',
            }"
        >
            <slot></slot>
        </div>

        <div
            v-if="hasActions"
            class="button-group flex items-center"
            :class="{ 'always-visible': alwaysVisible }"
        >
            <!-- 快捷操作按钮 -->
            <template v-if="actionsConfig && actionsConfig.length > 0">
                <div
                    v-for="(action, index) in actionsConfig"
                    :key="index"
                    class="action-btn"
                    :title="action.tooltip"
                    @click="() => action.onClick && action.onClick(record)"
                >
                    <span>{{ action.text }}</span>
                    <Icon :icon="action.icon" size="16" />
                </div>
            </template>

            <!-- 更多操作下拉菜单 -->
            <div
                v-if="moreConfig && moreConfig.length > 0"
                class="more-dropdown"
                ref="dropdownRef"
            >
                <div
                    class="more-trigger"
                    ref="triggerRef"
                    @click="toggleDropdown"
                    @mouseenter="onMouseEnter"
                    @mouseleave="onMouseLeave"
                >
                    <Icon icon="material-symbols:more-horiz" size="20" />
                </div>
            </div>
        </div>

        <!-- 使用Portal渲染下拉菜单 -->
        <DropdownPortal
            :visible="showDropdown"
            :triggerRef="triggerRef"
            placement="auto"
            :offset="{ x: -120, y: 4 }"
        >
            <div
                class="dropdown-menu"
                @mouseenter="onDropdownEnter"
                @mouseleave="onDropdownLeave"
            >
                <template v-for="(item, index) in filteredItems" :key="index">
                    <div
                        v-if="item.type === 'divider'"
                        class="dropdown-divider"
                    ></div>
                    <div
                        v-else
                        class="dropdown-item"
                        :class="{ danger: item.danger }"
                        @click="() => handleMenuClick(item)"
                    >
                        <Icon
                            v-if="item.icon"
                            :icon="item.icon"
                            size="16"
                            class="mr-2"
                        />
                        <span v-if="item.text">{{ item.text }}</span>
                    </div>
                </template>
            </div>
        </DropdownPortal>
    </div>
</template>

<script lang="ts">
import {
    computed,
    defineComponent,
    inject,
    PropType,
    ref,
    nextTick,
    watchEffect,
    onBeforeUnmount,
} from 'vue';
import { ButtonGroup, ButtonGroupProps } from '@/components/common/ButtonGroup';
import { Icon } from '@iconify/vue';
import DropdownPortal from './DropdownPortal.vue';
import { hasPermission } from '@/directives/modules/permission';

export default defineComponent({
    name: 'TableMoreAction',
    components: {
        ButtonGroup,
        Icon,
        DropdownPortal,
    },
    props: {
        text: {
            type: String,
        },
        config: {
            type: Object,
        },
        record: {
            type: Object,
        },
        // 是否始终显示操作按钮
        alwaysVisible: {
            type: Boolean,
            default: false,
        },
    },
    emits: [],
    setup(props, { emit, attrs, slots }) {
        const tableRef = inject('__table');
        const showDropdown = ref(false);
        const hoverTimer = ref<number | ReturnType<typeof setTimeout> | null>(
            null
        );
        const dropdownRef = ref<HTMLElement>();
        const triggerRef = ref<HTMLElement>();

        const moreConfig = computed(() => {
            if (props.config?.moreConfig) {
                return [props.config.moreConfig];
            } else {
                return [];
            }
        });

        // 添加过滤后的items计算属性
        const filteredItems = computed(() => {
            const configGroup = moreConfig.value[0];
            if (!configGroup?.items) return [];

            return configGroup.items.filter((item) => {
                // 权限检查提前终止
                if (!hasPermission(item.permission)) return false;

                // 条件函数检查
                return typeof item.condition !== 'function'
                    ? true
                    : item.condition(props.record);
            });
        });

        const actionsConfig = computed(() => {
            return (
                props.config?.actions?.map((item) => {
                    return {
                        ...item,
                        tooltip: item.tooltip || '',
                    };
                }) || []
            )?.filter((item) => {
                // 权限检查提前终止
                if (!hasPermission(item.permission)) return false;

                // 条件函数检查
                return typeof item.condition !== 'function'
                    ? true
                    : item.condition(props.record);
            });
        });

        const hasActions = computed(() => {
            return (
                actionsConfig.value.length > 0 || moreConfig.value.length > 0
            );
        });

        const toggleDropdown = () => {
            showDropdown.value = !showDropdown.value;
        };

        const hideDropdown = () => {
            showDropdown.value = false;
        };

        const onMouseEnter = () => {
            if (hoverTimer.value) {
                clearTimeout(hoverTimer.value);
            }
            if (moreConfig.value[0]?.trigger === 'hover') {
                showDropdown.value = true;
            }
        };

        const onMouseLeave = () => {
            if (moreConfig.value[0]?.trigger === 'hover') {
                hoverTimer.value = setTimeout(() => {
                    showDropdown.value = false;
                }, 200);
            }
        };

        const onDropdownEnter = () => {
            if (hoverTimer.value) {
                clearTimeout(hoverTimer.value);
            }
        };

        const onDropdownLeave = () => {
            if (moreConfig.value[0]?.trigger === 'hover') {
                hoverTimer.value = setTimeout(() => {
                    showDropdown.value = false;
                }, 200);
            }
        };

        const handleMenuClick = (item: any) => {
            if (item.onClick) {
                item.onClick(props.record);
            }
            showDropdown.value = false;
        };

        // 点击外部关闭下拉菜单
        const handleDocumentClick = (event: MouseEvent) => {
            if (
                dropdownRef.value &&
                !dropdownRef.value.contains(event.target as Node)
            ) {
                showDropdown.value = false;
            }
        };

        // 监听全局点击事件
        watchEffect(() => {
            if (showDropdown.value) {
                document.addEventListener('click', handleDocumentClick);
            } else {
                document.removeEventListener('click', handleDocumentClick);
            }
        });

        // 组件卸载时清理事件监听
        onBeforeUnmount(() => {
            document.removeEventListener('click', handleDocumentClick);
            if (hoverTimer.value) {
                clearTimeout(hoverTimer.value);
            }
        });

        return {
            moreConfig,
            filteredItems,
            actionsConfig,
            hasActions,
            showDropdown,
            dropdownRef,
            triggerRef,
            toggleDropdown,
            hideDropdown,
            onMouseEnter,
            onMouseLeave,
            onDropdownEnter,
            onDropdownLeave,
            handleMenuClick,
        };
    },
});
</script>

<style lang="scss">
// 全局强制隐藏操作按钮的规则 - 最高优先级
.vxe-table .table_more-action .button-group {
    visibility: hidden !important;
    opacity: 0 !important;
    transition: all 0.2s ease-in-out;
}

// 确保操作按钮区域默认完全隐藏
.table_more-action .button-group {
    visibility: hidden !important;
    opacity: 0 !important;
    transition: all 0.2s ease-in-out;

    // 只有always-visible类时才显示
    &.always-visible {
        visibility: visible !important;
        opacity: 1 !important;
    }
}

// 表格行hover效果 - 更精确的选择器和更高优先级
.vxe-table .vxe-body--row:hover {
    .table_more-action .button-group {
        visibility: visible !important;
        opacity: 1 !important;
    }
}

// 确保表格行不会限制子元素的z-index
.vxe-table .vxe-body--row {
    isolation: auto;

    .vxe-body--column {
        isolation: auto;
        overflow: visible;
    }
}

.table_more-action {
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    min-height: 32px;

    &.has-actions {
        padding-right: 8px;
    }

    .text-wrapper {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
        min-width: 0;
    }

    .button-group {
        position: absolute;
        right: 4px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 50;
        gap: 4px;
        background-color: var(--vxe-ui-table-body-background-color, #fff);
        border-radius: 4px;
        padding: 2px 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .action-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            color: #666;

            &:hover {
                background: rgba(102, 152, 255, 0.1);
                color: #1890ff;
            }
        }

        .more-dropdown {
            position: relative;
            display: inline-flex;
            z-index: 1000;

            .more-trigger {
                background: rgba(102, 152, 255, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.2s ease-in-out;
                color: #666;

                &:hover {
                    background: rgba(102, 152, 255, 0.2);
                    color: #1890ff;
                }
            }
        }
    }
}

// 兼容现有的mouseenter样式 - 更高优先级
.vxe-body--column.mouseenter {
    .table_more-action .button-group {
        visibility: visible !important;
        opacity: 1 !important;
    }
}

// 全局点击外部关闭下拉菜单
body {
    .dropdown-menu {
        // 确保下拉菜单在最上层
        z-index: 9999;
    }
}

// 下拉菜单样式（全局）
.dropdown-menu {
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
    min-width: 120px;
    padding: 4px 0;

    .dropdown-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        font-size: 14px;
        color: #333;
        transition: all 0.2s ease-in-out;

        &:hover {
            background: #f5f5f5;
        }

        &.danger {
            color: #ff4d4f;

            &:hover {
                background: #fff2f0;
                color: #ff4d4f;
            }
        }
    }

    .dropdown-divider {
        height: 1px;
        background: #e8e8e8;
        margin: 4px 0;
    }
}
</style>
