<template>
  <div v-if="comName">
    <component
      :is="comName"
      v-bind="getColumnBind(record, column?.schema?.componentProps)"
      :field="column?.schema?.component.field || column.field"
      :record="record"
      :key="record.row.id"
    >
      <template v-for="item in getComponentSlots(column.schema)" #[item]="data">
        <slot :name="column.schema?.slots?.[item]" v-bind="data || {}" />
      </template>
    </component>
  </div>
</template>

<script lang="ts">
import { cloneDeep, get, isEmpty, isFunction, merge, set } from 'lodash-es'
import {
  computed,
  defineComponent,
  inject,
  onMounted,
  ref,
  unref,
  watch,
} from 'vue'
import Select from '../../../FormEngine/FormComponents/Select.vue'
import { Input } from '@/components/ui/input'
import { ValueType } from '@/components/common/ValueType'
import Radio from '@/components/common/FormEngine/FormComponents/radio.vue'
import { Checkbox } from '@/components/ui/checkbox'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import MultipleSelect from '@/components/common/FormEngine/FormComponents/MultipleSelect.vue'
import NumberInput from '@/components/common/FormEngine/FormComponents/NumberInput.vue'
import DatePicker from '@/components/common/FormEngine/FormComponents/DatePicker.vue'
import DateRangerPicker from '@/components/common/FormEngine/FormComponents/DateRangerPicker.vue'
import Placeholder from '@/components/common/FormEngine/FormComponents/Placeholder.vue'
import { FormFieldConfig } from '@/components'
import { ElTimePicker } from 'element-plus'
import Tags from '@/components/common/FormEngine/FormComponents/Tags.vue'
import LinkText from './LinkText.vue'
import UnitRate from '@/views/ops/materials-list/components/unit-rate.vue'
import TreeSelect from '@/components/common/FormEngine/FormComponents/TreeSelect.vue'
import { getDict } from '@/utils/dict'

export default defineComponent({
  name: 'TableCell',
  components: {
    Select,
    Input,
    Radio,
    Checkbox,
    Switch,
    Textarea,
    MultipleSelect,
    NumberInput,
    DatePicker,
    DateRangerPicker,
    Placeholder,
    ElTimePicker,
    Tags,
    LinkText,
    UnitRate,
    TreeSelect,
    ValueType,
  },
  emits: ['register', 'click'],
  props: {
    record: {
      type: Object,
    },
    column: {
      type: Object,
    },
  },
  setup(props, { slots }) {
    const currentSchema = ref(cloneDeep(props.column?.schema))
    const comName = computed(() => unref(currentSchema)?.component)

    const getColumnBind = computed(() => {
      return (record, componentProps) => {
        const field =
          unref(currentSchema)?.component.field || props.column?.field

        const updataModelObj = getUpdataObj(
          unref(currentSchema)?.component,
          record,
          field
        )

        return {
          ...unref(currentSchema)?.componentProps,
          class: 'inner-input',
          modelValue: get(record?.row, field),
          ...updataModelObj,
        }
      }
    })

    function getUpdataObj(type: string, record, field) {
      const check = ['switch', 'checkbox']

      let obj = {}

      if (check.includes(type)) {
        obj = {
          checked: get(record?.row, field),
          ['onUpdate:checked'](val) {
            set(record.row, field, val)

            if (isFunction(unref(currentSchema)?.componentProps?.onUpdate)) {
              unref(currentSchema)?.componentProps?.onUpdate(val, {
                record,
                column: props.column,
              })
            }
          },
        }
      } else {
        obj = {
          modelValue: get(record?.row, field),
          ['onUpdate:modelValue'](val) {
            set(record.row, field, val)

            if (isFunction(unref(currentSchema)?.componentProps?.onUpdate)) {
              unref(currentSchema)?.componentProps?.onUpdate(val, {
                record,
                column: props.column,
              })
            }
          },
        }
      }

      return obj
    }

    function getComponentSlots(schema: FormFieldConfig) {
      const { slots: comSlots } = schema
      let allSlotsKey = Object.keys(slots)
      if (!comSlots || isEmpty(slots)) return []

      const compSlot: Recordable = {}

      Object.entries(comSlots).forEach(([originName, slotName]) => {
        if (slots?.[slotName]) {
          compSlot[originName] = slots[slotName]
          allSlotsKey = allSlotsKey.filter((key) => key !== slotName)
        }
      })

      return Object.keys(compSlot)
    }

    const schemaMaps: Map<any, any> = inject('__schemaMaps')

    function _updateDict() {
      const {
        dict,
        dictPath = 'componentProps.options',
        dictAlias = {
          label: 'name',
          value: 'id',
          isDefault: 'is_default',
        },
      } = unref(currentSchema)

      if (!dict) return

      // options已经有值将不再通过dict赋值
      if (get(unref(currentSchema), dictPath)) return

      getDict(dict).then((data) => {
        set(
          currentSchema.value,
          dictPath,
          data?.map((item) => {
            return {
              ...item,
              label: get(item, dictAlias.label),
              value: get(item, dictAlias.value),
              isDefault: get(item, dictAlias.isDefault),
            }
          })
        )
      })
    }

    function _updateApi() {
      const { record } = props
      const { api } = unref(currentSchema)

      if (!api || !isFunction(api)) return
      api(unref(currentSchema), record)
    }

    onMounted(() => {
      const { column, record } = props
      const { updateCellSchema } = column || {}

      // 提前终止非函数场景
      if (!isFunction(updateCellSchema)) {
        _updateDict()
        _updateApi()
        return
      } else {
        // 优化 Map 操作 - 减少重复 get 调用
        const rowKey = record.row
        const fieldKey = column.field

        let rowMaps = schemaMaps.get(rowKey)
        if (!rowMaps) {
          rowMaps = new Map()
          schemaMaps.set(rowKey, rowMaps)
        }

        // 直接设置值（避免额外 Map 层级）
        rowMaps.set(fieldKey, unref(currentSchema))

        const newSchema = updateCellSchema(props.record, unref(currentSchema))
        merge(unref(currentSchema), newSchema)

        _updateDict()
        _updateApi()
      }
    })

    return {
      comName,
      getColumnBind,
      getComponentSlots,
      currentSchema,
    }
  },
})
</script>

<style lang="scss">
.el-date-editor.el-input,
.el-date-editor.el-input__wrapper {
  width: 100% !important;
}
</style>
