<template>
    <Dialog v-model:open="isOpen">
        <DialogContent class="sm:max-w-[800px]">
            <DialogHeader>
                <DialogTitle>表头显示属性</DialogTitle>
            </DialogHeader>
            <!-- <DialogDescription class="flex justify-end pr-[24px]"
                >是否展示</DialogDescription
            > -->

            <div class="flex flex-col">
                <div class="flex text-gray-600">
                    <p class="w-[50%] pr-[10px] pb-3">
                        可选择属性 · {{ originalColumns?.length }}
                    </p>
                    <p class="w-[50%] pf-[10px] pb-3">
                        已选择属性 ·
                        {{ showColumns?.length + freezeColumns?.length }}
                    </p>
                </div>
                <div class="flex">
                    <div
                        class="left relative max-h-[60vh] w-[50%] overflow-y-auto pr-[10px]"
                    >
                        <div
                            class="item flex items-center gap-3 p-3 bg-gray-50 hover:bg-gray-100 rounded-md cursor-pointer mt-[8px]"
                            @click="() => handleAdd(item)"
                            v-for="item in originalColumns"
                        >
                            <div
                                :class="[
                                    true
                                        ? 'text-gray-300 cursor-not-allowed'
                                        : 'drag-handle text-gray-400',
                                    'w-4 h-4 shrink-0',
                                ]"
                            />

                            <!-- 字段名称 -->
                            <span class="flex-1 text-sm"
                                >{{ item.title || item.comment || item.name }}
                            </span>

                            <!-- 多选开关 -->
                            <Icon
                                v-show="showColumnsKeys.includes(item.field)"
                                color="#3b82f6"
                                icon="ant-design:check-outlined"
                            />
                        </div>
                    </div>
                    <div
                        class="right relative w-[50%] max-h-[60vh] overflow-y-auto pf-[10px] mr-[10px]"
                    >
                        <div class="mb-[20px]">
                            <h5 class="text-gray-400 pl-[20px]">
                                冻结 (上限3个)
                            </h5>
                            <draggable
                                v-model="freezeColumns"
                                :group="freezeGroup"
                                item-key="id"
                                handle=".drag-handle"
                                class="space-y-2 freeze-area"
                            >
                                <template #item="{ element }">
                                    <div
                                        class="drag-handle flex items-center gap-3 p-3 bg-gray-50 hover:bg-gray-100 rounded-md cursor-grab"
                                        :class="[
                                            freezeColumns?.length >= 3
                                                ? 'cursor-no-drop'
                                                : '',
                                        ]"
                                    >
                                        <!-- 拖拽手柄 -->
                                        <GripVertical
                                            :class="[
                                                true
                                                    ? ' text-gray-300 '
                                                    : ' text-gray-400',
                                                'w-4 h-4 shrink-0',
                                            ]"
                                        />

                                        <!-- 字段名称 -->
                                        <span class="flex-1 text-sm"
                                            >{{ element.title }}
                                        </span>

                                        <Icon
                                            icon="ant-design:close-outlined"
                                            class="cursor-pointer text-gray-400"
                                            @click="handleRemoveFreeze(element)"
                                        />
                                    </div>
                                </template>
                            </draggable>
                        </div>

                        <div>
                            <h5 class="text-gray-400 pl-[20px]">未冻结</h5>
                            <draggable
                                v-model="showColumns"
                                group="columns"
                                item-key="id"
                                handle=".drag-handle"
                                class="space-y-2"
                                :move="validateFreezeMove"
                            >
                                <template #item="{ element }">
                                    <div
                                        class="drag-handle flex items-center gap-3 p-3 bg-gray-50 hover:bg-gray-100 rounded-md cursor-grab mr-[10px] ml-[10px]"
                                    >
                                        <!-- 拖拽手柄 -->
                                        <GripVertical
                                            :class="[
                                                true
                                                    ? ' text-gray-300 '
                                                    : ' text-gray-400',
                                                'w-4 h-4 shrink-0',
                                            ]"
                                        />

                                        <!-- 字段名称 -->
                                        <span class="flex-1 text-sm"
                                            >{{ element.title }}
                                        </span>

                                        <Icon
                                            icon="ant-design:close-outlined"
                                            class="cursor-pointer text-gray-400"
                                            @click="handleRemove(element)"
                                        />
                                    </div>
                                </template>
                            </draggable>
                        </div>
                    </div>
                </div>
            </div>

            <DialogFooter class="gap-2">
                <Button variant="outline" @click="handleCancel">取消</Button>
                <Button variant="outline" @click="resetDefault"
                    >恢复默认</Button
                >
                <Button @click="handleSave">保存设置</Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>

<script setup>
import { ref, watch, inject, computed } from 'vue';
import { set, cloneDeep, isEqual } from 'lodash-es';
import draggable from 'vuedraggable';
import { GripVertical } from 'lucide-vue-next';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Icon } from '@iconify/vue';

const props = defineProps({
    // modelValue: {
    //     type: Array,
    //     required: true,
    // },
    originColumns: {
        type: Array,
        required: true,
    },
    viewColumns: {
        type: Array,
        required: true,
    },
});

const emit = defineEmits(['update:modelValue', 'save', 'reset', 'register']);

const tableRef = inject('__table');

// 响应式数据
const isOpen = ref(false);
const showColumns = ref();
const freezeColumns = ref();
const originalColumns = ref();

const showColumnsKeys = computed(() => {
    return (showColumns.value?.map((column) => column.field) || []).concat(
        freezeColumns.value?.map((column) => column.field) || []
    );
});

const freezeGroup = computed(() => {
    return {
        name: 'columns',
        pull: true,
        put: !(freezeColumns.value?.length >= 3),
    };
});

const handleCancel = () => {
    showColumns.value = cloneDeep(originalColumns.value);
    isOpen.value = false;
};

function open() {
    const excludes = ['checkbox', 'index'];
    isOpen.value = true;
    const fullColumns = tableRef.value.getFullColumns();

    const columns = fullColumns
        .filter((column) => !excludes.includes(column.type) && column.field)
        ?.map((column) => {
            const originalColumn = originalColumns.value?.find(
                (item) => item.field === column.field
            );
            set(originalColumn, 'fixed', column.fixed);
            return originalColumn || column;
        });

    showColumns.value = columns?.filter((column) => !column.fixed);
    freezeColumns.value = columns?.filter((column) => column.fixed);
    originalColumns.value = props.originColumns;
}

function handleRemove(item) {
    const index = showColumns.value.findIndex(
        (column) => column.field === item.field
    );
    if (index !== -1) {
        showColumns.value.splice(index, 1);
    }
}

function handleRemoveFreeze(item) {
    const index = freezeColumns.value.findIndex(
        (column) => column.field === item.field
    );
    if (index !== -1) {
        freezeColumns.value.splice(index, 1);

        // handleAdd(item);
    }
}

function handleAdd(item) {
    set(item, 'fixed', undefined);
    showColumns.value.push(item);
}

const handleSave = () => {
    const freeze =
        freezeColumns.value?.map((column) => {
            const c =
                originalColumns.value?.find(
                    (item) => item.field === column.field
                ) || column;

            return {
                ...c,
                fixed: 'left',
            };
        }) || [];
    const columns =
        showColumns.value?.map((column) => {
            return (
                originalColumns.value?.find(
                    (item) => item.field === column.field
                ) || column
            );
        }) || [];
    emit('save', [...freeze, ...columns]);

    isOpen.value = false;
};

const resetDefault = () => {
    emit('reset');
    isOpen.value = false;
};

const validateFreezeMove = (e) => {
    // 判断目标容器是否是冻结区域
    const isFreezeArea = e.to.classList.contains('freeze-area');
    if (isFreezeArea) {
        // 检查当前冻结字段数是否已达到上限
        if (freezeColumns.value.length >= 3) {
            return false; // 阻止拖入
        }
    }
    return true; // 允许其他操作
};

emit('register', {
    open,
});

defineExpose({
    open,
});
</script>
