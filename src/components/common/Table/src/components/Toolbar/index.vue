<template>
  <div
    class="flex justify-between items-center w-full"
    ref="proTableHeader"
    v-if="getProps.buttonList || getProps.showSearch || getProps.showPagination"
  >
    <div class="flex-1">
      <ButtonGroup :list="getProps.buttonList" />
    </div>
    <Search
      ref="SearchRef"
      v-if="getProps.showSearch"
      :fieldsConfig="(toolbar?.getViewColumns || [])?.map((item) => item.field)"
      :metaData="toolbar?.metaData"
      @search="(...args) => toolbar?.reload()"
    />
    <div class="flex-1 flex justify-end">
      <Pagination
        ref="PaginationRef"
        v-if="getProps.showPagination"
        :pagination="toolbar?.pagination"
        @queryPage="() => toolbar?.reload()"
      />

      <div class="toolbar flex items-center ml-2" v-if="getProps.toolbarConfig">
        <div
          v-if="getProps.toolbarConfig.isFull"
          class="item p-[8px] cursor-pointer"
          @click="toggleFull"
        >
          <Icon
            v-if="toolbar.isFullscreen"
            icon="ant-design:fullscreen-exit-outlined"
          ></Icon>
          <Icon v-else icon="ant-design:fullscreen-outlined"></Icon>
        </div>
        <div
          v-if="getProps.toolbarConfig.isExport"
          class="item p-[8px] cursor-pointer"
          @click="toolbar?.exportData"
        >
          <Icon icon="ant-design:download-outlined"></Icon>
        </div>
        <div
          v-if="getProps.toolbarConfig.isReload"
          class="item p-[8px] cursor-pointer"
          @click="toolbar?.reload"
        >
          <Icon icon="ant-design:reload-outlined"></Icon>
        </div>
        <div
          v-if="getProps.toolbarConfig.isZoom"
          class="item p-[8px] cursor-pointer"
          @click="openColumnsZoom"
        >
          <Icon icon="ant-design:setting-outlined"></Icon>
        </div>
      </div>
    </div>
  </div>

  <ColumnsZoom
    :origin-columns="toolbar?.originColumns"
    :view-columns="toolbar?.getViewColumns"
    @save="toolbar?.setColumns"
    @reset="toolbar?.resetColumns"
    @register="registerColumnsZoom"
  >
  </ColumnsZoom>
</template>

<script lang="ts" setup>
import { ComputedRef, inject, onMounted, ref } from 'vue'
import { BaseTableProps } from '../../types'
import { ButtonGroup } from '@/components/common/ButtonGroup'
import Pagination from '@/components/common/Pagination.vue'
import Search from '@/components/common/Search/Search.vue'
import { Icon } from '@iconify/vue'
import ColumnsZoom from '../ColumnsZoom.vue'

const getProps = inject('__props') as ComputedRef<BaseTableProps>
const toolbar = inject('__toolbar') as any
const columnsZoomRef = ref()
const SearchRef = ref()
const PaginationRef = ref()

defineExpose({
  SearchRef,
  PaginationRef,
})

function toggleFull() {
  toolbar.value.isFullscreen.value = !toolbar.value.isFullscreen.value
}

function registerColumnsZoom(api) {
  columnsZoomRef.value = api
}

function openColumnsZoom() {
  columnsZoomRef.value?.open()
}
</script>
