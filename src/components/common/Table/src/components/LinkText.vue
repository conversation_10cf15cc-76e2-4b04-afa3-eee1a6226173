<!-- 可点击单元格文字 -->
<template>
    <span class="text-blue-500 cursor-pointer" @click="handleClick">{{ modelValue }}</span>
</template>
<script setup lang='ts'>
import { record } from 'zod';


defineOptions({
    name: 'LinkText',
});

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    field: {
        type: String,
        default: '',
    },
    record: {
        type: Object,
        default: () => ({}),
    },
    column: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(['click']);
const handleClick = () => {
    emit('click', props.modelValue, { record: props.record, row: props.record.row });
};
</script>