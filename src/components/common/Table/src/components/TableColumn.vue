<template>
    <!-- 动态渲染字段列 -->
    <template v-if="hasChildren">
        <VxeColgroup :title="column.title">
            <template v-for="(child, index) in column.children" :key="index">
                <TableColumn :column="child" />
            </template>
        </VxeColgroup>
    </template>
    <template v-else>
        <VxeColumn v-bind="column">
            <template v-if="column.footerSlot" #footer="{ row }">
                <slot
                    :name="column.footerSlot"
                    v-bind="{ row: row, column: column }"
                ></slot>
            </template>
            <template #[schemaRenderType]="row">
                <template v-if="column?.schema?.component">
                    <More
                        :config="column.cellActionConfig"
                        :record="row"
                        v-if="column?.cellActionConfig"
                    >
                        <TableCell :record="row" :column="column">
                            <template
                                v-for="item in Object.keys($slots)"
                                #[item]="data"
                            >
                                <slot :name="item" v-bind="data || {}" />
                            </template>
                        </TableCell>
                    </More>
                    <TableCell v-else :record="row" :column="column">
                        <template
                            v-for="item in Object.keys($slots)"
                            #[item]="data"
                        >
                            <slot :name="item" v-bind="data || {}" />
                        </template>
                    </TableCell>
                </template>
                <!-- 插件渲染器处理 -->
                <template v-else-if="hasCellRender">
                    <component
                        :is="getCellRenderComponent"
                        :params="getCellRenderParams(row)"
                    />
                </template>
                <!-- 复合数据渲染 -->
                <template v-else-if="hasCompositeConfig">
                    <More
                        :config="column.cellActionConfig"
                        :record="row"
                        v-if="column?.cellActionConfig"
                    >
                        <PluginCompositeRenderer
                            :params="getCompositeRenderParams(row)"
                        />
                    </More>
                    <PluginCompositeRenderer
                        v-else
                        :params="getCompositeRenderParams(row)"
                    />
                </template>
                <!-- 其他字段的默认处理 -->
                <template v-else-if="!!column?.cellActionConfig">
                    <More :record="row" :config="column.cellActionConfig">
                        {{ getValue(column, row) }}</More
                    >
                </template>
                <template v-else>
                    <!-- 处理自定义插槽 -->
                    <template v-if="hasCustomSlot">
                        <slot :name="getSlotName()" v-bind="row" />
                    </template>
                    <template v-else>
                        <span
                            v-if="column.showTooltip"
                            :title="getValue(column, row)"
                        >
                            {{ getValue(column, row) }}
                        </span>
                        <template v-else>{{ getValue(column, row) }}</template>
                    </template>
                </template>
            </template>
        </VxeColumn>
    </template>
</template>

<script lang="ts">
import { computed, defineComponent, inject, PropType, provide } from 'vue';
import { VxeColumn, VxeColgroup } from 'vxe-table';
import { BaseColumn } from '../types';
import Select from '@/components/common/FormEngine/FormComponents/Select.vue';
import { Input } from '@/components/ui/input';
import More from './More.vue';
import TableCell from './TableCell.vue';
import { get, isEmpty, isFunction, set } from 'lodash-es';
import { FormFieldConfig } from '@/components/common/FormEngine';
// 导入插件渲染器
import StatusRenderer from '../../plugins/statusColumn/StatusRenderer.vue';
import PluginBooleanRenderer from '../../plugins/booleanColumn/BooleanRenderer.vue';
import NumberRenderer from '../../plugins/numberColumn/NumberRenderer.vue';
import DateRenderer from '../../plugins/dateColumn/DateRenderer.vue';
import LinkRenderer from '../../plugins/linkColumn/LinkRenderer.vue';
import PluginCompositeRenderer from '../../plugins/compositeColumn/CompositeRenderer.vue';
import PluginTextRenderer from '../../plugins/textColumn/TextRenderer.vue';
import PluginRelationRenderer from '../../plugins/relationColumn/RelationRenderer.vue';
import UserMappingRenderer from '../../plugins/userMappingColumn/UserMappingRenderer.vue';
import GroupRenderer from '../../plugins/groupColumn/GroupRenderer.vue';
import DictRenderer from '../../plugins/dictColumn/DictRenderer.vue';
import AvatarRenderer from '../../plugins/avatarColumn/AvatarRenderer.vue';
import ImageRenderer from '../../plugins/imageColumn/ImageRenderer.vue';

export default defineComponent({
    name: 'TableColumn',
    components: {
        VxeColumn,
        VxeColgroup,
        Select,
        Input,
        More,
        TableCell,
        StatusRenderer,
        PluginBooleanRenderer,
        NumberRenderer,
        DateRenderer,
        LinkRenderer,
        PluginCompositeRenderer,
        PluginTextRenderer,
        PluginRelationRenderer,
        UserMappingRenderer,
        GroupRenderer,
        DictRenderer,
        AvatarRenderer,
        ImageRenderer,
    },
    props: {
        column: {
            type: Object as PropType<BaseColumn>,
        },
    },
    emits: [],
    setup(props, { emit, attrs, slots }) {
        const tableRef = inject('__table');
        const tableProps = inject('__props');

        const schemaRenderType = computed(() => {
            return props.column?.schemaRenderType || 'default';
        });

        const getValue = computed(() => {
            return (column, record) => {
                const columnObj = column as any;
                const field = columnObj?.field;
                const formatter = columnObj?.formatter;
                if (isFunction(formatter)) {
                    // 获取单元格值
                    const cellValue = get(record?.row, field);
                    // 传递正确的参数格式：{ cellValue, row, column }
                    return formatter({ cellValue, row: record?.row, column });
                }
                return get(record?.row, field);
            };
        });

        const hasChildren = computed(() => {
            return props.column?.children?.length;
        });

        const hasCustomSlot = computed(() => {
            return !!props.column?.slots?.default;
        });

        const hasCompositeConfig = computed(() => {
            return !!props.column?.compositeConfig;
        });

        // cellRender处理逻辑
        const hasCellRender = computed(() => {
            return !!props.column?.cellRender?.name;
        });

        const getCellRenderComponent = computed(() => {
            const renderName = props.column?.cellRender?.name;
            if (!renderName) return null;

            // 返回组件名称，Vue会从已注册的组件中找到对应的组件
            const rendererMap = {
                StatusRenderer: 'StatusRenderer',
                BooleanRenderer: 'PluginBooleanRenderer',
                NumberRenderer: 'NumberRenderer',
                DateRenderer: 'DateRenderer',
                LinkRenderer: 'LinkRenderer',
                CompositeRenderer: 'PluginCompositeRenderer',
                TextRenderer: 'PluginTextRenderer',
                RelationRenderer: 'PluginRelationRenderer',
                UserMappingRenderer: 'UserMappingRenderer',
                GroupRenderer: 'GroupRenderer',
                DictRenderer: 'DictRenderer',
                AvatarRenderer: 'AvatarRenderer',
                ImageRenderer: 'ImageRenderer',
            };

            return rendererMap[renderName] || null;
        });

        const getSlotName = () => {
            return props.column?.slots?.default || '';
        };

        const getCompatibleCompositeConfig = (config) => {
            if (!config) return config;

            // 确保layout属性兼容CompositeConfig接口
            const compatibleConfig = { ...config };

            // 如果layout是'mixed'，转换为'vertical'
            if (compatibleConfig.layout === 'mixed') {
                compatibleConfig.layout = 'vertical';
            }

            return compatibleConfig;
        };

        const getCellRenderParams = (row) => {
            const columnObj = props.column as any;
            const field = columnObj?.field;
            const cellValue = get(row.row, field);
            return {
                row: row.row,
                column: columnObj,
                rowIndex: row.$rowIndex,
                cellValue,
            };
        };

        const getCompositeRenderParams = (row) => {
            const columnObj = props.column as any;
            const field = columnObj?.field;
            const cellValue = get(row.row, field);

            // 创建带有 compositeConfig 的 column 副本
            const columnWithParams = {
                ...columnObj,
                params: getCompatibleCompositeConfig(columnObj.compositeConfig),
            };

            return {
                row: row.row,
                column: columnWithParams,
                rowIndex: row.$rowIndex,
                cellValue,
            };
        };

        return {
            hasChildren,
            getValue,
            tableProps,
            hasCustomSlot,
            hasCompositeConfig,
            getSlotName,
            get, // 导出 get 函数供模板使用
            hasCellRender,
            getCellRenderComponent,
            getCompatibleCompositeConfig,
            getCellRenderParams,
            getCompositeRenderParams,
            schemaRenderType,
        };
    },
});
</script>
