<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

const props = defineProps<{
    value: any;
    field: any;
    row: any;
}>();

const emit = defineEmits<{
    (e: 'update:value', value: any): void;
    (e: 'save', value: any): void;
    (e: 'cancel'): void;
}>();

// 部门列表
const deptList = ref([
    { id: 1, name: '研发部', code: 'dev' },
    { id: 2, name: '研发一部', code: 'dev_one' },
    { id: 3, name: '研发二部', code: 'dev_two' },
    { id: 4, name: '市场部', code: 'market' },
    { id: 5, name: '销售部', code: 'sales' },
    { id: 6, name: '测试部', code: 'test' },
    { id: 7, name: '人事部', code: 'hr' },
    { id: 8, name: '财务部', code: 'finance' },
]);

// 当前选中的部门ID
const selectedDeptId = ref<number | null>(null);

// 初始化选中值
onMounted(() => {
    if (props.value && props.value.id) {
        selectedDeptId.value = props.value.id;
    }
});

// 监听值变化
watch(
    () => props.value,
    (newValue) => {
        if (newValue && newValue.id) {
            selectedDeptId.value = newValue.id;
        } else {
            selectedDeptId.value = null;
        }
    }
);

// 处理选择变化
const handleSelectChange = (value: string) => {
    const deptId = parseInt(value);
    selectedDeptId.value = deptId;

    // 找到选中的部门对象
    const selectedDept = deptList.value.find((dept) => dept.id === deptId);

    if (selectedDept) {
        emit('update:value', selectedDept);
    }
};

// 保存编辑
const saveEdit = () => {
    if (selectedDeptId.value) {
        const selectedDept = deptList.value.find(
            (dept) => dept.id === selectedDeptId.value
        );
        if (selectedDept) {
            emit('save', selectedDept);
        }
    } else {
        emit('save', null);
    }
};

// 取消编辑
const cancelEdit = () => {
    emit('cancel');
};

const handleClick = () => {
    debugger;
};
</script>

<template>
    <div class="dept-selector">
        <Select
            :value="selectedDeptId?.toString()"
            @update:value="handleSelectChange"
            @select="handleClick"
        >
            <SelectTrigger class="w-full">
                <SelectValue placeholder="选择部门" />
            </SelectTrigger>
            <SelectContent @click="handleClick">
                <SelectItem
                    v-for="dept in deptList"
                    :key="dept.id"
                    :value="dept.id.toString()"
                    @click="handleClick"
                >
                    {{ dept.name }} ({{ dept.code }})
                </SelectItem>
            </SelectContent>
        </Select>
    </div>
</template>

<style scoped>
.dept-selector {
    min-width: 200px;
}
</style>
