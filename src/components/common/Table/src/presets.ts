import { BaseTableProps } from './types';

/**
 * Table组件预设配置
 * 降低开发人员配置复杂度，提供开箱即用的配置模板
 */

// 基础列表页配置
export const BASIC_LIST_PRESET: Partial<BaseTableProps> = {
    showIndex: true,
    showSearch: true,
    showPagination: true,
    toolbarConfig: {
        isReload: true,
        isZoom: true,
        isFull: true,
    },
    layoutHeight: 180,
    observeScrollY: true,
    stripe: true,
    border: true,
    round: true,
    size: 'medium',
    immediate: true,
};

// 只读展示配置
export const READONLY_PRESET: Partial<BaseTableProps> = {
    showIndex: true,
    showSearch: true,
    showPagination: true,
    toolbarConfig: {
        isReload: true,
        isFull: true,
    },
    stripe: true,
    border: true,
    checkType: undefined,
};

// 可编辑表格配置
export const EDITABLE_PRESET: Partial<BaseTableProps> = {
    showIndex: true,
    showSearch: false,
    showPagination: false,
    toolbarConfig: {
        isReload: true,
    },
    border: true,
    checkType: 'checkbox',
    keyboardEventsConfig: {
        fields: [],
        enterField: '',
    },
};

// 选择器配置
export const SELECTOR_PRESET: Partial<BaseTableProps> = {
    showIndex: false,
    showSearch: true,
    showPagination: true,
    checkType: 'checkbox',
    toolbarConfig: {
        isReload: true,
        isFull: true,
    },
    layoutHeight: 120,
};

// 报表配置
export const REPORT_PRESET: Partial<BaseTableProps> = {
    showIndex: false,
    showSearch: false,
    showPagination: false,
    toolbarConfig: {
        isFull: true,
    },
    stripe: false,
    border: true,
    size: 'small',
};

/**
 * 预设配置合并函数
 */
export function mergePreset(
    preset: Partial<BaseTableProps>,
    customConfig: Partial<BaseTableProps> = {}
): Partial<BaseTableProps> {
    return {
        ...preset,
        ...customConfig,
        // 深度合并特殊配置
        toolbarConfig: {
            ...preset.toolbarConfig,
            ...customConfig.toolbarConfig,
        },
        keyboardEventsConfig: {
            ...preset.keyboardEventsConfig,
            ...customConfig.keyboardEventsConfig,
        },
    };
}

/**
 * 预设配置类型
 */
export type PresetType = 
    | 'basic-list'    // 基础列表
    | 'readonly'      // 只读展示
    | 'editable'      // 可编辑
    | 'selector'      // 选择器
    | 'report';       // 报表

/**
 * 获取预设配置
 */
export function getPreset(type: PresetType): Partial<BaseTableProps> {
    const presets = {
        'basic-list': BASIC_LIST_PRESET,
        'readonly': READONLY_PRESET,
        'editable': EDITABLE_PRESET,
        'selector': SELECTOR_PRESET,
        'report': REPORT_PRESET,
    };
    
    return presets[type] || BASIC_LIST_PRESET;
} 