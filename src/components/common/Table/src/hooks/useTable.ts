import { computed, Ref, ref, unref } from 'vue'
import { BaseColumn, BaseTableProps } from '../types'
import { get, set } from 'lodash-es'
import { ValueOf } from 'type-fest'
import {
  VxeColumnPropTypes,
  VxeTableDefines,
  VxeTableEmits,
  VxeTableMethods,
  VxeTablePropTypes,
  VxeToolbarConstructor,
  VxeToolbarInstance,
} from 'vxe-table'

interface TableMethods extends VxeTableMethods {
  refresh(): unknown
  instance: Ref<Recordable>
  init: (props: BaseTableProps) => void
  reload: (opt?: Recordable) => void
  setTableData: (data: Recordable[]) => void
  update: () => void
  updateCellSchema: (field: string, row: Recordable, schema: Recordable) => void
  checked: Ref<Recordable>
  getSelectedRows: (isFull?: boolean) => any[]
  saveCondition: (text, filter, index?) => void
}

type IUseTable = [register: (api: any) => void, methods: TableMethods]

export function useTable(props: BaseTableProps): IUseTable {
  const methods = {}
  const checkedRef = ref()
  const instanceRef = ref()
  const methodsRef: Ref<TableMethods> = ref()

  const register = (api: TableMethods) => {
    instanceRef.value = api?.instance?.value
    methodsRef.value = api
    init(props)

    checkedRef.value = api?.checked

    Object.keys(api).forEach((key) => {
      set(methods, key, get(api, key))
    })
  }

  function init(props: BaseTableProps) {
    unref(methodsRef)?.init(props)
  }

  function updateCellSchema(
    field: string,
    row: Recordable,
    schema: Recordable
  ) {
    unref(methodsRef)?.updateCellSchema(field, row, schema)
  }

  function reload(opt?: Recordable) {
    unref(methodsRef)?.reload(opt)
  }
  function setTableData(data: Recordable[]) {
    unref(methodsRef)?.setTableData(data)
  }
  function update() {
    unref(methodsRef)?.update()
  }

  function saveCondition(text, filter, index) {
    unref(methodsRef)?.saveCondition(text, filter, index)
  }

  // function setColumns(
  //     columnList: Partial<BaseColumn>[] | (string | string[])[]
  // ) {
  //     unref(methodsRef)?.setColumns(columnList);
  // }
  // function updateColumn(column: BaseColumn) {
  //     unref(methodsRef)?.updateColumn(column);
  // }
  // function getColumns() {
  //     return unref(methodsRef)?.getColumns();
  // }

  function insert(data: any) {
    return unref(methodsRef)?.insert(data)
  }

  function insertAt(records: any, targetRow?: any | -1 | null) {
    return unref(methodsRef)?.insertAt(records, targetRow)
  }

  const validate = (
    rows?:
      | boolean
      | object
      | any[]
      | ((errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void),
    callback?: (errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void
  ): Promise<Recordable> => {
    return unref(methodsRef)?.validate(rows, callback)
  }

  const validateField = (
    rows?: boolean | object | any[],
    fieldOrColumn?:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | VxeColumnPropTypes.Field[]
      | VxeTableDefines.ColumnInfo<any>[]
  ): Promise<Recordable> => {
    return unref(methodsRef)?.validateField(rows, fieldOrColumn)
  }
  const fullValidate = (
    rows?:
      | boolean
      | object
      | any[]
      | ((errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void),
    callback?: (errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void
  ): Promise<Recordable> => {
    return unref(methodsRef)?.validate(rows, callback)
  }

  const fullValidateField = (
    rows?: boolean | object | any[],
    fieldOrColumn?:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | VxeColumnPropTypes.Field[]
      | VxeTableDefines.ColumnInfo<any>[]
  ): Promise<Recordable> => {
    return unref(methodsRef)?.validateField(rows, fieldOrColumn)
  }
  /**

    const dispatchEvent = (
        type: ValueOf<VxeTableEmits>,
        params: Record<string, any>,
        evnt: Event | null
    ): void => {
        unref(methodsRef)?.dispatchEvent(type, params, evnt);
    };

    const getEl = () => {
        return unref(methodsRef)?.getEl();
    };

    /**
     * 重置表格的一切数据状态
     */
  const clearAll = (): Promise<void> => {
    return unref(methodsRef)?.clearAll()
  }

  /**
   * 手动处理数据，用于手动排序与筛选
   * 对于手动更改了排序、筛选...等条件后需要重新处理数据时可能会用到
   */
  const updateData = (): Promise<void> => {
    unref(methodsRef)?.updateData()
    return Promise.resolve()
  }

  /**
   * 重新加载数据，不会清空表格状态
   * @param {Array} data 数据
   */
  const loadData = (data: any[]): Promise<any> => {
    return unref(methodsRef)?.loadData(data)
  }
  /**
   * 重新加载数据，会清空表格状态
   * @param {Array} data 数据
   */
  const reloadData = (data: any[]): Promise<void> => {
    return unref(methodsRef)?.reloadData(data)
  }

  /**
   * 修改行数据
   */
  const setRow = (rows: any | any[], record?: any): Promise<void> => {
    return unref(methodsRef)?.setRow(rows, record)
  }

  /**
   * 局部加载行数据并恢复到初始状态
   * 对于行数据需要局部更改的场景中可能会用到
   * @param {Row} row 行对象
   * @param {Object} record 新数据
   * @param {String} field 字段名
   */
  const reloadRow = (
    rows: any | any[],
    record?: any,
    field?: string
  ): Promise<void> => {
    return unref(methodsRef)?.reloadRow(rows, record, field)
  }

  const getParams = (): any => {
    return unref(methodsRef)?.getParams()
  }

  /**
   * 用于树结构，给行数据加载子节点
   */
  const loadTreeChildren = (row: any, children: any[]): Promise<any[]> => {
    return unref(methodsRef)?.loadTreeChildren(row, children)
  }

  /**
   * 加载列配置
   * 对于表格列需要重载、局部递增场景下可能会用到
   * @param {ColumnInfo} columns 列配置
   */
  const loadColumn = (
    columns: (
      | VxeTableDefines.ColumnOptions<any>
      | VxeTableDefines.ColumnInfo<any>
    )[]
  ): Promise<any> => {
    return unref(methodsRef)?.loadColumn(columns)
  }

  /**
   * 加载列配置并恢复到初始状态
   * 对于表格列需要重载、局部递增场景下可能会用到
   * @param {ColumnInfo} columns 列配置
   */
  const reloadColumn = (
    columns: (
      | VxeTableDefines.ColumnOptions<any>
      | VxeTableDefines.ColumnInfo<any>
    )[]
  ): Promise<any> => {
    return unref(methodsRef)?.reloadColumn(columns)
  }

  /**
   * 根据 tr 元素获取对应的 row 信息
   * @param {Element} trElem 元素
   */
  const getRowNode = (
    trElem: HTMLElement
  ): {
    rowid: string
    item: any
    items: any[]
    index: number
    parent?: any
  } | null => {
    return unref(methodsRef)?.getRowNode(trElem)
  }

  /**
   * 根据 th/td 元素获取对应的 column 信息
   * @param {Element} cell 元素
   */
  const getColumnNode = (
    cellElem: HTMLElement
  ): {
    colid: string
    item: VxeTableDefines.ColumnInfo<any>
    items: VxeTableDefines.ColumnInfo<any>[]
    index: number
    parent: VxeTableDefines.ColumnInfo<any>
  } => {
    return unref(methodsRef)?.getColumnNode(cellElem)
  }

  /**
   * 根据 row 获取序号
   * @param {Row} row 行对象
   */
  const getRowSeq = (row: any): string | number => {
    return unref(methodsRef)?.getRowSeq(row)
  }

  /**
   * 根据 row 获取相对于 data 中的索引
   * @param {Row} row 行对象
   */
  const getRowIndex = (row: any): number => unref(methodsRef)?.getRowIndex(row)

  /**
   * 根据 row 获取相对于当前数据中的索引
   * @param {Row} row 行对象
   */
  const getVTRowIndex = (row: any): number =>
    unref(methodsRef)?.getVTRowIndex(row)
  /**
   * 根据 row 获取渲染中的虚拟索引
   * @param {Row} row 行对象
   */
  const getVMRowIndex = (row: any): number =>
    unref(methodsRef)?.getVMRowIndex(row)

  /**
   * 根据 column 获取相对于 columns 中的索引
   * @param {ColumnInfo} column 列配置
   */
  const getColumnIndex = (row: any): number =>
    unref(methodsRef)?.getVMRowIndex(row)
  /**
   * 根据 column 获取相对于当前表格列中的索引
   * @param {ColumnInfo} column 列配置
   */
  const getVTColumnIndex = (row: any): number =>
    unref(methodsRef)?.getVTColumnIndex(row)
  /**
   * 根据 column 获取渲染中的虚拟索引
   * @param {ColumnInfo} column 列配置
   */
  const getVMColumnIndex = (row: any): number =>
    unref(methodsRef)?.getVMColumnIndex(row)
  /**
   * 创建 data 对象
   * 对于某些特殊场景可能会用到，会自动对数据的字段名进行检测，如果不存在就自动定义
   * @param {Array} records 新数据
   */
  const createData = (records: any[]): Promise<any[]> =>
    unref(methodsRef)?.createData(records)
  /**
   * 创建 Row|Rows 对象
   * 对于某些特殊场景需要对数据进行手动插入时可能会用到
   * @param {Array/Object} records 新数据
   */
  const createRow = (records: any | any[]): Promise<any | any[]> =>
    unref(methodsRef)?.createRow(records)
  /**
   * 还原数据
   * 如果不传任何参数，则还原整个表格
   * 如果传 row 则还原一行
   * 如果传 rows 则还原多行
   * 如果还额外传了 field 则还原指定的单元格数据
   */
  const revertData = (rows?: any | any[], field?: string): Promise<any> =>
    unref(methodsRef)?.revertData(rows, field)
  /**
   * 清空单元格内容
   * 如果不创参数，则清空整个表格内容
   * 如果传 row 则清空一行内容
   * 如果传 rows 则清空多行内容
   * 如果还额外传了 field 则清空指定单元格内容
   * @param {Array/Row} rows 行数据
   * @param {String} field 字段名
   */
  const clearData = (rows?: any | any[], field?: string): Promise<any> =>
    unref(methodsRef)?.revertData(rows, field)

  const getCellElement = (
    row: any,
    fieldOrColumn:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | null
  ): HTMLTableDataCellElement | null =>
    unref(methodsRef)?.getCellElement(row, fieldOrColumn)

  const getCellLabel = (
    row: any,
    fieldOrColumn:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | null
  ): string | number | null =>
    unref(methodsRef)?.getCellLabel(row, fieldOrColumn)
  /**
   * 检查是否为临时行数据
   */
  const isInsertByRow = (row: any | null): boolean =>
    unref(methodsRef)?.isInsertByRow(row)
  const isRemoveByRow = (row: any | null): boolean =>
    unref(methodsRef)?.isRemoveByRow(row)
  /**
   * 删除所有新增的临时数据
   */
  const removeInsertRow = (): Promise<{
    row: any
    rows: any[]
  }> => unref(methodsRef)?.removeInsertRow()
  /**
   * 检查行或列数据是否发生改变
   */
  const isUpdateByRow = (row: any, field?: string | null): boolean =>
    unref(methodsRef)?.isUpdateByRow(row, field)
  /**
   * 获取表格的可视列，也可以指定索引获取列
   * @param {Number} columnIndex 索引
   */
  const getColumns = (columnIndex?: number) =>
    unref(methodsRef)?.getColumns(columnIndex) as any
  /**
   * 根据列获取列的唯一主键
   */
  const getColid = (
    fieldOrColumn:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | null
  ): string | null => unref(methodsRef)?.getColid(fieldOrColumn)
  /**
   * 根据列的唯一主键获取列
   * @param {String} colid 列主键
   */
  const getColumnById = (
    colid: string | null
  ): VxeTableDefines.ColumnInfo<any> => unref(methodsRef)?.getColumnById(colid)
  /**
   * 根据列的字段名获取列
   * @param {String} field 字段名
   */
  const getColumnByField = (
    field: VxeColumnPropTypes.Field | null
  ): VxeTableDefines.ColumnInfo<any> =>
    unref(methodsRef)?.getColumnByField(field)

  const getParentColumn = (
    fieldOrColumn:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | null
  ): VxeTableDefines.ColumnInfo<any> =>
    unref(methodsRef)?.getParentColumn(fieldOrColumn)
  /**
   * 获取当前表格的列
   * 收集到的全量列、全量表头列、处理条件之后的全量表头列、当前渲染中的表头列
   */
  const getTableColumn = () => unref(methodsRef)?.getTableColumn()
  /**
   * 移动列到指定列的位置
   * @param fieldOrColumn
   * @param targetFieldOrColumn
   * @param options
   */
  const moveColumnTo = (
    fieldOrColumn: any,
    targetFieldOrColumn: any,
    options: any
  ): Promise<{
    status: boolean
  }> =>
    unref(methodsRef)?.moveColumnTo(fieldOrColumn, targetFieldOrColumn, options)
  /**
   * 移动行到指定行的位置
   * @param rowidOrRow
   * @param targetRowidOrRow
   * @param options
   */
  const moveRowTo = (
    rowidOrRow: any,
    targetRowidOrRow: any,
    options?: {
      isCrossDrag?: boolean
      dragToChild?: boolean
      dragPos?: 'top' | 'bottom' | '' | null
    }
  ): Promise<{
    status: boolean
  }> => unref(methodsRef)?.moveRowTo(rowidOrRow, targetRowidOrRow, options)
  /**
   * 获取表格的全量列
   */
  const getFullColumns = () => unref(methodsRef)?.getFullColumns()
  /**
   * 获取数据，和 data 的行为一致，也可以指定索引获取数据
   */
  const getData = (rowIndex?: number): any =>
    unref(methodsRef)?.getData(rowIndex)
  /**
   * 用于多选行，获取已选中的数据
   */
  const getCheckboxRecords = (isFull?: boolean): any[] =>
    unref(methodsRef)?.getCheckboxRecords(isFull)
  /**
   * 只对 tree-config 有效，获取行的子级
   */
  const getTreeRowChildren = (rowOrRowid: any): any[] =>
    unref(methodsRef)?.getTreeRowChildren(rowOrRowid)
  /**
   * 只对 tree-config 有效，获取行的父级
   */
  const getTreeParentRow = (rowOrRowid: any): any =>
    unref(methodsRef)?.getTreeParentRow(rowOrRowid)

  /**
   * 根据行的唯一主键获取行
   * @param {String/Number} rowid 行主键
   */
  const getRowById = (rowid: string | number | null): any =>
    unref(methodsRef)?.getRowById(rowid)
  /**
   * 根据行获取行的唯一主键
   * @param {Row} row 行对象
   */
  const getRowid = (row: any | null): string => unref(methodsRef)?.getRowid(row)
  /**
   * 获取处理后的表格数据
   * 如果存在筛选条件，继续处理
   * 如果存在排序，继续处理
   */
  const getTableData = () => unref(methodsRef)?.getTableData()
  /**
   * 获取表格的全量数据，如果是 tree-config 则返回带层级的树结构
   */
  const getFullData = (): any[] => unref(methodsRef)?.getFullData()
  /**
   * 设置为固定列
   */
  const setColumnFixed = (
    fieldOrColumns:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | VxeColumnPropTypes.Field[]
      | VxeTableDefines.ColumnInfo<any>[],
    fixed: VxeColumnPropTypes.Fixed
  ): Promise<void> => unref(methodsRef)?.setColumnFixed(fieldOrColumns, fixed)
  /**
   * 取消指定固定列
   */
  const clearColumnFixed = (
    fieldOrColumns:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | VxeColumnPropTypes.Field[]
      | VxeTableDefines.ColumnInfo<any>[]
  ): Promise<void> => unref(methodsRef)?.clearColumnFixed(fieldOrColumns)
  /**
   * 隐藏指定列
   */
  const hideColumn = (
    fieldOrColumns:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | VxeColumnPropTypes.Field[]
      | VxeTableDefines.ColumnInfo<any>[]
  ): Promise<void> => unref(methodsRef)?.hideColumn(fieldOrColumns)
  /**
   * 显示指定列
   */
  const showColumn = (
    fieldOrColumn:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | VxeColumnPropTypes.Field[]
      | VxeTableDefines.ColumnInfo<any>[]
  ): Promise<void> => unref(methodsRef)?.showColumn(fieldOrColumn)

  const setColumnWidth = (
    fieldOrColumns:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | VxeColumnPropTypes.Field[]
      | VxeTableDefines.ColumnInfo<any>[],
    width: number | string
  ): Promise<{
    status: boolean
  }> => unref(methodsRef)?.setColumnWidth(fieldOrColumns, width)

  const getColumnWidth = (
    fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
  ): number => unref(methodsRef)?.getColumnWidth(fieldOrColumn)

  /**
   * 刷新列信息
   * 将固定的列左边、右边分别靠边
   * 如果传 true 则会检查列顺序并排序
   */
  const refreshColumn = (resiveOrder?: boolean): Promise<void> =>
    unref(methodsRef)?.refreshColumn(resiveOrder)

  const setRowHeightConf = (
    heightConf: Record<string, number>
  ): Promise<{
    status: boolean
  }> => unref(methodsRef)?.setRowHeightConf(heightConf)

  const getRowHeightConf = (isFull?: boolean): Record<string, number> =>
    unref(methodsRef)?.getRowHeightConf(isFull)

  const setRowHeight = (
    rowOrId: any | any[],
    height: number | string
  ): Promise<{
    status: boolean
  }> => unref(methodsRef)?.setRowHeight(rowOrId, height)

  const getRowHeight = (rowOrId: any): number =>
    unref(methodsRef)?.getRowHeight(rowOrId)
  /**
   * 刷新滚动操作，手动同步滚动相关位置（对于某些特殊的操作，比如滚动条错位、固定列不同步）
   */
  const refreshScroll = (): Promise<void> => unref(methodsRef)?.refreshScroll()
  /**
   * 重新渲染布局
   * 刷新布局
   */
  const recalculate = (refull?: boolean): Promise<void> =>
    unref(methodsRef)?.recalculate(refull)

  const openTooltip = (
    target: HTMLElement,
    content: string | number
  ): Promise<any> => unref(methodsRef)?.openTooltip(target, content)
  /**
   * 关闭 tooltip
   */
  const closeTooltip = (): Promise<any> => unref(methodsRef)?.closeTooltip()
  /**
   * 判断列头复选框是否被选中
   */
  const isAllCheckboxChecked = (): boolean =>
    unref(methodsRef)?.isAllCheckboxChecked()
  /**
   * 判断列头复选框是否被半选
   */
  const isAllCheckboxIndeterminate = (): boolean =>
    unref(methodsRef)?.isAllCheckboxIndeterminate()
  /**
   * 获取复选框半选状态的行数据
   */
  const getCheckboxIndeterminateRecords = (isFull?: boolean): any[] =>
    unref(methodsRef)?.getCheckboxIndeterminateRecords(isFull)
  /**
   * 用于多选行，设置行为选中状态，第二个参数为选中与否
   * @param {Array/Row} rows 行数据
   * @param {Boolean} value 是否选中
   */
  const setCheckboxRow = (rows: any | any[], checked: boolean): Promise<any> =>
    unref(methodsRef)?.setCheckboxRow(rows, checked)

  const setCheckboxRowKey = (
    keys: string | number | (string | number)[] | null | undefined,
    checked: boolean
  ): Promise<any> => unref(methodsRef)?.setCheckboxRowKey(keys, checked)

  const isCheckedByCheckboxRow = (row: any): boolean =>
    unref(methodsRef)?.isCheckedByCheckboxRow(row)

  const isCheckedByCheckboxRowKey = (
    key: string | number | null | undefined
  ): boolean => unref(methodsRef)?.isCheckedByCheckboxRowKey(key)

  const isIndeterminateByCheckboxRow = (row: any): boolean =>
    unref(methodsRef)?.isIndeterminateByCheckboxRow(row)

  const isIndeterminateByCheckboxRowKey = (
    key: string | number | null | undefined
  ): boolean => unref(methodsRef)?.isIndeterminateByCheckboxRowKey(key)
  /**
   * 多选，切换某一行的选中状态
   */
  const toggleCheckboxRow = (row: any): Promise<any> =>
    unref(methodsRef)?.toggleCheckboxRow(row)
  /**
   * 用于多选行，设置所有行的选中状态
   * @param {Boolean} checked 是否选中
   */
  const setAllCheckboxRow = (checked: boolean): Promise<any> =>
    unref(methodsRef)?.setAllCheckboxRow(checked)
  /**
   * 获取单选框保留选中的行
   */
  const getRadioReserveRecord = (isFull?: boolean): any =>
    unref(methodsRef)?.getRadioReserveRecord(isFull)

  const clearRadioReserve = (): Promise<any> =>
    unref(methodsRef)?.clearRadioReserve()
  /**
   * 获取复选框保留选中的行
   */
  const getCheckboxReserveRecords = (isFull?: boolean): any[] =>
    unref(methodsRef)?.getCheckboxReserveRecords(isFull)

  const clearCheckboxReserve = (): Promise<any> =>
    unref(methodsRef)?.clearCheckboxReserve()
  /**
   * 多选，切换所有行的选中状态
   */
  const toggleAllCheckboxRow = (): Promise<any> =>
    unref(methodsRef)?.toggleAllCheckboxRow()
  /**
   * 用于多选行，手动清空用户的选择
   * 清空行为不管是否被禁用还是保留记录，都将彻底清空选中状态
   */
  const clearCheckboxRow = (): Promise<any> =>
    unref(methodsRef)?.clearCheckboxRow()
  /**
   * 用于当前行，设置某一行为高亮状态
   * @param {Row} row 行对象
   */
  const setCurrentRow = (row: any): Promise<any> =>
    unref(methodsRef)?.setCurrentRow(row)

  const isCheckedByRadioRow = (row: any | null): boolean =>
    unref(methodsRef)?.isCheckedByRadioRow(row)

  const isCheckedByRadioRowKey = (
    key: string | number | null | undefined
  ): boolean => unref(methodsRef)?.isCheckedByRadioRowKey(key)
  /**
   * 用于单选行，设置某一行为选中状态
   * @param {Row} row 行对象
   */
  const setRadioRow = (row: any): Promise<any> =>
    unref(methodsRef)?.setRadioRow(row)
  /**
   * 用于单选行，设置某一行为选中状态
   * @param key 行主键
   */
  const setRadioRowKey = (
    key: string | number | null | undefined
  ): Promise<any> => unref(methodsRef)?.setRadioRowKey(key)
  /**
   * 用于当前行，手动清空当前高亮的状态
   */
  const clearCurrentRow = (): Promise<any> =>
    unref(methodsRef)?.clearCurrentRow()
  /**
   * 用于单选行，手动清空用户的选择
   */
  const clearRadioRow = (): Promise<any> => unref(methodsRef)?.clearRadioRow()
  /**
   * 用于当前行，获取当前行的数据
   */
  const getCurrentRecord = (): any => unref(methodsRef)?.getCurrentRecord()
  /**
   * 通用获取选中行方法
   * 自动兼容 checkbox 和 radio 两种模式
   * @param {Boolean} isFull 是否获取完整数据
   * @returns {Array} 选中的行数据数组，radio 模式下返回单个元素的数组，checkbox 模式下返回多个元素的数组
   */
  const getSelectedRows = (isFull?: boolean): any[] => {
    const checkType = props?.checkType

    try {
      if (checkType === 'radio') {
        // radio 模式：获取单选选中的行
        const radioRecord = unref(methodsRef)?.getRadioRecord(isFull)
        return radioRecord ? [radioRecord] : []
      } else if (checkType === 'checkbox') {
        // checkbox 模式：获取多选选中的行
        return unref(methodsRef)?.getCheckboxRecords(isFull) || []
      } else {
        // 没有启用选择模式
        console.warn('Table checkType is not enabled or is false')
        return []
      }
    } catch (error) {
      console.error('Error getting selected rows:', error)
      return []
    }
  }

  const getCurrentColumn = () => unref(methodsRef)?.getCurrentColumn()
  /**
   * 用于当前列，设置某列行为高亮状态
   */
  const setCurrentColumn = (
    fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
  ): Promise<void> => unref(methodsRef)?.setCurrentColumn(fieldOrColumn)
  /**
   * 用于当前列，手动清空当前高亮的状态
   */
  const clearCurrentColumn = (): Promise<void> =>
    unref(methodsRef)?.clearCurrentColumn()

  const setPendingRow = (rows: any | any[], status: boolean): Promise<any> =>
    unref(methodsRef)?.setPendingRow(rows, status)

  const togglePendingRow = (rows: any | any[]): Promise<any> =>
    unref(methodsRef)?.togglePendingRow(rows)

  const isPendingByRow = (row: any): boolean =>
    unref(methodsRef)?.isPendingByRow(row)

  const getPendingRecords = (): any[] => unref(methodsRef)?.getPendingRecords()

  const clearPendingRow = (): Promise<any> =>
    unref(methodsRef)?.clearPendingRow()

  const sort = (
    field: string,
    order?: VxeTablePropTypes.SortOrder
  ): Promise<void> => unref(methodsRef)?.sort(field, order)

  const setSort = (
    sortConfs: VxeTableDefines.SortConfs | VxeTableDefines.SortConfs[],
    update?: boolean
  ): Promise<void> => unref(methodsRef)?.setSort(sortConfs, update)

  const setSortByEvent = (
    event: Event,
    sortConfs: VxeTableDefines.SortConfs | VxeTableDefines.SortConfs[],
    update?: boolean
  ): Promise<void> =>
    unref(methodsRef)?.setSortByEvent(event, sortConfs, update)
  /**
   * 清空指定列的排序条件
   * 如果为空则清空所有列的排序条件
   * @param {String} fieldOrColumn 列或字段名
   */
  const clearSort = (
    fieldOrColumn?:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | null
  ): Promise<void> => unref(methodsRef)?.clearSort(fieldOrColumn)

  const clearSortByEvent = (
    event: Event,
    fieldOrColumn?:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | null
  ): Promise<void> => unref(methodsRef)?.clearSortByEvent(event, fieldOrColumn)

  const isSort = (
    fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
  ): boolean => unref(methodsRef)?.isSort(fieldOrColumn)

  const getSortColumns = (): VxeTableDefines.SortCheckedParams[] =>
    unref(methodsRef)?.getSortColumns()

  const setFilterByEvent = (
    event: Event,
    fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>,
    options: VxeColumnPropTypes.FilterItem[],
    update?: boolean
  ): Promise<void> =>
    unref(methodsRef)?.setFilterByEvent(event, fieldOrColumn, options, update)
  /**
   * 关闭筛选
   * @param {Event} evnt 事件
   */
  const closeFilter = (): Promise<any> => unref(methodsRef)?.closeFilter()
  /**
   * 判断指定列是否为筛选状态，如果为空则判断所有列
   * @param {String} fieldOrColumn 字段名
   */
  const isActiveFilterByColumn = (
    fieldOrColumn:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | null
  ): boolean => unref(methodsRef)?.isActiveFilterByColumn(fieldOrColumn)

  const clearFilterByEvent = (
    event: Event,
    fieldOrColumn?:
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo<any>
      | null
  ): Promise<void> =>
    unref(methodsRef)?.clearFilterByEvent(event, fieldOrColumn)
  /**
   * 判断展开行是否懒加载完成
   * @param {Row} row 行对象
   */
  const isRowExpandLoaded = (row: any | null): boolean =>
    unref(methodsRef)?.isRowExpandLoaded(row)

  const clearRowExpandLoaded = (row: any): Promise<void> =>
    unref(methodsRef)?.clearRowExpandLoaded(row)
  /**
   * 重新懒加载展开行，并展开内容
   * @param {Row} row 行对象
   */
  const reloadRowExpand = (row: any): Promise<void> =>
    unref(methodsRef)?.reloadRowExpand(row)

  /**
   * 切换展开行
   */
  const toggleRowExpand = (row: any): Promise<void> =>
    unref(methodsRef)?.toggleRowExpand(row)
  /**
   * 设置所有行的展开与否
   * @param {Boolean} expanded 是否展开
   */
  const setAllRowExpand = (expanded: boolean): Promise<void> =>
    unref(methodsRef)?.setAllRowExpand(expanded)
  /**
   * 设置展开行，二个参数设置这一行展开与否
   * 支持单行
   * 支持多行
   * @param {Array/Row} rows 行数据
   * @param {Boolean} expanded 是否展开
   */
  const setRowExpand = (rows: any | any[], expanded: boolean): Promise<void> =>
    unref(methodsRef)?.setRowExpand(rows, expanded)
  /**
   * 判断行是否为展开状态
   * @param {Row} row 行对象
   */
  const isRowExpandByRow = (row: any | null): boolean =>
    unref(methodsRef)?.isRowExpandByRow(row)
  /**
   * 手动清空展开行状态，数据会恢复成未展开的状态
   */
  const clearRowExpand = (): Promise<void> =>
    unref(methodsRef)?.clearRowExpand()

  const clearRowExpandReserve = (): Promise<void> =>
    unref(methodsRef)?.clearRowExpandReserve()

  const getRowExpandRecords = (): any[] =>
    unref(methodsRef)?.getRowExpandRecords()

  const setRowGroups = (
    fieldOrColumns:
      | (VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo)[]
      | VxeColumnPropTypes.Field
      | VxeTableDefines.ColumnInfo
      | null
  ): Promise<void> => unref(methodsRef)?.setRowGroups(fieldOrColumns)

  const clearRowGroups = (): Promise<void> =>
    unref(methodsRef)?.clearRowGroups()

  const isRowGroupRecord = (row: any): boolean =>
    unref(methodsRef)?.isRowGroupRecord(row)

  const isRowGroupExpandByRow = (row: any): boolean =>
    unref(methodsRef)?.isRowGroupExpandByRow(row)

  const setRowGroupExpand = (
    rows: any | any[],
    expanded: boolean
  ): Promise<void> => unref(methodsRef)?.setRowGroupExpand(rows, expanded)

  const setAllRowGroupExpand = (expanded: boolean): Promise<void> =>
    unref(methodsRef)?.setAllRowGroupExpand(expanded)

  const clearRowGroupExpand = (): Promise<void> =>
    unref(methodsRef)?.clearRowGroupExpand()

  const getTreeExpandRecords = (): any[] =>
    unref(methodsRef)?.getTreeExpandRecords()
  /**
   * 判断树节点是否懒加载完成
   * @param {Row} row 行对象
   */
  const isTreeExpandLoaded = (row: any | null): boolean =>
    unref(methodsRef)?.isTreeExpandLoaded(row)

  const clearTreeExpandLoaded = (rows: any | any[]): Promise<any> =>
    unref(methodsRef)?.clearTreeExpandLoaded(rows)
  /**
   * 重新懒加载树节点，并展开该节点
   * @param {Row} row 行对象
   */
  const reloadTreeExpand = (row: any): Promise<any> =>
    unref(methodsRef)?.reloadTreeExpand(row)
  /**
   * 切换/展开树节点
   */
  const toggleTreeExpand = (row: any): Promise<any> =>
    unref(methodsRef)?.toggleTreeExpand(row)
  /**
   * 设置所有树节点的展开与否
   * @param {Boolean} expanded 是否展开
   */
  const setAllTreeExpand = (expanded: boolean): Promise<void> =>
    unref(methodsRef)?.setAllTreeExpand(expanded)
  /**
   * 设置展开树形节点，二个参数设置这一行展开与否
   * 支持单行
   * 支持多行
   * @param {Array/Row} rows 行数据
   * @param {Boolean} expanded 是否展开
   */
  const setTreeExpand = (rows: any | any[], expanded: boolean): Promise<void> =>
    unref(methodsRef)?.setTreeExpand(rows, expanded)
  /**
   * 判断行是否为树形节点展开状态
   * @param {Row} row 行对象
   */
  const isTreeExpandByRow = (row: any | null): boolean =>
    unref(methodsRef)?.isTreeExpandByRow(row)
  /**
   * 手动清空树形节点的展开状态，数据会恢复成未展开的状态
   */
  const clearTreeExpand = (): Promise<void> =>
    unref(methodsRef)?.clearTreeExpand()
  const clearTreeExpandReserve = (): Promise<void> =>
    unref(methodsRef)?.clearTreeExpandReserve()
  /**
   * 获取表格的滚动状态
   */
  const getScroll = (): {
    virtualX: boolean
    virtualY: boolean
    scrollTop: number
    scrollLeft: number
  } => unref(methodsRef)?.getScroll()
  /**
   * 如果有滚动条，则滚动到对应的位置
   * @param {Number} scrollLeft 左距离
   * @param {Number} scrollTop 上距离
   */
  const scrollTo = (
    scrollLeft: number | null,
    scrollTop?: number | null
  ): Promise<void> => unref(methodsRef)?.scrollTo(scrollLeft, scrollTop)
  /**
   * 如果有滚动条，则滚动到对应的行
   * @param {Row} row 行对象
   * @param {ColumnInfo} fieldOrColumn 列配置
   */
  const scrollToRow = (
    row: any,
    fieldOrColumn?: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
  ): Promise<any> => unref(methodsRef)?.scrollToRow(row, fieldOrColumn)
  /**
   * 如果有滚动条，则滚动到对应的列
   */
  const scrollToColumn = (
    fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
  ): Promise<any> => unref(methodsRef)?.scrollToColumn(fieldOrColumn)
  /**
   * 手动清除滚动相关信息，还原到初始状态
   */
  const clearScroll = (): Promise<any> => unref(methodsRef)?.clearScroll()
  /**
   * 更新表尾合计
   */
  const updateFooter = (): Promise<any> => unref(methodsRef)?.updateFooter()
  /**
   * 更新列状态 updateStatus({ row, column }, cellValue)
   * 如果组件值 v-model 发生 change 时，调用改函数用于更新某一列编辑状态
   * 如果单元格配置了校验规则，则会进行校验
   */
  const updateStatus = (
    params: {
      row: any
      column: VxeTableDefines.ColumnInfo<any>
    },
    cellValue?: any
  ): Promise<any> => unref(methodsRef)?.updateStatus(params, cellValue)
  /**
   * 设置合并单元格
   * @param {TableMergeConfig[]} merges { row: Row|number, column: ColumnInfo|number, rowspan: number, colspan: number }
   */
  const setMergeCells = (
    merges:
      | VxeTableDefines.MergeOptions<any>
      | VxeTableDefines.MergeOptions<any>[]
  ): Promise<any> => unref(methodsRef)?.setMergeCells(merges)
  /**
   * 移除单元格合并
   * @param {TableMergeConfig[]} merges 多个或数组 [{row:Row|number, col:ColumnInfo|number}]
   */
  const removeMergeCells = (
    merges:
      | VxeTableDefines.MergeOptions<any>
      | VxeTableDefines.MergeOptions<any>[]
  ): Promise<VxeTableDefines.MergeInfo[]> =>
    unref(methodsRef)?.removeMergeCells(merges)
  /**
   * 获取所有被合并的单元格
   */
  const getMergeCells = (): VxeTableDefines.MergeInfo[] =>
    unref(methodsRef)?.getMergeCells()
  /**
   * 清除所有单元格合并
   */
  const clearMergeCells = (): Promise<any> =>
    unref(methodsRef)?.clearMergeCells()

  const setMergeFooterItems = (
    merges:
      | VxeTableDefines.MergeOptions<any>
      | VxeTableDefines.MergeOptions<any>[]
  ): Promise<any> => unref(methodsRef)?.setMergeFooterItems(merges)

  const removeMergeFooterItems = (
    merges:
      | VxeTableDefines.MergeOptions<any>
      | VxeTableDefines.MergeOptions<any>[]
  ): Promise<VxeTableDefines.MergeInfo[]> =>
    unref(methodsRef)?.removeMergeFooterItems(merges)
  /**
   * 获取所有被合并的表尾
   */
  const getMergeFooterItems = (): VxeTableDefines.MergeInfo[] =>
    unref(methodsRef)?.getMergeFooterItems()
  /**
   * 清除所有表尾合并
   */
  const clearMergeFooterItems = (): Promise<any> =>
    unref(methodsRef)?.clearMergeFooterItems()

  const updateCellAreas = (): Promise<any> =>
    unref(methodsRef)?.updateCellAreas()

  const getCustomStoreData = (): VxeTableDefines.CustomStoreData =>
    unref(methodsRef)?.getCustomStoreData()

  const focus = (): Promise<void> => unref(methodsRef)?.focus()

  const blur = (): Promise<void> => unref(methodsRef)?.blur()
  /**
   * 连接工具栏
   * @param toolbar
   */
  const connect = (
    toolbar: VxeToolbarConstructor | VxeToolbarInstance
  ): Promise<void> => unref(methodsRef)?.connect(toolbar)

  const dispatchEvent = (
    type: VxeTableEmits[number],
    params: Record<string, any>,
    evnt: Event | null
  ): void => {
    unref(methodsRef)?.dispatchEvent(type, params, evnt)
  }

  // const result = [register, methods];

  // result[0] = register;
  // result['methods'] = instanceRef.value;

  // return result;

  return [
    register,
    // instanceRef.value,
    {
      instance: instanceRef,
      init,
      reload,
      setTableData,
      update,
      updateCellSchema,
      // setColumns,
      // updateColumn,
      // getColumns,
      checked: checkedRef,
      saveCondition,

      insert,
      insertAt,
      validate,
      validateField,
      fullValidate,
      fullValidateField,
      dispatchEvent,
      clearAll,
      updateData,
      loadData,
      reloadData,
      setRow,
      reloadRow,
      getParams,
      loadTreeChildren,
      loadColumn,
      reloadColumn,
      getRowNode,
      getColumnNode,
      getRowSeq,
      getRowIndex,
      getVTRowIndex,
      getVMRowIndex,
      getColumnIndex,
      getVTColumnIndex,
      getVMColumnIndex,
      createData,
      createRow,
      revertData,
      clearData,
      getCellElement,
      getCellLabel,
      isInsertByRow,
      isRemoveByRow,
      removeInsertRow,
      isUpdateByRow,
      getColumns,
      getColid,
      getColumnById,
      getColumnByField,
      getParentColumn,
      getTableColumn,
      moveColumnTo,
      moveRowTo,
      getFullColumns,
      getData,
      getCheckboxRecords,
      getTreeRowChildren,
      getTreeParentRow,
      getRowById,
      getRowid,
      getTableData,
      getFullData,
      setColumnFixed,
      clearColumnFixed,
      hideColumn,
      showColumn,
      setColumnWidth,
      getColumnWidth,
      refreshColumn,
      setRowHeightConf,
      getRowHeightConf,
      setRowHeight,
      getRowHeight,
      refreshScroll,
      recalculate,
      openTooltip,
      closeTooltip,
      isAllCheckboxChecked,
      isAllCheckboxIndeterminate,
      getCheckboxIndeterminateRecords,
      setCheckboxRow,
      setCheckboxRowKey,
      isCheckedByCheckboxRow,
      isCheckedByCheckboxRowKey,
      isIndeterminateByCheckboxRow,
      isIndeterminateByCheckboxRowKey,
      toggleCheckboxRow,
      setAllCheckboxRow,
      getRadioReserveRecord,
      clearRadioReserve,
      getCheckboxReserveRecords,
      clearCheckboxReserve,
      toggleAllCheckboxRow,
      clearCheckboxRow,
      setCurrentRow,
      isCheckedByRadioRow,
      isCheckedByRadioRowKey,
      setRadioRow,
      setRadioRowKey,
      clearCurrentRow,
      clearRadioRow,
      getCurrentRecord,
      getSelectedRows,
      getCurrentColumn,
      setCurrentColumn,
      clearCurrentColumn,
      setPendingRow,
      togglePendingRow,
      isPendingByRow,
      getPendingRecords,
      clearPendingRow,
      sort,
      setSort,
      setSortByEvent,
      clearSort,
      clearSortByEvent,
      isSort,
      getSortColumns,
      setFilterByEvent,
      closeFilter,
      isActiveFilterByColumn,
      clearFilterByEvent,
      isRowExpandLoaded,
      clearRowExpandLoaded,
      reloadRowExpand,
      toggleRowExpand,
      setAllRowExpand,
      setRowExpand,
      isRowExpandByRow,
      clearRowExpand,
      clearRowExpandReserve,
      getRowExpandRecords,
      setRowGroups,
      clearRowGroups,
      isRowGroupRecord,
      isRowGroupExpandByRow,
      setRowGroupExpand,
      setAllRowGroupExpand,
      clearRowGroupExpand,
      getTreeExpandRecords,
      isTreeExpandLoaded,
      clearTreeExpandLoaded,
      reloadTreeExpand,
      toggleTreeExpand,
      setAllTreeExpand,
      setTreeExpand,
      isTreeExpandByRow,
      clearTreeExpand,
      clearTreeExpandReserve,
      getScroll,
      scrollTo,
      scrollToRow,
      scrollToColumn,
      clearScroll,
      updateFooter,
      updateStatus,
      setMergeCells,
      removeMergeCells,
      getMergeCells,
      clearMergeCells,
      setMergeFooterItems,
      removeMergeFooterItems,
      getMergeFooterItems,
      clearMergeFooterItems,
      updateCellAreas,
      getCustomStoreData,
      focus,
      blur,
      connect,
    },
  ]
}
