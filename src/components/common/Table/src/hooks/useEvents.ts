import { ComputedRef, nextTick, ref, Ref, unref } from 'vue'
import {
    ValueOf,
    VxeColumnPropTypes,
    VxeTableDefines,
    VxeTableEmits,
    VxeTableInstance,
    VxeTablePropTypes,
    VxeToolbarConstructor,
    VxeToolbarInstance,
} from 'vxe-table'
import { BaseTableProps, VxeTableMethods } from '../types'
import { convertToOnEventName } from '../helper'
import { get } from 'xe-utils'
import { cloneDeep, isEqual, isFunction, merge, set } from 'lodash-es'
import { math, toNum } from '@/utils/math'
import { useGlobDialogForm } from '@/components/common/DialogForm'

export interface IEvents extends VxeTableMethods {
    checkedRef: Ref<Recordable>
    coverEvent: (events: Recordable) => void
}
export function useEvents(
    tableRef: Ref<VxeTableInstance>,
    getProps: ComputedRef<BaseTableProps>,
    { columnsRef, setColumns, schemaMaps }
): IEvents {
    const checkedRef = ref()
    let currentInnerInput

    function coverEvent(events: Recordable) {
        // 保存原始事件处理器
        const originCheckboxChange = events.onCheckboxChange
        const originRadioChange = events.onRadioChange
        const originCheckboxAll = events.onCheckboxAll
        const originCellMouseenter = events.onCellMouseenter
        const originCellMouseleave = events.onCellMouseleave
        const originColumnResizableChange = events.onColumnResizableChange
        const originColumnDragend = events.onColumnDragend
        const originKeydown = events.onKeydown
        const originMenuClick = events.onMenuClick

        // 新事件处理器
        const newHandlers = {
            onCheckboxChange: (record) => {
                const { checkType } = unref(getProps)
                const newChecked =
                    checkType === 'checkbox' ? record.records : record.newValue

                checkedRef.value = newChecked

                // 调用原始处理器
                originCheckboxChange?.call(null, record)
            },
            onRadioChange: (record) => {
                const { checkType } = unref(getProps)
                const newChecked =
                    checkType === 'checkbox'
                        ? record.records
                        : record.newValue
                            ? [record.newValue]
                            : null

                checkedRef.value = newChecked

                // 调用原始处理器
                originRadioChange?.call(null, record)
            },
            onCheckboxAll: (record) => {
                const { checkType } = unref(getProps)
                if (checkType === 'checkbox') {
                    checkedRef.value = record.records
                }
                originCheckboxAll?.call(null, record)
            },
            onCellMouseenter: (record) => {
                // 移除所有已存在的 mouseenter 类
                const prevCells = tableRef.value?.$el?.querySelectorAll('.mouseenter')
                prevCells.forEach((cell) => cell.classList.remove('mouseenter'))

                // 添加当前单元格的类
                const currentCell = record.cell
                currentCell?.classList.add('mouseenter')

                // 调用原始逻辑（如需要）
                originCellMouseenter?.call(null, record)
            },
            onCellMouseleave: (record) => {
                // 移除当前单元格的类
                const currentCell = record.cell
                currentCell.classList.remove('mouseenter')

                // 调用原始逻辑（如需要）
                originCellMouseleave?.call(null, record)
            },
            onColumnResizableChange: (record) => {
                const currentColumn = unref(columnsRef)?.find(
                    (c) => c.field === record?.column.field
                )
                set(currentColumn, 'width', record?.resizeWidth)
                setColumns(unref(columnsRef), { refresh: false })

                // 调用原始逻辑（如需要）
                originColumnResizableChange?.call(null, record)
            },
            onColumnDragend: (record) => {
                const excludes = ['checkbox', 'index']
                const fullColumns = getFullColumns()

                const columns =
                    fullColumns
                        ?.filter((column) => !excludes.includes(column.type))
                        ?.map((column) => {
                            return (
                                columnsRef.value?.find((item) => item.field === column.field) ||
                                column
                            )
                        }) || []

                setColumns(columns, { refresh: false })

                // 调用原始逻辑（如需要）
                originColumnDragend?.call(null, record)
            },
            // onCellMouseleave: (record) => {
            //     const cell = record.cell;
            //     cell?.classList.remove('mouseenter');
            //     originCellMouseleave?.call(null, record);
            // },
            onKeydown: (record) => {
                // ArrowRight ArrowLeft ArrowDown ArrowUp Enter
                keyboardEvents(record.code)

                originKeydown?.call(null, record)
            },
            onMenuClick: (record) => {
                const { row, column, menu, rowIndex } = record
                const val = get(row, column.field)
                const code = menu.code
                const tableData = getTableData()?.fullData || []

                switch (code) {
                    case 'addRow':
                        break
                    case 'addRowAbove':
                        tableData?.splice(rowIndex, 0, {})
                        loadData(tableData)
                        break
                    case 'addRowBellow':
                        tableData?.splice(rowIndex + 1, 0, {})
                        loadData(tableData)
                        break
                    case 'removeRow':
                        const records = getTableData()?.fullData || []
                        const index = records.findIndex((r) => r == row)

                        records?.splice(index, 1)
                        loadData(records)
                        break
                    case 'emptyRow':
                        // 清空对象内容但不改变引用
                        Object.keys(row).forEach((key) => {
                            if (key.startsWith('_')) return
                            delete row[key] // 删除所有属性
                        })

                        break
                    case 'emptyRange':
                        set(row, column.field, '')
                        break
                    case 'copyUpRow':
                        const upRow = cloneDeep(row)
                        Object.keys(upRow).forEach((key) => {
                            if (!key.startsWith('_')) return
                            delete row[key]
                        })
                        console.log('newRow', upRow)
                        tableData?.splice(rowIndex, 0, upRow)
                        loadData(tableData)
                        break
                    case 'copyRow':
                        copyRow(row)
                        break
                    case 'copyDownRow':
                        const downRow = cloneDeep(row)
                        Object.keys(downRow).forEach((key) => {
                            if (!key.startsWith('_')) return
                            delete row[key]
                        })
                        tableData?.splice(rowIndex + 1, 0, downRow)
                        loadData(tableData)
                        break
                    case 'fillColumnData':
                        tableData?.forEach((item) => {
                            set(item, column.field, val)
                        })
                        break
                    case 'fillIncreaseData':
                        tableData?.forEach((item, index) => {
                            set(item, column.field, math.add(val, index))
                        })
                        break
                }

                originMenuClick?.call(null, record)
            },
        }

        // 合并而非覆盖
        return { ...events, ...newHandlers }
    }

    function copyRow(row) {
        const { openDialogForm } = useGlobDialogForm()

        const api = openDialogForm({
            dialogProps: {
                title: '要复制的行数',
                size: 'mini',
            },
            fields: [
                {
                    field: '_number',
                    component: 'input',
                    defaultValue: '1',
                    span: true,
                },
            ],
            submit(params) {
                const n = params._number
                const tableData = getTableData()?.fullData || []

                for (let i = 0; i < (toNum(n) || 0); i++) {
                    const copyRow = cloneDeep(row)
                    Object.keys(copyRow).forEach((key) => {
                        if (!key.startsWith('_')) return
                        delete row[key]
                    })
                    tableData.push(copyRow)
                }

                return loadData(tableData)
            },
        })
    }

    function updateCellSchema(
        field: string,
        row: Recordable,
        schema: Recordable
    ) {
        const rowMaps = schemaMaps.get(row)
        const currentSchema = rowMaps.get(field)

        if (!currentSchema) return

        merge(currentSchema, schema)
    }

    const dispatchEvent = (
        type: ValueOf<VxeTableEmits>,
        params: Record<string, any>,
        evnt: Event | null
    ) => unref(tableRef)?.dispatchEvent(type, params, evnt)

    const getEl = () => {
        return unref(tableRef)?.getEl()
    }

    async function keyboardEvents(keyCode: string) {
        const keys = [
            'ArrowRight',
            'ArrowLeft',
            'ArrowDown',
            'ArrowUp',
            'Enter',
            'NumpadEnter',
        ]
        if (!keys.includes(keyCode)) return

        currentInnerInput?.blur()

        const { keyboardEventsConfig } = unref(getProps)
        const { afterEnter, afterDown, enterField, nextField, fields } =
            keyboardEventsConfig || {}
        if (fields && !fields?.length) return

        try {
            setTimeout(async () => {
                const activeCell = unref(tableRef)?.getSelectedCell()
                const { column, row } = activeCell || {}
                const rowIndex = unref(tableRef)?.getRowIndex(row)
                const cell = unref(tableRef)?.getCellElement(row, column)

                const { field } = activeCell?.column
                const enterKey = enterField

                if (fields && !fields?.includes(field)) return

                if (
                    (!enterKey || enterKey === column.field) &&
                    ['NumpadEnter', 'Enter'].includes(keyCode)
                ) {
                    // 每次回车，下一个输入框的索引
                    let nextRow = getFullData()?.[rowIndex + 1]
                    const columns = unref(tableRef)?.getColumns()
                    const nextColumn = nextField
                        ? columns?.find((item) => item.field === nextField)
                        : column

                    if (isFunction(afterEnter)) {
                        afterEnter(row, nextRow, column)
                        await nextTick()
                        await nextTick()
                        nextRow = getFullData()?.[rowIndex + 1]
                    }

                    setTimeout(() => {
                        unref(tableRef)?.setSelectCell(nextRow, nextColumn)
                        keyboardEvents('ArrowDown')
                    })
                } else if (['ArrowDown'].includes(keyCode)) {
                    let preRow = getFullData()?.[rowIndex - 1]
                    let row = getFullData()?.[rowIndex]

                    if (isFunction(afterDown)) {
                        afterDown(preRow, row, field)
                    }

                    setInnerInput(cell)
                } else {
                    setInnerInput(cell)
                }
            })
        } catch (error) {
            console.log('err', error)
        }
    }

    function setInnerInput(tr) {
        try {
            const inputEl =
                tr.querySelector('.inner-input input') ||
                tr.querySelector('.inner-input')
            currentInnerInput = inputEl
            inputEl?.focus()

            return true
        } catch (error) {
            return false
        }
    }

    /**
     * 用于 edit-config，激活单元格编辑
     */
    function setEditCell(
        row: Recordable,
        fieldOrColumn: string | Recordable
    ): Promise<{
        row: any
        rows: any[]
    }> {
        return unref(tableRef)?.setEditCell(row, fieldOrColumn)
    }

    /**
     * 用于 mouse-config.selected，选中指定的单元格
     */
    function setSelectCell(
        row: Recordable,
        fieldOrColumn: string | Recordable
    ): Promise<{
        row: any
        rows: any[]
    }> {
        return unref(tableRef)?.setSelectCell(row, fieldOrColumn)
    }

    const insert = (
        records: any
    ): Promise<{
        row: any
        rows: any[]
    }> => {
        return unref(tableRef)?.insert(records)
    }

    const insertAt = (
        records: any,
        targetRow?: any | -1 | null
    ): Promise<{
        row: any
        rows: any[]
    }> => {
        return unref(tableRef)?.insertAt(records, targetRow)
    }

    const validate = (
        rows?:
            | boolean
            | object
            | any[]
            | ((errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void),
        callback?: (errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void
    ): Promise<Recordable> => {
        return unref(tableRef)?.validate(rows, callback)
    }

    const fullValidate = (
        rows?:
            | boolean
            | object
            | any[]
            | ((errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void),
        callback?: (errMap?: VxeTableDefines.ValidatorErrorMapParams<any>) => void
    ): Promise<Recordable> => {
        return unref(tableRef)?.fullValidate(rows, callback)
    }

    const fullValidateField = (
        rows?: boolean | object | any[],
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ): Promise<Recordable> => {
        return unref(tableRef)?.fullValidateField(rows, fieldOrColumn)
    }

    const validateField = (
        rows?: boolean | object | any[],
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ): Promise<Recordable> => {
        return unref(tableRef)?.validateField(rows, fieldOrColumn)
    }
    /**
     * 重置表格的一切数据状态
     */
    const clearAll = (): Promise<void> => {
        return unref(tableRef)?.clearAll()
    }

    /**
     * 手动处理数据，用于手动排序与筛选
     * 对于手动更改了排序、筛选...等条件后需要重新处理数据时可能会用到
     */
    const updateData = (): void => {
        unref(tableRef)?.updateData()
    }

    /**
     * 重新加载数据，不会清空表格状态
     * @param {Array} data 数据
     */
    const loadData = (data: any[]): Promise<any> => {
        return unref(tableRef)?.loadData(data)
    }
    /**
     * 重新加载数据，会清空表格状态
     * @param {Array} data 数据
     */
    const reloadData = (data: any[]): Promise<void> => {
        return unref(tableRef)?.reloadData(data)
    }

    /**
     * 修改行数据
     */
    const setRow = (rows: any | any[], record?: any): Promise<void> => {
        return unref(tableRef)?.setRow(rows, record)
    }

    /**
     * 局部加载行数据并恢复到初始状态
     * 对于行数据需要局部更改的场景中可能会用到
     * @param {Row} row 行对象
     * @param {Object} record 新数据
     * @param {String} field 字段名
     */
    const reloadRow = (
        rows: any | any[],
        record?: any,
        field?: string
    ): Promise<void> => {
        return unref(tableRef)?.reloadRow(rows, record, field)
    }

    const getParams = (): any => {
        return unref(tableRef)?.getParams()
    }

    /**
     * 用于树结构，给行数据加载子节点
     */
    const loadTreeChildren = (row: any, children: any[]): Promise<any[]> => {
        return unref(tableRef)?.loadTreeChildren(row, children)
    }

    /**
     * 加载列配置
     * 对于表格列需要重载、局部递增场景下可能会用到
     * @param {ColumnInfo} columns 列配置
     */
    const loadColumn = (
        columns: (
            | VxeTableDefines.ColumnOptions<any>
            | VxeTableDefines.ColumnInfo<any>
        )[]
    ): Promise<any> => {
        return unref(tableRef)?.loadColumn(columns)
    }

    /**
     * 加载列配置并恢复到初始状态
     * 对于表格列需要重载、局部递增场景下可能会用到
     * @param {ColumnInfo} columns 列配置
     */
    const reloadColumn = (
        columns: (
            | VxeTableDefines.ColumnOptions<any>
            | VxeTableDefines.ColumnInfo<any>
        )[]
    ): Promise<any> => {
        return unref(tableRef)?.reloadColumn(columns)
    }

    /**
     * 根据 tr 元素获取对应的 row 信息
     * @param {Element} trElem 元素
     */
    const getRowNode = (
        trElem: HTMLElement
    ): {
        rowid: string
        item: any
        items: any[]
        index: number
        parent?: any
    } | null => {
        return unref(tableRef)?.getRowNode(trElem)
    }

    /**
     * 根据 th/td 元素获取对应的 column 信息
     * @param {Element} cell 元素
     */
    const getColumnNode = (
        cellElem: HTMLElement
    ): {
        colid: string
        item: VxeTableDefines.ColumnInfo<any>
        items: VxeTableDefines.ColumnInfo<any>[]
        index: number
        parent: VxeTableDefines.ColumnInfo<any>
    } => {
        return unref(tableRef)?.getColumnNode(cellElem)
    }

    /**
     * 根据 row 获取序号
     * @param {Row} row 行对象
     */
    const getRowSeq = (row: any): string | number => {
        return unref(tableRef)?.getRowSeq(row)
    }

    /**
     * 根据 row 获取相对于 data 中的索引
     * @param {Row} row 行对象
     */
    const getRowIndex = (row: any): number => unref(tableRef)?.getRowIndex(row)

    /**
     * 根据 row 获取相对于当前数据中的索引
     * @param {Row} row 行对象
     */
    const getVTRowIndex = (row: any): number =>
        unref(tableRef)?.getVTRowIndex(row)
    /**
     * 根据 row 获取渲染中的虚拟索引
     * @param {Row} row 行对象
     */
    const getVMRowIndex = (row: any): number =>
        unref(tableRef)?.getVMRowIndex(row)

    /**
     * 根据 column 获取相对于 columns 中的索引
     * @param {ColumnInfo} column 列配置
     */
    const getColumnIndex = (row: any): number =>
        unref(tableRef)?.getVMRowIndex(row)
    /**
     * 根据 column 获取相对于当前表格列中的索引
     * @param {ColumnInfo} column 列配置
     */
    const getVTColumnIndex = (row: any): number =>
        unref(tableRef)?.getVTColumnIndex(row)
    /**
     * 根据 column 获取渲染中的虚拟索引
     * @param {ColumnInfo} column 列配置
     */
    const getVMColumnIndex = (row: any): number =>
        unref(tableRef)?.getVMColumnIndex(row)
    /**
     * 创建 data 对象
     * 对于某些特殊场景可能会用到，会自动对数据的字段名进行检测，如果不存在就自动定义
     * @param {Array} records 新数据
     */
    const createData = (records: any[]): Promise<any[]> =>
        unref(tableRef)?.createData(records)
    /**
     * 创建 Row|Rows 对象
     * 对于某些特殊场景需要对数据进行手动插入时可能会用到
     * @param {Array/Object} records 新数据
     */
    const createRow = (records: any | any[]): Promise<any | any[]> =>
        unref(tableRef)?.createRow(records)
    /**
     * 还原数据
     * 如果不传任何参数，则还原整个表格
     * 如果传 row 则还原一行
     * 如果传 rows 则还原多行
     * 如果还额外传了 field 则还原指定的单元格数据
     */
    const revertData = (rows?: any | any[], field?: string): Promise<any> =>
        unref(tableRef)?.revertData(rows, field)
    /**
     * 清空单元格内容
     * 如果不创参数，则清空整个表格内容
     * 如果传 row 则清空一行内容
     * 如果传 rows 则清空多行内容
     * 如果还额外传了 field 则清空指定单元格内容
     * @param {Array/Row} rows 行数据
     * @param {String} field 字段名
     */
    const clearData = (rows?: any | any[], field?: string): Promise<any> =>
        unref(tableRef)?.revertData(rows, field)

    const getCellElement = (
        row: any,
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ): HTMLTableDataCellElement | null =>
        unref(tableRef)?.getCellElement(row, fieldOrColumn)

    const getCellLabel = (
        row: any,
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ): string | number | null => unref(tableRef)?.getCellLabel(row, fieldOrColumn)
    /**
     * 检查是否为临时行数据
     */
    const isInsertByRow = (row: any | null): boolean =>
        unref(tableRef)?.isInsertByRow(row)
    const isRemoveByRow = (row: any | null): boolean =>
        unref(tableRef)?.isRemoveByRow(row)
    /**
     * 删除所有新增的临时数据
     */
    const removeInsertRow = (): Promise<{
        row: any
        rows: any[]
    }> => unref(tableRef)?.removeInsertRow()
    /**
     * 检查行或列数据是否发生改变
     */
    const isUpdateByRow = (row: any, field?: string | null): boolean =>
        unref(tableRef)?.isUpdateByRow(row, field)
    /**
     * 获取表格的可视列，也可以指定索引获取列
     * @param {Number} columnIndex 索引
     */
    const getColumns = (columnIndex?: number) =>
        unref(tableRef)?.getColumns(columnIndex)
    /**
     * 根据列获取列的唯一主键
     */
    const getColid = (
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ): string | null => unref(tableRef)?.getColid(fieldOrColumn)
    /**
     * 根据列的唯一主键获取列
     * @param {String} colid 列主键
     */
    const getColumnById = (
        colid: string | null
    ): VxeTableDefines.ColumnInfo<any> => unref(tableRef)?.getColumnById(colid)
    /**
     * 根据列的字段名获取列
     * @param {String} field 字段名
     */
    const getColumnByField = (
        field: VxeColumnPropTypes.Field | null
    ): VxeTableDefines.ColumnInfo<any> => unref(tableRef)?.getColumnByField(field)

    const getParentColumn = (
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ): VxeTableDefines.ColumnInfo<any> =>
        unref(tableRef)?.getParentColumn(fieldOrColumn)
    /**
     * 获取当前表格的列
     * 收集到的全量列、全量表头列、处理条件之后的全量表头列、当前渲染中的表头列
     */
    const getTableColumn = () => unref(tableRef)?.getTableColumn()
    /**
     * 移动列到指定列的位置
     * @param fieldOrColumn
     * @param targetFieldOrColumn
     * @param options
     */
    const moveColumnTo = (
        fieldOrColumn: any,
        targetFieldOrColumn: any,
        options: any
    ): Promise<{
        status: boolean
    }> =>
        unref(tableRef)?.moveColumnTo(fieldOrColumn, targetFieldOrColumn, options)
    /**
     * 移动行到指定行的位置
     * @param rowidOrRow
     * @param targetRowidOrRow
     * @param options
     */
    const moveRowTo = (
        rowidOrRow: any,
        targetRowidOrRow: any,
        options?: {
            isCrossDrag?: boolean
            dragToChild?: boolean
            dragPos?: 'top' | 'bottom' | '' | null
        }
    ): Promise<{
        status: boolean
    }> => unref(tableRef)?.moveRowTo(rowidOrRow, targetRowidOrRow, options)
    /**
     * 获取表格的全量列
     */
    const getFullColumns = () => unref(tableRef)?.getFullColumns()
    /**
     * 获取数据，和 data 的行为一致，也可以指定索引获取数据
     */
    const getData = (rowIndex?: number): any => unref(tableRef)?.getData(rowIndex)
    /**
     * 用于多选行，获取已选中的数据
     */
    const getCheckboxRecords = (isFull?: boolean): any[] =>
        unref(tableRef)?.getCheckboxRecords(isFull)
    /**
     * 只对 tree-config 有效，获取行的子级
     */
    const getTreeRowChildren = (rowOrRowid: any): any[] =>
        unref(tableRef)?.getTreeRowChildren(rowOrRowid)
    /**
     * 只对 tree-config 有效，获取行的父级
     */
    const getTreeParentRow = (rowOrRowid: any): any =>
        unref(tableRef)?.getTreeParentRow(rowOrRowid)

    /**
     * 根据行的唯一主键获取行
     * @param {String/Number} rowid 行主键
     */
    const getRowById = (rowid: string | number | null): any =>
        unref(tableRef)?.getRowById(rowid)
    /**
     * 根据行获取行的唯一主键
     * @param {Row} row 行对象
     */
    const getRowid = (row: any | null): string => unref(tableRef)?.getRowid(row)
    /**
     * 获取处理后的表格数据
     * 如果存在筛选条件，继续处理
     * 如果存在排序，继续处理
     */
    const getTableData = () => unref(tableRef)?.getTableData()
    /**
     * 获取表格的全量数据，如果是 tree-config 则返回带层级的树结构
     */
    const getFullData = (): any[] => unref(tableRef)?.getFullData()
    /**
     * 设置为固定列
     */
    const setColumnFixed = (
        fieldOrColumns:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[],
        fixed: VxeColumnPropTypes.Fixed
    ): Promise<void> => unref(tableRef)?.setColumnFixed(fieldOrColumns, fixed)
    /**
     * 取消指定固定列
     */
    const clearColumnFixed = (
        fieldOrColumns:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ): Promise<void> => unref(tableRef)?.clearColumnFixed(fieldOrColumns)
    /**
     * 隐藏指定列
     */
    const hideColumn = (
        fieldOrColumns:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ): Promise<void> => unref(tableRef)?.hideColumn(fieldOrColumns)
    /**
     * 显示指定列
     */
    const showColumn = (
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[]
    ): Promise<void> => unref(tableRef)?.showColumn(fieldOrColumn)

    const setColumnWidth = (
        fieldOrColumns:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | VxeColumnPropTypes.Field[]
            | VxeTableDefines.ColumnInfo<any>[],
        width: number | string
    ): Promise<{
        status: boolean
    }> => unref(tableRef)?.setColumnWidth(fieldOrColumns, width)

    const getColumnWidth = (
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ): number => unref(tableRef)?.getColumnWidth(fieldOrColumn)

    /**
     * 刷新列信息
     * 将固定的列左边、右边分别靠边
     * 如果传 true 则会检查列顺序并排序
     */
    const refreshColumn = (resiveOrder?: boolean): Promise<void> =>
        unref(tableRef)?.refreshColumn(resiveOrder)

    const setRowHeightConf = (
        heightConf: Record<string, number>
    ): Promise<{
        status: boolean
    }> => unref(tableRef)?.setRowHeightConf(heightConf)

    const getRowHeightConf = (isFull?: boolean): Record<string, number> =>
        unref(tableRef)?.getRowHeightConf(isFull)

    const setRowHeight = (
        rowOrId: any | any[],
        height: number | string
    ): Promise<{
        status: boolean
    }> => unref(tableRef)?.setRowHeight(rowOrId, height)

    const getRowHeight = (rowOrId: any): number =>
        unref(tableRef)?.getRowHeight(rowOrId)
    /**
     * 刷新滚动操作，手动同步滚动相关位置（对于某些特殊的操作，比如滚动条错位、固定列不同步）
     */
    const refreshScroll = (): Promise<void> => unref(tableRef)?.refreshScroll()
    /**
     * 重新渲染布局
     * 刷新布局
     */
    const recalculate = (refull?: boolean): Promise<void> =>
        unref(tableRef)?.recalculate(refull)

    const openTooltip = (
        target: HTMLElement,
        content: string | number
    ): Promise<any> => unref(tableRef)?.openTooltip(target, content)
    /**
     * 关闭 tooltip
     */
    const closeTooltip = (): Promise<any> => unref(tableRef)?.closeTooltip()
    /**
     * 判断列头复选框是否被选中
     */
    const isAllCheckboxChecked = (): boolean =>
        unref(tableRef)?.isAllCheckboxChecked()
    /**
     * 判断列头复选框是否被半选
     */
    const isAllCheckboxIndeterminate = (): boolean =>
        unref(tableRef)?.isAllCheckboxIndeterminate()
    /**
     * 获取复选框半选状态的行数据
     */
    const getCheckboxIndeterminateRecords = (isFull?: boolean): any[] =>
        unref(tableRef)?.getCheckboxIndeterminateRecords(isFull)
    /**
     * 用于多选行，设置行为选中状态，第二个参数为选中与否
     * @param {Array/Row} rows 行数据
     * @param {Boolean} value 是否选中
     */
    const setCheckboxRow = (rows: any | any[], checked: boolean): Promise<any> =>
        unref(tableRef)?.setCheckboxRow(rows, checked)

    const setCheckboxRowKey = (
        keys: string | number | (string | number)[] | null | undefined,
        checked: boolean
    ): Promise<any> => unref(tableRef)?.setCheckboxRowKey(keys, checked)

    const isCheckedByCheckboxRow = (row: any): boolean =>
        unref(tableRef)?.isCheckedByCheckboxRow(row)

    const isCheckedByCheckboxRowKey = (
        key: string | number | null | undefined
    ): boolean => unref(tableRef)?.isCheckedByCheckboxRowKey(key)

    const isIndeterminateByCheckboxRow = (row: any): boolean =>
        unref(tableRef)?.isIndeterminateByCheckboxRow(row)

    const isIndeterminateByCheckboxRowKey = (
        key: string | number | null | undefined
    ): boolean => unref(tableRef)?.isIndeterminateByCheckboxRowKey(key)
    /**
     * 多选，切换某一行的选中状态
     */
    const toggleCheckboxRow = (row: any): Promise<any> =>
        unref(tableRef)?.toggleCheckboxRow(row)
    /**
     * 用于多选行，设置所有行的选中状态
     * @param {Boolean} checked 是否选中
     */
    const setAllCheckboxRow = (checked: boolean): Promise<any> =>
        unref(tableRef)?.setAllCheckboxRow(checked)
    /**
     * 获取单选框保留选中的行
     */
    const getRadioReserveRecord = (isFull?: boolean): any =>
        unref(tableRef)?.getRadioReserveRecord(isFull)

    const clearRadioReserve = (): Promise<any> =>
        unref(tableRef)?.clearRadioReserve()
    /**
     * 获取复选框保留选中的行
     */
    const getCheckboxReserveRecords = (isFull?: boolean): any[] =>
        unref(tableRef)?.getCheckboxReserveRecords(isFull)

    const clearCheckboxReserve = (): Promise<any> =>
        unref(tableRef)?.clearCheckboxReserve()
    /**
     * 多选，切换所有行的选中状态
     */
    const toggleAllCheckboxRow = (): Promise<any> =>
        unref(tableRef)?.toggleAllCheckboxRow()
    /**
     * 用于多选行，手动清空用户的选择
     * 清空行为不管是否被禁用还是保留记录，都将彻底清空选中状态
     */
    const clearCheckboxRow = (): Promise<any> =>
        unref(tableRef)?.clearCheckboxRow()
    /**
     * 用于当前行，设置某一行为高亮状态
     * @param {Row} row 行对象
     */
    const setCurrentRow = (row: any): Promise<any> =>
        unref(tableRef)?.setCurrentRow(row)

    const isCheckedByRadioRow = (row: any | null): boolean =>
        unref(tableRef)?.isCheckedByRadioRow(row)

    const isCheckedByRadioRowKey = (
        key: string | number | null | undefined
    ): boolean => unref(tableRef)?.isCheckedByRadioRowKey(key)
    /**
     * 用于单选行，设置某一行为选中状态
     * @param {Row} row 行对象
     */
    const setRadioRow = (row: any): Promise<any> =>
        unref(tableRef)?.setRadioRow(row)
    /**
     * 用于单选行，设置某一行为选中状态
     * @param key 行主键
     */
    const setRadioRowKey = (
        key: string | number | null | undefined
    ): Promise<any> => unref(tableRef)?.setRadioRowKey(key)
    /**
     * 用于当前行，手动清空当前高亮的状态
     */
    const clearCurrentRow = (): Promise<any> => unref(tableRef)?.clearCurrentRow()
    /**
     * 用于单选行，手动清空用户的选择
     */
    const clearRadioRow = (): Promise<any> => unref(tableRef)?.clearRadioRow()
    /**
     * 用于当前行，获取当前行的数据
     */
    const getCurrentRecord = (): any => unref(tableRef)?.getCurrentRecord()
    /**
     * 用于单选行，获取当已选中的数据
     */
    const getRadioRecord = (isFull?: boolean): any =>
        unref(tableRef)?.getRadioRecord(isFull)

    const getCurrentColumn = () => unref(tableRef)?.getCurrentColumn()
    /**
     * 用于当前列，设置某列行为高亮状态
     */
    const setCurrentColumn = (
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ): Promise<void> => unref(tableRef)?.setCurrentColumn(fieldOrColumn)
    /**
     * 用于当前列，手动清空当前高亮的状态
     */
    const clearCurrentColumn = (): Promise<void> =>
        unref(tableRef)?.clearCurrentColumn()

    const setPendingRow = (rows: any | any[], status: boolean): Promise<any> =>
        unref(tableRef)?.setPendingRow(rows, status)

    const togglePendingRow = (rows: any | any[]): Promise<any> =>
        unref(tableRef)?.togglePendingRow(rows)

    const isPendingByRow = (row: any): boolean =>
        unref(tableRef)?.isPendingByRow(row)

    const getPendingRecords = (): any[] => unref(tableRef)?.getPendingRecords()

    const clearPendingRow = (): Promise<any> => unref(tableRef)?.clearPendingRow()

    const sort = (
        field: string,
        order?: VxeTablePropTypes.SortOrder
    ): Promise<void> => unref(tableRef)?.sort(field, order)

    const setSort = (
        sortConfs: VxeTableDefines.SortConfs | VxeTableDefines.SortConfs[],
        update?: boolean
    ): Promise<void> => unref(tableRef)?.setSort(sortConfs, update)

    const setSortByEvent = (
        event: Event,
        sortConfs: VxeTableDefines.SortConfs | VxeTableDefines.SortConfs[],
        update?: boolean
    ): Promise<void> => unref(tableRef)?.setSortByEvent(event, sortConfs, update)
    /**
     * 清空指定列的排序条件
     * 如果为空则清空所有列的排序条件
     * @param {String} fieldOrColumn 列或字段名
     */
    const clearSort = (
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ): Promise<void> => unref(tableRef)?.clearSort(fieldOrColumn)

    const clearSortByEvent = (
        event: Event,
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ): Promise<void> => unref(tableRef)?.clearSortByEvent(event, fieldOrColumn)

    const isSort = (
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ): boolean => unref(tableRef)?.isSort(fieldOrColumn)

    const getSortColumns = (): VxeTableDefines.SortCheckedParams[] =>
        unref(tableRef)?.getSortColumns()

    const setFilterByEvent = (
        event: Event,
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>,
        options: VxeColumnPropTypes.FilterItem[],
        update?: boolean
    ): Promise<void> =>
        unref(tableRef)?.setFilterByEvent(event, fieldOrColumn, options, update)
    /**
     * 关闭筛选
     * @param {Event} evnt 事件
     */
    const closeFilter = (): Promise<any> => unref(tableRef)?.closeFilter()
    /**
     * 判断指定列是否为筛选状态，如果为空则判断所有列
     * @param {String} fieldOrColumn 字段名
     */
    const isActiveFilterByColumn = (
        fieldOrColumn:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ): boolean => unref(tableRef)?.isActiveFilterByColumn(fieldOrColumn)

    const clearFilterByEvent = (
        event: Event,
        fieldOrColumn?:
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo<any>
            | null
    ): Promise<void> => unref(tableRef)?.clearFilterByEvent(event, fieldOrColumn)
    /**
     * 判断展开行是否懒加载完成
     * @param {Row} row 行对象
     */
    const isRowExpandLoaded = (row: any | null): boolean =>
        unref(tableRef)?.isRowExpandLoaded(row)

    const clearRowExpandLoaded = (row: any): Promise<void> =>
        unref(tableRef)?.clearRowExpandLoaded(row)
    /**
     * 重新懒加载展开行，并展开内容
     * @param {Row} row 行对象
     */
    const reloadRowExpand = (row: any): Promise<void> =>
        unref(tableRef)?.reloadRowExpand(row)

    /**
     * 切换展开行
     */
    const toggleRowExpand = (row: any): Promise<void> =>
        unref(tableRef)?.toggleRowExpand(row)
    /**
     * 设置所有行的展开与否
     * @param {Boolean} expanded 是否展开
     */
    const setAllRowExpand = (expanded: boolean): Promise<void> =>
        unref(tableRef)?.setAllRowExpand(expanded)
    /**
     * 设置展开行，二个参数设置这一行展开与否
     * 支持单行
     * 支持多行
     * @param {Array/Row} rows 行数据
     * @param {Boolean} expanded 是否展开
     */
    const setRowExpand = (rows: any | any[], expanded: boolean): Promise<void> =>
        unref(tableRef)?.setRowExpand(rows, expanded)
    /**
     * 判断行是否为展开状态
     * @param {Row} row 行对象
     */
    const isRowExpandByRow = (row: any | null): boolean =>
        unref(tableRef)?.isRowExpandByRow(row)
    /**
     * 手动清空展开行状态，数据会恢复成未展开的状态
     */
    const clearRowExpand = (): Promise<void> => unref(tableRef)?.clearRowExpand()

    const clearRowExpandReserve = (): Promise<void> =>
        unref(tableRef)?.clearRowExpandReserve()

    const getRowExpandRecords = (): any[] =>
        unref(tableRef)?.getRowExpandRecords()

    const setRowGroups = (
        fieldOrColumns:
            | (VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo)[]
            | VxeColumnPropTypes.Field
            | VxeTableDefines.ColumnInfo
            | null
    ): Promise<void> => unref(tableRef)?.setRowGroups(fieldOrColumns)

    const clearRowGroups = (): Promise<void> => unref(tableRef)?.clearRowGroups()

    const isRowGroupRecord = (row: any): boolean =>
        unref(tableRef)?.isRowGroupRecord(row)

    const isRowGroupExpandByRow = (row: any): boolean =>
        unref(tableRef)?.isRowGroupExpandByRow(row)

    const setRowGroupExpand = (
        rows: any | any[],
        expanded: boolean
    ): Promise<void> => unref(tableRef)?.setRowGroupExpand(rows, expanded)

    const setAllRowGroupExpand = (expanded: boolean): Promise<void> =>
        unref(tableRef)?.setAllRowGroupExpand(expanded)

    const clearRowGroupExpand = (): Promise<void> =>
        unref(tableRef)?.clearRowGroupExpand()

    const getTreeExpandRecords = (): any[] =>
        unref(tableRef)?.getTreeExpandRecords()
    /**
     * 判断树节点是否懒加载完成
     * @param {Row} row 行对象
     */
    const isTreeExpandLoaded = (row: any | null): boolean =>
        unref(tableRef)?.isTreeExpandLoaded(row)

    const clearTreeExpandLoaded = (rows: any | any[]): Promise<any> =>
        unref(tableRef)?.clearTreeExpandLoaded(rows)
    /**
     * 重新懒加载树节点，并展开该节点
     * @param {Row} row 行对象
     */
    const reloadTreeExpand = (row: any): Promise<any> =>
        unref(tableRef)?.reloadTreeExpand(row)
    /**
     * 切换/展开树节点
     */
    const toggleTreeExpand = (row: any): Promise<any> =>
        unref(tableRef)?.toggleTreeExpand(row)
    /**
     * 设置所有树节点的展开与否
     * @param {Boolean} expanded 是否展开
     */
    const setAllTreeExpand = (expanded: boolean): Promise<void> =>
        unref(tableRef)?.setAllTreeExpand(expanded)
    /**
     * 设置展开树形节点，二个参数设置这一行展开与否
     * 支持单行
     * 支持多行
     * @param {Array/Row} rows 行数据
     * @param {Boolean} expanded 是否展开
     */
    const setTreeExpand = (rows: any | any[], expanded: boolean): Promise<void> =>
        unref(tableRef)?.setTreeExpand(rows, expanded)
    /**
     * 判断行是否为树形节点展开状态
     * @param {Row} row 行对象
     */
    const isTreeExpandByRow = (row: any | null): boolean =>
        unref(tableRef)?.isTreeExpandByRow(row)
    /**
     * 手动清空树形节点的展开状态，数据会恢复成未展开的状态
     */
    const clearTreeExpand = (): Promise<void> =>
        unref(tableRef)?.clearTreeExpand()
    const clearTreeExpandReserve = (): Promise<void> =>
        unref(tableRef)?.clearTreeExpandReserve()
    /**
     * 获取表格的滚动状态
     */
    const getScroll = (): {
        virtualX: boolean
        virtualY: boolean
        scrollTop: number
        scrollLeft: number
    } => unref(tableRef)?.getScroll()
    /**
     * 如果有滚动条，则滚动到对应的位置
     * @param {Number} scrollLeft 左距离
     * @param {Number} scrollTop 上距离
     */
    const scrollTo = (
        scrollLeft: number | null,
        scrollTop?: number | null
    ): Promise<void> => unref(tableRef)?.scrollTo(scrollLeft, scrollTop)
    /**
     * 如果有滚动条，则滚动到对应的行
     * @param {Row} row 行对象
     * @param {ColumnInfo} fieldOrColumn 列配置
     */
    const scrollToRow = (
        row: any,
        fieldOrColumn?: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ): Promise<any> => unref(tableRef)?.scrollToRow(row, fieldOrColumn)
    /**
     * 如果有滚动条，则滚动到对应的列
     */
    const scrollToColumn = (
        fieldOrColumn: VxeColumnPropTypes.Field | VxeTableDefines.ColumnInfo<any>
    ): Promise<any> => unref(tableRef)?.scrollToColumn(fieldOrColumn)
    /**
     * 手动清除滚动相关信息，还原到初始状态
     */
    const clearScroll = (): Promise<any> => unref(tableRef)?.clearScroll()
    /**
     * 更新表尾合计
     */
    const updateFooter = (): Promise<any> => unref(tableRef)?.updateFooter()
    /**
     * 更新列状态 updateStatus({ row, column }, cellValue)
     * 如果组件值 v-model 发生 change 时，调用改函数用于更新某一列编辑状态
     * 如果单元格配置了校验规则，则会进行校验
     */
    const updateStatus = (
        params: {
            row: any
            column: VxeTableDefines.ColumnInfo<any>
        },
        cellValue?: any
    ): Promise<any> => unref(tableRef)?.updateStatus(params, cellValue)
    /**
     * 设置合并单元格
     * @param {TableMergeConfig[]} merges { row: Row|number, column: ColumnInfo|number, rowspan: number, colspan: number }
     */
    const setMergeCells = (
        merges:
            | VxeTableDefines.MergeOptions<any>
            | VxeTableDefines.MergeOptions<any>[]
    ): Promise<any> => unref(tableRef)?.setMergeCells(merges)
    /**
     * 移除单元格合并
     * @param {TableMergeConfig[]} merges 多个或数组 [{row:Row|number, col:ColumnInfo|number}]
     */
    const removeMergeCells = (
        merges:
            | VxeTableDefines.MergeOptions<any>
            | VxeTableDefines.MergeOptions<any>[]
    ): Promise<VxeTableDefines.MergeInfo[]> =>
        unref(tableRef)?.removeMergeCells(merges)
    /**
     * 获取所有被合并的单元格
     */
    const getMergeCells = (): VxeTableDefines.MergeInfo[] =>
        unref(tableRef)?.getMergeCells()
    /**
     * 清除所有单元格合并
     */
    const clearMergeCells = (): Promise<any> => unref(tableRef)?.clearMergeCells()

    const setMergeFooterItems = (
        merges:
            | VxeTableDefines.MergeOptions<any>
            | VxeTableDefines.MergeOptions<any>[]
    ): Promise<any> => unref(tableRef)?.setMergeFooterItems(merges)

    const removeMergeFooterItems = (
        merges:
            | VxeTableDefines.MergeOptions<any>
            | VxeTableDefines.MergeOptions<any>[]
    ): Promise<VxeTableDefines.MergeInfo[]> =>
        unref(tableRef)?.removeMergeFooterItems(merges)
    /**
     * 获取所有被合并的表尾
     */
    const getMergeFooterItems = (): VxeTableDefines.MergeInfo[] =>
        unref(tableRef)?.getMergeFooterItems()
    /**
     * 清除所有表尾合并
     */
    const clearMergeFooterItems = (): Promise<any> =>
        unref(tableRef)?.clearMergeFooterItems()

    const updateCellAreas = (): Promise<any> => unref(tableRef)?.updateCellAreas()

    const getCustomStoreData = (): VxeTableDefines.CustomStoreData =>
        unref(tableRef)?.getCustomStoreData()

    const focus = (): Promise<void> => unref(tableRef)?.focus()

    const blur = (): Promise<void> => unref(tableRef)?.blur()
    /**
     * 连接工具栏
     * @param toolbar
     */
    const connect = (
        toolbar: VxeToolbarConstructor | VxeToolbarInstance
    ): Promise<void> => unref(tableRef)?.connect(toolbar)

    return {
        checkedRef,
        coverEvent,
        updateCellSchema,

        setEditCell,
        setSelectCell,
        insert,
        insertAt,
        validate,
        validateField,
        fullValidate,
        fullValidateField,
        dispatchEvent,
        getEl,
        clearAll,
        updateData,
        loadData,
        reloadData,
        setRow,
        reloadRow,
        getParams,
        loadTreeChildren,
        loadColumn,
        reloadColumn,
        getRowNode,
        getColumnNode,
        getRowSeq,
        getRowIndex,
        getVTRowIndex,
        getVMRowIndex,
        getColumnIndex,
        getVTColumnIndex,
        getVMColumnIndex,
        createData,
        createRow,
        revertData,
        clearData,
        getCellElement,
        getCellLabel,
        isInsertByRow,
        isRemoveByRow,
        removeInsertRow,
        isUpdateByRow,
        getColumns,
        getColid,
        getColumnById,
        getColumnByField,
        getParentColumn,
        getTableColumn,
        moveColumnTo,
        moveRowTo,
        getFullColumns,
        getData,
        getCheckboxRecords,
        getTreeRowChildren,
        getTreeParentRow,
        getRowById,
        getRowid,
        getTableData,
        getFullData,
        setColumnFixed,
        clearColumnFixed,
        hideColumn,
        showColumn,
        setColumnWidth,
        getColumnWidth,
        refreshColumn,
        setRowHeightConf,
        getRowHeightConf,
        setRowHeight,
        getRowHeight,
        refreshScroll,
        recalculate,
        openTooltip,
        closeTooltip,
        isAllCheckboxChecked,
        isAllCheckboxIndeterminate,
        getCheckboxIndeterminateRecords,
        setCheckboxRow,
        setCheckboxRowKey,
        isCheckedByCheckboxRow,
        isCheckedByCheckboxRowKey,
        isIndeterminateByCheckboxRow,
        isIndeterminateByCheckboxRowKey,
        toggleCheckboxRow,
        setAllCheckboxRow,
        getRadioReserveRecord,
        clearRadioReserve,
        getCheckboxReserveRecords,
        clearCheckboxReserve,
        toggleAllCheckboxRow,
        clearCheckboxRow,
        setCurrentRow,
        isCheckedByRadioRow,
        isCheckedByRadioRowKey,
        setRadioRow,
        setRadioRowKey,
        clearCurrentRow,
        clearRadioRow,
        getCurrentRecord,
        getRadioRecord,
        getCurrentColumn,
        setCurrentColumn,
        clearCurrentColumn,
        setPendingRow,
        togglePendingRow,
        isPendingByRow,
        getPendingRecords,
        clearPendingRow,
        sort,
        setSort,
        setSortByEvent,
        clearSort,
        clearSortByEvent,
        isSort,
        getSortColumns,
        setFilterByEvent,
        closeFilter,
        isActiveFilterByColumn,
        clearFilterByEvent,
        isRowExpandLoaded,
        clearRowExpandLoaded,
        reloadRowExpand,
        toggleRowExpand,
        setAllRowExpand,
        setRowExpand,
        isRowExpandByRow,
        clearRowExpand,
        clearRowExpandReserve,
        getRowExpandRecords,
        setRowGroups,
        clearRowGroups,
        isRowGroupRecord,
        isRowGroupExpandByRow,
        setRowGroupExpand,
        setAllRowGroupExpand,
        clearRowGroupExpand,
        getTreeExpandRecords,
        isTreeExpandLoaded,
        clearTreeExpandLoaded,
        reloadTreeExpand,
        toggleTreeExpand,
        setAllTreeExpand,
        setTreeExpand,
        isTreeExpandByRow,
        clearTreeExpand,
        clearTreeExpandReserve,
        getScroll,
        scrollTo,
        scrollToRow,
        scrollToColumn,
        clearScroll,
        updateFooter,
        updateStatus,
        setMergeCells,
        removeMergeCells,
        getMergeCells,
        clearMergeCells,
        setMergeFooterItems,
        removeMergeFooterItems,
        getMergeFooterItems,
        clearMergeFooterItems,
        updateCellAreas,
        getCustomStoreData,
        focus,
        blur,
        connect,
    }
}

