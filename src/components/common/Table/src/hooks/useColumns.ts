import {
  cloneDeep,
  get,
  isArray,
  isBoolean,
  isEmpty,
  isEqual,
  isFunction,
  isNil,
  isString,
  omit,
  pick,
  set,
} from 'lodash-es'
import {
  computed,
  ComputedRef,
  nextTick,
  onMounted,
  reactive,
  Ref,
  ref,
  toRaw,
  unref,
} from 'vue'
import { BaseColumn, BaseTableProps } from '../types'
import { deepMerge } from '@/utils/object'
import { columnProps } from '../props'
import { getDict } from '@/utils/dict'
import { VxeTableInstance } from 'vxe-table'
import { getUUID } from '@/utils/util'
import { useStorage } from '@vueuse/core'
import { func } from 'vue-types'

const COLUMNS_STORE_KEY = '_columns_store'

export function useColumns(
  propsRef: ComputedRef<BaseTableProps>,
  tableRef: Ref<VxeTableInstance, VxeTableInstance>
) {
  const columnsRef = ref(unref(propsRef).columns) as unknown as Ref<
    BaseColumn[]
  >
  // 原始columns
  const originColumns = ref([])
  const metaData = ref([])
  // 创建字段信息映射表，便于快速查找
  const fieldMap = new Map<string, any>()

  const getColumnsRef = computed(() => {
    const columns = cloneDeep(unref(columnsRef))

    if (!columns) return []

    return columns
  })

  function isIfShow(column: BaseColumn): boolean {
    const ifShow = column.ifShow

    let isIfShow = true

    if (isBoolean(ifShow)) isIfShow = ifShow

    if (isFunction(ifShow)) isIfShow = ifShow(column)

    return isIfShow
  }

  const getViewColumns = computed(() => {
    const columns = cloneDeep(unref(getColumnsRef))

    return columns
      .filter((column) => hasPermission(column?.auth) && isIfShow(column))
      .map((column) => {
        // Support table multiple header editable
        if (column.children?.length) {
          column.children = column.children.map(reactive)
        }

        const viewComponent = ['placeholder', 'valueType']

        return reactive(
          Object.assign(
            column,
            unref(propsRef).mode === 'detail' &&
              // @ts-ignore
              !viewComponent.includes(column?.schema?.component)
              ? { schema: null, cellActionConfig: null }
              : {}
          )
        )
      })
  })

  async function fetchColumns(params?: Recordable) {
    const props = unref(propsRef)
    const { columnsApi, showFields, enableSort, columns } = props

    function _updateColumnsRef(columns) {
      const storeColumns = getStoreColumns()

      if (storeColumns?.length) {
        columnsRef.value =
          storeColumns?.map((column) => {
            const item = originColumns.value?.find(
              (item) => item.field === column.field
            )
            return item ? Object.assign(column, item) : column
          }) || []
      } else {
        columnsRef.value = columns
      }
    }

    // 如果没有columnsApi，直接使用传入的columns
    if (!columnsApi || !isFunction(columnsApi)) {
      const c = formatColumns(columns)
      originColumns.value = cloneDeep(c)

      _updateColumnsRef(c)
      return
    }

    try {
      // 获取字段元数据
      const result = await columnsApi(params)

      // 处理不同的返回格式，获取字段信息
      let fields
      if (Array.isArray(result)) {
        // 如果直接返回数组
        fields = result
      } else if (result && result.data && result.data.fields) {
        // 如果返回的是 { code, msg, data: { fields: [...] } } 格式（后端标准格式）
        fields = result.data.fields
      } else if (result && result.fields) {
        // 如果返回的是 { fields: [...] } 格式
        fields = result.fields
      } else if (result && result.data) {
        // 如果返回的是 { data: [...] } 格式
        fields = Array.isArray(result.data) ? result.data : [result.data]
      } else {
        // 其他格式
        fields = result ? [result] : []
      }

      // 如果 fields 不存在，直接使用传入的columns
      if (!fields || !Array.isArray(fields)) {
        console.warn('columnsApi 返回的数据格式不正确:', result)
        const c = formatColumns(columns)
        originColumns.value = cloneDeep(c)

        _updateColumnsRef(c)
        return
      }

      fields.forEach((field) => {
        const fieldName = field.field || field.name
        if (fieldName) {
          fieldMap.set(fieldName, field)
        }
      })

      // 保存元数据
      metaData.value = fields

      // 增强现有的columns配置
      let enhancedColumns

      if (showFields && showFields.length > 0) {
        // 如果指定了showFields，使用showFields作为基础
        enhancedColumns = showFields.map((columnConfig) => {
          const fieldName = columnConfig.field
          const fieldInfo = fieldMap.get(fieldName)

          return enhanceColumnWithFieldInfo(columnConfig, fieldInfo)
        })
      } else if (columns && columns.length > 0) {
        // 如果没有showFields但有columns，增强现有columns
        enhancedColumns = columns.map((columnConfig) => {
          const fieldName = columnConfig.field
          const fieldInfo = fieldMap.get(fieldName)

          return enhanceColumnWithFieldInfo(columnConfig, fieldInfo)
        })
      } else {
        // 如果既没有showFields也没有columns，则使用所有字段创建列
        enhancedColumns = formatColumns(fields, enableSort)
      }

      // 对处理后的列进行排序
      const sortedColumns = enhancedColumns ? sortColumns(enhancedColumns) : []

      // 查询字典
      sortedColumns?.forEach((column) => {
        const { dict } = column
        getDict(dict)
      })

      const storeColumns = getStoreColumns()
      originColumns.value = cloneDeep(fields)?.map((columnConfig) => {
        const fieldName = columnConfig.field || columnConfig.name
        const fieldInfo = fieldMap.get(fieldName)
        const storeColumn = storeColumns?.find((s) => s.field === fieldName)
        const { width, fixed } = storeColumn || {}

        let column

        const hasEnhancedColumn = sortedColumns?.find(
          (c) => c.field == fieldName
        )

        const enhancedColumn = hasEnhancedColumn
          ? null
          : enhanceColumnWithFieldInfo(columnConfig, fieldInfo)

        if (hasEnhancedColumn) {
          column = hasEnhancedColumn
        } else {
          column = enhancedColumn
        }

        // 针对性的设置 width fixed, 如果已经缓存设置缓存的值
        !isNil(width) && set(column, 'width', width)
        !isNil(fixed) && set(column, 'fixed', fixed)

        set(column, '_key', getUUID())
        return column
      })

      // 额外自定义的column也需要保存
      const originColumnsKeys = originColumns.value?.map((c) => c.field)
      const customerColumns =
        sortedColumns?.filter((c) => !originColumnsKeys.includes(c.field)) || []
      originColumns.value = [...customerColumns, ...originColumns.value]
      // originColumns.value = cloneDeep(sortedColumns);

      // 更新引用变量
      _updateColumnsRef(sortedColumns)
    } catch (error) {
      console.error('Failed to fetch or process columns:', error)
      // 出错时使用原始columns
      const c = formatColumns(columns)
      columnsRef.value = c
      originColumns.value = cloneDeep(c)
    }
  }

  function setColumns(
    columnList: Partial<BaseColumn>[] | (string | string[])[],
    opt: { refresh: boolean } = { refresh: true }
  ) {
    // 增强现有的columns配置
    let enhancedColumns
    enhancedColumns = cloneDeep(columnList)
      ?.filter((column) => column?.field)
      ?.map((columnConfig) => {
        const fieldName = columnConfig?.field
        const fieldInfo = fieldMap.get(fieldName)

        const column = enhanceColumnWithFieldInfo(columnConfig, fieldInfo)
        if (opt?.refresh) {
          if (columnsRef.value.find((c) => c.field !== fieldName))
            set(column, '_key', getUUID())
        }
        return column
      })
    // 对处理后的列进行排序
    // const sortedColumns = sortColumns(enhancedColumns);

    // 查询字典
    enhancedColumns?.forEach((column) => {
      const { dict } = column || {}
      dict && getDict(dict)
    })

    // 更新引用变量
    columnsRef.value = enhancedColumns

    const props = unref(propsRef)
    const { id } = props

    if (!id) return

    setStoreColumns(columnsRef.value)
    return
  }

  async function resetColumns() {
    setStoreColumns(null)
    await nextTick()
    await fetchColumns()
    setColumns(unref(columnsRef))
  }

  function getStoreColumns(): Recordable[] {
    const props = unref(propsRef)
    const { id } = props

    if (!id) return
    const storeColumns = useStorage(COLUMNS_STORE_KEY, {})
    const currentColumns = get(storeColumns.value, id)

    return currentColumns
  }

  function setStoreColumns(columns) {
    try {
      const props = unref(propsRef)
      const { id } = props

      if (!id) return

      const storeColumns = useStorage(COLUMNS_STORE_KEY, {})
      const currentKey = id
      set(storeColumns.value, currentKey, columns)

      useStorage(COLUMNS_STORE_KEY, storeColumns.value)
    } catch (error) {
      console.error(error)
    }
  }

  function updateColumn(column: BaseColumn) {
    unref(getColumnsRef)?.map((c) => {
      if (c.field === column.field) {
        return deepMerge(c, column)
      }
      return c
    })
  }

  function getColumns(opt?: Recordable) {
    const { ignoreIndex, ignoreAction, sortNumber, ignoreExpand } = opt || {}
    let columns = toRaw(unref(getColumnsRef))
    // if (ignoreIndex)
    //     columns = columns.filter((item) => item.type !== INDEX_COLUMN_TYPE);

    // if (ignoreAction)
    //     columns = columns.filter(
    //         (item) => item.type !== ACTION_COLUMN_TYPE
    //     );

    // if (ignoreExpand)
    //     columns = columns.filter(
    //         (item) => item.type !== EXPAND_COLUMN_TYPE
    //     );

    return columns
  }

  onMounted(async () => {
    await nextTick()
    fetchColumns()
  })

  return {
    columnsRef,
    originColumns,
    getViewColumns,
    metaData,
    fetchColumns,
    setColumns,
    updateColumn,
    getColumns,
    setStoreColumns,
    resetColumns,
  }
}

function formatColumns(fields: Recordable[], enableSort: boolean = false) {
  const extraKeys = ['field', 'title', 'width']
  const columnKeys = Object.keys(columnProps)?.filter(
    (key) => !extraKeys.includes(key)
  )
  return fields?.map((field) => {
    // 保留原始字段的所有属性
    const column = { ...field }

    // 然后设置或覆盖标准属性
    columnKeys?.forEach((key) => {
      if (field[key] !== undefined) {
        set(column, key, get(field, key))
      }
    })

    set(column, 'field', field.field || field.name)
    set(column, 'title', field.title || field.comment || field.name)
    set(column, 'width', Reflect.has(field, 'width') ? field.width : 160)

    return column
  })
}

/**
 * 根据 sortNumber 属性对对象数组进行排序
 * @param columns 需要排序的数组，元素可能包含 sortNumber 属性
 * @returns 排序后的新数组（不修改原数组）
 *
 * 排序规则：
 * 1. 优先按 sortNumber 升序排列（数值小的在前）
 * 2. sortNumber 不存在视为 Infinity（排在有值的后面）
 * 3. 相同 sortNumber 保持原数组顺序（稳定排序）
 */
function sortColumns(columns: Recordable[]): Recordable[] {
  // 创建带索引的副本数组
  const indexedColumns = columns.map((item, index) => ({ item, index }))

  // 执行稳定排序
  return indexedColumns
    .sort((a, b) => {
      // 获取排序号（不存在则视为 Infinity）
      const aSort = a.item.sortNumber ?? Infinity
      const bSort = b.item.sortNumber ?? Infinity

      // 优先按 sortNumber 排序
      if (aSort !== bSort) {
        return aSort - bSort
      }

      // sortNumber 相同时保持原顺序
      return a.index - b.index
    })
    .map(({ item }) => item) // 提取排序后的元素
}

function hasPermission(str: string | string[]) {
  return true
}

/**
 * 根据字段信息增强列配置
 * @param columnConfig 现有的列配置
 * @param fieldInfo 从API获取的字段信息
 * @returns 增强后的列配置
 */
function enhanceColumnWithFieldInfo(columnConfig: any, fieldInfo: any) {
  // 如果没有字段信息，直接返回原始配置
  if (!fieldInfo) {
    return columnConfig
  }

  // console.log(
  //     `[列增强] 字段 ${fieldInfo.name} 类型为 ${fieldInfo.type}，开始处理...`
  // );
  const enhanced = { ...columnConfig }

  !enhanced.field && set(enhanced, 'field', enhanced.field || enhanced.name)

  // 基础信息增强
  if (fieldInfo.comment && !enhanced.title) {
    enhanced.title = fieldInfo.comment
    // console.log(`  - 设置标题: ${fieldInfo.comment}`);
  }

  if (fieldInfo.nullable === false && !enhanced.required) {
    enhanced.required = true
    // console.log(`  - 设置为必填字段`);
  }

  // 根据字段类型进行增强
  if (fieldInfo.type === 'string') {
    if (!Reflect.has(enhanced, 'width')) {
      enhanced.width = 150
      // console.log(`  - 设置字符串默认宽度: 150`);
    }
  } else if (fieldInfo.type === 'integer') {
    if (!enhanced.align) enhanced.align = 'right'
    if (!Reflect.has(enhanced, 'width')) enhanced.width = 100
    // console.log(`  - 设置整数格式: 右对齐，宽度100`);
  } else if (fieldInfo.type === 'float') {
    // console.log(`  - 检测到浮点数类型，字段名: ${fieldInfo.name}`);

    // 智能识别字段用途
    const fieldName = fieldInfo.name.toLowerCase()
    let formatType = 'decimal' // 默认格式

    if (
      fieldName.includes('rate') ||
      fieldName.includes('percent') ||
      fieldName.includes('ratio')
    ) {
      formatType = 'percentage'
      // console.log(`  - 识别为百分比字段: ${fieldName}`);
    } else if (
      fieldName.includes('price') ||
      fieldName.includes('amount') ||
      fieldName.includes('cost') ||
      fieldName.includes('fee')
    ) {
      formatType = 'currency'
      // console.log(`  - 识别为货币字段: ${fieldName}`);
    } else {
      // console.log(`  - 识别为普通浮点数字段: ${fieldName}`);
    }

    if (!enhanced.formatter) {
      if (formatType === 'percentage') {
        enhanced.formatter = ({ cellValue }: any) => {
          if (cellValue == null) return ''
          const numValue = Number(cellValue)
          if (isNaN(numValue)) return cellValue

          // 智能判断是0-1小数还是0-100百分比
          if (numValue <= 1) {
            return `${(numValue * 100).toFixed(1)}%`
          } else {
            return `${numValue.toFixed(1)}%`
          }
        }
        // console.log(`  - 设置百分比格式化器`);
      } else if (formatType === 'currency') {
        enhanced.formatter = ({ cellValue }: any) => {
          if (cellValue == null) return ''
          const numValue = Number(cellValue)
          if (isNaN(numValue)) return cellValue
          return `¥${numValue.toFixed(2)}`
        }
        // console.log(`  - 设置货币格式化器`);
      } else {
        enhanced.formatter = ({ cellValue }: any) => {
          if (cellValue == null) return ''
          const numValue = Number(cellValue)
          if (isNaN(numValue)) return cellValue
          return numValue.toFixed(2)
        }
        // console.log(`  - 设置小数格式化器`);
      }
    }

    if (!enhanced.align) enhanced.align = 'right'
    if (!Reflect.has(enhanced, 'width')) enhanced.width = 120
    // console.log(`  - 设置浮点数格式: 右对齐，宽度120`);
  } else if (fieldInfo.type === 'boolean') {
    if (!enhanced.formatter) {
      enhanced.formatter = ({ cellValue }: any) => (cellValue ? '是' : '否')
      // console.log(`  - 设置布尔值格式化器`);
    }
    if (!Reflect.has(enhanced, 'width')) enhanced.width = 80
    if (!enhanced.align) enhanced.align = 'center'
    // console.log(`  - 设置布尔值格式: 居中对齐，宽度80`);
  } else if (fieldInfo.type === 'datetime') {
    if (!enhanced.formatter) {
      enhanced.formatter = ({ cellValue }: any) => {
        if (!cellValue) return ''
        const date = new Date(cellValue)
        return date.toLocaleString('zh-CN')
      }
      // console.log(`  - 设置日期时间格式化器`);
    }
    if (!Reflect.has(enhanced, 'width')) enhanced.width = 160
    // console.log(`  - 设置日期时间宽度: 160`);
  } else if (fieldInfo.type === 'enum' && fieldInfo.enum_info) {
    // console.log(
    //     `  - 检测到枚举类型，枚举值:`,
    //     fieldInfo.enum_info.enum_values
    // );
    enhanced.enumInfo = fieldInfo.enum_info
    enhanced.isEnum = true

    if (!enhanced.formatter) {
      enhanced.formatter = ({ cellValue }: any) => {
        if (!cellValue) return ''
        return fieldInfo.enum_info.enum_values[cellValue] || cellValue
      }
      // console.log(`  - 设置枚举格式化器`);
    }
    if (!Reflect.has(enhanced, 'width')) enhanced.width = 120
    // console.log(`  - 设置枚举宽度: 120`);
  } else if (fieldInfo.type === 'relation' && fieldInfo.relation_info) {
    // console.log(`  - 检测到关系类型，关系信息:`, fieldInfo.relation_info);
    enhanced.relationInfo = fieldInfo.relation_info
    enhanced.isRelation = true

    // 只有在没有formatter时才添加关系格式化
    if (!enhanced.formatter) {
      const direction = fieldInfo.relation_info.direction
      // console.log(`  - 关系方向: ${direction}`);

      if (direction === 'RelationshipDirection.ONETOMANY') {
        // 一对多关系：显示关联项目数量
        enhanced.formatter = ({ cellValue }: any) => {
          if (!cellValue) return '0项'
          if (Array.isArray(cellValue)) {
            const count = cellValue.length
            return count > 0 ? `${count}项` : '0项'
          }
          return '0项'
        }
        // console.log(`  - 设置一对多关系格式化器`);
      } else if (direction === 'RelationshipDirection.MANYTOONE') {
        // 多对一关系：显示关联对象的名称
        enhanced.formatter = ({ cellValue }: any) => {
          if (!cellValue) return '-'
          if (typeof cellValue === 'object') {
            // 优先显示 name，然后 title，最后 code
            const displayValue =
              cellValue.name ||
              cellValue.title ||
              cellValue.code ||
              cellValue.id ||
              '-'
            // console.log(
            //     `  - 多对一关系显示值: ${displayValue}`,
            //     cellValue
            // );
            return displayValue
          }
          return cellValue
        }
        // console.log(`  - 设置多对一关系格式化器`);
      } else {
        // 其他关系类型的默认处理
        enhanced.formatter = ({ cellValue }: any) => {
          if (!cellValue) return '-'
          if (Array.isArray(cellValue)) {
            return `${cellValue.length}项`
          }
          if (typeof cellValue === 'object') {
            return (
              cellValue.name ||
              cellValue.title ||
              cellValue.code ||
              cellValue.id ||
              '-'
            )
          }
          return cellValue
        }
        // console.log(`  - 设置通用关系格式化器`);
      }
    }

    // 设置关系字段的默认宽度
    if (!Reflect.has(enhanced, 'width')) {
      if (
        fieldInfo.relation_info.direction === 'RelationshipDirection.ONETOMANY'
      ) {
        enhanced.width = 100 // 一对多显示数量，宽度较小
        // console.log(`  - 设置一对多关系宽度: 100`);
      } else {
        enhanced.width = 150 // 多对一显示名称，宽度较大
        // console.log(`  - 设置多对一关系宽度: 150`);
      }
    }

    // 设置对齐方式
    if (!enhanced.align) {
      if (
        fieldInfo.relation_info.direction === 'RelationshipDirection.ONETOMANY'
      ) {
        enhanced.align = 'center' // 数量居中显示
        // console.log(`  - 设置一对多关系对齐: 居中`);
      } else {
        enhanced.align = 'left' // 名称左对齐
        // console.log(`  - 设置多对一关系对齐: 左对齐`);
      }
    }
  }

  // console.log(
  //     `[列增强] 字段 ${fieldInfo.name} 处理完成，最终配置:`,
  //     enhanced
  // );
  return enhanced
}
