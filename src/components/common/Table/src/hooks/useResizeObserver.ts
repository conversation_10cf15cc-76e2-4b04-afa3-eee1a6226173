import { ref, onMounted, unref, nextTick } from 'vue';

export function useResizeObserver(propsRef) {
    const proTableHeader = ref();
    const proTableHeaderHeight = ref(0);
    const appHeight = ref(0);
    const totalHeight = ref(0);

    onMounted(async () => {
        await nextTick();
        if (!propsRef.value.observeScrollY) return;

        const contentEl = document.querySelector('#app');
        appHeight.value = contentEl?.clientHeight || 0;

        // const resizeObserverTableSearch = new ResizeObserver((entries) => {
        //     for (let entry of entries) {
        //         // 处理高度变化的逻辑
        //         proTableHeaderHeight.value = entry.contentRect.height;
        //     }

        //     totalHeight.value =
        //         appHeight.value -
        //         unref(propsRef)?.layoutHeight -
        //         proTableHeaderHeight.value;
        // });

        // resizeObserverTableSearch.observe(proTableHeader.value);
        const resizeObserverContent = new ResizeObserver((entries) => {
            for (let entry of entries) {
                // 处理高度变化的逻辑
                appHeight.value = entry.contentRect.height;
            }

            totalHeight.value =
                (appHeight.value || 0) -
                unref(propsRef)?.layoutHeight -
                proTableHeaderHeight.value;
        });

        contentEl && resizeObserverContent.observe(contentEl);
    });

    return {
        proTableHeader,
        proTableHeaderHeight,
        totalHeight,
    };
}
