import {
  ComputedRef,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  Ref,
  ShallowRef,
  unref,
  watch,
  type SetupContext,
} from 'vue'
import { BaseTableProps } from '../types'
import { debounce, get, isArray, isBoolean, isFunction, set } from 'lodash-es'
import { Pagination } from '@/components/common/ToolsBar/Patigation.vue'

export function useDataSource(
  propsRef: ComputedRef<BaseTableProps>,
  { emit, tableRef }
) {
  const dataSourceRef = ref<Recordable[]>(propsRef.value.data)

  // 分页数据
  const pagination = ref<Partial<Pagination>>(propsRef.value.pagination)
  const loading = ref(false)

  const SearchRef = ref()
  const PaginationRef = ref()

  let originLoadData = null
  let originReloadData = null
  let originUpdateData = null

  watch(
    () => propsRef?.value?.modelValue,
    async (newVal) => {
      const table = unref(tableRef)
      if (!table) return

      // 如果需要等待数据更新完成
      // await nextTick();

      const tableData = table.getTableData()?.fullData
      if (!tableData) return

      const { dataChange } = unref(propsRef)
      if (isFunction(dataChange)) {
        dataChange(tableData)
      }
    },
    { immediate: true }
  )

  async function fetch(reloadParams?) {
    const {
      fetchApi,
      pagination: fetchSettingPagination,
      usePagination = true,
      on,
      afterFetch,
    } = unref(propsRef)
    if (!fetchApi || !isFunction(fetchApi)) return

    const startTime = Date.now()

    try {
      loading.value = true

      let pageParams: Recordable = unref(PaginationRef)?.getPage() || {}
      const { start = 0, end = 20, queryParams } = unref(fetchSettingPagination)

      const searchParams = SearchRef.value?.getRequestParams() || {}

      // getSearchData()
      // getPaginationData()

      const res = await fetchApi(
        pageParams,
        searchParams || {},
        queryParams,
        reloadParams
      )

      if (isFunction(afterFetch)) afterFetch(tableRef)

      if (res === false) return

      setTableData(res?.items)
      pagination.value.total = res.total

      // 触发数据加载完成事件
      const loadTime = Date.now() - startTime
      const eventHandler = on?.['data-loaded']
      if (eventHandler && isFunction(eventHandler)) {
        eventHandler({
          loadTime,
          data: res?.items,
          total: res.total,
          success: true,
        })
      }

      return res
    } catch (error) {
      pagination.value.total = 0

      // 触发数据加载错误事件
      const loadTime = Date.now() - startTime
      const eventHandler = on?.['data-loaded']
      if (eventHandler && isFunction(eventHandler)) {
        eventHandler({
          loadTime,
          error,
          success: false,
        })
      }
    } finally {
      loading.value = false
    }
  }

  async function reload(opt?: Recordable) {
    return await fetch(opt)
  }

  function setTableData<T = Recordable>(data: T[]) {
    const table = unref(tableRef)
    if (!table) return
    table.loadData(data)

    update()
  }

  function setValues<T = Recordable>(data: T[]) {
    const table = unref(tableRef)
    if (!table) return
    table.loadData(data)

    update()
  }

  function getData<T = Recordable>() {
    // return dataSourceRef.value as T[];
    const table = unref(tableRef)
    if (!table) return
    return table.getFullData()
  }

  const update = debounce(() => {
    const table = unref(tableRef)
    if (!table) return
    console.log('update table')

    try {
      const tableData = table.getTableData()?.fullData
      emit('update:modelValue', tableData)
    } catch (error) {
      console.error('Failed to update data:', error)
    }
  })

  function updateEvent() {
    const table = unref(tableRef)
    if (!table) return

    // 保存原始方法
    originLoadData = table.loadData
    originReloadData = table.reloadData
    originUpdateData = table.updateData

    // 劫持并增强方法
    table.loadData = (...args) => {
      const result = originLoadData.apply(table, args)
      update()
      return result
    }

    table.reloadData = (...args) => {
      const result = originReloadData.apply(table, args)
      update()
      return result
    }

    table.updateData = (...args) => {
      const result = originUpdateData.apply(table, args)
      update()
      return result
    }
  }

  function exportData() {
    const $table = tableRef.value
    if ($table) {
      $table.exportData({
        type: 'xlsx',
      })
    }
  }

  onMounted(async () => {
    await nextTick()
    updateEvent()
    unref(propsRef).immediate && fetch()
  })

  onBeforeUnmount(() => {
    const table = unref(tableRef)
    if (table) {
      // 恢复原始方法
      table.loadData = originLoadData
      table.reloadData = originReloadData
      table.updateData = originUpdateData
    }
    // 清理防抖定时器
    update.cancel()
  })

  return {
    pagination,
    loading,
    dataSourceRef,
    SearchRef,
    PaginationRef,
    fetch,
    reload,
    setTableData,
    setValues,
    getData,
    update,
    exportData,
  }
}
