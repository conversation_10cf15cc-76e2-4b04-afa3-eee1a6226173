/**
 * Table组件开发调试工具
 * 提供调试信息、性能监控、错误追踪等功能
 */

export interface TableDebugInfo {
    componentId: string;
    dataCount: number;
    columnCount: number;
    renderTime: number;
    apiCallCount: number;
    lastApiCall: Date | null;
    errors: Array<{
        time: Date;
        type: string;
        message: string;
        stack?: string;
    }>;
}

class TableDebugger {
    private debugInfo = new Map<string, TableDebugInfo>();
    private enabled = import.meta.env.DEV;

    /**
     * 注册表格组件实例
     */
    register(componentId: string) {
        if (!this.enabled) return;
        
        this.debugInfo.set(componentId, {
            componentId,
            dataCount: 0,
            columnCount: 0,
            renderTime: 0,
            apiCallCount: 0,
            lastApiCall: null,
            errors: [],
        });
    }

    /**
     * 记录数据更新
     */
    recordDataUpdate(componentId: string, dataCount: number) {
        if (!this.enabled) return;
        
        const info = this.debugInfo.get(componentId);
        if (info) {
            info.dataCount = dataCount;
        }
    }

    /**
     * 记录列配置更新
     */
    recordColumnUpdate(componentId: string, columnCount: number) {
        if (!this.enabled) return;
        
        const info = this.debugInfo.get(componentId);
        if (info) {
            info.columnCount = columnCount;
        }
    }

    /**
     * 记录API调用
     */
    recordApiCall(componentId: string) {
        if (!this.enabled) return;
        
        const info = this.debugInfo.get(componentId);
        if (info) {
            info.apiCallCount++;
            info.lastApiCall = new Date();
        }
    }

    /**
     * 记录渲染时间
     */
    recordRenderTime(componentId: string, time: number) {
        if (!this.enabled) return;
        
        const info = this.debugInfo.get(componentId);
        if (info) {
            info.renderTime = time;
        }
    }

    /**
     * 记录错误
     */
    recordError(componentId: string, error: Error, type = 'unknown') {
        if (!this.enabled) return;
        
        const info = this.debugInfo.get(componentId);
        if (info) {
            info.errors.push({
                time: new Date(),
                type,
                message: error.message,
                stack: error.stack,
            });
            
            // 只保留最近10个错误
            if (info.errors.length > 10) {
                info.errors.splice(0, info.errors.length - 10);
            }
        }

        // 在控制台输出错误信息
        console.group(`🔴 Table Error [${componentId}]`);
        console.error('Type:', type);
        console.error('Message:', error.message);
        console.error('Stack:', error.stack);
        console.groupEnd();
    }

    /**
     * 获取调试信息
     */
    getDebugInfo(componentId?: string) {
        if (!this.enabled) return null;
        
        if (componentId) {
            return this.debugInfo.get(componentId);
        }
        
        return Object.fromEntries(this.debugInfo);
    }

    /**
     * 打印调试信息到控制台
     */
    printDebugInfo(componentId?: string) {
        if (!this.enabled) return;
        
        const info = componentId 
            ? { [componentId]: this.debugInfo.get(componentId) }
            : Object.fromEntries(this.debugInfo);

        console.group('📊 Table Debug Info');
        console.table(info);
        console.groupEnd();
    }

    /**
     * 清理调试信息
     */
    cleanup(componentId: string) {
        if (!this.enabled) return;
        
        this.debugInfo.delete(componentId);
    }

    /**
     * 检查性能问题
     */
    checkPerformance(componentId: string): string[] {
        if (!this.enabled) return [];
        
        const info = this.debugInfo.get(componentId);
        if (!info) return [];

        const warnings: string[] = [];

        // 检查渲染时间
        if (info.renderTime > 500) {
            warnings.push(`渲染时间过长: ${info.renderTime}ms`);
        }

        // 检查数据量
        if (info.dataCount > 1000) {
            warnings.push(`数据量过大: ${info.dataCount} 条，建议使用虚拟滚动`);
        }

        // 检查列数量
        if (info.columnCount > 20) {
            warnings.push(`列数量过多: ${info.columnCount} 列，可能影响性能`);
        }

        // 检查API调用频率
        const now = new Date();
        if (info.lastApiCall && (now.getTime() - info.lastApiCall.getTime()) < 1000) {
            warnings.push('API调用过于频繁，建议添加防抖处理');
        }

        // 检查错误数量
        if (info.errors.length > 5) {
            warnings.push(`错误数量过多: ${info.errors.length} 个`);
        }

        return warnings;
    }
}

// 单例实例
export const tableDebugger = new TableDebugger();

/**
 * 性能监控装饰器
 */
export function withPerformanceMonitor<T extends (...args: any[]) => any>(
    fn: T,
    componentId: string,
    name: string
): T {
    return ((...args: any[]) => {
        if (!import.meta.env.DEV) {
            return fn(...args);
        }

        const start = performance.now();
        
        try {
            const result = fn(...args);
            
            // 如果是Promise，监控异步执行时间
            if (result && typeof result.then === 'function') {
                return result.finally(() => {
                    const end = performance.now();
                    console.log(`⏱️ [${componentId}] ${name}: ${(end - start).toFixed(2)}ms`);
                });
            }
            
            const end = performance.now();
            console.log(`⏱️ [${componentId}] ${name}: ${(end - start).toFixed(2)}ms`);
            
            return result;
        } catch (error) {
            const end = performance.now();
            console.error(`❌ [${componentId}] ${name} failed after ${(end - start).toFixed(2)}ms:`, error);
            throw error;
        }
    }) as T;
}

/**
 * Vue组合式函数：表格调试
 */
export function useTableDebug(componentId: string) {
    if (!import.meta.env.DEV) {
        return {
            recordDataUpdate: () => {},
            recordColumnUpdate: () => {},
            recordApiCall: () => {},
            recordRenderTime: () => {},
            recordError: () => {},
            getDebugInfo: () => null,
            printDebugInfo: () => {},
            checkPerformance: () => [],
        };
    }

    // 注册组件
    tableDebugger.register(componentId);

    return {
        recordDataUpdate: (count: number) => tableDebugger.recordDataUpdate(componentId, count),
        recordColumnUpdate: (count: number) => tableDebugger.recordColumnUpdate(componentId, count),
        recordApiCall: () => tableDebugger.recordApiCall(componentId),
        recordRenderTime: (time: number) => tableDebugger.recordRenderTime(componentId, time),
        recordError: (error: Error, type?: string) => tableDebugger.recordError(componentId, error, type),
        getDebugInfo: () => tableDebugger.getDebugInfo(componentId),
        printDebugInfo: () => tableDebugger.printDebugInfo(componentId),
        checkPerformance: () => tableDebugger.checkPerformance(componentId),
    };
}

/**
 * 开发环境下的控制台命令
 */
if (import.meta.env.DEV && typeof window !== 'undefined') {
    // 添加全局调试命令
    (window as any).__TABLE_DEBUG__ = {
        getAll: () => tableDebugger.getDebugInfo(),
        get: (id: string) => tableDebugger.getDebugInfo(id),
        print: (id?: string) => tableDebugger.printDebugInfo(id),
        check: (id: string) => tableDebugger.checkPerformance(id),
    };

    console.log('🛠️ Table调试工具已启用，使用 __TABLE_DEBUG__ 对象进行调试');
} 