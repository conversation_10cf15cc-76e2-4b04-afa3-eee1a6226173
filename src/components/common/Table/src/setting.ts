export const defaultTableSetting = {
  // 复选框配置（多选模式）
  checkboxConfig: {
    // trigger: 'row',  // 触发方式，默认点击复选框触发。若取消注释则整行点击可勾选
    highlight: true, // 高亮选中行
  },

  // 单选框配置（单选模式）
  radioConfig: {
    // trigger: 'row',  // 触发方式，默认点击单选框触发。若取消注释则整行点击可选中
    highlight: true, // 高亮选中行
  },

  checkType: 'checkbox', // 选择模式：checkbox(多选)/radio(单选)
  border: true, // 显示表格边框（网页4说明边框配置项）
  showIndex: true, // 显示序号列（网页4提到的rowNumber功能）
  enableSort: true, // 启用列排序功能（网页6提到的sortable配置）
  showOverflow: 'ellipsis', // 内容溢出显示省略号
  observeScrollY: true, // 监听垂直滚动优化渲染性能

  // height: '100%',

  // 列行为配置
  columnConfig: {
    resizable: true, // 允许调整列宽
    drag: true, // 允许拖拽调整列顺序
    useKey: true, // 使用唯一key优化列渲染
  },

  columnDragConfig: {
    trigger: 'cell',
    showIcon: false,
    showGuidesStatus: true,
  },

  // 行行为配置
  rowConfig: {
    drag: true, // 允许行拖拽排序
    useKey: true, // 使用唯一key优化行渲染
    isCurrent: true, // 高亮当前行
  },

  // 提示工具配置（留空表示使用默认配置）
  tooltipConfig: {}, // 工具提示设置

  // 行编辑配置
  editConfig: {
    trigger: 'click', // 点击单元格触发编辑
    mode: 'row', // 行编辑模式
    showIcon: true, // 显示编辑状态图标
    icon: 'vxe-icon-edit', // 指定编辑图标
  },
}

const fullEvents = [
  'addRow',
  'addRowAbove',
  'addRowBellow',
  'removeRow',
  'emptyRow',
  'emptyRange',
  'copyRow',
  'copyUpRow',
  'copyDownRow',
  'fillColumnData',
  'fillIncreaseData',
]

export function rightMouseEvent(events) {
  const finallyEvents = events ? events : fullEvents
  const fullMenuStructure = [
    [
      {
        code: 'addRow',
        name: '插入空行',
        children: [
          { code: 'addRowAbove', name: '在该行上方插入' },
          { code: 'addRowBellow', name: '在该行下方插入' },
        ],
      },
      { code: 'removeRow', name: '删除当前行' },
      { code: 'emptyRow', name: '清空当前行' },
      { code: 'emptyRange', name: '清空选中内容' },
    ],
    [
      { code: 'copyRow', name: '复制当行' },
      { code: 'copyUpRow', name: '向上复制当前行' },
      { code: 'copyDownRow', name: '向下复制当前行' },
    ],
    [
      { code: 'fillColumnData', name: '填充整列内容' },
      { code: 'fillIncreaseData', name: '填充递增值到选中区域' },
    ],
  ]

  const filteredGroups = filterGroups(fullMenuStructure, finallyEvents)

  return {
    body: {
      options: filteredGroups,
    },
  }
}

function filterGroups(groups, allowedEvents) {
  return groups
    .map((group) => {
      const filteredGroup = group
        .map((menu) => filterMenu(menu, allowedEvents))
        .filter(Boolean)
      return filteredGroup.length > 0 ? filteredGroup : null
    })
    .filter(Boolean)
}

function filterMenu(menu, allowedEvents) {
  if (!menu.children) {
    // 普通菜单项
    return allowedEvents.includes(menu.code) ? menu : null
  } else {
    // 父菜单，递归处理子菜单
    const filteredChildren = menu.children
      .map((child) => filterMenu(child, allowedEvents))
      .filter(Boolean)

    return filteredChildren.length > 0
      ? { ...menu, children: filteredChildren }
      : null
  }
}
