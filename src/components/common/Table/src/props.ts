import { PropType } from 'vue'
import { getConfig, VxeTableProps, VxeTableEmits } from 'vxe-table'
import {
  BaseColumn,
  BaseTableProps,
  TableMethods,
  TableStyleOptions,
} from './types'
import { isObject } from 'lodash-es'
import { ButtonGroupProps } from '../../ButtonGroup'

//  ---------------------------------------------以下是扩展配置 ---------------------------------------------
export const extendTableProps = {
  modelValue: {},

  showIndex: {
    type: Boolean,
    default: false,
  },
  usePagination: {
    type: Boolean,
    default: true,
  },
  pagination: {
    type: Object,
    default: () => ({
      start: 1,
      end: 20,
      total: 0,
    }),
  },

  // 立即请求接口
  immediate: { type: Boolean, default: true },

  // 请求接口
  // 请求之前调用，用于修正请求参数和阻止继续查询 返回Recordable是请求参数，返回false会阻止后续的查询
  // searchParams: 列表查询框
  // pageParams： 分页
  // otherParams：fetchSetting.queryParams 一般用于一些固定参数传递进去
  // reloadParams：api.reload 查询参数
  fetchApi: {
    type: Function as PropType<
      (
        searchParams: Recordable,
        pageParams: Recordable,
        otherParams?: Recordable,
        reloadParams?: Recordable
      ) => Promise<Recordable | false>
    >,
    default: null,
  },

  afterFetch: {
    type: Function,
  },

  columns: {
    type: Array,
  },

  // columns查询函数
  columnsApi: {
    type: Function,
  },

  // 查询columns时对columns进行编辑
  mergeColumns: {
    type: Array as PropType<(BaseColumn & { code: string })[]>,
  },

  // 显示的字段
  showFields: {
    type: Array,
  },

  // 数据变动调用
  dataChange: {
    type: Function,
  },

  // 是否支持排序
  enableSort: {
    type: Boolean,
  },

  // toolbar 按钮
  buttonList: {
    type: Array as PropType<ButtonGroupProps[]>,
  },

  // 事件绑定 对应 tableEmits
  on: {
    Object,
  },

  // 是否监听页面高度 动态设置table高度
  observeScrollY: {
    type: Boolean,
  },

  // 是否使用分页器
  showPagination: {
    type: Boolean,
    default: true,
  },

  // 是否使用搜索框
  showSearch: {
    type: Boolean,
    default: true,
  },

  // 额外的布局高度 参与计算页面高度
  layoutHeight: {
    type: Number,
    default: 125,
  },

  // 是否自动高度占满容器
  autoHeight: {
    type: Boolean,
    default: false,
  },

  checkType: {
    type: [String, Boolean],
    default: 'checkbox',
  },

  toolbarConfig: {
    type: Object as PropType<BaseTableProps['toolbarConfig']>,
  },

  keyboardEventsConfig: {
    type: Object as PropType<BaseTableProps['keyboardEventsConfig']>,
  },

  tableWrapConfig: {
    type: Object as PropType<BaseTableProps['tableWrapConfig']>,
  },

  // 合计列配置
  summaryConfig: {
    type: Object as PropType<BaseTableProps['summaryConfig']>,
    default: () => ({}),
  },
  mode: {
    type: String,
  },

  // 内置的鼠标右键事件配置
  rightMouseEventConfig: {
    type: Object as PropType<BaseTableProps['rightMouseEventConfig']>,
  },
}
export const tableProps = {
  /** 基本属性 */
  id: [String, Function],
  // 数据
  data: Array,
  // 表格的高度
  height: [Number, String],
  // 表格的最小高度
  minHeight: {
    type: [Number, String],
    default: () => getConfig().table.minHeight,
  },
  // 表格的最大高度
  maxHeight: [Number, String],
  // 已废弃，被 column-config.resizable 替换
  resizable: {
    type: Boolean,
    default: () => getConfig().table.resizable,
  },
  // 是否带有斑马纹
  stripe: {
    type: Boolean,
    default: () => getConfig().table.stripe,
  },
  // 是否带有边框
  border: {
    type: [Boolean, String],
    default: () => getConfig().table.border,
  },
  // 已废弃，被 cell-config.padding 替换
  padding: {
    type: Boolean,
    default: null,
  },
  // 是否圆角边框
  round: {
    type: Boolean,
    default: () => getConfig().table.round,
  },
  // 表格的尺寸
  size: {
    type: String,
    default: () => getConfig().table.size || getConfig().size,
  },
  // 列的宽度是否自撑开（可能会被废弃的参数，不要使用）
  fit: {
    type: Boolean,
    default: () => getConfig().table.fit,
  },
  // 表格是否加载中
  loading: Boolean,
  // 所有的列对其方式
  align: {
    type: String,
    default: () => getConfig().table.align,
  },
  // 所有的表头列的对齐方式
  headerAlign: {
    type: String,
    default: () => getConfig().table.headerAlign,
  },
  // 所有的表尾列的对齐方式
  footerAlign: {
    type: String,
    default: () => getConfig().table.footerAlign,
  },
  // 是否显示表头
  showHeader: {
    type: Boolean,
    default: () => getConfig().table.showHeader,
  },
  // （即将废弃）是否要高亮当前选中行
  highlightCurrentRow: {
    type: Boolean,
    default: () => getConfig().table.highlightCurrentRow,
  },
  // （即将废弃）鼠标移到行是否要高亮显示
  highlightHoverRow: {
    type: Boolean,
    default: () => getConfig().table.highlightHoverRow,
  },
  /**
   * （即将废弃）是否要高亮当前选中列
   * @deprecated
   */
  highlightCurrentColumn: {
    type: Boolean,
    default: () => getConfig().table.highlightCurrentColumn,
  },
  /**
   * （即将废弃）鼠标移到列是否要高亮显示
   * @deprecated
   */
  highlightHoverColumn: {
    type: Boolean,
    default: () => getConfig().table.highlightHoverColumn,
  },
  // （即将废弃）激活单元格编辑时是否高亮显示
  highlightCell: Boolean,
  // 是否显示表尾合计
  showFooter: Boolean,
  // 表尾数据
  footerData: Array,
  // 表尾合计的计算方法
  footerMethod: Function,
  // 给行附加 className
  rowClassName: [String, Function],
  // 给单元格附加 className
  cellClassName: [String, Function],
  // 给表头的行附加 className
  headerRowClassName: [String, Function],
  // 给表头的单元格附加 className
  headerCellClassName: [String, Function],
  // 给表尾的行附加 className
  footerRowClassName: [String, Function],
  // 给表尾的单元格附加 className
  footerCellClassName: [String, Function],
  // 给单元格附加样式
  cellStyle: [Object, Function],
  // 给表头单元格附加样式
  headerCellStyle: [Object, Function],
  // 给表尾单元格附加样式
  footerCellStyle: [Object, Function],
  // 给行附加样式
  rowStyle: [Object, Function],
  // 给表头行附加样式
  headerRowStyle: [Object, Function],
  // 给表尾行附加样式
  footerRowStyle: [Object, Function],
  // 合并指定单元格
  mergeCells: Array,
  // 合并指定的表尾
  mergeFooterItems: Array,
  // 自定义合并行或列的方法
  spanMethod: Function,
  // 表尾合并行或列
  footerSpanMethod: Function,
  // 设置所有内容过长时显示为省略号
  showOverflow: {
    type: [Boolean, String],
    default: () => getConfig().table.showOverflow,
  },
  // 设置表头所有内容过长时显示为省略号
  showHeaderOverflow: {
    type: [Boolean, String],
    default: () => getConfig().table.showHeaderOverflow,
  },
  // 设置表尾所有内容过长时显示为省略号
  showFooterOverflow: {
    type: [Boolean, String],
    default: () => getConfig().table.showFooterOverflow,
  },
  /** 高级属性 */
  // （即将废弃）columnKey 已废弃，被 column-config.useKey 替换
  columnKey: Boolean,
  // （即将废弃）rowKey 已废弃，被 row-config.useKey 替换
  rowKey: Boolean,
  // （即将废弃）rowId 已废弃，被 row-config.keyField 替换
  rowId: {
    type: String,
    default: () => getConfig().table.rowId,
  },
  zIndex: Number,
  emptyText: {
    type: String,
    default: () => getConfig().table.emptyText,
  },
  keepSource: {
    type: Boolean,
    default: () => getConfig().table.keepSource,
  },
  // 是否自动监听父容器变化去更新响应式表格宽高
  autoResize: {
    type: Boolean,
    default: () => getConfig().table.autoResize,
  },
  // 是否自动根据状态属性去更新响应式表格宽高
  syncResize: [Boolean, String, Number],
  // 响应式布局配置项
  resizeConfig: Object,
  // 列配置项
  columnConfig: Object,
  // 当前列配置项
  currentColumnConfig: Object,
  // 单元格配置项
  cellConfig: Object,
  // 表头单元格配置项
  headerCellConfig: Object,
  // 表尾单元格配置项
  footerCellConfig: Object,
  // 行配置项
  rowConfig: Object,
  // 行分组配置项
  rowGroupConfig: Object,
  // 当前行配置项
  currentRowConfig: Object,
  // 已废弃，被 rowDragConfig 替换
  dragConfig: Object,
  // 行拖拽排序配置项
  rowDragConfig: Object,
  // 列拖拽排序配置项
  columnDragConfig: Object,
  // 列调整配置项
  resizableConfig: Object,
  // 序号配置项
  seqConfig: Object,
  // 排序配置项
  sortConfig: Object,
  // 筛选配置项
  filterConfig: Object,
  // 单选框配置
  radioConfig: Object,
  // 复选框配置项
  checkboxConfig: Object,
  // tooltip 配置项
  tooltipConfig: Object,
  // 导出配置项
  exportConfig: Object,
  // 导入配置项
  importConfig: Object,
  // 打印配置项
  printConfig: Object,
  // 展开行配置项
  expandConfig: Object,
  // 树形结构配置项
  treeConfig: Object,
  // 快捷菜单配置项
  menuConfig: Object,
  // 鼠标配置项
  mouseConfig: Object,
  // 区域配置项
  areaConfig: Object,
  // 按键配置项
  keyboardConfig: Object,
  // 复制粘/贴配置项
  clipConfig: Object,
  // 查找/替换配置项
  fnrConfig: Object,
  // 编辑配置项
  editConfig: Object,
  // 校验配置项
  validConfig: Object,
  // 校验规则配置项
  editRules: Object,
  // 加载中配置项
  loadingConfig: Object,
  // 空内容渲染配置项
  emptyRender: Object,
  // 自定义列配置项
  customConfig: Object,
  // （即将废弃）横向虚拟滚动配置项
  scrollX: Object,
  // （即将废弃）纵向虚拟滚动配置项
  scrollY: Object,
  // 横向虚拟滚动配置项
  virtualXConfig: Object,
  // 纵向虚拟滚动配置项
  virtualYConfig: Object,
  // 滚动条配置项
  scrollbarConfig: Object,
  // （即将废弃）优化相关
  animat: {
    type: Boolean,
    default: () => getConfig().table.animat,
  },
  // （可能会被废弃的参数，不要使用）
  delayHover: {
    type: Number,
    default: () => getConfig().table.delayHover,
  },
  // 额外的参数
  params: Object,
}

export const defaultTableProps = {
  ...tableProps,
  ...extendTableProps,
}

export const columnProps = {
  // 列唯一主键
  colId: [String, Number],
  // 渲染类型 index,radio,checkbox,expand,html
  type: String,
  // 列字段名
  field: String,
  // 列标题
  title: String,
  // 列宽度
  width: [Number, String],
  // 列最小宽度，把剩余宽度按比例分配
  minWidth: [Number, String],
  // 列最大宽度
  maxWidth: [Number, String],
  // 是否允许拖动列宽调整大小
  resizable: {
    type: Boolean,
    default: null,
  },
  // 将列固定在左侧或者右侧
  fixed: String,
  // 列对其方式
  align: String,
  // 表头对齐方式
  headerAlign: String,
  // 表尾列的对齐方式
  footerAlign: String,
  // 当内容过长时显示为省略号
  showOverflow: {
    type: [Boolean, String],
    default: null,
  },
  // 当表头内容过长时显示为省略号
  showHeaderOverflow: {
    type: [Boolean, String],
    default: null,
  },
  // 当表尾内容过长时显示为省略号
  showFooterOverflow: {
    type: [Boolean, String],
    default: null,
  },
  // 给单元格附加 className
  className: [String, Function],
  // 给表头单元格附加 className
  headerClassName: [String, Function],
  // 给表尾单元格附加 className
  footerClassName: [String, Function],
  // 格式化显示内容
  formatter: [Function, Array, String],
  // 格式化表尾显示内容
  footerFormatter: [Function, Array, String],
  // 是否显示间距
  padding: {
    type: Boolean,
    default: null,
  },
  // 垂直对齐方式
  verticalAlign: {
    type: String,
    default: null,
  },
  // 是否允许排序
  sortable: Boolean,
  // 自定义排序的属性
  sortBy: [String, Function],
  // 排序的字段类型，比如字符串转数值等
  sortType: String,
  // 配置筛选条件数组
  filters: {
    type: Array,
    default: null,
  },
  // 筛选是否允许多选
  filterMultiple: {
    type: Boolean,
    default: true,
  },
  // 自定义筛选方法
  filterMethod: Function,
  // 筛选重置方法
  filterResetMethod: Function,
  // 筛选复原方法
  filterRecoverMethod: Function,
  // 筛选模板配置项
  filterRender: Object,
  // 设置为分组节点
  rowGroupNode: Boolean,
  // 设置为树节点
  treeNode: Boolean,
  // 设置为拖拽排序
  dragSort: Boolean,
  // 设置为行高拖拽
  rowResize: Boolean,
  // 是否可视
  visible: {
    type: Boolean,
    default: null,
  },
  // 表头单元格数据导出方法
  headerExportMethod: Function,
  // 单元格数据导出方法
  exportMethod: Function,
  // 表尾单元格数据导出方法
  footerExportMethod: Function,
  // 已废弃，被 titlePrefix 替换
  titleHelp: Object,
  // 标题前缀图标配置项
  titlePrefix: Object,
  // 标题后缀图标配置项
  titleSuffix: Object,
  // 单元格值类型
  cellType: String,
  // 单元格渲染配置项
  cellRender: Object,
  // 单元格编辑渲染配置项
  editRender: Object,
  // 内容渲染配置项
  contentRender: Object,
  // 额外的参数
  params: Object,

  //  ---------------------------------------------以下是扩展配置 ---------------------------------------------

  // 字典 会自动转化把值当成 key 来取出要显示的内容
  dict: {
    type: String,
  },

  // 值的类型，会生成不同的渲染器
  valueType: {
    type: String,
  },

  // 根据权限编码来控制当前列是否显示
  auth: {
    type: [String, Array] as PropType<string | string[]>,
  },

  // 排序
  sort: {
    type: Number,
  },

  // 默认隐藏，可在列配置显示
  defaultHidden: {
    type: Boolean as PropType<BaseColumn['defaultHidden']>,
  },

  children: {
    type: Array as PropType<BaseColumn['children']>,
  },

  // 单元格编辑配置
  schema: {
    type: Object as PropType<BaseColumn['schema']>,
  },

  // 自定义slots
  slots: {
    type: Object as PropType<BaseColumn['slots']>,
  },

  // 单元格操作配置
  cellActionConfig: {
    type: Object as PropType<BaseColumn['cellActionConfig']>,
  },

  updateCellSchema: {
    type: Function as PropType<BaseColumn['updateCellSchema']>,
  },

  showTooltip: {
    type: Boolean,
  },

  // 唯一key 用于刷新
  _key: {
    type: String,
  },
}

export const extraTableEmits = {
  register: (val: TableMethods) => isObject(val),
  'update:modelValue': (val: any) => true,
}

const VxeTableExtendCellAreaEmits = [
  'change-fnr', // 废弃

  'open-fnr',
  'show-fnr',
  'hide-fnr',
  'fnr-change',
  'fnr-find',
  'fnr-find-all',
  'fnr-replace',
  'fnr-replace-all',
  'cell-area-copy',
  'cell-area-cut',
  'cell-area-paste',
  'cell-area-merge',
  'clear-cell-area-selection',
  'clear-cell-area-merge',
  'header-cell-area-selection',
  'cell-area-selection-invalid',
  'cell-area-selection-start',
  'cell-area-selection-drag',
  'cell-area-selection-end',
  'cell-area-extension-start',
  'cell-area-extension-drag',
  'cell-area-extension-end',
  'cell-area-selection-all-start',
  'cell-area-selection-all-end',
  'cell-area-arrows-start',
  'cell-area-arrows-end',
  'active-cell-change-start',
  'active-cell-change-end',
]

export const tableEmits = [
  'update:data',
  'keydown-start',
  'keydown',
  'keydown-end',
  'paste',
  'copy',
  'cut',

  'current-change', // 已废弃

  'current-row-change',
  'current-row-disabled',
  'current-column-change',
  'current-column-disabled',
  'radio-change',
  'checkbox-change',
  'checkbox-all',
  'checkbox-range-start',
  'checkbox-range-change',
  'checkbox-range-end',
  'checkbox-range-select',
  'cell-click',
  'cell-dblclick',
  'cell-menu',
  'cell-mouseenter',
  'cell-mouseleave',
  'cell-selected',
  'cell-delete-value',
  'cell-backspace-value',
  'header-cell-click',
  'header-cell-dblclick',
  'header-cell-menu',
  'footer-cell-click',
  'footer-cell-dblclick',
  'footer-cell-menu',
  'clear-merge',
  'sort-change',
  'clear-sort',
  'clear-all-sort',
  'filter-change',
  'filter-visible',
  'clear-filter',
  'clear-all-filter',

  'resizable-change', // 已废弃

  'column-resizable-change',
  'row-resizable-change',
  'toggle-row-group-expand',
  'toggle-row-expand',
  'toggle-tree-expand',
  'menu-click',
  'edit-closed',
  'row-dragstart',
  'row-dragover',
  'row-dragend',
  'column-dragstart',
  'column-dragover',
  'column-dragend',
  'enter-append-row',

  'edit-actived', // 已废弃

  'edit-activated',
  'edit-disabled',
  'valid-error',
  'scroll',
  'scroll-boundary',
  'custom',

  ...VxeTableExtendCellAreaEmits,
]

export const basicProps = {
  id: {
    type: String,
  },
  columns: {
    type: Array as PropType<BaseColumn[]>,
    default: () => [],
  },
  mergeColumns: {
    type: Array,
    default: () => [],
  },
  columnsApi: {
    type: Function,
  },
  showFields: {
    type: Array as PropType<BaseColumn[]>,
  },
  // 是否使用序号
  showIndex: {
    type: Boolean,
    default: false,
  },
  // 是否使用翻页
  usePagination: {
    type: Boolean,
    default: true,
  },
  // 翻页配置
  pagination: {
    type: Object,
    default: () => ({}),
  },
  // 是否立即请求接口
  immediate: {
    type: Boolean,
    default: true,
  },
  // 请求接口
  fetchApi: Function,
  // 数据变化回调
  dataChange: Function,
  // 是否支持排序
  enableSort: {
    type: Boolean,
    default: true,
  },
  // 是否监听页面高度 动态设置table高度
  observeScrollY: {
    type: Boolean,
    default: true,
  },
  // 额外的布局高度 参与计算页面高度
  layoutHeight: {
    type: Number,
    default: 82,
  },
  // 单选 多选
  checkType: {
    type: [String, Boolean] as PropType<'radio' | 'checkbox' | false>,
    default: 'checkbox',
  },
  // 显示分页
  showPagination: {
    type: Boolean,
    default: true,
  },
  // 显示搜索
  showSearch: {
    type: Boolean,
    default: false,
  },
  // 工具栏配置
  toolbarConfig: {
    type: Object,
    default: () => ({}),
  },
  // 键盘事件配置
  keyboardEventsConfig: {
    type: Object,
    default: () => ({}),
  },
  // 表格包装配置
  tableWrapConfig: {
    type: Object,
    default: () => ({}),
  },
  // 按钮列表
  buttonList: {
    type: Array,
    default: () => [],
  },
  // 事件绑定
  on: {
    type: Object,
    default: () => ({}),
  },

  // 表格样式配置
  tableStyle: {
    type: Object as PropType<TableStyleOptions>,
    default: () => ({
      striped: true,
      border: 'horizontal',
      compact: true,
      headerStyle: 'gray',
      rowHeight: 'medium',
      rounded: 'md',
      shadow: 'sm',
      responsive: true,
      theme: 'light',
    }),
  },

  // 自定义样式类
  customClass: {
    type: String,
    default: '',
  },

  // 是否启用默认样式
  enableDefaultStyle: {
    type: Boolean,
    default: true,
  },
}
