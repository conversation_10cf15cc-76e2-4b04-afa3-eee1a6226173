<template>
  <div
    class="v-table flex-grow flex flex-col gap-2 w-full"
    v-fullscreen="isFullscreen"
  >
    <Toolbar ref="toolbarRef" />

    <div
      :style="
        getProps.height
          ? {}
          : { height: `calc(100vh - ${getProps.layoutHeight}px)` }
      "
    >
      <div
        class="table-wrap h-full"
        v-bind="getProps?.tableWrapConfig?.wrapProps"
      >
        <div class="table-left" v-bind="getProps?.tableWrapConfig?.leftProps">
          <slot name="table-left" />
        </div>
        <div
          class="table-main h-full w-full"
          v-bind="getProps?.tableWrapConfig?.mainProps"
        >
          <VxeTable
            ref="tableRef"
            v-bind="getBind"
            :class="computedTableClasses"
            :style="computedTableStyle"
            :show-footer="summaryEnabled"
            :footer-method="footerMethod"
            v-loading="loading"
          >
            <!-- 序号列 -->
            <VxeColumn
              v-if="getProps.showIndex && !getProps.checkType"
              type="seq"
              title="序号"
              width="60"
              fixed="left"
            />
            <VxeColumn
              type="checkbox"
              width="40"
              fixed="left"
              v-if="getProps.checkType === 'checkbox'"
            >
              <template #default="record">
                <span v-if="!(record as any)?.checked" class="index">{{
                  record.seq
                }}</span>
              </template>
            </VxeColumn>
            <VxeColumn
              type="radio"
              width="40"
              fixed="left"
              v-if="getProps.checkType === 'radio'"
            >
              <template #default="record">
                <span v-if="!(record as any)?.checked" class="index">{{
                  record.seq
                }}</span>
              </template>
            </VxeColumn>
            <!-- 动态渲染字段列 -->
            <template v-for="column in getViewColumns" :key="column.field">
              <vxe-column
                v-if="column.type === 'expand'"
                type="expand"
                v-bind="column"
              >
                <template #content="record">
                  <slot
                    v-bind="record"
                    :name="
                      column?.schema?.slots?.['default'] ||
                      column?.slots?.['default']
                    "
                  ></slot>
                </template>
              </vxe-column>

              <TableColumn v-else :column="column" :key="column._key">
                <template v-for="name in Object.keys($slots)" #[name]="data">
                  <slot :name="name as string" v-bind="data" />
                </template>
              </TableColumn>
            </template>

            <template #empty>
              <div class="text-center py-4 text-gray-500">暂无数据</div>
            </template>
          </VxeTable>
        </div>
        <div class="table-right" v-bind="getProps?.tableWrapConfig?.rightProps">
          <slot name="table-right" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  ComputedRef,
  defineComponent,
  h,
  inject,
  onMounted,
  provide,
  ref,
  toRefs,
  unref,
} from 'vue'
import {
  VxeTable,
  VxeColumn,
  VxeToolbarInstance,
  VxeTableInstance,
  VxeColgroup,
  VXETable,
} from 'vxe-table'
import { defaultTableProps, extendTableProps, extraTableEmits } from './props'
import { useColumns } from './hooks/useColumns'
import { useDataSource } from './hooks/useDataSource'
import { useEvents } from './hooks/useEvents'
import { BaseTableProps } from './types'
import { convertToOnEventName } from './helper'
import { get, isFunction, isNil, isObject, omit, pick } from 'lodash-es'
import TableColumn from './components/TableColumn.vue'
import { deepMerge } from '@/utils/object'
import { defaultTableSetting, rightMouseEvent } from './setting'
import DeptSelector from './renderer/DeptSelector.vue'
import { Icon } from '@iconify/vue'
import Toolbar from './components/Toolbar/index.vue'
import { calculateSummary, formatSummaryValue } from '../plugins/summaryColumn'

export default defineComponent({
  name: 'VTable',
  components: {
    VxeTable,
    VxeColumn,
    VxeColgroup,
    TableColumn,
    DeptSelector,
    Icon,
    Toolbar,
  },
  props: defaultTableProps,
  emits: extraTableEmits,
  setup(props, { emit, attrs, slots }) {
    const schemaMaps = new Map()
    const tableRef = ref<VxeTableInstance>()
    const toolbarRef = ref()
    // @ts-ignore
    const innerPropsRef = ref<Partial<BaseTableProps>>({})
    const isFullscreen = ref(false)

    const getProps = computed(() => {
      // 按优先级从低到高排列（后面的覆盖前面的）
      const sources = [defaultTableSetting, props]

      const obj = sources.reduce((merged, current) => {
        Object.entries(current || {}).forEach(([key, val]) => {
          // 仅当新值不为空时覆盖（undefined/null/空字符串等视为无值）
          if (val !== undefined && val !== null && val !== '') {
            merged[key] = val
          }
        })
        return merged
      }, {})

      return Object.assign(obj, unref(innerPropsRef))
    }) as ComputedRef<BaseTableProps>

    const getBind = computed(() => {
      let eventObj: any = {}
      const { on, rightMouseEventConfig, menuConfig, height, toolbarConfig } =
        unref(getProps)
      isObject(on) &&
        Object.keys(on)?.forEach((event) => {
          if (!convertToOnEventName(event)) return
          eventObj[convertToOnEventName(event)] = get(on, event)
        })

      eventObj = coverEvent(eventObj)
      const rightMouseEventMenuConfig =
        rightMouseEventConfig?.enable == true
          ? rightMouseEvent(rightMouseEventConfig?.events)
          : menuConfig

      // const dataSource = unref(dataSourceRef);

      let propsData: any = {
        ...attrs,
        class: 'z-0',
        ...unref(getProps),
        ...eventObj,
        // tableLayout: 'fixed',
        // data: dataSource,
        loading: loading.value,
        height:
          isNil(height) ||
          (toolbarConfig?.isFull && unref(isFullscreen) === true)
            ? '100%'
            : height,
        menuConfig: rightMouseEventMenuConfig,
      }

      propsData = omit(
        propsData,
        Object.keys({
          ...extendTableProps,
        })
      )

      return propsData
    })

    function init(props: BaseTableProps) {
      innerPropsRef.value = deepMerge(unref(innerPropsRef) || {}, props)
    }

    const getColumnBind = computed(() => {
      return (column) => {
        return column
      }
    })

    const {
      columnsRef,
      originColumns,
      getViewColumns,
      metaData,
      fetchColumns,
      setColumns,
      updateColumn,
      resetColumns,
      // getColumns,
    } = useColumns(getProps, tableRef)

    const {
      pagination,
      loading,
      dataSourceRef,
      SearchRef,
      PaginationRef,
      fetch,
      reload,
      setTableData,
      setValues,
      update,
      exportData,
      // getData,
    } = useDataSource(getProps, { emit, tableRef })

    const {
      checkedRef,
      updateCellSchema,
      coverEvent,
      setEditCell,
      setSelectCell,
      insert,
      insertAt,
      validate,
      validateField,
      fullValidate,
      fullValidateField,
      dispatchEvent,
      getEl,
      clearAll,
      updateData,
      loadData,
      reloadData,
      setRow,
      reloadRow,
      getParams,
      loadTreeChildren,
      loadColumn,
      reloadColumn,
      getRowNode,
      getColumnNode,
      getRowSeq,
      getRowIndex,
      getVTRowIndex,
      getVMRowIndex,
      getColumnIndex,
      getVTColumnIndex,
      getVMColumnIndex,
      createData,
      createRow,
      revertData,
      clearData,
      getCellElement,
      getCellLabel,
      isInsertByRow,
      isRemoveByRow,
      removeInsertRow,
      isUpdateByRow,
      getColumns,
      getColid,
      getColumnById,
      getColumnByField,
      getParentColumn,
      getTableColumn,
      moveColumnTo,
      moveRowTo,
      getFullColumns,
      getData,
      getCheckboxRecords,
      getTreeRowChildren,
      getTreeParentRow,
      getRowById,
      getRowid,
      getTableData,
      getFullData,
      setColumnFixed,
      clearColumnFixed,
      hideColumn,
      showColumn,
      setColumnWidth,
      getColumnWidth,
      refreshColumn,
      setRowHeightConf,
      getRowHeightConf,
      setRowHeight,
      getRowHeight,
      refreshScroll,
      recalculate,
      openTooltip,
      closeTooltip,
      isAllCheckboxChecked,
      isAllCheckboxIndeterminate,
      getCheckboxIndeterminateRecords,
      setCheckboxRow,
      setCheckboxRowKey,
      isCheckedByCheckboxRow,
      isCheckedByCheckboxRowKey,
      isIndeterminateByCheckboxRow,
      isIndeterminateByCheckboxRowKey,
      toggleCheckboxRow,
      setAllCheckboxRow,
      getRadioReserveRecord,
      clearRadioReserve,
      getCheckboxReserveRecords,
      clearCheckboxReserve,
      toggleAllCheckboxRow,
      clearCheckboxRow,
      setCurrentRow,
      isCheckedByRadioRow,
      isCheckedByRadioRowKey,
      setRadioRow,
      setRadioRowKey,
      clearCurrentRow,
      clearRadioRow,
      getCurrentRecord,
      getRadioRecord,
      getCurrentColumn,
      setCurrentColumn,
      clearCurrentColumn,
      setPendingRow,
      togglePendingRow,
      isPendingByRow,
      getPendingRecords,
      clearPendingRow,
      sort,
      setSort,
      setSortByEvent,
      clearSort,
      clearSortByEvent,
      isSort,
      getSortColumns,
      setFilterByEvent,
      closeFilter,
      isActiveFilterByColumn,
      clearFilterByEvent,
      isRowExpandLoaded,
      clearRowExpandLoaded,
      reloadRowExpand,
      toggleRowExpand,
      setAllRowExpand,
      setRowExpand,
      isRowExpandByRow,
      clearRowExpand,
      clearRowExpandReserve,
      getRowExpandRecords,
      setRowGroups,
      clearRowGroups,
      isRowGroupRecord,
      isRowGroupExpandByRow,
      setRowGroupExpand,
      setAllRowGroupExpand,
      clearRowGroupExpand,
      getTreeExpandRecords,
      isTreeExpandLoaded,
      clearTreeExpandLoaded,
      reloadTreeExpand,
      toggleTreeExpand,
      setAllTreeExpand,
      setTreeExpand,
      isTreeExpandByRow,
      clearTreeExpand,
      clearTreeExpandReserve,
      getScroll,
      scrollTo,
      scrollToRow,
      scrollToColumn,
      clearScroll,
      updateFooter,
      updateStatus,
      setMergeCells,
      removeMergeCells,
      getMergeCells,
      clearMergeCells,
      setMergeFooterItems,
      removeMergeFooterItems,
      getMergeFooterItems,
      clearMergeFooterItems,
      updateCellAreas,
      getCustomStoreData,
      focus,
      blur,
      connect,
    } = useEvents(tableRef, getProps, {
      columnsRef,
      setColumns,
      schemaMaps,
    })

    onMounted(() => {
      SearchRef.value = unref(unref(toolbarRef).SearchRef)
      PaginationRef.value = unref(unref(toolbarRef).PaginationRef)

      const tableApi = {
        instance: tableRef,
        init,
        reload,
        setTableData,
        setValues,
        update,
        updateCellSchema,
        // setColumns,
        // updateColumn,
        // getColumns,
        checked: checkedRef,
        _validate: async () => {
          const values = await validate(true)
          console.log(values)
          if (isNil(values)) {
            return undefined
          } else {
            const errs = Object.keys(values).map((field) => {
              const errs = values[field]
              if (!errs || !errs?.length) return
              let message = ''
              errs.forEach((err) => {
                message +=
                  err.rule?.$options?.message || err.rule?.$options?.content
              })
              const rowIndex = (errs?.[0].rowIndex || 0) + 1
              return `第${rowIndex}行: ${message} `
            })
            return errs
          }
        },
        saveCondition: SearchRef?.value?.saveCondition,

        setEditCell,
        setSelectCell,
        insert,
        insertAt,
        validate,
        validateField,
        fullValidate,
        fullValidateField,
        dispatchEvent,
        getEl,
        clearAll,
        updateData,
        loadData,
        reloadData,
        setRow,
        reloadRow,
        getParams,
        loadTreeChildren,
        loadColumn,
        reloadColumn,
        getRowNode,
        getColumnNode,
        getRowSeq,
        getRowIndex,
        getVTRowIndex,
        getVMRowIndex,
        getColumnIndex,
        getVTColumnIndex,
        getVMColumnIndex,
        createData,
        createRow,
        revertData,
        clearData,
        getCellElement,
        getCellLabel,
        isInsertByRow,
        isRemoveByRow,
        removeInsertRow,
        isUpdateByRow,
        getColumns,
        getColid,
        getColumnById,
        getColumnByField,
        getParentColumn,
        getTableColumn,
        moveColumnTo,
        moveRowTo,
        getFullColumns,
        getData,
        getCheckboxRecords,
        getTreeRowChildren,
        getTreeParentRow,
        getRowById,
        getRowid,
        getTableData,
        getFullData,
        setColumnFixed,
        clearColumnFixed,
        hideColumn,
        showColumn,
        setColumnWidth,
        getColumnWidth,
        refreshColumn,
        setRowHeightConf,
        getRowHeightConf,
        setRowHeight,
        getRowHeight,
        refreshScroll,
        recalculate,
        openTooltip,
        closeTooltip,
        isAllCheckboxChecked,
        isAllCheckboxIndeterminate,
        getCheckboxIndeterminateRecords,
        setCheckboxRow,
        setCheckboxRowKey,
        isCheckedByCheckboxRow,
        isCheckedByCheckboxRowKey,
        isIndeterminateByCheckboxRow,
        isIndeterminateByCheckboxRowKey,
        toggleCheckboxRow,
        setAllCheckboxRow,
        getRadioReserveRecord,
        clearRadioReserve,
        getCheckboxReserveRecords,
        clearCheckboxReserve,
        toggleAllCheckboxRow,
        clearCheckboxRow,
        setCurrentRow,
        isCheckedByRadioRow,
        isCheckedByRadioRowKey,
        setRadioRow,
        setRadioRowKey,
        clearCurrentRow,
        clearRadioRow,
        getCurrentRecord,
        getRadioRecord,
        getCurrentColumn,
        setCurrentColumn,
        clearCurrentColumn,
        setPendingRow,
        togglePendingRow,
        isPendingByRow,
        getPendingRecords,
        clearPendingRow,
        sort,
        setSort,
        setSortByEvent,
        clearSort,
        clearSortByEvent,
        isSort,
        getSortColumns,
        setFilterByEvent,
        closeFilter,
        isActiveFilterByColumn,
        clearFilterByEvent,
        isRowExpandLoaded,
        clearRowExpandLoaded,
        reloadRowExpand,
        toggleRowExpand,
        setAllRowExpand,
        setRowExpand,
        isRowExpandByRow,
        clearRowExpand,
        clearRowExpandReserve,
        getRowExpandRecords,
        setRowGroups,
        clearRowGroups,
        isRowGroupRecord,
        isRowGroupExpandByRow,
        setRowGroupExpand,
        setAllRowGroupExpand,
        clearRowGroupExpand,
        getTreeExpandRecords,
        isTreeExpandLoaded,
        clearTreeExpandLoaded,
        reloadTreeExpand,
        toggleTreeExpand,
        setAllTreeExpand,
        setTreeExpand,
        isTreeExpandByRow,
        clearTreeExpand,
        clearTreeExpandReserve,
        getScroll,
        scrollTo,
        scrollToRow,
        scrollToColumn,
        clearScroll,
        updateFooter,
        updateStatus,
        setMergeCells,
        removeMergeCells,
        getMergeCells,
        clearMergeCells,
        setMergeFooterItems,
        removeMergeFooterItems,
        getMergeFooterItems,
        clearMergeFooterItems,
        updateCellAreas,
        getCustomStoreData,
        focus,
        blur,
        connect,
      }

      // @ts-ignore
      emit('register', tableApi)
    })

    const computedTableClasses = computed(() => {
      const style = getProps.value.tableStyle
      const customClass = getProps.value.customClass
      const enableDefaultStyle = getProps.value.enableDefaultStyle !== false

      const classes: string[] = []

      // 基础样式类
      if (enableDefaultStyle) {
        classes.push('v-table-styled')

        // 斑马纹效果
        if (style?.striped) classes.push('table-striped')

        // 紧凑模式
        if (style?.compact) classes.push('table-compact')

        // 响应式配置
        if (style?.responsive) classes.push('table-responsive')

        // 边框样式
        if (style?.border === 'horizontal') classes.push('border-horizontal')
        else if (style?.border === 'vertical') classes.push('border-vertical')
        else if (style?.border === 'all' || style?.border === true)
          classes.push('border-all')
        else classes.push('border-none')

        // 表头样式
        if (style?.headerStyle && style.headerStyle !== 'none') {
          classes.push(`header-${style.headerStyle}`)
        }

        // 圆角
        if (style?.rounded) {
          if (typeof style.rounded === 'string') {
            classes.push(`rounded-${style.rounded}`)
          } else {
            classes.push('rounded-md')
          }
        }

        // 阴影
        if (style?.shadow) {
          if (typeof style.shadow === 'string') {
            classes.push(`shadow-${style.shadow}`)
          } else {
            classes.push('shadow-sm')
          }
        }

        // 主题
        if (style?.theme && style.theme !== 'auto') {
          classes.push(`theme-${style.theme}`)
        }
      }

      // 自定义类
      if (customClass) {
        classes.push(customClass)
      }

      // 插件样式自定义类
      if (style?.customClass) {
        classes.push(style.customClass)
      }

      return classes.join(' ')
    })

    const computedTableStyle = computed(() => {
      const style = getProps.value.tableStyle
      const enableDefaultStyle = getProps.value.enableDefaultStyle !== false

      const styleObj: Record<string, any> = {}

      if (enableDefaultStyle && style) {
        // 行高配置
        if (style.rowHeight && typeof style.rowHeight === 'number') {
          styleObj['--row-height'] = `${style.rowHeight}px`
        } else if (style.rowHeight === 'small') {
          styleObj['--row-height'] = '32px'
        } else if (style.rowHeight === 'large') {
          styleObj['--row-height'] = '48px'
        } else {
          styleObj['--row-height'] = '40px' // medium
        }
      }

      return styleObj
    })

    // 辅助函数：获取字段值
    const getFieldValue = (obj: any, path: string): any => {
      if (!path) return obj

      const keys = path.split('.')
      let result = obj

      for (const key of keys) {
        if (result == null) return null
        result = result[key]
      }

      return result
    }

    // 检查是否启用合计功能
    const summaryEnabled = computed(() => {
      const config = getProps.value.summaryConfig
      return !!(config?.enabled && config.summaries)
    })

    // VxeTable footer方法
    const footerMethod = ({ columns, data }: any) => {
      const config = getProps.value.summaryConfig
      if (!config?.enabled || !config.summaries) {
        return []
      }

      const footerData: any[][] = [[]]

      columns.forEach((column: any, index: number) => {
        const field = column.field || column.property
        let cellValue = ''

        // 跳过序号列、选择列等特殊列
        if (!field || field.startsWith('__')) {
          cellValue = ''
        } else if (config.summaries[field]) {
          const summaryConfig = config.summaries[field]

          if (summaryConfig.calculator) {
            // 有计算器时进行计算
            try {
              const value = calculateSummary(data, field, summaryConfig as any)
              cellValue = formatSummaryValue(value, summaryConfig as any)
            } catch (error) {
              console.error(
                `Failed to calculate summary for field ${field}:`,
                error
              )
              cellValue = summaryConfig.showEmpty
                ? summaryConfig.emptyText || '-'
                : ''
            }
          } else if (summaryConfig.label) {
            // 没有计算器时显示label
            cellValue = summaryConfig.label
          }
        }

        footerData[0][index] = cellValue
      })

      return footerData
    }

    provide('__table', tableRef)
    provide('__schemaMaps', schemaMaps)
    provide('__props', getProps)
    provide(
      '__toolbar',
      ref({
        getViewColumns,
        metaData,
        pagination,
        originColumns,
        isFullscreen,
        reload,
        exportData,
        setColumns,
        resetColumns,
        SearchRef,
      })
    )

    return {
      tableRef,
      getBind,
      getProps,
      pagination,
      loading,
      getColumnBind,
      getViewColumns,
      metaData,

      SearchRef,
      PaginationRef,
      isFullscreen,
      checkedRef,
      toolbarRef,

      columnsRef,
      originColumns,
      setColumns,
      fetchColumns,
      resetColumns,
      exportData,

      reload,

      computedTableClasses,
      computedTableStyle,

      // 合计行相关
      summaryEnabled,
      footerMethod,
    }
  },
})
</script>

<style lang="scss" scoped>
/* 导入表格样式插件的样式 */
@import '../plugins/styleColumn/tableStyles.css';

.v-table {
  :deep(.vxe-table) {
    .col--checkbox,
    .col--radio {
      position: relative;
      // background-color: red;

      .index {
        position: absolute;
        // z-index: 99;
        // display: inline-block;
        background: #fff;
        left: 1px;
        right: 1px;
        top: 1px;
        bottom: 2px;
        padding: 0px;
        display: flex;
        align-content: center;
        align-items: center;
        justify-content: center;
      }

      &:hover {
        .index {
          display: none;
        }
      }
    }

    .vxe-cell--checkbox,
    .vxe-cell--radio {
      display: inline-flex;
    }
  }
}

:deep(.vxe-table--render-default) {
  .vxe-body--row.row--radio > .vxe-body--column {
    .index {
      background-color: var(--vxe-ui-table-row-radio-checked-background-color);
    }
  }

  .vxe-body--column.col--valid-error .inner-input,
  .vxe-body--column.col--valid-error .inner-select,
  .vxe-body--column.col--valid-error .inner-textarea,
  .vxe-body--column.col--valid-error .inner-picker,
  .vxe-body--column.col--valid-error .vxe-input {
    border-color: var(--vxe-ui-table-validate-error-color);
  }
}

/* VxeTable footer 合计行样式 */
:deep(.vxe-table .vxe-table--footer-wrapper) {
  .vxe-footer--row {
    background-color: #fafafa;
    border-top: 2px solid #e8e8e8;
    font-weight: 500;

    .vxe-footer--column {
      color: #333;

      &:first-child {
        font-weight: bold;
      }
    }
  }
}
</style>
