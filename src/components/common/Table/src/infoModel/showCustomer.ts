import { useGlobDrawerForm } from '@/components';
import { nextTick } from 'vue';
import { getMetadata, getCustomer } from '@/api/sal/customer';

// column.dict('attribute', '客户属性', {
//         dict: 'attribute_enum',
//     }),
//     column.dict('type.name', '客户类型', {
//         dict: 'CUSTOMER_TYPE',
//     }),
//     column.dict('source.name', '客户来源', {
//         dict: 'QUOTE_TYPE',
//     }),
//     column.dict('grade', '客户等级', {
//         dict: 'grade_enum',
//         width: 100,
//     }),

export const showCustomer = async (opt: Recordable) => {
    const { openDrawerForm } = useGlobDrawerForm();

    const api = openDrawerForm({
        dialogProps: {
            title: `详情 - ${opt?.customer?.name}`,
            showFooter: false,
            size: 'mini',
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 2,
            '2xl': 2,
        },
        size: 'small',
        mode: 'detail',
        labelPosition: 'left',
        labelPrefix: ':',
        columnsApi: getMetadata,
        fetchApi: () => {
            return getCustomer(opt.customer_id);
        },
        fields: [
            { field: 'code' },
            { field: 'name' },
            {
                field: 'attribute',
                componentProps: {
                    type: 'dict',
                    dict: 'attribute_enum',
                },
            },
            {
                field: 'type.name',
                label: '客户类型',
                componentProps: {
                    type: 'dict',
                    dict: 'CUSTOMER_TYPE',
                },
            },
            {
                field: 'source.name',
                label: '客户来源',
                componentProps: {
                    type: 'dict',
                    dict: 'grade_enum',
                },
            },
            {
                field: 'grade',
                componentProps: {
                    type: 'dict',
                    dict: 'grade_enum',
                },
            },
            { field: 'salesman_id' },
            { field: 'address' },
            { field: 'contact' },
            { field: 'phone' },
            { field: 'mailbox' },
            { field: 'fax' },
            { field: 'opening_bank' },
            { field: 'quota' },
            { field: 'valid' },
            { field: 'notes', span: true },
            { field: 'created_at' },
            { field: 'created_by' },
        ],
    });

    // const res = await getCustomer(opt.customer_id);

    // await nextTick();
    // api.setValues(res);
};
