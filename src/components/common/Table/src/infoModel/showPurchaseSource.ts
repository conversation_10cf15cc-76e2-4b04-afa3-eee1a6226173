import {
    BaseTableProps,
    createPluginManager,
    PluginPresets,
    useGlobDialogForm,
    useGlobDrawerForm,
    showCustomer,
} from '@/components';
import {
    getSinglePurchaseInboundSourceSource, getPurchasePageMetaData
} from '@/api/wms/inbound';
import { useRoute } from 'vue-router';
import { dataListFun } from '@/utils/dateFormat';
import { $t } from '@/locales';
const { openDialogForm } = useGlobDialogForm();
const { openDrawerForm } = useGlobDrawerForm();

export const showPurchaseSource = async (opt: Recordable) => {
    const pluginManager = createPluginManager(PluginPresets.full);
    const column = pluginManager.getColumnHelper();
    const api = openDrawerForm?.({
        dialogProps: {
            title: `详情 - ${opt?.name}`,
            showFooter: false,
            size: 'mini',
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 2,
            '2xl': 2,
        },
        size: 'small',
        mode: 'detail',
        labelPosition: 'left',
        labelPrefix: ':',
        columnsApi: getPurchasePageMetaData,
        fetchApi: () => {
            let queryData = {
                id: Number(opt.id),
                max_depth: 3,
            };
            return getSinglePurchaseInboundSourceSource(queryData);
        },
        fields: [
            {
                field: 'receipt_no',
                label: '收货单号',
            },
            {
                field: 'org_no',
                label: '源单单号',
            },
            {
                field: 'org_type',
                label: '源单类型',
            },
            {
                field: 'receipt_date',
                label: '收货日期',
            },
            {
                field: 'supplier_name',
                label: '供应商',
            },
            {
                field: 'purchase_no',
                label: '采购单号',
            },
            {
                field: 'code',
                label: '物料编号',
            },
            {
                field: 'material_id',
                label: '物料ID',
            },
            {
                field: 'variant_id',
                label: '变体ID',
            },
            {
                field: 'name',
                label: '品名',
            },
            {
                field: 'specs',
                label: '规格型号',
            },
            {
                field: 'width',
                label: '宽（mm)',
            },
            {
                field: 'height',
                label: '高（mm）',
            },
            {
                field: 'deep',
                label: '厚度（mm）',
            },
            {
                field: 'unit_area',
                label: '单件面积',
            },
            {
                field: 'unit_weight',
                label: '单件重量',
            },
            {
                field: 'unit_girth',
                label: '单件周长',
            },
            {
                field: 'uom',
                label: '计量单位',
            },
            {
                field: 'org_qty',
                label: '收货数量',
            },
            {
                field: 'qty',
                label: '未入库数量',
            },
            {
                field: 'delivery_date',
                label: '要求交货日期',
            },
            // column.userMapping('created_by', '创建者'),

            //     column.userMapping('updated_by', '更新者'),

            //     column.dateOnly('created_at', '创建时间'),

            column.text('notes', $t('product.table.notes'), { width: 150 }),


        ],
    });
};
