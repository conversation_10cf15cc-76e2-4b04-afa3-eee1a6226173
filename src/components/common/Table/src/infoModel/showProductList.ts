import {
    BaseTableProps,
    createPluginManager,
    PluginPresets,
    useGlobDialogForm,
    useGlobDrawerForm,
    showCustomer,
} from '@/components';
import { getSingleProduct, getPageMetaData } from '@/api/ops/product';
import { useRoute } from 'vue-router';
import { dataListFun } from '@/utils/dateFormat';
import { $t } from '@/locales';

export const showProductList = async (opt: Recordable) => {
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const pluginManager = createPluginManager(PluginPresets.full);
    const column = pluginManager.getColumnHelper();
    const api = openDrawerForm?.({
        dialogProps: {
            title: `详情 - ${opt?.product?.name}`,
            showFooter: false,
            size: 'mini',
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 2,
            '2xl': 2,
        },
        size: 'small',
        mode: 'detail',
        labelPosition: 'left',
        labelPrefix: ':',
        columnsApi: getPageMetaData,
        fetchApi: () => {
            let queryData = {
                id: Number(opt.product.id),
                max_depth: 3,
            };
            return getSingleProduct(queryData);
        },
        fields: [
            {
                field: 'name',
                label: '产品名称',
            },
            {
                field: 'code',
                label: '产品编码',
            },
            {
                field: 'height',
                label: '长/高',
            },
            {
                field: 'width',
                label: '宽',
            },
            {
                field: 'weight',
                label: '⚖️重量',
            },
            {
                field: 'deep',
                label: '厚度',
            },
            {
                field: 'type.name',
                label: '类别',
            },
            {
                field: 'product_attribute',
                label: '属性',
            },
            {
                field: 'brand.name',
                label: '品牌',
            },
            {
                field: 'color.name',
                label: '颜色',
            },
            {
                field: 'pricing_mode',
                label: '计价方式',
            },
            {
                field: 'unit_price',
                label: '单价',
            },
            {
                field: 'base_unit.name',
                label: '单位',
            },
            {
                field: 'process_route_id',
                label: '工艺路线',
            },
            // column.userMapping('created_by', '创建者'),

            //     column.userMapping('updated_by', '更新者'),

            //     column.dateOnly('created_at', '创建时间'),

            column.text('notes', $t('product.table.notes'), { width: 150 }),
        ],
    });
};
