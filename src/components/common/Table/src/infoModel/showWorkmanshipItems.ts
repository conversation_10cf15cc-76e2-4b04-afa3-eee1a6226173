import {
    BaseTableProps,
    createPluginManager,
    PluginPresets,
    useGlobDialogForm,
    useGlobDrawerForm,
    showCustomer,
} from '@/components';
import { getWorkmanship } from '@/api/bas/workmanship/index';

export const showWorkmanshipItems = async (opt: Recordable) => {
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    // 定义表格列配置
    const columns = [
        {
            field: 'name',
            title: '参数名称',
            width: 200,
        },
        {
            field: 'value',
            title: '参数值',
            width: 200,
        },
        {
            field: 'workmanship_requirement',
            title: '工艺要求',

            width: 200,
        },
        {
            field: 'notes',
            title: '备注',
            width: '20%',
        },
        {
            width: undefined,
        },
    ];
    const api = openDialogForm({
        dialogProps: {
            title: '工艺参数列表明细',
            // showFooter: false,
            size: 'middle',
            height: 450,
            zIndex: 51,
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 1,
            '2xl': 1,
        },

        fields: [
            {
                field: 'list',
                component: 'table',
                span: true,
                componentProps: {
                    showPagination: false,
                    showSearch: false,
                    checkType: false,
                    height: 450,
                    fetchApi: async (
                        searchParams: any,
                        pageParams: any,
                        otherParams?: any,
                        reloadParams?: any
                    ) => {
                        const res = await getWorkmanship(opt.id);
                        return {
                            items: res?.items || [],
                        };
                    },
                    columns: columns,
                } as BaseTableProps,
            },
        ],
    });
};
