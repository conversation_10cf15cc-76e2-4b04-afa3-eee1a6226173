import {
    BaseTableProps,
    createPluginManager,
    PluginPresets,
    useGlobDialogForm,
    useGlobDrawerForm,
    showCustomer,
} from '@/components';
import {
    getMaterialInfo, getMaterialInfoMetadata
} from '@/api/ops/material_list/main';
import { useRoute } from 'vue-router';
import { dataListFun } from '@/utils/dateFormat';
import { $t } from '@/locales';
const { openDialogForm } = useGlobDialogForm();
const { openDrawerForm } = useGlobDrawerForm();
interface FormFieldConfig {
    field: string;
    label: string;
    formatter?: ({ row }: { row: any }) => any;
}
export const showMaterialsSource = async (opt: Recordable) => {
    const pluginManager = createPluginManager(PluginPresets.full);
    const column = pluginManager.getColumnHelper();
    const api = openDrawerForm?.({
        dialogProps: {
            title: `详情 - ${opt?.name}`,
            showFooter: false,
            size: 'mini',
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 2,
            '2xl': 2,
        },
        size: 'small',
        mode: 'detail',
        labelPosition: 'left',
        labelPrefix: ':',
        columnsApi: getMaterialInfoMetadata,
        fetchApi: () => {
            let queryData = {
                id: Number(opt.id),
                max_depth: 3,
            };
            return getMaterialInfo(queryData);
        },
        fields: [
            {
                field: 'code',
                label: '物料编码',
            },
            {
                field: 'name',
                label: '物料名称',
            },
            {
                field: 'type.name',
                label: '物料类别',
            },
            {
                field: 'base_unit.name',
                label: '基本单位',
            },
            {
                field: 'purchase_unit',
                label: '采购单位',
            },
            {
                field: 'stock_unit',
                label: '库存单位',
            },
            {
                field: 'opt_type',
                label: '优化类型',
            },
            {
                field: 'management_mode',
                label: '管理模式',
            },

            {
                field: 'alias',
                label: '物料别名',
            },
            {
                field: 'place_origin.name',
                label: '产地',
            },
            {
                field: 'brand.name',
                label: '品牌',
            },
            {
                field: 'grade.name',
                label: '等级',
            },
            {
                field: 'color.name',
                label: '颜色',
            },
            {
                field: 'deep',
                label: '厚度（mm）',
            },
            {
                field: 'material_quality',
                label: '材质',
            },
            {
                field: 'model',
                label: '规格型号',
            },

            {
                field: 'pricing_mode',
                label: '计价方式',
            },
            {
                field: 'sale_unit_price',
                label: '销售单价',
            },
            {
                field: 'supplier.name',
                label: '供应商',
            },
            {
                field: 'purchase_unit_price',
                label: '最近采购单价',
            },
            {
                field: 'purchase_advance_days',
                label: '采购提前天数',
            },
            {
                field: 'store',
                label: '所属仓库',
            },
            {
                field: 'position',
                label: '使用位置',
            },

            {
                field: 'inbound_qty',
                label: '入库数',
            },
            {
                field: 'outbound_qty',
                label: '出库数',
            },
            column.avatar('pic', '图片'),
            // column.userMapping('created_by', '创建者'),

            //     column.userMapping('updated_by', '更新者'),

            //     column.dateOnly('created_at', '创建时间'),

            column.text('notes', $t('product.table.notes'), { width: 150 }),


        ],
    });
};
