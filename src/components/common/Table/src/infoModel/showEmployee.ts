import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { getPageMetaData, getEmployeeDetail } from '@/api/hr/employee';

export const showEmployee = async (opt: Recordable) => {
    const { openDialogForm } = useGlobDialogForm?.() || {};
    const { openDrawerForm } = useGlobDrawerForm?.() || {};

    const api = openDrawerForm?.({
        dialogProps: {
            title: `详情 - ${opt?.salesman?.nick_name}`,
            showFooter: false,
            size: 'mini',
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 2,
            '2xl': 2,
        },
        size: 'small',
        mode: 'detail',
        labelPosition: 'left',
        labelPrefix: ':',
        columnsApi: getPageMetaData,
        fetchApi: () => {
            return getEmployeeDetail(opt.salesman_id);
        },
        fields: [
            {
                field: 'employee_no',
                label: '员工工号',
            },
            {
                field: 'employee_state.name',
                label: '人员状态',
            },
            {
                field: 'factory_id',
                label: '所属组织',
            },
            {
                field: 'dept.name',
                label: '所属部门',
            },
            {
                field: 'post.name',
                label: '所属岗位',
            },
            {
                field: 'employee_type.name',
                label: '用工类型',
            },
            {
                field: 'employee_state.name',
                label: '人员状态',
            },
            {
                label: '地址',
                field: 'account_address',
            },
            {
                label: '邮箱',
                field: 'mailbox',
            },
            {
                label: '电话',
                field: 'phone_number',
            },
        ],
    });
};
