import {
    BaseTableProps,
    createPluginManager,
    PluginPresets,
    useGlobDialogForm,
    useGlobDrawerForm,
    showCustomer,
} from '@/components';
import {
    getSingleInboundSource, getInboundSourcePageMetaData
} from '@/api/wms/inbound';
import { useRoute } from 'vue-router';
import { dataListFun } from '@/utils/dateFormat';
import { $t } from '@/locales';
const { openDialogForm } = useGlobDialogForm();
const { openDrawerForm } = useGlobDrawerForm();

export const showSaleOrderSource = async (opt: Recordable) => {
    const pluginManager = createPluginManager(PluginPresets.full);
    const column = pluginManager.getColumnHelper();
    const api = openDrawerForm?.({
        dialogProps: {
            title: `详情 - ${opt?.name}`,
            showFooter: false,
            size: 'mini',
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 2,
            '2xl': 2,
        },
        size: 'small',
        mode: 'detail',
        labelPosition: 'left',
        labelPrefix: ':',
        columnsApi: getInboundSourcePageMetaData,
        fetchApi: () => {
            let queryData = {
                id: Number(opt.id),
                max_depth: 3,
            };
            return getSingleInboundSource(queryData);
        },
        fields: [
            {
                field: 'order_no',
                label: '订单编号',
            },
            {
                field: 'order_date',
                label: '订单日期',
            },
            {
                field: 'delivery_date',
                label: '交货日期',
            },
            {
                field: 'customer_name',
                label: '客户名称',
            },
            {
                field: 'project_name',
                label: '项目名称',
            },
            {
                field: 'org_qty',
                label: '来源数量',
            },
            {
                field: 'org_no',
                label: '来源单号',
            },
            {
                field: 'org_seq_no',
                label: '源单序号',
            },
            {
                field: 'org_type',
                label: '来源单类型',
            },
            {
                field: 'product_id',
                label: '产品ID',
            },
            {
                field: 'code',
                label: '产品编号',
            },
            {
                field: 'name',
                label: '品名',
            },
            {
                field: 'specs',
                label: '规格型号',
            },
            {
                field: 'width',
                label: '宽（mm)',
            },
            {
                field: 'height',
                label: '高（mm）',
            },
            {
                field: 'deep',
                label: '厚度（mm）',
            },
            {
                field: 'unit_area',
                label: '单件面积',
            },
            {
                field: 'unit_weight',
                label: '单件重量',
            },
            {
                field: 'unit_girth',
                label: '单件周长',
            },
            {
                field: 'uom',
                label: '计量单位',
            },
            {
                field: 'order_qty',
                label: '订单数量',
            },
            {
                field: 'un_inbound_qty',
                label: '未入库数量',
            },
            // column.userMapping('created_by', '创建者'),

            //     column.userMapping('updated_by', '更新者'),

            //     column.dateOnly('created_at', '创建时间'),

            column.text('notes', $t('product.table.notes'), { width: 150 }),


        ],
    });
};
