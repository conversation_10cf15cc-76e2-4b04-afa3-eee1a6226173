import {
    BaseTableProps,
    createPluginManager,
    PluginPresets,
    useGlobDialogForm,
    useGlobDrawerForm,
    showCustomer,
} from '@/components';
import { getSingleProject } from '@/api/pm/project/index';
import { useRoute } from 'vue-router';
import { dataListFun } from '@/utils/dateFormat';

export const showContacts = async (opt: Recordable) => {
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const pluginManager = createPluginManager(PluginPresets.full);
    const column = pluginManager.getColumnHelper();
    // 定义表格列配置
    const columns = [
        column.composite('customer_info', '客户信息', {
            sortable: true,
            main: {
                field: 'stakeholder.customer.name',
                formatter: (value) => value || '未命名',
                props: {
                    style: {
                        color: '#3b82f6',
                        cursor: 'pointer',
                    },
                    onClick: ({ row }) => {
                        showCustomer(row);
                    },
                },
            },
            subs: [
                {
                    field: 'stakeholder.customer.code',
                    template: '🔤编码: ${value}',
                },
            ],
        }),
        column.composite('contact_info', '干系人信息', {
            main: {
                field: 'stakeholder.contact',
                formatter: (value) => value || '未设置',
                style: { fontWeight: '500' },
            },
            subs: [
                {
                    field: 'stakeholder.position',
                    template: '🛠️职位:${value}',
                    style: { color: '#7c3aed' },
                    condition: (record) => !!record.position,
                },
                {
                    field: 'stakeholder.influence',
                    template: '🔥影响力:${value}',
                    style: { color: '#f56c6c' },
                    condition: (record) => !!record.influence,
                },
                {
                    field: 'stakeholder.company_address',
                    template: '📍 ${value}',
                    style: { color: '#7c3aed' },
                    condition: (record) => !!record.company_address,
                },
            ],
            layout: 'horizontal',
        }),
        column.composite('phone', '联系电话', {
            main: {
                field: 'stakeholder.phone',
                formatter: (value) => value || '-',
                style: { fontWeight: '500', color: '#059669' },
            },
        }),
        column.composite('mailbox', '邮箱', {
            main: {
                field: 'stakeholder.mailbox',
                formatter: (value) => value || '-',
                style: { fontWeight: '500', color: '#f59e0b' },
            },
        }),
        column.composite('fax', '传真', {
            main: {
                field: 'stakeholder.fax',
                formatter: (value) => value || '-',
                style: { fontWeight: '500', color: '#3b82f6' },
            },
        }),
        column.composite('notes', '备注', {
            main: {
                field: 'stakeholder.notes',
                formatter: (value) => value || '-',
                style: { fontWeight: '500', color: '#333' },
            },
        }),
        column.composite('created_by', '创建者', {
            main: {
                field: 'stakeholder.created_by',
                formatter: (value) => value || '-',
                style: { fontWeight: '500', color: '#666' },
            },
        }),
        column.composite('updated_by', '更新者', {
            main: {
                field: 'stakeholder.updated_by',
                formatter: (value) => value || '-',
                style: { fontWeight: '500', color: '#666' },
            },
        }),
        column.composite('created_at', '创建时间', {
            main: {
                field: 'stakeholder.created_at',
                formatter: (value) =>
                    dataListFun(value, 'yyyy-mm-dd hh:ss:mm') || '-',
                style: { fontWeight: '500', color: '#666' },
            },
        }),
        column.composite('updated_at', '更新时间', {
            main: {
                field: 'stakeholder.updated_at',
                formatter: (value) =>
                    dataListFun(value, 'yyyy-mm-dd hh:ss:mm') || '-',
                style: { fontWeight: '500', color: '#666' },
            },
        }),
    ];
    const api = openDialogForm({
        dialogProps: {
            title: '联系人 - 明细',
            // showFooter: false,
            size: 'middle',
            height: 450,
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 1,
            '2xl': 1,
        },

        fields: [
            {
                field: 'list',
                component: 'table',
                span: 3,
                componentProps: {
                    showPagination: false,
                    showSearch: false,
                    checkType: false,
                    height: 450,
                    fetchApi: async (
                        searchParams: any,
                        pageParams: any,
                        otherParams?: any,
                        reloadParams?: any
                    ) => {
                        let queryData = {
                            id: Number(opt.id),
                            max_depth: 3,
                        };
                        const res = await getSingleProject(queryData);
                        return {
                            items: res.contacts || [],
                        };
                    },
                    columns: columns,
                } as BaseTableProps,
            },
        ],
    });
};
