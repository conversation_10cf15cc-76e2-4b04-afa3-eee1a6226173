import {
    BaseTableProps,
    createPluginManager,
    PluginPresets,
    useGlobDialogForm,
    useGlobDrawerForm,
} from '@/components';
import { nextTick } from 'vue';
import { getEmployeeList } from '@/api/hr/employee';
import { getOrder } from '@/api/sal/order';
import { useRoute } from 'vue-router';
import { getDetail } from '@/api/common';
import { $t } from '@/locales';

export const showOutBoundMaterials = async (opt: Recordable) => {
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const pluginManager = createPluginManager(PluginPresets.full);
    const column = pluginManager.getColumnHelper();
    const api = openDialogForm({
        dialogProps: {
            title: '出库/入库产品 - 明细',
            // showFooter: false,
            size: 'middle',
            height: 450,
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 1,
            '2xl': 1,
        },

        fields: [
            {
                field: 'list',
                component: 'table',
                span: 3,
                componentProps: {
                    showPagination: false,
                    showSearch: false,
                    checkType: false,
                    height: 450,
                    fetchApi: async (
                        searchParams: any,
                        pageParams: any,
                        otherParams?: any,
                        reloadParams?: any
                    ) => {
                        const route = useRoute();
                        const url = "/v1/wms/inbound/get";
                        console.log('route', route, window.location);
                        const res = await getDetail(url, opt.id);

                        return {
                            items: res.items || [],
                        };
                    },
                    columns: [
                        column.composite('store', $t('product.table.storage_warehouse'), {
                            main: {
                                field: 'store.name',
                                formatter: (value, row) => {
                                    if (row?.store) {
                                        return `${row?.store.name} (${row.store.code})`;
                                    }
                                },
                                style: {
                                    fontWeight: 'bold',
                                    fontSize: '14px',
                                },
                            },
                        }),
                        column.composite('shelves', "货架", {
                            main: {
                                field: 'shelves',
                                formatter: (value, row) => {
                                    if (row?.shelves) {
                                        return `${row?.shelves.name} (${row.shelves.code})`;
                                    }
                                },
                                style: {
                                    fontWeight: 'bold',
                                    fontSize: '14px',
                                },
                            },
                        }),
                        // column.number('storage_location', $t('product.table.location'), {
                        //     width: 80,
                        // }),
                        column.name('location.code', $t('product.table.location'), { width: 100 }),
                        column.name('code', $t('product.table.material') + $t('product.table.code'), { width: 120 }),
                        column.number('qty', $t('product.table.outbound') + '/' + $t('product.table.inbound') + $t('product.table.num'), { width: 130 }),
                        column.number('unit_price', '成本单价', { width: 130 }),
                        column.number('amount', '金额', { width: 130 }),
                        column.name('name', $t('product.table.product_name'), { width: 100 }),
                        column.name('specs', $t('project.form.spec'), { width: 100 }),
                        column.number('width', $t('product.form.width')),
                        column.number('height', $t('product.form.height')),
                        column.number('deep', $t('product.form.deep')),
                        column.dict('uom.name', $t('product.table.uom'), {
                            dict: 'UOM',
                        }),
                        column.text('notes', $t('product.table.notes'), { width: 150 }),
                    ],
                } as BaseTableProps,
            },
        ],
    });
};
