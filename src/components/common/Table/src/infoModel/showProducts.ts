import {
    BaseTableProps,
    createPluginManager,
    PluginPresets,
    useGlobDialogForm,
    useGlobDrawerForm,
} from '@/components';
import { nextTick } from 'vue';
import { getEmployeeList } from '@/api/hr/employee';
import { getOrder } from '@/api/sal/order';
import { useRoute } from 'vue-router';
import { getDetail } from '@/api/common';

export const showProducts = async (opt: Recordable) => {
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const pluginManager = createPluginManager(PluginPresets.full);
    const column = pluginManager.getColumnHelper();
    const api = openDialogForm({
        dialogProps: {
            title: '物料/产品 - 明细',
            // showFooter: false,
            size: 'middle',
            height: 450,
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 1,
            xl: 1,
            '2xl': 1,
        },

        fields: [
            {
                field: 'list',
                component: 'table',
                span: 3,
                componentProps: {
                    showPagination: false,
                    showSearch: false,
                    checkType: false,
                    height: 450,
                    fetchApi: async (
                        searchParams: any,
                        pageParams: any,
                        otherParams?: any,
                        reloadParams?: any
                    ) => {
                        const route = useRoute();
                        const url = `/v1${window.location.pathname}/get`;
                        console.log('route', route, window.location);
                        const res = await getDetail(url, opt.id);

                        return {
                            items: res.items || [],
                        };
                    },
                    columns: [
                        // column.name('sale_type', '订单类型', {}),
                        column.composite('name', '物料/产品', {
                            main: {
                                field: 'name',
                                formatter: (value, row) => {
                                    return `${row?.name} (${row.code})`;
                                },
                                style: {
                                    fontWeight: 'bold',
                                    fontSize: '14px',
                                },
                            },
                            subs: [
                                {
                                    field: 'code',
                                    style: { color: '#999', fontSize: '12px' },
                                    // condition: (record) => !!record.code,
                                },
                            ],
                            width: 180,
                        }),
                        column.composite('name', '长*宽(mm)', {
                            main: {
                                field: 'name',
                                formatter: (value, row) => {
                                    return `${row?.width} * ${row.height}`;
                                },
                                style: {
                                    fontWeight: 'bold',
                                    fontSize: '14px',
                                },
                            },
                            width: 180,
                        }),
                        // column.name('code', '编码', {}),
                        // column.name('name', '物料/产品', {}),
                        // column.number('width', '宽（mm）'),
                        // column.number('height', '长/高（mm）'),
                        column.number('qty', '数量', { width: 80 }),
                        column.currency('price', '单价', '¥', { width: 80 }),
                        column.status('pricing_mode', '计价方式', {
                            // vip: { text: 'VIP', color: '#8b5cf6' },
                            // premium: { text: '高级', color: '#3b82f6' },
                            // normal: { text: '普通', color: '#6b7280' },
                        }),
                        column.currency('wip_cost', '工艺费', '¥', {
                            width: 100,
                        }),
                        column.number('sales_area', '单件最小结算面积'),
                        column.number('total_actual_area', '实际总面积'),
                        { width: undefined },
                    ],
                } as BaseTableProps,
            },
        ],
    });
};
