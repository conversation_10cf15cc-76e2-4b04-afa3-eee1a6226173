import type { BaseColumn } from '../src/types';

/**
 * 基础列助手方法接口
 */
export interface BaseColumnHelper {
    // 基础列方法
    column: (field: string, title: string, options?: any) => BaseColumn;
    id: (title?: string, options?: any) => BaseColumn;
    text: (field: string, title: string, options?: any) => BaseColumn;
    name: (field?: string, title?: string, options?: any) => BaseColumn;
    index: (title?: string, options?: any) => BaseColumn;
    selection: (options?: any) => BaseColumn;
}

/**
 * 状态列助手方法（StatusColumn 插件）
 */
export interface StatusColumnHelper {
    status: (
        field: string,
        title: string,
        statusMap: Record<string | number, any>,
        options?: any
    ) => BaseColumn;
    activeStatus: (field: string, title?: string, options?: any) => BaseColumn;
    orderStatus: (field: string, title?: string, options?: any) => BaseColumn;
}

/**
 * 复合列助手方法（CompositeColumn 插件）
 */
export interface CompositeColumnHelper {
    composite: (field: string, title: string, options: any) => BaseColumn;
}

/**
 * 头像列助手方法（AvatarColumn 插件）
 */
export interface AvatarColumnHelper {
    avatar: (field: string, title: string, options?: any) => BaseColumn;
    userAvatar: (
        avatarField: string,
        nameField: string,
        title: string,
        options?: any
    ) => BaseColumn;
    simpleAvatar: (field: string, title: string, options?: any) => BaseColumn;
}

/**
 * 链接列助手方法（LinkColumn 插件）
 */
export interface LinkColumnHelper {
    link: (
        field: string,
        title: string,
        urlOrFunction: string | ((row: any) => string),
        options?: any
    ) => BaseColumn;
    emailLink: (field: string, title: string, options?: any) => BaseColumn;
    phoneLink: (field: string, title: string, options?: any) => BaseColumn;
    urlLink: (field: string, title: string, options?: any) => BaseColumn;
}

/**
 * 数字列助手方法（NumberColumn 插件）
 */
export interface NumberColumnHelper {
    number: (field: string, title: string, options?: any) => BaseColumn;
    currency: (
        field: string,
        title: string,
        symbol: string,
        options?: any
    ) => BaseColumn;
    percentage: (field: string, title: string, options?: any) => BaseColumn;
}

/**
 * 日期列助手方法（DateColumn 插件）
 */
export interface DateColumnHelper {
    date: (field: string, title: string, options?: any) => BaseColumn;
    datetime: (field: string, title: string, options?: any) => BaseColumn;
    dateOnly: (field: string, title: string, options?: any) => BaseColumn;
    timeOnly: (field: string, title: string, options?: any) => BaseColumn;
    relativeTime: (field: string, title: string, options?: any) => BaseColumn;
}

/**
 * 布尔列助手方法（BooleanColumn 插件）
 */
export interface BooleanColumnHelper {
    boolean: (field: string, title: string, options?: any) => BaseColumn;
}

/**
 * 操作列助手方法（ActionColumn 插件）
 */
export interface ActionColumnHelper {
    action: (title: string, options?: any) => BaseColumn;
}

/**
 * 用户映射列助手方法（UserMappingColumn 插件）
 */
export interface UserMappingColumnHelper {
    userMapping: (field: string, title: string, options?: any) => BaseColumn;
    createdBy: (field?: string, title?: string, options?: any) => BaseColumn;
    updatedBy: (field?: string, title?: string, options?: any) => BaseColumn;
}

/**
 * 复合列助手方法（GroupColumn 插件）
 */
export interface GroupColumnHelper {
    group: (field: string, title: string, options?: any) => BaseColumn;
}

/**
 * 字典/枚举展示（DictColumn 插件）
 */
export interface DictColumnHelper {
    dict: (field: string, title: string, options?: any) => BaseColumn;
}
export interface ImageColumnHelper {
    image: (field: string, title: string, options?: any) => BaseColumn;
}

/**
 * 完整的列助手接口（包含所有可能的方法）
 */
export interface FullColumnHelper
    extends BaseColumnHelper,
        StatusColumnHelper,
        CompositeColumnHelper,
        AvatarColumnHelper,
        LinkColumnHelper,
        NumberColumnHelper,
        DateColumnHelper,
        BooleanColumnHelper,
        ActionColumnHelper,
        GroupColumnHelper,
        DictColumnHelper,
        ImageColumnHelper,
        UserMappingColumnHelper {
    // 可以扩展更多方法
    [key: string]: (...args: any[]) => BaseColumn;
}

/**
 * 列助手代理对象，提供动态类型支持
 */
export type ColumnHelper = FullColumnHelper;
