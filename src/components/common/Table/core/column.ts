import type { BaseColumn } from '../src/types';
import { pluginManager } from './pluginManager';

/**
 * 基础的列配置选项
 */
export interface BaseColumnOptions {
  width?: number | string;
  minWidth?: number | string;
  fixed?: 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  resizable?: boolean;
  visible?: boolean;
  formatter?: (params: { cellValue: any; row: any; column: any }) => string | number;
  auth?: string | string[];
}

/**
 * 基础列创建函数
 */
function createBaseColumn(options: BaseColumn): BaseColumn {
  return {
    resizable: true,
    align: 'left',
    ...options
  };
}

/**
 * 动态扩展的column对象
 * 插件可以通过 pluginManager.extendColumnHelper 添加新方法
 */
export const column = new Proxy({
  // 基础列创建方法
  create: (options: BaseColumn): BaseColumn => createBaseColumn(options),
  
  // 基础文本列
  text: (field: string, title: string, options: BaseColumnOptions = {}): BaseColumn => {
    return createBaseColumn({
      field,
      title,
      ...options
    });
  }
}, {
  get(target, prop: string) {
    // 如果是已定义的方法，直接返回
    if (prop in target) {
      return target[prop];
    }
    
    // 尝试从插件管理器获取扩展的列助手方法
    const helper = pluginManager.getColumnHelper(prop);
    if (helper) {
      return helper;
    }
    
    // 如果都找不到，返回undefined
    return undefined;
  }
});

/**
 * 工具函数：获取字段值
 */
export function getFieldValue(obj: any, path: string, defaultValue: any = null): any {
  if (!obj || !path) return defaultValue;
  
  const keys = path.split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result === null || result === undefined) {
      return defaultValue;
    }
    result = result[key];
  }
  
  return result !== undefined ? result : defaultValue;
}

/**
 * 工具函数：格式化字段值
 */
export function formatFieldValue(
  value: any, 
  fieldType?: string,
  options: {
    dateFormat?: string;
    currencyCode?: string;
    precision?: number;
    enumMap?: Record<string, string>;
    nullText?: string;
  } = {}
): string {
  const { dateFormat = 'YYYY-MM-DD', currencyCode = '¥', precision = 2, enumMap, nullText = '-' } = options;
  
  if (value === null || value === undefined || value === '') {
    return nullText;
  }
  
  switch (fieldType) {
    case 'date':
    case 'datetime':
      return new Date(value).toLocaleDateString();
    case 'currency':
      return `${currencyCode}${Number(value).toFixed(precision)}`;
    case 'number':
      return Number(value).toFixed(precision);
    case 'enum':
      return enumMap?.[value] || value;
    case 'boolean':
      return value ? '是' : '否';
    default:
      return String(value);
  }
} 