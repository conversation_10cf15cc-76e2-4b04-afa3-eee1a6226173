import type { Component } from 'vue';
import type { BaseColumn } from '../src/types';

/**
 * 表格核心API接口
 * 提供插件与核心表格系统交互的方法
 */
export interface TableCore {
  // 注册新的列类型及其默认配置/渲染器
  registerColumnType(typeName: string, config: Partial<BaseColumn>, renderer?: string | Component): void;
  
  // 注册自定义单元格渲染器组件
  registerRenderer(name: string, component: Component): void;
  
  // 扩展 column 辅助对象的方法
  extendColumnHelper(methodName: string, factoryFunction: (...args: any[]) => BaseColumn): void;
  
  // 注册全局配置
  setGlobalConfig(key: string, value: any): void;
  getGlobalConfig(key: string): any;
  
  // 事件系统
  emit(event: string, ...args: any[]): void;
  on(event: string, callback: Function): void;
  off(event: string, callback?: Function): void;
  
  // 获取核心实例信息
  getVersion(): string;
  isPluginRegistered(name: string): boolean;
  
  // 扩展表格配置
  extendConfig(key: string, config: any): void;
  
  // 扩展表格API方法
  extendTableApi(methodName: string, method: (...args: any[]) => any): void;
}

/**
 * 插件安装函数类型
 */
export type TablePluginInstallFunction = (core: TableCore, options?: any) => void;

/**
 * 表格插件接口
 */
export interface TablePlugin {
  name: string; // 插件名称，用于调试或管理
  version?: string; // 插件版本
  install: TablePluginInstallFunction;
  // 可选：插件依赖的其他插件名称
  dependencies?: string[];
  // 可选：插件卸载时的清理函数
  uninstall?: (core: TableCore) => void;
}

/**
 * 插件注册选项
 */
export interface PluginInstallOptions {
  // 是否覆盖已存在的插件
  override?: boolean;
  // 是否跳过依赖检查
  skipDependencyCheck?: boolean;
} 