import type { Component } from 'vue';
import type { BaseColumn } from '../src/types';
import type {
    TableCore,
    TablePlugin,
    TablePluginInstallFunction,
    PluginInstallOptions,
} from './pluginApi';
import { getFieldValue, formatFieldValue } from './column';
import type { ColumnHelper } from './columnHelperTypes';

/**
 * 插件管理器实现
 */
export class PluginManager implements TableCore {
    private plugins = new Map<string, TablePlugin>();
    private columnTypes = new Map<
        string,
        { config: Partial<BaseColumn>; renderer?: string | Component }
    >();
    private renderers = new Map<string, Component>();
    private columnHelpers = new Map<string, (...args: any[]) => BaseColumn>();
    private globalConfig = new Map<string, any>();
    private tableApi = new Map<string, any>();
    private eventListeners = new Map<string, Function[]>();
    private readonly version = '1.0.0';

    constructor() {
        this.initializeColumnHelper();
    }

    /**
     * 注册插件
     */
    use(
        plugin: TablePlugin,
        options?: any,
        installOptions: PluginInstallOptions = {}
    ): void {
        const { name } = plugin;
        const { override = false, skipDependencyCheck = false } =
            installOptions;

        // 检查插件是否已注册
        if (this.plugins.has(name) && !override) {
            console.warn(`Table plugin "${name}" already registered.`);
            return;
        }

        // 检查依赖
        if (!skipDependencyCheck && plugin.dependencies) {
            this.checkDependencies(plugin.dependencies);
        }

        try {
            // 安装插件
            plugin.install(this, options);
            this.plugins.set(name, plugin);

            this.emit('plugin:installed', { plugin, options });
            // console.log(`Table plugin "${name}" installed successfully.`);
        } catch (error) {
            console.error(`Failed to install plugin "${name}":`, error);
            throw error;
        }
    }

    /**
     * 卸载插件
     */
    unuse(pluginName: string): boolean {
        const plugin = this.plugins.get(pluginName);
        if (!plugin) {
            console.warn(`Plugin "${pluginName}" not found.`);
            return false;
        }

        try {
            // 执行插件卸载函数
            if (plugin.uninstall) {
                plugin.uninstall(this);
            }

            this.plugins.delete(pluginName);
            this.emit('plugin:uninstalled', { plugin });
            // console.log(`Plugin "${pluginName}" uninstalled successfully.`);
            return true;
        } catch (error) {
            console.error(`Failed to uninstall plugin "${pluginName}":`, error);
            return false;
        }
    }

    /**
     * 检查插件依赖
     */
    private checkDependencies(dependencies: string[]): void {
        const missing = dependencies.filter((dep) => !this.plugins.has(dep));
        if (missing.length > 0) {
            throw new Error(
                `Missing plugin dependencies: ${missing.join(', ')}`
            );
        }
    }

    // === TableCore 接口实现 ===

    registerColumnType(
        typeName: string,
        config: Partial<BaseColumn>,
        renderer?: string | Component
    ): void {
        this.columnTypes.set(typeName, { config, renderer });
        this.emit('columnType:registered', { typeName, config, renderer });
    }

    registerRenderer(name: string, component: Component): void {
        this.renderers.set(name, component);
        this.emit('renderer:registered', { name, component });
    }

    extendColumnHelper(
        methodName: string,
        factoryFunction: (...args: any[]) => BaseColumn
    ): void {
        this.columnHelpers.set(methodName, factoryFunction);
        this.emit('columnHelper:extended', { methodName, factoryFunction });
    }

    setGlobalConfig(key: string, value: any): void {
        this.globalConfig.set(key, value);
        this.emit('config:changed', { key, value });
    }

    getGlobalConfig(key: string): any {
        return this.globalConfig.get(key);
    }

    extendConfig(key: string, config: any): void {
        this.globalConfig.set(key, config);
        this.emit('config:extended', { key, config });
    }

    extendTableApi(methodName: string, method: (...args: any[]) => any): void {
        this.tableApi.set(methodName, method);
        this.emit('tableApi:extended', { methodName, method });
    }

    emit(event: string, ...args: any[]): void {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach((callback) => {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(
                        `Error in event listener for "${event}":`,
                        error
                    );
                }
            });
        }
    }

    on(event: string, callback: Function): void {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event)!.push(callback);
    }

    off(event: string, callback?: Function): void {
        if (!callback) {
            this.eventListeners.delete(event);
            return;
        }

        const listeners = this.eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    getVersion(): string {
        return this.version;
    }

    isPluginRegistered(name: string): boolean {
        return this.plugins.has(name);
    }

    // === 获取注册的资源 ===

    getColumnType(typeName: string) {
        return this.columnTypes.get(typeName);
    }

    getRenderer(name: string) {
        return this.renderers.get(name);
    }

    /**
     * 获取单个列助手方法
     */
    getColumnHelperMethod(methodName: string) {
        return this.columnHelpers.get(methodName);
    }

    /**
     * 获取列助手对象，包含所有注册的列助手方法
     */
    getColumnHelper(): ColumnHelper {
        const helper: any = {};

        // 将所有注册的列助手方法添加到helper对象
        for (const [
            methodName,
            factoryFunction,
        ] of this.columnHelpers.entries()) {
            helper[methodName] = factoryFunction;
        }

        return helper as ColumnHelper;
    }

    getAllColumnTypes() {
        return Array.from(this.columnTypes.keys());
    }

    getAllRenderers() {
        return Array.from(this.renderers.keys());
    }

    getAllColumnHelpers() {
        return Array.from(this.columnHelpers.keys());
    }

    getInstalledPlugins() {
        return Array.from(this.plugins.values());
    }

    getTableApi(methodName?: string): any {
        if (methodName) {
            return this.tableApi.get(methodName);
        }
        return Object.fromEntries(this.tableApi);
    }

    // === 调试和监控 ===

    getDebugInfo() {
        return {
            version: this.version,
            plugins: Array.from(this.plugins.keys()),
            columnTypes: Array.from(this.columnTypes.keys()),
            renderers: Array.from(this.renderers.keys()),
            columnHelpers: Array.from(this.columnHelpers.keys()),
            globalConfig: Object.fromEntries(this.globalConfig.entries()),
            tableApi: Object.fromEntries(this.tableApi),
        };
    }

    private initializeColumnHelper(): void {
        // 基础列助手方法
        this.columnHelpers.set(
            'column',
            (field: string, title: string, options: any = {}) => ({
                field,
                title,
                ...options,
            })
        );

        // ID列
        this.columnHelpers.set(
            'id',
            (title: string = 'ID', options: any = {}) => ({
                field: 'id',
                title,
                width: 80,
                align: 'center',
                sortable: true,
                ...options,
            })
        );

        // 文本列
        this.columnHelpers.set(
            'text',
            (field: string, title: string, options: any = {}) => ({
                field,
                title,
                ...options,
            })
        );

        // 名称列
        this.columnHelpers.set(
            'name',
            (
                field: string = 'name',
                title: string = '名称',
                options: any = {}
            ) => ({
                field,
                title,
                minWidth: 120,
                ...options,
            })
        );

        // 索引列
        this.columnHelpers.set(
            'index',
            (title: string = '#', options: any = {}) => ({
                type: 'seq',
                title,
                width: 60,
                align: 'center',
                ...options,
            })
        );

        // 选择列
        this.columnHelpers.set('selection', (options: any = {}) => ({
            type: 'checkbox',
            width: 60,
            align: 'center',
            fixed: 'left',
            ...options,
        }));
    }
}

// 创建全局插件管理器实例
export const pluginManager = new PluginManager();

/**
 * 安装插件包的工具函数
 */
export function installPlugins(
    pluginManager: PluginManager,
    plugins: any[],
    options?: any
): void {
    plugins.forEach((plugin) => {
        try {
            pluginManager.use(plugin, options);
        } catch (error) {
            console.error(`Failed to install plugin ${plugin.name}:`, error);
        }
    });
}

/**
 * 创建预配置的插件管理器
 * 需要从plugins/index.ts导入预设配置
 */
export function createPluginManager(
    presetPlugins: any[] = [],
    options?: any
): PluginManager {
    const manager = new PluginManager();
    // console.log('manager -----------', manager);
    installPlugins(manager, presetPlugins, options);
    return manager;
}
