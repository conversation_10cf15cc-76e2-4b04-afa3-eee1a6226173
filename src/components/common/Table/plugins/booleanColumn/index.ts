import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import BooleanRenderer from './BooleanRenderer.vue';

export interface BooleanColumnOptions extends BaseColumn {
    trueText?: string;
    falseText?: string;
    nullText?: string;
    showIcon?: boolean;
    style?: 'text' | 'tag' | 'switch';
    onChange?: (value: boolean, row: any, field: string) => void;
}

/**
 * 布尔列插件
 */
const booleanColumnPlugin: TablePlugin = {
    name: 'BooleanColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 BooleanRenderer 渲染器
        core.registerRenderer('BooleanRenderer', BooleanRenderer);

        // 2. 注册 boolean 列类型
        core.registerColumnType('boolean', {
            cellRender: { name: 'Boolean<PERSON>enderer' },
        });

        // 3. 扩展 column 辅助对象，添加 boolean 方法
        core.extendColumnHelper(
            'boolean',
            (
                field: string,
                title: string,
                options: Omit<BooleanColumnOptions, 'field' | 'title'> = {}
            ): BooleanColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'BooleanRenderer',
                        props: options,
                    },
                    trueText: options.trueText || '是',
                    falseText: options.falseText || '否',
                    nullText: options.nullText || '-',
                    style: options.style || 'tag',
                    ...options,
                } as BooleanColumnOptions;
            }
        );

        // console.log('BooleanColumn plugin installed!!!!!!!!');
    },

    uninstall: (core: TableCore) => {
        // console.log('BooleanColumn plugin uninstalled');
    },
};

export default booleanColumnPlugin;
// export type { BooleanColumnOptions };
