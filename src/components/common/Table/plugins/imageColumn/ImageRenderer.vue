<template>
  <div class="image-renderer" @click="handleClick">
    <template v-if="!cellValue"></template>
    <template v-else>
      <ElImage
        style="width: 40px; height: 40px"
        :src="cellValue.url"
        :zoom-rate="1.2"
        :max-scale="7"
        :min-scale="0.2"
        :preview-src-list="cellValue.srcList"
        show-progress
        preview-teleported
        fit="cover"
      />
    </template>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue'
import { ElImage } from 'element-plus'
import { get, set, isFunction, isString, isArray } from 'lodash-es'

export default defineComponent({
  components: {
    ElImage,
  },
  props: {
    params: {
      type: Object,
    },
  },
  setup(props, { attrs }) {
    const cellValue = computed(() => {
      const { row, column } = props.params
      const params = {
        url: '',
        srcList: [],
      }
      const tempVal = get(row, column?.field)
      console.log('imageColumn val', tempVal)
      // 去除 VITE_GLOB_API_URL 末尾的 "api" 三个字符
      const preUrl = import.meta.env.VITE_GLOB_API_URL.replace(/api\/?$/, '')
      let val = preUrl + tempVal
      if (isString(val)) {
        set(params, 'url', val)
        set(params, 'srcList', [val])
      } else if (isArray(val)) {
        set(params, 'url', val?.[0])
        set(params, 'srcList', val)
      }
      console.log('imageColumn params', params)
      return params
    })

    const handleClick = () => {
      const { onClick } = props.params?.column || {}
      if (isFunction(onClick)) {
        onClick(props)
      }
    }

    return {
      cellValue,
      handleClick,
    }
  },
})
</script>

<style scoped></style>
