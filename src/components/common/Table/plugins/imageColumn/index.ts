import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import ImageRenderer from './ImageRenderer.vue';

export interface ImageRendererOptions extends BaseColumn {
    onClick?: (row: any) => void; // 点击事件
}

/**
 * 头像插件
 */
const imageColumnPlugin: TablePlugin = {
    name: 'ImageColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 ImageRenderer 渲染器
        core.registerRenderer('ImageRenderer', ImageRenderer);

        // 2. 注册 avatar 列类型
        core.registerColumnType('image', {
            cellRender: { name: 'ImageRenderer' },
            align: 'center',
            sortable: false,
            width: 80,
        });

        // 3. 扩展 column 辅助对象，添加 avatar 方法
        core.extendColumnHelper(
            'image',
            (
                field: string,
                title: string,
                options: Omit<ImageRendererOptions, 'field' | 'title'> = {}
            ): ImageRendererOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'ImageRenderer',
                        props: options,
                    },
                    align: options.align || 'center',
                    sortable: false,
                    width: options.width || 120,
                    ...options,
                } as ImageRendererOptions;
            }
        );

        // console.log('ImageColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('ImageColumn plugin uninstalled');
    },
};

export default imageColumnPlugin;
