<template>
  <span class="number-renderer" :class="numberClass" :style="numberStyle">
    {{ formattedNumber }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  params: {
    row: any
    column: any
    rowIndex: number
  }
}>()

const { row, column } = props.params
const options = column.cellRender?.props || column.options || {}
const { precision = 2 } = options
// 获取原始数值
const rawValue = computed(() => {
  const value = row[column.field]
  return value !== null && value !== undefined ? Number(value) : null
})

// 格式化数字
const formattedNumber = computed(() => {
  const value = rawValue.value

  // 处理 null/undefined 值
  if (value === null || value === undefined || isNaN(value)) {
    return options.nullDisplay || '-'
  }
  let formatted = ''
  //  const precision = options.precision || 2;
  const format = options.format || 'number'
  // 基础数字格式化
  let numberStr = ''

  if (format === 'percentage') {
    // 百分比格式：假设原始值是小数，转换为百分比
    numberStr = (value * 100).toFixed(precision)
  } else {
    // numberStr = value.toFixed(precision);
    // console.log('precision', precision);
    numberStr =
      precision === 0 ? Math.round(value).toString() : value.toFixed(precision)
  }

  // 千分位分隔符
  if (options.thousandsSeparator !== false) {
    const parts = numberStr.split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    // 判断小数部分是否需要显示
    if (parts[1]) {
      // 去除末尾的0
      parts[1] = parts[1].replace(/0+$/, '')
      // 如果小数部分全为0或去除后为空，则不显示小数位
      if (parts[1] === '') {
        numberStr = parts[0]
      } else {
        numberStr = parts[0] + '.' + parts[1]
      }
    } else {
      numberStr = parts[0]
    }
  }

  // 添加正号
  if (options.showPlusSign && value > 0) {
    numberStr = '+' + numberStr
  }

  // 根据格式类型添加前缀后缀
  switch (format) {
    case 'currency':
      formatted = (options.currency || '¥') + numberStr
      break
    case 'percentage':
      formatted = numberStr + (options.suffix || '%')
      break
    default:
      formatted = numberStr
  }

  // 添加自定义前缀和后缀
  if (options.prefix) {
    formatted = options.prefix + formatted
  }
  if (options.suffix && format !== 'percentage') {
    formatted = formatted + options.suffix
  }
  if (options.unit) {
    formatted = formatted + ' ' + options.unit
  }

  return formatted
})

// 数字样式类
const numberClass = computed(() => {
  const value = rawValue.value
  if (value === null || value === undefined || isNaN(value)) {
    return 'null-value'
  }

  return {
    positive: value > 0,
    negative: value < 0,
    zero: value === 0,
    currency: options.format === 'currency',
    percentage: options.format === 'percentage',
  }
})

// 数字颜色样式
const numberStyle = computed(() => {
  const value = rawValue.value
  const colors = options.color || {}

  if (value === null || value === undefined || isNaN(value)) {
    return {}
  }

  if (value > 0 && colors.positive) {
    return { color: colors.positive }
  } else if (value < 0 && colors.negative) {
    return { color: colors.negative }
  } else if (value === 0 && colors.zero) {
    return { color: colors.zero }
  }

  return {}
})
</script>

<style scoped>
.number-renderer {
  font-variant-numeric: tabular-nums;
  font-family:
    'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Droid Sans Mono',
    'Source Code Pro', monospace;
}

.number-renderer.positive {
  color: #16a34a;
}

.number-renderer.negative {
  color: #dc2626;
}

.number-renderer.zero {
  color: #6b7280;
}

.number-renderer.null-value {
  color: #9ca3af;
  font-style: italic;
}

.number-renderer.currency {
  font-weight: 500;
}

.number-renderer.percentage {
  font-weight: 500;
}
</style>
