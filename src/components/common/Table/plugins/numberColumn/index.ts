import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import NumberRenderer from './NumberRenderer.vue';

export interface NumberColumnOptions extends BaseColumn {
    precision?: number; // 小数位数
    thousandsSeparator?: boolean; // 是否显示千分位分隔符
    currency?: string; // 货币符号，如 ¥, $, €
    prefix?: string; // 前缀
    suffix?: string; // 后缀
    unit?: string; // 单位
    format?: 'number' | 'currency' | 'percentage' | 'custom';
    color?: {
        positive?: string; // 正数颜色
        negative?: string; // 负数颜色
        zero?: string; // 零值颜色
    };
    showPlusSign?: boolean; // 是否显示正号
    nullDisplay?: string; // null值显示
}

/**
 * 数字列插件
 */
const numberColumnPlugin: TablePlugin = {
    name: 'NumberColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 NumberRenderer 渲染器
        core.registerRenderer('NumberRenderer', NumberRenderer);

        // 2. 注册 number 列类型
        core.registerColumnType('number', {
            cellRender: { name: 'NumberRenderer' },
            align: 'right',
            sortable: true,
        });

        // 3. 扩展 column 辅助对象，添加 number 方法
        core.extendColumnHelper(
            'number',
            (
                field: string,
                title: string,
                options: Omit<NumberColumnOptions, 'field' | 'title'> = {}
            ): NumberColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'NumberRenderer',
                        props: options,
                    },
                    align: options.align || 'right',
                    sortable: options.sortable !== false,
                    precision: options.precision || 2,
                    thousandsSeparator: options.thousandsSeparator !== false,
                    format: options.format || 'number',
                    ...options,
                } as NumberColumnOptions;
            }
        );

        // 4. 扩展具体的数字列类型
        core.extendColumnHelper(
            'currency',
            (
                field: string,
                title: string,
                currency: string = '¥',
                options: Omit<
                    NumberColumnOptions,
                    'field' | 'title' | 'currency'
                > = {}
            ): NumberColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'NumberRenderer',
                        props: { currency, format: 'currency', ...options },
                    },
                    align: 'right',
                    sortable: true,
                    precision: 2,
                    thousandsSeparator: true,
                    format: 'currency',
                    currency,
                    ...options,
                } as NumberColumnOptions;
            }
        );

        core.extendColumnHelper(
            'percentage',
            (
                field: string,
                title: string,
                options: Omit<NumberColumnOptions, 'field' | 'title'> = {}
            ): NumberColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'NumberRenderer',
                        props: {
                            format: 'percentage',
                            suffix: '%',
                            ...options,
                        },
                    },
                    align: 'right',
                    sortable: true,
                    precision: 1,
                    format: 'percentage',
                    suffix: '%',
                    ...options,
                } as NumberColumnOptions;
            }
        );

        // console.log('NumberColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('NumberColumn plugin uninstalled');
    },
};

export default numberColumnPlugin;
