import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import StatusRenderer from './StatusRenderer.vue';

export interface StatusConfig {
    value: any; // 状态值
    label: string; // 显示文本
    color?: string; // 颜色
    bgColor?: string; // 背景色
    icon?: string; // 图标
    dot?: boolean; // 是否显示圆点
}

export interface StatusColumnOptions extends BaseColumn {
    statusMap?: Record<string | number, StatusConfig>; // 状态映射
    defaultStatus?: StatusConfig; // 默认状态
    size?: 'small' | 'medium' | 'large'; // 尺寸
    variant?: 'filled' | 'outlined' | 'text' | 'dot'; // 变体样式
    shape?: 'rounded' | 'square' | 'pill'; // 形状
    showIcon?: boolean; // 是否显示图标
    showDot?: boolean; // 是否显示状态点
}

/**
 * 状态标签插件
 */
const statusColumnPlugin: TablePlugin = {
    name: 'StatusColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 StatusRenderer 渲染器
        core.registerRenderer('StatusRenderer', StatusRenderer);

        // 2. 注册 status 列类型
        core.registerColumnType('status', {
            cellRender: { name: 'StatusRenderer' },
            align: 'center',
            sortable: true,
        });

        // 3. 扩展 column 辅助对象，添加 status 方法
        core.extendColumnHelper(
            'status',
            (
                field: string,
                title: string,
                options: Omit<StatusColumnOptions, 'field' | 'title'> = {}
            ): StatusColumnOptions => {
                const statusMap = options.statusMap || {};

                return {
                    field,
                    title,
                    cellRender: {
                        name: 'StatusRenderer',
                        props: { statusMap, ...options },
                    },
                    align: options.align || 'center',
                    sortable: options.sortable !== false,
                    // 将配置也放在column上供渲染器访问
                    statusMap,
                    size: options.size || 'medium',
                    variant: options.variant || 'filled',
                    shape: options.shape || 'rounded',
                    ...options,
                } as StatusColumnOptions;
            }
        );

        // 4. 扩展预定义的状态列
        core.extendColumnHelper(
            'activeStatus',
            (
                field: string,
                title: string = '状态',
                options: Omit<
                    StatusColumnOptions,
                    'field' | 'title' | 'statusMap'
                > = {}
            ): StatusColumnOptions => {
                const statusMap = {
                    true: {
                        value: true,
                        label: '启用',
                        color: '#ffffff',
                        bgColor: '#10b981',
                    },
                    false: {
                        value: false,
                        label: '禁用',
                        color: '#ffffff',
                        bgColor: '#ef4444',
                    },
                    1: {
                        value: 1,
                        label: '启用',
                        color: '#ffffff',
                        bgColor: '#10b981',
                    },
                    0: {
                        value: 0,
                        label: '禁用',
                        color: '#ffffff',
                        bgColor: '#ef4444',
                    },
                };

                return {
                    field,
                    title,
                    cellRender: {
                        name: 'StatusRenderer',
                        props: { statusMap, ...options },
                    },
                    align: 'center',
                    sortable: true,
                    statusMap,
                    ...options,
                } as StatusColumnOptions;
            }
        );

        core.extendColumnHelper(
            'orderStatus',
            (
                field: string,
                title: string = '订单状态',
                options: Omit<
                    StatusColumnOptions,
                    'field' | 'title' | 'statusMap'
                > = {}
            ): StatusColumnOptions => {
                const statusMap = {
                    pending: {
                        value: 'pending',
                        label: '待处理',
                        color: '#ffffff',
                        bgColor: '#f59e0b',
                    },
                    processing: {
                        value: 'processing',
                        label: '处理中',
                        color: '#ffffff',
                        bgColor: '#3b82f6',
                    },
                    completed: {
                        value: 'completed',
                        label: '已完成',
                        color: '#ffffff',
                        bgColor: '#10b981',
                    },
                    cancelled: {
                        value: 'cancelled',
                        label: '已取消',
                        color: '#ffffff',
                        bgColor: '#ef4444',
                    },
                };

                return {
                    field,
                    title,
                    cellRender: {
                        name: 'StatusRenderer',
                        props: { statusMap, ...options },
                    },
                    align: 'center',
                    sortable: true,
                    statusMap,
                    ...options,
                } as StatusColumnOptions;
            }
        );

        // console.log('StatusColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('StatusColumn plugin uninstalled');
    },
};

export default statusColumnPlugin;
