import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import TextRenderer from './TextRenderer.vue';

export interface TextColumnOptions extends BaseColumn {
    maxLength?: number; // 最大长度
    ellipsis?: boolean; // 是否显示省略号
    showTooltip?: boolean; // 是否显示完整内容的提示
    nullDisplay?: string; // null值显示
    placeholder?: string; // 占位符
    copyable?: boolean; // 是否可复制
    searchable?: boolean; // 是否可搜索
    highlight?: string | RegExp; // 高亮文本
    case?: 'upper' | 'lower' | 'capitalize'; // 文本大小写转换
}

/**
 * 文本列插件
 */
const textColumnPlugin: TablePlugin = {
    name: 'TextColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 TextRenderer 渲染器
        core.registerRenderer('TextRenderer', TextRenderer);

    // 2. 注册 text 列类型
    core.registerColumnType('text', {
      cellRender: { name: 'TextRenderer' },
      // sortable: true
    });

    // 3. 扩展 column 辅助对象，添加 text 方法
    core.extendColumnHelper('text', (
      field: string,
      title: string,
      options: Omit<TextColumnOptions, 'field' | 'title'> = {}
    ): TextColumnOptions => {
      return {
        field,
        title,
        cellRender: { 
          name: 'TextRenderer',
          props: options
        },
        // sortable: options.sortable !== false,
        align: options.align || 'left',
        ellipsis: options.ellipsis !== false,
        showTooltip: options.showTooltip !== false,
        ...options
      } as TextColumnOptions;
    });

        // console.log('TextColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('TextColumn plugin uninstalled');
    },
};

export default textColumnPlugin;
