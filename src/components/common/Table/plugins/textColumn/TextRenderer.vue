<template>
  <span 
    class="text-renderer" 
    :class="textClass"
    :title="showTooltip ? fullText : undefined"
    @click="handleClick"
  >
    {{ displayText }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  params: {
    row: any;
    column: any;
    rowIndex: number;
  };
}>();

const { row, column } = props.params;
const options = column.cellRender?.props || column.options || {};

// 获取原始文本值
const rawValue = computed(() => {
  let value = row[column.field];
  
  // 处理 null/undefined 值
  if (value === null || value === undefined || value === '') {
    return options.nullDisplay || options.placeholder || '';
  }
  
  return String(value);
});

// 应用大小写转换
const caseTransformed = computed(() => {
  const text = rawValue.value;
  const caseType = options.case;
  
  switch (caseType) {
    case 'upper':
      return text.toUpperCase();
    case 'lower':
      return text.toLowerCase();
    case 'capitalize':
      return text.replace(/\b\w/g, l => l.toUpperCase());
    default:
      return text;
  }
});

// 完整文本
const fullText = computed(() => {
  return caseTransformed.value;
});

// 显示文本（处理长度限制）
const displayText = computed(() => {
  const text = fullText.value;
  const maxLength = options.maxLength;
  
  if (maxLength && text.length > maxLength) {
    return options.ellipsis !== false 
      ? text.slice(0, maxLength) + '...'
      : text.slice(0, maxLength);
  }
  
  // 如果有自定义格式化函数
  if (options.formatter && typeof options.formatter === 'function') {
    return options.formatter({ cellValue: text, row, column });
  }
  
  return text;
});

// 是否显示提示
const showTooltip = computed(() => {
  if (options.showTooltip === false) return false;
  
  const text = fullText.value;
  const maxLength = options.maxLength;
  
  // 当文本被截断时显示完整内容
  return maxLength && text.length > maxLength;
});

// 样式类
const textClass = computed(() => {
  const value = rawValue.value;
  
  return {
    'null-value': !value,
    'copyable': options.copyable,
    'searchable': options.searchable,
    'highlighted': !!options.highlight,
    'truncated': showTooltip.value
  };
});

// 点击处理
const handleClick = (event: Event) => {
  // 如果可复制
  if (options.copyable) {
    navigator.clipboard?.writeText(fullText.value);
  }
  
  // 自定义点击处理
  if (options.onClick && typeof options.onClick === 'function') {
    options.onClick(row, event);
  }
};
</script>

<style scoped>
.text-renderer {
  display: inline-block;
  word-break: break-word;
  line-height: 1.4;
}

.text-renderer.null-value {
  color: #9ca3af;
  font-style: italic;
}

.text-renderer.copyable {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.text-renderer.copyable:hover {
  background-color: #f3f4f6;
  border-radius: 4px;
  padding: 2px 4px;
  margin: -2px -4px;
}

.text-renderer.searchable {
  position: relative;
}

.text-renderer.highlighted {
  background-color: #fef3c7;
  padding: 1px 2px;
  border-radius: 2px;
}

.text-renderer.truncated {
  cursor: help;
}
</style> 