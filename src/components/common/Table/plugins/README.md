# Table 插件系统

## 概述

Table 插件系统是一个模块化、可扩展的表格组件架构，旨在提供强大的功能性和优秀的开发体验。通过插件化设计，核心表格组件保持轻量，而各种功能特性可以按需加载和使用。

## 🎯 核心特性

- **🔌 插件化架构** - 功能模块化，按需加载
- **📦 预设插件包** - 针对不同场景的插件组合
- **🎨 丰富的列类型** - 复合列、状态列、头像列、链接列等
- **🔧 易于扩展** - 标准化的插件开发接口
- **💡 类型安全** - 完整的 TypeScript 支持
- **🎛️ 灵活配置** - 支持深度自定义
- **📱 响应式设计** - 适配各种屏幕尺寸

## 🚀 快速开始

### 安装和基础使用

```typescript
import { createPluginManager, BasicPlugins } from '@/components/common/Table/plugins';

// 1. 创建插件管理器
const pluginManager = createPluginManager(BasicPlugins);

// 2. 获取列助手对象
const column = pluginManager.getColumnHelper();

// 3. 定义表格列
const columns = [
  column.selection(),
  column.index(),
  column.composite('user', '用户信息', {
    main: { field: 'name' },
    subs: [
      { field: 'email', label: '邮箱' },
      { field: 'phone', label: '电话' }
    ]
  }),
  column.activeStatus('is_active', '状态'),
  column.datetime('created_at', '创建时间'),
  column.action('操作', {
    actions: [
      { text: '编辑', onClick: (row) => editUser(row) },
      { text: '删除', color: 'danger', onClick: (row) => deleteUser(row) }
    ]
  })
];
```

## 📋 可用插件

### 列渲染插件

| 插件名称 | 功能描述 | 主要特性 |
|---------|---------|---------|
| **CompositeColumn** | 复合列 | 单元格内多层信息展示、图标、操作按钮 |
| **StatusColumn** | 状态标签 | 多种样式变体、颜色配置、图标支持 |
| **AvatarColumn** | 头像显示 | 图片、字母、图标回退，多种形状 |
| **LinkColumn** | 链接列 | 邮箱、电话、网址链接，自定义样式 |
| **NumberColumn** | 数字格式化 | 货币、百分比、千分位分隔符 |
| **DateColumn** | 日期时间 | 多种格式、相对时间、时区支持 |
| **BooleanColumn** | 布尔值 | 标签、开关、文本样式 |
| **TextColumn** | 文本列 | 长度限制、省略号、大小写转换 |
| **RelationColumn** | 关系列 | 一对一、一对多、多对一、多对多关系 |

### 功能插件

| 插件名称 | 功能描述 | 主要特性 |
|---------|---------|---------|
| **ActionColumn** | 操作列 | 按钮组、下拉菜单、权限控制 |
| **Pagination** | 分页组件 | 页码导航、每页条数、快速跳转 |

## 🎨 预设插件包

```typescript
import { PluginPresets, createPluginManager } from '@/components/common/Table/plugins';

// 基础插件包 - 最常用功能
const basicManager = createPluginManager(PluginPresets.basic);

// CMS 内容管理系统
const cmsManager = createPluginManager(PluginPresets.cms);

// 用户管理场景
const userManager = createPluginManager(PluginPresets.userManagement);

// 数据分析场景
const analyticsManager = createPluginManager(PluginPresets.analytics);

// 完整功能集
const fullManager = createPluginManager(PluginPresets.full);
```

### 预设包详情

- **minimal** - 基础操作功能
- **basic** - 常用列类型和功能
- **enhanced** - 包含更多展示插件
- **full** - 所有可用插件
- **cms** - 内容管理系统场景
- **analytics** - 数据分析场景
- **userManagement** - 用户管理场景

## 🔧 插件详细说明

### CompositeColumn 插件

复合列是最强大的列类型，支持在单个单元格内展示多层信息。

```typescript
// 用户信息复合列
column.composite('user_info', '用户', {
  main: { 
    field: 'name', 
    style: { fontWeight: 'bold', fontSize: '14px' }
  },
  subs: [
    { field: 'email', label: '邮箱', icon: 'mail' },
    { field: 'department', label: '部门' },
    { 
      computed: (row) => `${row.posts_count} 篇文章`,
      style: { color: '#666' }
    }
  ],
  icon: { field: 'avatar', type: 'image', size: 'small' },
  actions: [
    { text: '发消息', onClick: (row) => sendMessage(row.id) }
  ],
  layout: 'horizontal'
})
```

**主要特性：**
- 主信息 + 多个子信息
- 图标支持（图片/字体图标）
- 内嵌操作按钮
- 水平/垂直布局
- 计算字段支持
- 丰富的样式配置

### StatusColumn 插件

状态列提供美观的状态标签显示。

```typescript
// 订单状态
column.status('status', '状态', {
  'pending': { value: 'pending', label: '待付款', bgColor: '#f59e0b' },
  'paid': { value: 'paid', label: '已付款', bgColor: '#10b981' },
  'shipped': { value: 'shipped', label: '已发货', bgColor: '#3b82f6' },
  'completed': { value: 'completed', label: '已完成', bgColor: '#8b5cf6' }
}, {
  variant: 'filled',    // filled | outlined | text | dot
  shape: 'pill',        // rounded | square | pill
  size: 'medium'        // small | medium | large
})

// 预定义状态
column.activeStatus('is_active', '启用状态')
column.orderStatus('order_status', '订单状态')
```

### AvatarColumn 插件

头像列支持多种回退机制和显示样式。

```typescript
// 用户头像 + 姓名
column.userAvatar('avatar', 'name', '用户', {
  size: 'medium',               // small | medium | large | number
  shape: 'circle',              // circle | square | rounded
  namePosition: 'right',        // right | bottom
  fallbackType: 'letter',       // letter | icon | image
  clickable: true,
  onClick: (row) => viewProfile(row.id)
})

// 简单头像
column.simpleAvatar('photo', '头像', {
  size: 'small',
  fallbackIcon: 'user-icon'
})
```

### LinkColumn 插件

链接列支持多种链接类型和交互方式。

```typescript
// 邮箱链接
column.emailLink('email', '邮箱')

// 电话链接  
column.phoneLink('phone', '联系电话')

// 网址链接
column.urlLink('website', '官网', {
  maxLength: 30,
  tooltip: true
})

// 自定义链接
column.link('profile_url', '个人资料', (row) => `/user/${row.id}`, {
  textField: 'username',
  icon: 'user',
  target: '_blank'
})
```

### NumberColumn 插件

数字列提供丰富的格式化选项。

```typescript
// 货币格式
column.currency('price', '价格', '¥', {
  precision: 2,
  color: {
    positive: '#10b981',
    negative: '#ef4444'
  }
})

// 百分比
column.percentage('growth_rate', '增长率', {
  precision: 1,
  showPlusSign: true
})

// 自定义数字格式
column.number('score', '评分', {
  precision: 1,
  prefix: '★ ',
  suffix: ' 分',
  thousandsSeparator: true
})
```

### DateColumn 插件

日期列支持多种时间格式和显示方式。

```typescript
// 基础日期时间
column.datetime('created_at', '创建时间')

// 相对时间
column.relativeTime('last_login', '最后登录')

// 自定义格式
column.date('event_date', '活动日期', {
  format: 'YYYY年MM月DD日',
  nullDisplay: '待定',
  timezone: 'Asia/Shanghai'
})
```

### ActionColumn 插件

操作列提供灵活的按钮和菜单配置。

```typescript
column.action('操作', {
  actions: [
    {
      text: '编辑',
      icon: 'edit',
      color: 'primary',
      onClick: (row) => editItem(row)
    },
    {
      text: '删除',
      icon: 'delete', 
      color: 'danger',
      disabled: (row) => !row.can_delete,
      onClick: (row) => deleteItem(row)
    }
  ],
  moreActions: [
    { text: '复制', onClick: (row) => copyItem(row) },
    { text: '移动', onClick: (row) => moveItem(row) }
  ],
  maxVisible: 2,
  size: 'medium'
})
```

### RelationColumn 插件

关系列插件提供对数据库关系的完整支持，包括一对一、一对多、多对一和多对多关系。

```typescript
// 多对一关系 - 显示关联对象信息
column.manyToOne('user', '用户', {
  displayField: 'name',
  template: '${name} (${email})',
  clickable: true,
  onItemClick: (user, row) => viewUserProfile(user.id)
})

// 一对多关系 - 显示关联项目数量
column.oneToMany('orders', '订单', {
  itemName: '个订单',
  clickable: true,
  onItemClick: (orders, row) => viewOrderList(orders)
})

// 多对多关系 - 显示标签列表
column.manyToMany('tags', '标签', {
  maxDisplay: 3,
  itemName: '个标签',
  onMoreClick: (allTags, row) => viewAllTags(allTags)
})

// 通用关系列
column.relation('department', '部门', {
  relationType: 'manyToOne',
  displayField: 'name',
  fallbackFields: ['title', 'code'],
  clickable: true
})
```

**主要特性：**
- 支持四种关系类型：oneToOne, oneToMany, manyToOne, manyToMany
- 智能字段显示（优先级：displayField > fallbackFields）
- 模板支持（${field} 变量替换）
- 数量显示和限制
- 点击交互支持
- 自定义样式配置
- 空值处理

**配置选项：**
```typescript
interface RelationColumnOptions {
  relationType?: 'oneToOne' | 'oneToMany' | 'manyToOne' | 'manyToMany';
  displayField?: string;          // 主显示字段
  fallbackFields?: string[];      // 备选字段
  template?: string;              // 显示模板
  itemName?: string;              // 项目单位名称
  showEmpty?: boolean;            // 是否显示空值
  showCount?: boolean;            // 是否显示数量
  clickable?: boolean;            // 是否可点击
  maxDisplay?: number;            // 最大显示数量
  separator?: string;             // 分隔符
  onItemClick?: (data: any, row: any) => void;
  onMoreClick?: (allData: any[], row: any) => void;
}
```

## 🛠️ 插件开发

### 创建自定义插件

```typescript
import type { TablePlugin, TableCore } from '../core/pluginApi';

const myCustomPlugin: TablePlugin = {
  name: 'MyCustomPlugin',
  version: '1.0.0',
  dependencies: ['CompositeColumn'], // 可选的依赖
  
  install: (core: TableCore, options?: any) => {
    // 1. 注册渲染器组件
    core.registerRenderer('MyRenderer', MyRendererComponent);
    
    // 2. 注册列类型
    core.registerColumnType('myColumn', {
      cellRender: { name: 'MyRenderer' },
      align: 'center'
    });
    
    // 3. 扩展列助手
    core.extendColumnHelper('myColumn', (field, title, options = {}) => ({
      field,
      title,
      cellRender: { name: 'MyRenderer', props: options },
      ...options
    }));
    
    console.log('MyCustomPlugin installed');
  },
  
  uninstall: (core: TableCore) => {
    console.log('MyCustomPlugin uninstalled');
  }
};

export default myCustomPlugin;
```

### 插件开发指南

1. **命名规范** - 使用 PascalCase，以 Plugin 结尾
2. **版本管理** - 遵循语义化版本号
3. **依赖声明** - 明确声明插件间依赖关系
4. **错误处理** - 提供详细的错误信息
5. **类型支持** - 导出完整的 TypeScript 类型定义
6. **文档完善** - 提供使用示例和 API 说明

## 🔍 调试和监控

### 插件调试

```typescript
// 获取调试信息
const debugInfo = pluginManager.getDebugInfo();
console.log('插件状态:', debugInfo);

// 检查插件是否已安装
if (pluginManager.isPluginRegistered('StatusColumn')) {
  // 使用状态列功能
}

// 监听插件事件
pluginManager.on('plugin:installed', (plugin) => {
  console.log(`插件 ${plugin.name} 已安装`);
});
```

### 性能监控

```typescript
// 启用性能监控
const pluginManager = createPluginManager(BasicPlugins, {
  enablePerformanceMonitoring: true
});

// 获取性能报告
const perfReport = pluginManager.getPerformanceReport();
console.log('渲染性能:', perfReport);
```

## 📚 最佳实践

### 1. 插件选择策略

```typescript
// ❌ 不推荐 - 一次性加载所有插件
const manager = createPluginManager(AllPlugins);

// ✅ 推荐 - 按需选择插件
const manager = createPluginManager([
  CompositeColumnPlugin,
  StatusColumnPlugin,
  ActionColumnPlugin
]);

// ✅ 推荐 - 使用预设配置
const manager = createPluginManager(PluginPresets.userManagement);
```

### 2. 类型安全使用

```typescript
import type { 
  StatusColumnOptions, 
  CompositeColumnOptions 
} from '@/components/common/Table/plugins';

// 使用类型约束
const statusColumn: StatusColumnOptions = column.status('status', '状态', statusMap);
const userColumn: CompositeColumnOptions = column.composite('user', '用户', config);
```

### 3. 配置复用

```typescript
// 定义通用配置
const commonStyles = {
  headerStyle: { fontWeight: 'bold' },
  cellStyle: { padding: '8px' }
};

// 创建配置工厂
const createUserColumn = (field: string, title: string) => 
  column.userAvatar(field, `${field}_name`, title, {
    ...commonStyles,
    size: 'medium',
    clickable: true
  });
```

### 4. 错误处理

```typescript
try {
  pluginManager.use(CustomPlugin);
} catch (error) {
  console.error('插件安装失败:', error);
  // 使用降级方案
  pluginManager.use(BasicPlugin);
}
```