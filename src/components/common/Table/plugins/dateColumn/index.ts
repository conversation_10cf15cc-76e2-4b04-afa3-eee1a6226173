import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import DateRenderer from './DateRenderer.vue';

export interface DateColumnOptions extends BaseColumn {
    format?: string; // 日期格式，如 'YYYY-MM-DD', 'YYYY-MM-DD HH:mm:ss'
    locale?: string; // 语言环境
    timezone?: string; // 时区
    relative?: boolean; // 是否显示相对时间（如：3天前）
    nullDisplay?: string; // null值显示
    showTime?: boolean; // 是否显示时间
    showSeconds?: boolean; // 是否显示秒数
    dateOnly?: boolean; // 仅显示日期
    timeOnly?: boolean; // 仅显示时间
}

/**
 * 日期列插件
 */
const dateColumnPlugin: TablePlugin = {
    name: 'DateColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 DateRenderer 渲染器
        core.registerRenderer('DateRenderer', DateRenderer);

        // 2. 注册 date 列类型
        core.registerColumnType('date', {
            cellRender: { name: 'DateRenderer' },
            sortable: true,
            align: 'center',
        });

        // 3. 扩展 column 辅助对象，添加 date 相关方法
        core.extendColumnHelper(
            'date',
            (
                field: string,
                title: string,
                options: Omit<DateColumnOptions, 'field' | 'title'> = {}
            ): DateColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'DateRenderer',
                        props: options,
                    },
                    sortable: options.sortable !== false,
                    align: options.align || 'center',
                    format: options.format || 'YYYY-MM-DD',
                    ...options,
                } as DateColumnOptions;
            }
        );

        // 4. 扩展具体的日期列类型
        core.extendColumnHelper(
            'datetime',
            (
                field: string,
                title: string,
                options: Omit<DateColumnOptions, 'field' | 'title'> = {}
            ): DateColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'DateRenderer',
                        props: {
                            format: 'YYYY-MM-DD HH:mm',
                            showTime: true,
                            ...options,
                        },
                    },
                    sortable: true,
                    align: 'center',
                    format: 'YYYY-MM-DD HH:mm',
                    showTime: true,
                    ...options,
                } as DateColumnOptions;
            }
        );

        core.extendColumnHelper(
            'dateOnly',
            (
                field: string,
                title: string,
                options: Omit<DateColumnOptions, 'field' | 'title'> = {}
            ): DateColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'DateRenderer',
                        props: {
                            format: 'YYYY-MM-DD',
                            dateOnly: true,
                            ...options,
                        },
                    },
                    sortable: true,
                    align: 'center',
                    format: 'YYYY-MM-DD',
                    dateOnly: true,
                    ...options,
                } as DateColumnOptions;
            }
        );

        core.extendColumnHelper(
            'timeOnly',
            (
                field: string,
                title: string,
                options: Omit<DateColumnOptions, 'field' | 'title'> = {}
            ): DateColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'DateRenderer',
                        props: {
                            format: 'HH:mm:ss',
                            timeOnly: true,
                            ...options,
                        },
                    },
                    sortable: true,
                    align: 'center',
                    format: 'HH:mm:ss',
                    timeOnly: true,
                    ...options,
                } as DateColumnOptions;
            }
        );

        core.extendColumnHelper(
            'relativeTime',
            (
                field: string,
                title: string,
                options: Omit<DateColumnOptions, 'field' | 'title'> = {}
            ): DateColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'DateRenderer',
                        props: { relative: true, ...options },
                    },
                    sortable: true,
                    align: 'center',
                    relative: true,
                    ...options,
                } as DateColumnOptions;
            }
        );

        // console.log('DateColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('DateColumn plugin uninstalled');
    },
};

export default dateColumnPlugin;
