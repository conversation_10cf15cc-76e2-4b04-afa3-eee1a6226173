<template>
    <span class="date-renderer" :class="dateClass" :title="fullDateTime">
        {{ formattedDate }}
    </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
    params: {
        row: any;
        column: any;
        rowIndex: number;
    };
}>();

const { row, column } = props.params;
const options = column.cellRender?.props || column.options || {};

// 获取原始日期值
const rawValue = computed(() => {
    const value = row[column.field];
    if (!value) return null;

    // 尝试解析各种日期格式
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date;
});

// 格式化日期
const formattedDate = computed(() => {
    const date = rawValue.value;

    // 处理 null/undefined 值
    if (!date) {
        return options.nullDisplay || '-';
    }

    const format = options.format || 'YYYY-MM-DD';

    // 如果是相对时间
    if (options.relative) {
        return getRelativeTime(date);
    }

    // 标准格式化
    return formatDate(date, format);
});

// 完整的日期时间字符串（用于 title 提示）
const fullDateTime = computed(() => {
    const date = rawValue.value;
    if (!date) return '';

    return formatDate(date, 'YYYY-MM-DD');
});

// 样式类
const dateClass = computed(() => {
    const date = rawValue.value;
    if (!date) return 'null-value';

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const dateOnly = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate()
    );

    return {
        today: dateOnly.getTime() === today.getTime(),
        past: date < now,
        future: date > now,
        weekend: date.getDay() === 0 || date.getDay() === 6,
        relative: options.relative,
    };
});

// 格式化日期函数
function formatDate(date: Date, format: string): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
        .replace(/YYYY/g, String(year))
        .replace(/YY/g, String(year).slice(-2))
        .replace(/MM/g, month)
        .replace(/DD/g, day)
        .replace(/HH/g, hours)
        .replace(/mm/g, minutes)
        .replace(/ss/g, seconds);
}

// 获取相对时间
function getRelativeTime(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    if (diffMs < 0) {
        // 未来时间
        const absDiffSecs = Math.abs(diffSecs);
        const absDiffMins = Math.abs(diffMins);
        const absDiffHours = Math.abs(diffHours);
        const absDiffDays = Math.abs(diffDays);

        if (absDiffSecs < 60) return '即将';
        if (absDiffMins < 60) return `${absDiffMins}分钟后`;
        if (absDiffHours < 24) return `${absDiffHours}小时后`;
        if (absDiffDays < 7) return `${absDiffDays}天后`;
        return formatDate(date, 'YYYY-MM-DD');
    }

    // 过去时间
    if (diffSecs < 60) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
    if (diffMonths < 12) return `${diffMonths}个月前`;
    if (diffYears > 0) return `${diffYears}年前`;

    return formatDate(date, 'YYYY-MM-DD');
}
</script>

<style scoped>
.date-renderer {
    font-variant-numeric: tabular-nums;
    white-space: nowrap;
}

.date-renderer.today {
    color: #3b82f6;
    font-weight: 500;
}

.date-renderer.past {
    color: #6b7280;
}

.date-renderer.future {
    color: #10b981;
}

.date-renderer.weekend {
    color: #f59e0b;
}

.date-renderer.relative {
    font-size: 12px;
    color: #8b5cf6;
}

.date-renderer.null-value {
    color: #9ca3af;
    font-style: italic;
}
</style>
