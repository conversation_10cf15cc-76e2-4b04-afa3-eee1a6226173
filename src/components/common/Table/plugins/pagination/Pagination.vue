<template>
  <div class="table-pagination" :class="paginationClass" v-if="shouldShow">
    <!-- 总条数信息 -->
    <div v-if="showTotal" class="pagination-total">
      共 {{ total }} 条记录
    </div>

    <!-- 每页条数选择器 -->
    <div v-if="showSizeChanger" class="pagination-size-changer">
      <span>每页</span>
      <select
        v-model="currentPageSize"
        @change="handleSizeChange"
        :disabled="disabled"
        class="size-selector"
      >
        <option
          v-for="size in pageSizes"
          :key="size"
          :value="size"
        >
          {{ size }}
        </option>
      </select>
      <span>条</span>
    </div>

    <!-- 分页按钮 -->
    <div class="pagination-buttons">
      <!-- 上一页 -->
      <button
        class="pagination-btn prev-btn"
        :class="{ disabled: currentPage <= 1 }"
        :disabled="disabled || currentPage <= 1"
        @click="handlePageChange(currentPage - 1)"
      >
        上一页
      </button>

      <!-- 页码按钮 -->
      <template v-for="page in visiblePages" :key="page">
        <button
          v-if="typeof page === 'number'"
          class="pagination-btn page-btn"
          :class="{
            active: page === currentPage,
            disabled: disabled
          }"
          :disabled="disabled"
          @click="handlePageChange(page)"
        >
          {{ page }}
        </button>
        <span v-else class="pagination-ellipsis">...</span>
      </template>

      <!-- 下一页 -->
      <button
        class="pagination-btn next-btn"
        :class="{ disabled: currentPage >= totalPages }"
        :disabled="disabled || currentPage >= totalPages"
        @click="handlePageChange(currentPage + 1)"
      >
        下一页
      </button>
    </div>

    <!-- 快速跳转 -->
    <div v-if="showQuickJumper" class="pagination-jumper">
      <span>跳至</span>
      <input
        v-model="jumpPage"
        @keyup.enter="handleJump"
        @blur="handleJump"
        :disabled="disabled"
        class="jump-input"
        type="number"
        :min="1"
        :max="totalPages"
      />
      <span>页</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

export interface PaginationProps {
  page?: number; // 当前页码
  pageSize?: number; // 每页条数
  total?: number; // 总条数
  pageSizes?: number[]; // 可选的每页条数
  background?: boolean; // 按钮是否有背景色
  small?: boolean; // 是否为小型分页
  hideOnSinglePage?: boolean; // 只有一页时是否隐藏
  showTotal?: boolean; // 是否显示总条数
  showSizeChanger?: boolean; // 是否显示页数选择器
  showQuickJumper?: boolean; // 是否显示快速跳转
  disabled?: boolean; // 是否禁用
}

const props = withDefaults(defineProps<PaginationProps>(), {
  page: 1,
  pageSize: 20,
  total: 0,
  pageSizes: () => [10, 20, 50, 100],
  background: true,
  small: false,
  hideOnSinglePage: false,
  showTotal: true,
  showSizeChanger: true,
  showQuickJumper: true,
  disabled: false
});

const emit = defineEmits<{
  change: [page: number, pageSize: number];
  'size-change': [current: number, size: number];
}>();

const currentPage = ref(props.page);
const currentPageSize = ref(props.pageSize);
const jumpPage = ref('');

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(props.total / currentPageSize.value);
});

const shouldShow = computed(() => {
  if (props.hideOnSinglePage && totalPages.value <= 1) {
    return false;
  }
  return props.total > 0;
});

const paginationClass = computed(() => {
  return {
    'pagination-small': props.small,
    'pagination-background': props.background,
    'pagination-disabled': props.disabled
  };
});

// 可见页码列表
const visiblePages = computed(() => {
  const current = currentPage.value;
  const total = totalPages.value;
  const pages: (number | string)[] = [];

  if (total <= 7) {
    // 总页数不超过7页，全部显示
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // 总页数超过7页，智能显示
    pages.push(1);

    if (current > 4) {
      pages.push('...');
    }

    const start = Math.max(2, current - 2);
    const end = Math.min(total - 1, current + 2);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    if (current < total - 3) {
      pages.push('...');
    }

    if (total > 1) {
      pages.push(total);
    }
  }

  return pages;
});

// 方法
function handlePageChange(page: number) {
  if (page < 1 || page > totalPages.value || page === currentPage.value) {
    return;
  }

  currentPage.value = page;
  emit('change', page, currentPageSize.value);
}

function handleSizeChange() {
  const newPageSize = currentPageSize.value;
  // 计算新的页码，确保数据连续性
  const newPage = Math.ceil(((currentPage.value - 1) * props.pageSize + 1) / newPageSize);

  currentPage.value = Math.max(1, Math.min(newPage, Math.ceil(props.total / newPageSize)));

  emit('size-change', currentPage.value, newPageSize);
  emit('change', currentPage.value, newPageSize);
}

function handleJump() {
  const page = parseInt(jumpPage.value);
  if (!isNaN(page) && page >= 1 && page <= totalPages.value) {
    handlePageChange(page);
  }
  jumpPage.value = '';
}
// 监听 props 变化
watch(() => props.page, (newPage) => {
  currentPage.value = newPage;
});

watch(() => props.pageSize, (newPageSize) => {
  currentPageSize.value = newPageSize;
});
</script>

<style scoped>
.table-pagination {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  font-size: 14px;
}

.table-pagination.pagination-small {
  font-size: 12px;
  gap: 12px;
  padding: 12px 0;
}

.table-pagination.pagination-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.pagination-total {
  color: #666;
  font-size: 13px;
}

.pagination-size-changer {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
}

.size-selector {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 13px;
  background-color: white;
}

.size-selector:focus {
  outline: none;
  border-color: #3b82f6;
}

.pagination-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: white;
  color: #374151;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-small .pagination-btn {
  padding: 4px 8px;
  font-size: 12px;
  min-width: 28px;
  height: 28px;
}

.pagination-btn:hover:not(.disabled) {
  border-color: #3b82f6;
  color: #3b82f6;
}

.pagination-btn.active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* .pagination-background .pagination-btn {
  background-color: #f9fafb;
} */

.pagination-background .pagination-btn:hover:not(.disabled) {
  background-color: #f3f4f6;
}

.pagination-ellipsis {
  padding: 6px 4px;
  color: #9ca3af;
  font-size: 14px;
}

.pagination-jumper {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
}

.jump-input {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 13px;
  text-align: center;
}

.jump-input:focus {
  outline: none;
  border-color: #3b82f6;
}
</style>
