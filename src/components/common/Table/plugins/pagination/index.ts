import type { TablePlugin, TableCore } from '../../core/pluginApi';
import Pagination from './Pagination.vue';

export interface PaginationOptions {
    page?: number; // 当前页码
    pageSize?: number; // 每页条数
    total?: number; // 总条数
    pageSizes?: number[]; // 可选的每页条数
    layout?: string; // 布局配置
    background?: boolean; // 按钮是否有背景色
    small?: boolean; // 是否为小型分页
    hideOnSinglePage?: boolean; // 只有一页时是否隐藏
    showTotal?: boolean; // 是否显示总条数
    showSizeChanger?: boolean; // 是否显示页数选择器
    showQuickJumper?: boolean; // 是否显示快速跳转
    disabled?: boolean; // 是否禁用
    onChange?: (page: number, pageSize: number) => void; // 页码改变回调
    onShowSizeChange?: (current: number, size: number) => void; // 页数改变回调
}

/**
 * 分页插件
 */
const paginationPlugin: TablePlugin = {
    name: 'Pagination',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 Pagination 组件
        core.registerRenderer('Pagination', Pagination);

        // 2. 扩展表格配置，添加分页相关功能
        core.extendConfig('pagination', {
            enabled: true,
            position: 'bottom', // top, bottom, both
            align: 'right', // left, center, right
            sticky: false, // 是否固定在底部
            ...options,
        });

        // 3. 注册分页助手函数
        core.extendTableApi(
            'setPagination',
            (pagination: Partial<PaginationOptions>) => {
                // 设置分页配置的逻辑
                console.log('Setting pagination:', pagination);
            }
        );

        core.extendTableApi('getPagination', () => {
            // 获取当前分页配置的逻辑
            return {
                page: 1,
                pageSize: 20,
                total: 0,
            };
        });

        // console.log('Pagination plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('Pagination plugin uninstalled');
    },
};

export default paginationPlugin;
