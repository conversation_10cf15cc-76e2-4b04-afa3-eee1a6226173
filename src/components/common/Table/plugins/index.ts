// 导出所有插件
export { default as CompositeColumnPlugin } from './compositeColumn';
export { default as BooleanColumnPlugin } from './booleanColumn';
export { default as ActionColumnPlugin } from './actionColumn';
export { default as NumberColumnPlugin } from './numberColumn';
export { default as DateColumnPlugin } from './dateColumn';
export { default as StatusColumnPlugin } from './statusColumn';
export { default as AvatarColumnPlugin } from './avatarColumn';
export { default as LinkColumnPlugin } from './linkColumn';
export { default as TextColumnPlugin } from './textColumn';
export { default as RelationColumnPlugin } from './relationColumn';
export { default as UserMappingColumnPlugin } from './userMappingColumn';
export { default as SummaryColumnPlugin } from './summaryColumn';
export { default as PaginationPlugin } from './pagination';
export { default as StyleColumnPlugin } from './styleColumn';

// 导出插件类型
export type { CompositeColumnOptions } from './compositeColumn/types';
export type { ActionConfig, ActionColumnOptions } from './actionColumn';
export type { NumberColumnOptions } from './numberColumn';
export type { DateColumnOptions } from './dateColumn';
export type { StatusConfig, StatusColumnOptions } from './statusColumn';
export type { AvatarColumnOptions } from './avatarColumn';
export type { LinkColumnOptions } from './linkColumn';
export type { TextColumnOptions } from './textColumn';
export type { RelationColumnOptions } from './relationColumn';
export type { UserMappingColumnOptions } from './userMappingColumn';
export type {
    SummaryConfig,
    SummaryColumnOptions,
    BuiltinCalculatorType,
} from './summaryColumn';
export type { PaginationOptions } from './pagination';
// export type { TableStyleOptions } from './styleColumn';

// 导出插件管理器
export {
    PluginManager,
    createPluginManager,
    installPlugins,
} from '../core/pluginManager';
export type { TablePlugin, TableCore } from '../core/pluginApi';
export type { ColumnHelper } from '../core/columnHelperTypes';

// 预设插件包
import CompositeColumnPlugin from './compositeColumn';
import BooleanColumnPlugin from './booleanColumn';
import ActionColumnPlugin from './actionColumn';
import NumberColumnPlugin from './numberColumn';
import DateColumnPlugin from './dateColumn';
import StatusColumnPlugin from './statusColumn';
import AvatarColumnPlugin from './avatarColumn';
import LinkColumnPlugin from './linkColumn';
import TextColumnPlugin from './textColumn';
import RelationColumnPlugin from './relationColumn';
import UserMappingColumnPlugin from './userMappingColumn';
import SummaryColumnPlugin from './summaryColumn';
import PaginationPlugin from './pagination';
import StyleColumnPlugin from './styleColumn';
import GroupColumnPlugin from './groupColumn';
import DictColumnPlugin from './dictColumn';
import ImageColumnPlugin from './imageColumn';

/**
 * 基础插件包 - 包含最常用的插件
 */
export const BasicPlugins = [
    StyleColumnPlugin, // 样式插件作为基础插件
    TextColumnPlugin,
    RelationColumnPlugin,
    CompositeColumnPlugin,
    BooleanColumnPlugin,
    ActionColumnPlugin,
    StatusColumnPlugin,
    UserMappingColumnPlugin, // 用户映射插件
    PaginationPlugin,
];

/**
 * 完整插件包 - 包含所有插件
 */
export const AllPlugins = [
    StyleColumnPlugin, // 样式插件
    TextColumnPlugin,
    RelationColumnPlugin,
    CompositeColumnPlugin,
    BooleanColumnPlugin,
    ActionColumnPlugin,
    NumberColumnPlugin,
    DateColumnPlugin,
    StatusColumnPlugin,
    AvatarColumnPlugin,
    LinkColumnPlugin,
    UserMappingColumnPlugin, // 用户映射插件
    SummaryColumnPlugin, // 合计列插件
    PaginationPlugin,
    GroupColumnPlugin,
    DictColumnPlugin,
    ImageColumnPlugin,
];

/**
 * 列渲染插件包 - 专注于列渲染的插件
 */
export const ColumnRenderPlugins = [
    TextColumnPlugin,
    RelationColumnPlugin,
    CompositeColumnPlugin,
    BooleanColumnPlugin,
    NumberColumnPlugin,
    DateColumnPlugin,
    StatusColumnPlugin,
    AvatarColumnPlugin,
    LinkColumnPlugin,
    UserMappingColumnPlugin, // 用户映射插件
    SummaryColumnPlugin, // 合计列插件
];

/**
 * 表格功能插件包 - 专注于表格功能的插件
 */
export const TableFeaturePlugins = [
    StyleColumnPlugin,
    ActionColumnPlugin,
    SummaryColumnPlugin, // 合计列插件
    PaginationPlugin,
];

/**
 * 内容展示插件包 - 专注于内容展示的插件
 */
export const ContentDisplayPlugins = [
    StatusColumnPlugin,
    AvatarColumnPlugin,
    LinkColumnPlugin,
    NumberColumnPlugin,
    DateColumnPlugin,
    UserMappingColumnPlugin, // 用户映射插件
];

/**
 * 业务插件包 - 常用的业务场景插件
 */
export const BusinessPlugins = [
    StyleColumnPlugin,
    CompositeColumnPlugin,
    StatusColumnPlugin,
    AvatarColumnPlugin,
    ActionColumnPlugin,
    DateColumnPlugin,
    UserMappingColumnPlugin, // 用户映射插件
];

// installPlugins 函数已从 ../core/pluginManager 导出

// createPluginManager 函数已从 ../core/pluginManager 导出

/**
 * 插件预设配置
 */
export const PluginPresets = {
    // 最小配置 - 只包含核心插件
    minimal: [StyleColumnPlugin, BooleanColumnPlugin, ActionColumnPlugin],

    // 基础配置 - 常用插件
    basic: BasicPlugins,

    // 增强配置 - 包含更多展示插件
    enhanced: [
        ...BasicPlugins,
        NumberColumnPlugin,
        DateColumnPlugin,
        LinkColumnPlugin,
        SummaryColumnPlugin, // 合计列插件
    ],

    // 完整配置 - 所有插件
    full: AllPlugins,

    // 内容管理系统配置
    cms: [
        StyleColumnPlugin,
        RelationColumnPlugin,
        CompositeColumnPlugin,
        StatusColumnPlugin,
        AvatarColumnPlugin,
        ActionColumnPlugin,
        DateColumnPlugin,
        LinkColumnPlugin,
        UserMappingColumnPlugin, // 用户映射插件
        PaginationPlugin,
    ],

    // 数据分析配置
    analytics: [
        StyleColumnPlugin,
        RelationColumnPlugin,
        NumberColumnPlugin,
        DateColumnPlugin,
        StatusColumnPlugin,
        ActionColumnPlugin,
        UserMappingColumnPlugin, // 用户映射插件
        SummaryColumnPlugin, // 合计列插件 - 数据分析必需
        PaginationPlugin,
    ],
};
