import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn, TableStyleOptions } from '../../src/types';

// export interface TableStyleOptions {
//   // 斑马纹配置
//   striped?: boolean;
//   // 边框配置
//   border?: boolean | 'horizontal' | 'vertical' | 'all';
//   // 紧凑模式
//   compact?: boolean;
//   // 表头样式
//   headerStyle?: 'default' | 'gray' | 'dark' | 'none';
//   // 行高配置
//   rowHeight?: 'small' | 'medium' | 'large' | number;
//   // 圆角边框
//   rounded?: boolean | 'sm' | 'md' | 'lg';
//   // 阴影
//   shadow?: boolean | 'sm' | 'md' | 'lg';
//   // 响应式配置
//   responsive?: boolean;
//   // 自定义CSS类
//   customClass?: string;
//   // 主题模式
//   theme?: 'light' | 'dark' | 'auto';
// }

/**
 * 默认样式配置
 */
export const defaultStyleOptions: TableStyleOptions = {
    striped: true,
    border: 'horizontal',
    compact: true,
    headerStyle: 'gray',
    rowHeight: 'medium',
    rounded: 'md',
    shadow: 'sm',
    responsive: true,
    theme: 'light',
};

/**
 * 样式管理插件
 */
const styleColumnPlugin: TablePlugin = {
    name: 'StyleColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: TableStyleOptions) => {
        const styleConfig = { ...defaultStyleOptions, ...options };

        // 1. 注册全局样式配置
        core.setGlobalConfig('tableStyle', styleConfig);

        // 2. 扩展表格配置，添加样式相关配置
        core.extendConfig('style', {
            // VxeTable样式配置
            stripe: styleConfig.striped,
            border: styleConfig.border === 'all' || styleConfig.border === true,
            round: !!styleConfig.rounded,
            size: styleConfig.compact ? 'mini' : 'medium',

            // 自定义样式类配置
            className: generateStyleClasses(styleConfig),

            // 行样式配置
            rowStyle: generateRowStyle(styleConfig),

            // 表头样式配置
            headerRowStyle: generateHeaderStyle(styleConfig),

            // 单元格样式配置
            cellStyle: generateCellStyle(styleConfig),

            // 表头单元格样式配置
            headerCellStyle: generateHeaderCellStyle(styleConfig),
        });

        // 3. 扩展列助手，添加样式配置方法
        core.extendColumnHelper(
            'withStyle',
            (
                column: BaseColumn,
                customStyle: Partial<TableStyleOptions> = {}
            ): BaseColumn => {
                const mergedStyle = { ...styleConfig, ...customStyle };
                return {
                    ...column,
                    className:
                        `${column.className || ''} ${generateColumnStyleClasses(mergedStyle)}`.trim(),
                    options: {
                        ...column.options,
                        customStyle: generateCellStyle(mergedStyle),
                    },
                };
            }
        );

        // 4. 提供样式更新方法
        core.extendTableApi(
            'updateTableStyle',
            (newStyleOptions: Partial<TableStyleOptions>) => {
                const updatedConfig = { ...styleConfig, ...newStyleOptions };
                core.setGlobalConfig('tableStyle', updatedConfig);
                // 触发样式更新事件
                core.emit('style:updated', updatedConfig);
            }
        );

        // 5. 提供获取当前样式配置的方法
        core.extendTableApi('getTableStyle', () => {
            return core.getGlobalConfig('tableStyle');
        });

        // console.log('StyleColumn plugin installed with config:', styleConfig);
    },

    uninstall: (core: TableCore) => {
        // console.log('StyleColumn plugin uninstalled');
    },
};

/**
 * 生成样式类名
 */
function generateStyleClasses(config: TableStyleOptions): string {
    const classes: string[] = ['v-table-styled'];

    // 基础样式类
    if (config.striped) classes.push('table-striped');
    if (config.compact) classes.push('table-compact');
    if (config.responsive) classes.push('table-responsive');

    // 边框样式
    if (config.border === 'horizontal') classes.push('border-horizontal');
    else if (config.border === 'vertical') classes.push('border-vertical');
    else if (config.border === 'all' || config.border === true)
        classes.push('border-all');
    else classes.push('border-none');

    // 表头样式
    if (config.headerStyle && config.headerStyle !== 'none') {
        classes.push(`header-${config.headerStyle}`);
    }

    // 圆角
    if (config.rounded) {
        if (typeof config.rounded === 'string') {
            classes.push(`rounded-${config.rounded}`);
        } else {
            classes.push('rounded-md');
        }
    }

    // 阴影
    if (config.shadow) {
        if (typeof config.shadow === 'string') {
            classes.push(`shadow-${config.shadow}`);
        } else {
            classes.push('shadow-sm');
        }
    }

    // 主题
    if (config.theme && config.theme !== 'auto') {
        classes.push(`theme-${config.theme}`);
    }

    // 自定义类
    if (config.customClass) {
        classes.push(config.customClass);
    }

    return classes.join(' ');
}

/**
 * 生成列样式类名
 */
function generateColumnStyleClasses(config: TableStyleOptions): string {
    const classes: string[] = [];

    if (config.compact) classes.push('col-compact');
    if (config.responsive) classes.push('col-responsive');

    return classes.join(' ');
}

/**
 * 生成行样式
 */
function generateRowStyle(config: TableStyleOptions): any {
    const style: any = {};

    // 行高配置
    if (config.rowHeight) {
        if (typeof config.rowHeight === 'number') {
            style.height = `${config.rowHeight}px`;
        } else {
            const heightMap = {
                small: '32px',
                medium: '40px',
                large: '48px',
            };
            style.height = heightMap[config.rowHeight];
        }
    }

    return style;
}

/**
 * 生成表头样式
 */
function generateHeaderStyle(config: TableStyleOptions): any {
    const style: any = {};

    // 表头背景色
    if (config.headerStyle === 'gray') {
        style.backgroundColor = '#f8fafc';
        style.borderBottom = '1px solid #e2e8f0';
    } else if (config.headerStyle === 'dark') {
        style.backgroundColor = '#374151';
        style.color = '#f9fafb';
        style.borderBottom = '1px solid #4b5563';
    }

    return style;
}

/**
 * 生成单元格样式
 */
function generateCellStyle(config: TableStyleOptions): any {
    const style: any = {};

    // 紧凑模式
    if (config.compact) {
        style.padding = '8px 12px';
    }

    return style;
}

/**
 * 生成表头单元格样式
 */
function generateHeaderCellStyle(config: TableStyleOptions): any {
    const style: any = {};

    // 表头样式
    if (config.headerStyle === 'gray') {
        style.fontWeight = '600';
        style.fontSize = '14px';
        style.color = '#374151';
    } else if (config.headerStyle === 'dark') {
        style.fontWeight = '600';
        style.fontSize = '14px';
        style.color = '#f9fafb';
    }

    // 紧凑模式
    if (config.compact) {
        style.padding = '12px';
    }

    return style;
}

export default styleColumnPlugin;
