/* Table 组件插件化样式 - TailwindCSS 4 兼容 */

/* 基础表格样式容器 */
.v-table-styled {
  width: 100%;
  background-color: white;
  overflow: hidden;
}

/* 斑马纹效果 */
.v-table-styled.table-striped :deep(.vxe-body--row:nth-child(even)) {
  background-color: #f9fafb;
}

.v-table-styled.table-striped :deep(.vxe-body--row:nth-child(odd)) {
  background-color: white;
}

/* 紧凑模式 */
.v-table-styled.table-compact :deep(.vxe-cell) {
  padding: 4px 8px;
  font-size: 14px;
}

.v-table-styled.table-compact :deep(.vxe-header--column .vxe-cell) {
  padding: 8px;
  font-size: 14px;
  font-weight: 600;
}

/* 边框样式 */
/* 水平边框 - 只显示行之间的边框 */
.v-table-styled.border-horizontal :deep(.vxe-table) {
  border-collapse: collapse;
  border-spacing: 0;
}

.v-table-styled.border-horizontal :deep(.vxe-header--row) {
  border-bottom: 1px solid #e5e7eb;
}

.v-table-styled.border-horizontal :deep(.vxe-body--row) {
  border-bottom: 1px solid #f3f4f6;
}

.v-table-styled.border-horizontal :deep(.vxe-body--row:last-child) {
  border-bottom: none;
}

/* 垂直边框 - 只显示列之间的边框 */
.v-table-styled.border-vertical :deep(.vxe-cell) {
  border-right: 1px solid #f3f4f6;
}

.v-table-styled.border-vertical :deep(.vxe-cell:last-child) {
  border-right: none;
}

/* 全边框 */
.v-table-styled.border-all :deep(.vxe-table) {
  border: 1px solid #e5e7eb;
}

.v-table-styled.border-all :deep(.vxe-cell) {
  border-right: 1px solid #f3f4f6;
  border-bottom: 1px solid #f3f4f6;
}

.v-table-styled.border-all :deep(.vxe-header--column .vxe-cell) {
  border-right: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
}

/* 无边框 */
.v-table-styled.border-none :deep(.vxe-table) {
  border: none;
}

.v-table-styled.border-none :deep(.vxe-cell) {
  border: none;
}

/* 表头样式 */
/* 灰色表头 */
.v-table-styled.header-gray :deep(.vxe-header--column) {
  background-color: #f9fafb;
}

.v-table-styled.header-gray :deep(.vxe-header--column .vxe-cell) {
  color: #374151;
  font-weight: 600;
  background-color: #f9fafb;
}

/* 深色表头 */
.v-table-styled.header-dark :deep(.vxe-header--column) {
  background-color: #1f2937;
}

.v-table-styled.header-dark :deep(.vxe-header--column .vxe-cell) {
  color: white;
  font-weight: 600;
  background-color: #1f2937;
}

/* 默认表头 */
.v-table-styled.header-default :deep(.vxe-header--column .vxe-cell) {
  color: #4b5563;
  font-weight: 500;
}

/* 圆角样式 */
.v-table-styled.rounded-sm {
  border-radius: 0.125rem;
  overflow: hidden;
}

.v-table-styled.rounded-md {
  border-radius: 0.375rem;
  overflow: hidden;
}

.v-table-styled.rounded-lg {
  border-radius: 0.5rem;
  overflow: hidden;
}

/* 阴影样式 */
.v-table-styled.shadow-sm {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.v-table-styled.shadow-md {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.v-table-styled.shadow-lg {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* 主题样式 */
.v-table-styled.theme-light {
  background-color: white;
  color: #111827;
}

.v-table-styled.theme-dark {
  background-color: #111827;
  color: white;
}

.v-table-styled.theme-dark :deep(.vxe-body--row:nth-child(even)) {
  background-color: #1f2937;
}

.v-table-styled.theme-dark :deep(.vxe-body--row:nth-child(odd)) {
  background-color: #111827;
}

/* 响应式样式 */
.v-table-styled.table-responsive {
  width: 100%;
  overflow-x: auto;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .v-table-styled.table-responsive :deep(.vxe-cell) {
    font-size: 12px;
    padding: 8px;
  }
  
  .v-table-styled.table-responsive :deep(.vxe-header--column .vxe-cell) {
    font-size: 12px;
    padding: 8px;
  }
  
  /* 在小屏幕上隐藏部分列 */
  .v-table-styled.table-responsive :deep(.col-responsive.hide-mobile) {
    display: none;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .v-table-styled.table-responsive :deep(.vxe-cell) {
    font-size: 14px;
    padding: 12px;
  }
  
  .v-table-styled.table-responsive :deep(.col-responsive.hide-tablet) {
    display: none;
  }
}

/* 列样式 */
.col-compact :deep(.vxe-cell) {
  padding: 4px 8px;
}

/* 悬停效果 */
.v-table-styled :deep(.vxe-body--row:hover) {
  background-color: #dbeafe;
  transition: background-color 0.2s ease;
}

.v-table-styled.theme-dark :deep(.vxe-body--row:hover) {
  background-color: #374151;
}

/* 选中行样式 */
.v-table-styled :deep(.vxe-body--row.row--checked) {
  background-color: #bfdbfe;
}

.v-table-styled.theme-dark :deep(.vxe-body--row.row--checked) {
  background-color: #1e3a8a;
}

/* 加载状态样式 */
.v-table-styled :deep(.vxe-loading) {
  background-color: rgb(255 255 255 / 0.75);
}

.v-table-styled.theme-dark :deep(.vxe-loading) {
  background-color: rgb(17 24 39 / 0.75);
}

/* 空数据状态 */
.v-table-styled :deep(.vxe-table--empty-placeholder) {
  color: #6b7280;
  padding: 32px 0;
}

.v-table-styled.theme-dark :deep(.vxe-table--empty-placeholder) {
  color: #9ca3af;
}

/* 固定列样式 */
.v-table-styled :deep(.vxe-table--fixed-left) {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  background-color: white;
}

.v-table-styled :deep(.vxe-table--fixed-right) {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  background-color: white;
}

.v-table-styled.theme-dark :deep(.vxe-table--fixed-left) {
  background-color: #111827;
}

.v-table-styled.theme-dark :deep(.vxe-table--fixed-right) {
  background-color: #111827;
}

/* 滚动条样式 */
.v-table-styled :deep(.vxe-table--body-wrapper)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.v-table-styled :deep(.vxe-table--body-wrapper)::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 4px;
}

.v-table-styled :deep(.vxe-table--body-wrapper)::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 4px;
}

.v-table-styled :deep(.vxe-table--body-wrapper)::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

.v-table-styled.theme-dark :deep(.vxe-table--body-wrapper)::-webkit-scrollbar-track {
  background-color: #1f2937;
}

.v-table-styled.theme-dark :deep(.vxe-table--body-wrapper)::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.v-table-styled.theme-dark :deep(.vxe-table--body-wrapper)::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* 过渡动画 */
.v-table-styled :deep(.vxe-body--row) {
  transition: background-color 0.15s ease;
}

.v-table-styled :deep(.vxe-cell) {
  transition: all 0.15s ease;
}

/* 排序图标样式 */
.v-table-styled :deep(.vxe-sort-icon) {
  color: #9ca3af;
}

.v-table-styled :deep(.vxe-sort-icon:hover) {
  color: #4b5563;
}

.v-table-styled.theme-dark :deep(.vxe-sort-icon) {
  color: #6b7280;
}

.v-table-styled.theme-dark :deep(.vxe-sort-icon:hover) {
  color: #d1d5db;
}

/* 筛选器样式 */
.v-table-styled :deep(.vxe-filter-icon) {
  color: #9ca3af;
}

.v-table-styled :deep(.vxe-filter-icon:hover) {
  color: #3b82f6;
}

.v-table-styled.theme-dark :deep(.vxe-filter-icon) {
  color: #6b7280;
}

.v-table-styled.theme-dark :deep(.vxe-filter-icon:hover) {
  color: #60a5fa;
} 