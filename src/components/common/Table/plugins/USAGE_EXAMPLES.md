# Table 插件系统使用示例

## 快速开始

### 1. 基础使用
```typescript
import { createPluginManager, BasicPlugins } from '@/components/common/Table/plugins';

// 创建插件管理器并安装基础插件
const pluginManager = createPluginManager(BasicPlugins);

// 获取列助手对象
const column = pluginManager.getColumnHelper();

// 定义列配置
const columns = [
  column.index(),
  column.selection(),
  column.composite('customer', '客户信息', {
    main: { field: 'name' },
    subs: [
      { field: 'phone', label: '电话' },
      { field: 'email', label: '邮箱' }
    ]
  }),
  column.activeStatus('valid', '状态'),
  column.action('操作', {
    actions: [
      { text: '编辑', color: 'primary', onClick: (row) => console.log('Edit:', row) },
      { text: '删除', color: 'danger', onClick: (row) => console.log('Delete:', row) }
    ]
  })
];
```

### 2. 预设配置使用
```typescript
import { createPluginManager, PluginPresets } from '@/components/common/Table/plugins';

// 使用预设配置
const pluginManager = createPluginManager(PluginPresets.cms);
const column = pluginManager.getColumnHelper();

// CMS 场景的列配置
const columns = [
  column.userAvatar('avatar', 'name', '用户'),
  column.status('status', '状态', {
    'draft': { value: 'draft', label: '草稿', bgColor: '#6b7280' },
    'published': { value: 'published', label: '已发布', bgColor: '#10b981' },
    'archived': { value: 'archived', label: '已归档', bgColor: '#f59e0b' }
  }),
  column.datetime('created_at', '创建时间'),
  column.action('操作')
];
```

## 插件详细使用

### CompositeColumn 插件
复合列支持在单个单元格内显示多层信息。

```typescript
// 基础复合列
column.composite('user', '用户信息', {
  main: { field: 'name', style: { fontWeight: 'bold' } },
  subs: [
    { field: 'email', label: '邮箱', icon: 'email' },
    { field: 'phone', label: '电话', icon: 'phone' }
  ],
  icon: { field: 'avatar', type: 'image', size: 'medium' }
})

// 带操作的复合列
column.composite('product', '产品信息', {
  main: { field: 'title' },
  subs: [
    { field: 'price', label: '价格', formatter: (value) => `¥${value}` },
    { field: 'stock', label: '库存' }
  ],
  actions: [
    { text: '查看', onClick: (row) => viewProduct(row.id) }
  ],
  layout: 'vertical'
})

// 支持计算字段
column.composite('summary', '统计', {
  main: { 
    computed: (row) => `${row.orders_count} 个订单`,
    style: { color: '#3b82f6' }
  },
  subs: [
    { 
      computed: (row) => `总金额: ¥${row.total_amount}`,
      style: { fontWeight: 'bold' }
    }
  ]
})
```

### StatusColumn 插件
状态列支持多种视觉样式和预定义状态。

```typescript
// 自定义状态映射
column.status('order_status', '订单状态', {
  'pending': { 
    value: 'pending', 
    label: '待付款', 
    bgColor: '#f59e0b',
    icon: 'clock' 
  },
  'paid': { 
    value: 'paid', 
    label: '已付款', 
    bgColor: '#10b981',
    icon: 'check' 
  },
  'shipped': { 
    value: 'shipped', 
    label: '已发货', 
    bgColor: '#3b82f6',
    icon: 'truck' 
  },
  'completed': { 
    value: 'completed', 
    label: '已完成', 
    bgColor: '#8b5cf6',
    icon: 'star' 
  }
}, {
  variant: 'filled',
  shape: 'pill',
  size: 'medium'
})

// 点状样式
column.status('priority', '优先级', {
  'high': { value: 'high', label: '高', bgColor: '#ef4444' },
  'medium': { value: 'medium', label: '中', bgColor: '#f59e0b' },
  'low': { value: 'low', label: '低', bgColor: '#10b981' }
}, {
  variant: 'dot',
  size: 'small'
})

// 预定义状态
column.activeStatus('is_active', '启用状态')
column.orderStatus('status', '订单状态')
```

### AvatarColumn 插件
头像列支持图片、字母、图标等多种回退方式。

```typescript
// 基础头像
column.avatar('avatar', '头像', {
  size: 'medium',
  shape: 'circle',
  fallbackType: 'letter'
})

// 用户信息列（头像+姓名）
column.userAvatar('avatar', 'name', '用户', {
  namePosition: 'right',
  size: 'large',
  clickable: true,
  onClick: (row) => goToUserProfile(row.id)
})

// 简单头像
column.simpleAvatar('photo', '照片', {
  size: 'small',
  fallbackIcon: 'user',
  borderColor: '#e5e7eb'
})

// 自定义样式头像
column.avatar('creator_avatar', '创建者', {
  nameField: 'creator_name',
  showName: true,
  namePosition: 'bottom',
  size: 48,
  shape: 'rounded',
  fallbackBg: '#3b82f6'
})
```

### LinkColumn 插件
链接列支持多种链接类型和自定义样式。

```typescript
// 基础链接
column.link('website', '网站', (row) => row.website_url, {
  target: '_blank',
  icon: 'external-link',
  maxLength: 30
})

// 邮箱链接
column.emailLink('email', '邮箱地址', {
  color: '#059669',
  underline: false
})

// 电话链接
column.phoneLink('phone', '联系电话')

// 网址链接
column.urlLink('homepage', '主页', {
  maxLength: 25,
  tooltip: true
})

// 自定义链接
column.link('profile', '个人资料', '/user/profile', {
  textField: 'username',
  icon: 'user',
  iconPosition: 'left',
  disabled: (row) => !row.profile_public,
  onClick: (row, event) => {
    event.preventDefault();
    openUserProfile(row.id);
  }
})
```

### NumberColumn 插件
数字列支持货币、百分比、千分位等格式化。

```typescript
// 货币格式
column.currency('price', '价格', '¥', {
  precision: 2,
  color: {
    positive: '#10b981',
    negative: '#ef4444'
  }
})

// 百分比格式
column.percentage('growth_rate', '增长率', {
  precision: 1,
  showPlusSign: true
})

// 自定义数字格式
column.number('quantity', '数量', {
  precision: 0,
  thousandsSeparator: true,
  unit: '件',
  color: {
    zero: '#6b7280'
  }
})

// 带前缀后缀的数字
column.number('score', '评分', {
  precision: 1,
  prefix: '★ ',
  suffix: ' 分',
  color: {
    positive: '#f59e0b'
  }
})
```

### DateColumn 插件
日期列支持多种格式和相对时间显示。

```typescript
// 基础日期
column.date('created_at', '创建日期')

// 日期时间
column.datetime('updated_at', '更新时间')

// 仅日期
column.dateOnly('birth_date', '出生日期')

// 仅时间
column.timeOnly('login_time', '登录时间')

// 相对时间
column.relativeTime('last_seen', '最后在线')

// 自定义格式
column.date('event_date', '活动日期', {
  format: 'YYYY年MM月DD日',
  nullDisplay: '待定'
})
```

### BooleanColumn 插件
布尔列支持多种显示样式和交互。

```typescript
// 标签样式
column.boolean('is_featured', '特色推荐', {
  style: 'tag',
  trueText: '推荐',
  falseText: '普通',
  trueColor: '#10b981',
  falseColor: '#6b7280'
})

// 开关样式
column.boolean('enabled', '启用状态', {
  style: 'switch',
  onChange: (value, row) => {
    updateStatus(row.id, value);
  }
})

// 文本样式
column.boolean('is_public', '公开', {
  style: 'text',
  trueText: '是',
  falseText: '否'
})
```

### ActionColumn 插件
操作列支持按钮和下拉菜单。

```typescript
// 基础操作列
column.action('操作', {
  actions: [
    {
      text: '编辑',
      icon: 'edit',
      color: 'primary',
      onClick: (row) => editItem(row.id)
    },
    {
      text: '删除',
      icon: 'delete',
      color: 'danger',
      disabled: (row) => !row.can_delete,
      onClick: (row) => deleteItem(row.id)
    }
  ],
  maxVisible: 2
})

// 带更多操作的列
column.action('操作', {
  actions: [
    { text: '查看', icon: 'eye', onClick: (row) => viewItem(row) },
    { text: '编辑', icon: 'edit', onClick: (row) => editItem(row) }
  ],
  moreActions: [
    { text: '复制', icon: 'copy', onClick: (row) => copyItem(row) },
    { text: '移动', icon: 'move', onClick: (row) => moveItem(row) },
    { text: '删除', icon: 'delete', color: 'danger', onClick: (row) => deleteItem(row) }
  ],
  maxVisible: 2,
  size: 'small'
})

// 权限控制的操作
column.action('操作', {
  actions: [
    {
      text: '编辑',
      visible: (row) => hasPermission('edit', row),
      onClick: (row) => editItem(row)
    },
    {
      text: '删除',
      visible: (row) => hasPermission('delete', row),
      color: 'danger',
      onClick: (row) => deleteItem(row)
    }
  ]
})
```

## 高级用法

### 插件组合使用
```typescript
// 用户管理表格
const userColumns = [
  column.selection(),
  column.userAvatar('avatar', 'name', '用户', {
    clickable: true,
    onClick: (row) => viewUserProfile(row.id)
  }),
  column.emailLink('email', '邮箱'),
  column.phoneLink('phone', '电话'),
  column.activeStatus('is_active', '状态'),
  column.relativeTime('last_login_at', '最后登录'),
  column.action('操作', {
    actions: [
      { text: '编辑', onClick: (row) => editUser(row) },
      { text: '重置密码', onClick: (row) => resetPassword(row) }
    ]
  })
];

// 订单管理表格
const orderColumns = [
  column.text('order_no', '订单号'),
  column.composite('customer', '客户信息', {
    main: { field: 'customer_name' },
    subs: [
      { field: 'customer_phone', label: '电话' }
    ]
  }),
  column.currency('total_amount', '金额', '¥'),
  column.orderStatus('status', '状态'),
  column.datetime('created_at', '下单时间'),
  column.action('操作')
];
```

### 自定义插件开发
```typescript
// 创建自定义进度条插件
const progressPlugin: TablePlugin = {
  name: 'ProgressColumn',
  version: '1.0.0',
  install: (core: TableCore) => {
    // 注册渲染器
    core.registerRenderer('ProgressRenderer', ProgressRenderer);
    
    // 注册列类型
    core.registerColumnType('progress', {
      cellRender: { name: 'ProgressRenderer' }
    });
    
    // 扩展列助手
    core.extendColumnHelper('progress', (field, title, options = {}) => ({
      field,
      title,
      cellRender: { name: 'ProgressRenderer', props: options },
      ...options
    }));
  }
};

// 使用自定义插件
pluginManager.use(progressPlugin);
const columns = [
  column.progress('completion', '完成度', {
    max: 100,
    showText: true,
    color: '#10b981'
  })
];
```

### 动态插件管理
```typescript
// 运行时安装插件
if (needAdvancedFeatures) {
  pluginManager.use(AdvancedTablePlugin);
}

// 检查插件状态
if (pluginManager.isPluginRegistered('StatusColumn')) {
  columns.push(column.status('status', '状态', statusMap));
}

// 获取调试信息
const debugInfo = pluginManager.getDebugInfo();
console.log('已安装的插件:', debugInfo.plugins);
console.log('可用的列类型:', debugInfo.columnTypes);
```

## 最佳实践

### 1. 性能优化
```typescript
// 使用最小插件集
const manager = createPluginManager(PluginPresets.minimal);

// 按需加载插件
if (showAdvancedColumns) {
  manager.use(NumberColumnPlugin);
  manager.use(DateColumnPlugin);
}
```

### 2. 类型安全
```typescript
import type { StatusColumnOptions } from '@/components/common/Table/plugins';

const statusColumn: StatusColumnOptions = column.status('status', '状态', statusMap, {
  variant: 'filled',
  size: 'medium'
});
```

### 3. 配置复用
```typescript
// 定义通用配置
const commonActionConfig = {
  maxVisible: 2,
  size: 'medium' as const
};

// 复用配置
const userActions = column.action('操作', {
  ...commonActionConfig,
  actions: userActionList
});

const orderActions = column.action('操作', {
  ...commonActionConfig,
  actions: orderActionList
});
```

### 4. 错误处理
```typescript
try {
  pluginManager.use(CustomPlugin);
} catch (error) {
  console.error('插件安装失败:', error);
  // 降级方案
  pluginManager.use(BasicPlugin);
}
```

这些示例展示了插件系统的强大功能和灵活性，帮助开发者快速构建功能丰富的表格应用。 