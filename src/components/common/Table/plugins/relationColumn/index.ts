import { BaseDialogConfig, BaseDrawerConfig } from '@/components';
import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import RelationRenderer from './RelationRenderer.vue';

export interface RelationColumnOptions extends BaseColumn {
    relationType?: 'oneToOne' | 'oneToMany' | 'manyToOne' | 'manyToMany';
    displayField?: string; // 显示字段名，如 'name', 'title', 'nick_name'
    fallbackFields?: string[]; // 备选字段，默认 ['name', 'title', 'nick_name', 'code', 'id']
    template?: string; // 显示模板，如 '${name} (${code})'
    itemName?: string; // 项目名称，如 '订单', '商品', '条'
    showEmpty?: boolean; // 是否显示空值（如 0项）
    showCount?: boolean; // 是否显示数量
    clickable?: boolean; // 是否可点击
    maxDisplay?: number; // 最多显示几个项目（对数组关系）
    separator?: string; // 分隔符，默认 ', '
    nullDisplay?: string; // 空值显示文本

    // 事件处理
    onItemClick?: (data: any, row: any) => void; // 点击项目事件
    onMoreClick?: (allData: any[], row: any) => void; // 点击更多事件

    // 样式配置
    itemStyle?: any; // 单个项目样式
    countStyle?: any; // 数量样式
    linkStyle?: any; // 链接样式

    dialogForm?: BaseDialogConfig; // 弹窗表单配置
    drawerForm?: BaseDrawerConfig; // 抽屉表单配置
}

/**
 * 关系列插件
 */
const relationColumnPlugin: TablePlugin = {
    name: 'RelationColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 RelationRenderer 渲染器
        core.registerRenderer('RelationRenderer', RelationRenderer);

        // 2. 注册 relation 列类型
        core.registerColumnType('relation', {
            cellRender: { name: 'RelationRenderer' },
            sortable: true,
        });

        // 3. 扩展 column 辅助对象，添加通用 relation 方法
        core.extendColumnHelper(
            'relation',
            (
                field: string,
                title: string,
                options: Omit<RelationColumnOptions, 'field' | 'title'> = {}
            ): RelationColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'RelationRenderer',
                        props: options,
                    },
                    relationType: options.relationType || 'manyToOne',
                    displayField: options.displayField || 'name',
                    fallbackFields: options.fallbackFields || [
                        'name',
                        'title',
                        'nick_name',
                        'code',
                        'id',
                    ],
                    itemName: options.itemName || '项',
                    showEmpty: options.showEmpty !== false,
                    showCount: options.showCount,
                    clickable: options.clickable || false,
                    separator: options.separator || ', ',
                    nullDisplay: options.nullDisplay || '-',
                    align:
                        options.align ||
                        (options.relationType === 'oneToMany' ||
                        options.relationType === 'manyToMany'
                            ? 'center'
                            : 'left'),
                    width:
                        options.width ||
                        (options.relationType === 'oneToMany' ||
                        options.relationType === 'manyToMany'
                            ? 100
                            : 150),
                    ...options,
                } as RelationColumnOptions;
            }
        );

        // 4. 扩展一对一关系
        core.extendColumnHelper(
            'oneToOne',
            (
                field: string,
                title: string,
                options: Omit<
                    RelationColumnOptions,
                    'field' | 'title' | 'relationType'
                > = {}
            ): RelationColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'RelationRenderer',
                        props: { relationType: 'oneToOne', ...options },
                    },
                    relationType: 'oneToOne',
                    align: 'left',
                    width: 150,
                    ...options,
                } as RelationColumnOptions;
            }
        );

        // 5. 扩展一对多关系
        core.extendColumnHelper(
            'oneToMany',
            (
                field: string,
                title: string,
                options: Omit<
                    RelationColumnOptions,
                    'field' | 'title' | 'relationType'
                > = {}
            ): RelationColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'RelationRenderer',
                        props: {
                            relationType: 'oneToMany',
                            showCount: true,
                            ...options,
                        },
                    },
                    relationType: 'oneToMany',
                    showCount: true,
                    align: 'center',
                    width: 100,
                    itemName: options.itemName || '项',
                    ...options,
                } as RelationColumnOptions;
            }
        );

        // 6. 扩展多对一关系
        core.extendColumnHelper(
            'manyToOne',
            (
                field: string,
                title: string,
                options: Omit<
                    RelationColumnOptions,
                    'field' | 'title' | 'relationType'
                > = {}
            ): RelationColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'RelationRenderer',
                        props: { relationType: 'manyToOne', ...options },
                    },
                    relationType: 'manyToOne',
                    align: 'left',
                    width: 150,
                    ...options,
                } as RelationColumnOptions;
            }
        );

        // 7. 扩展多对多关系
        core.extendColumnHelper(
            'manyToMany',
            (
                field: string,
                title: string,
                options: Omit<
                    RelationColumnOptions,
                    'field' | 'title' | 'relationType'
                > = {}
            ): RelationColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'RelationRenderer',
                        props: {
                            relationType: 'manyToMany',
                            showCount: true,
                            ...options,
                        },
                    },
                    relationType: 'manyToMany',
                    showCount: true,
                    align: 'center',
                    width: 120,
                    itemName: options.itemName || '项',
                    ...options,
                } as RelationColumnOptions;
            }
        );

        // console.log('RelationColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('RelationColumn plugin uninstalled');
    },
};

export default relationColumnPlugin;
