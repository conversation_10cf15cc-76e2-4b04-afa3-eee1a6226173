# 合计列插件 (SummaryColumn)

合计列插件为表格提供底部合计行功能，支持多种计算类型和自定义计算函数。

## 🚀 快速开始

### 基础使用

```typescript
import { createPluginManager, PluginPresets } from '@/components/common/Table/plugins';

// 创建包含合计列插件的管理器
const pluginManager = createPluginManager(PluginPresets.analytics);
const column = pluginManager.getColumnHelper();

// 在表格配置中启用合计功能
const tableConfig = {
  summaryConfig: {
    enabled: true,
    position: 'bottom',
    summaries: {
      'amount': {
        calculator: 'sum',
        label: '总计',
        formatter: (value) => `¥${value.toFixed(2)}`,
        precision: 2,
        prefix: '¥'
      },
      'quantity': {
        calculator: 'sum',
        label: '数量合计'
      },
      'price': {
        calculator: 'avg',
        label: '平均单价',
        precision: 2
      }
    }
  }
};
```

## 🎯 核心特性

- **多种计算类型** - 内置求和、平均值、最大值、最小值、计数等
- **自定义计算函数** - 支持复杂的业务计算逻辑
- **条件计算** - 基于条件的筛选计算
- **格式化显示** - 灵活的数值格式化
- **样式自定义** - 完全的样式控制
- **位置控制** - 顶部或底部显示

## 📋 内置计算类型

| 计算类型 | 说明 | 示例 |
|---------|------|------|
| `sum` | 求和 | 总金额、总数量 |
| `avg` | 平均值 | 平均价格、平均评分 |
| `max` | 最大值 | 最高价格、最大数量 |
| `min` | 最小值 | 最低价格、最小数量 |
| `count` | 计数 | 记录总数 |
| `countNonNull` | 非空计数 | 有效记录数 |
| `median` | 中位数 | 价格中位数 |
| `variance` | 方差 | 数据分散程度 |
| `stddev` | 标准差 | 数据标准偏差 |

## 🔧 配置选项

### SummaryConfig - 单列合计配置

```typescript
interface SummaryConfig {
  field?: string;                           // 要计算的字段
  calculator?: BuiltinCalculatorType | SummaryCalculator; // 计算函数
  label?: string;                          // 显示标签
  formatter?: (value: any, config: SummaryConfig) => string; // 格式化函数
  condition?: (value: any, row: any) => boolean; // 条件过滤
  precision?: number;                      // 数字精度
  prefix?: string;                         // 前缀
  suffix?: string;                         // 后缀
  style?: any;                            // 样式
  showEmpty?: boolean;                     // 是否显示空值
  emptyText?: string;                      // 空值显示文本
}
```

### SummaryColumnOptions - 整体配置

```typescript
interface SummaryColumnOptions {
  enabled?: boolean;                       // 是否启用合计
  position?: 'bottom' | 'top';            // 位置
  summaries?: Record<string, SummaryConfig>; // 各列的合计配置
  style?: any;                            // 合计行样式
  className?: string;                      // 合计行CSS类
  height?: number;                         // 合计行高度
  sticky?: boolean;                        // 是否固定
  showBorder?: boolean;                    // 是否显示边框
  background?: string;                     // 背景色
}
```

## 📚 使用示例

### 1. 基础数值合计

```typescript
const summaryConfig = {
  enabled: true,
  summaries: {
    'total_amount': {
      calculator: 'sum',
      label: '总金额',
      formatter: (value) => `¥${Number(value).toLocaleString()}`,
      style: { fontWeight: 'bold', color: '#52c41a' }
    },
    'item_count': {
      calculator: 'count',
      label: '订单数量',
      style: { color: '#1890ff' }
    }
  }
};
```

### 2. 条件计算

```typescript
const summaryConfig = {
  enabled: true,
  summaries: {
    'paid_amount': {
      calculator: 'sum',
      label: '已付金额',
      condition: (value, row) => row.status === 'paid',
      formatter: (value) => `¥${value.toFixed(2)}`,
      style: { color: '#52c41a' }
    },
    'pending_count': {
      calculator: 'count',
      label: '待付款',
      condition: (value, row) => row.status === 'pending',
      style: { color: '#fa8c16' }
    }
  }
};
```

### 3. 自定义计算函数

```typescript
const summaryConfig = {
  enabled: true,
  summaries: {
    'completion_rate': {
      calculator: (values, data) => {
        const completed = data.filter(row => row.status === 'completed').length;
        return data.length > 0 ? (completed / data.length * 100) : 0;
      },
      label: '完成率',
      formatter: (value) => `${value.toFixed(1)}%`,
      style: { fontWeight: 'bold' }
    },
    'avg_score': {
      calculator: (values, data) => {
        const scores = data.map(row => row.score).filter(score => score != null);
        return scores.length > 0 
          ? scores.reduce((sum, score) => sum + score, 0) / scores.length 
          : 0;
      },
      label: '平均评分',
      precision: 1,
      suffix: ' 分'
    }
  }
};
```

### 4. 复杂业务场景

```typescript
// 财务报表场景
const financialSummary = {
  enabled: true,
  position: 'bottom',
  sticky: true,
  background: '#f0f9f0',
  summaries: {
    'revenue': {
      calculator: 'sum',
      label: '总收入',
      formatter: (value) => `¥${Number(value).toLocaleString()}`,
      style: { fontWeight: 'bold', color: '#52c41a' }
    },
    'cost': {
      calculator: 'sum',
      label: '总成本',
      formatter: (value) => `¥${Number(value).toLocaleString()}`,
      style: { fontWeight: 'bold', color: '#f5222d' }
    },
    'profit': {
      calculator: (values, data) => {
        const totalRevenue = data.reduce((sum, row) => sum + (row.revenue || 0), 0);
        const totalCost = data.reduce((sum, row) => sum + (row.cost || 0), 0);
        return totalRevenue - totalCost;
      },
      label: '净利润',
      formatter: (value) => {
        const color = value >= 0 ? '#52c41a' : '#f5222d';
        const sign = value >= 0 ? '+' : '';
        return `${sign}¥${Number(value).toLocaleString()}`;
      },
      style: { fontWeight: 'bold' }
    }
  }
};

// 库存管理场景
const inventorySummary = {
  enabled: true,
  summaries: {
    'total_stock': {
      calculator: 'sum',
      label: '库存总量',
      suffix: ' 件'
    },
    'low_stock_count': {
      calculator: 'count',
      label: '低库存商品',
      condition: (value, row) => row.stock < row.min_stock,
      style: { color: '#fa8c16' }
    },
    'out_of_stock_count': {
      calculator: 'count',
      label: '缺货商品',
      condition: (value, row) => row.stock === 0,
      style: { color: '#f5222d' }
    },
    'total_value': {
      calculator: (values, data) => {
        return data.reduce((sum, row) => sum + (row.stock * row.price), 0);
      },
      label: '库存总价值',
      formatter: (value) => `¥${Number(value).toLocaleString()}`,
      style: { fontWeight: 'bold' }
    }
  }
};
```

### 5. 在表格组件中使用

```vue
<template>
  <BaseTable
    :columns="columns"
    :fetch-api="fetchData"
    :summary-config="summaryConfig"
  />
</template>

<script setup>
import { createPluginManager, PluginPresets } from '@/components/common/Table/plugins';

const pluginManager = createPluginManager(PluginPresets.analytics);
const column = pluginManager.getColumnHelper();

const columns = [
  column.selection({ fixed: 'left' }),
  column.text('product_name', '商品名称', { width: 200 }),
  column.number('price', '单价', { precision: 2, prefix: '¥' }),
  column.number('quantity', '数量'),
  column.number('amount', '金额', { precision: 2, prefix: '¥' }),
  column.status('status', '状态', statusMap),
  column.action('操作', { actions: actionList })
];

const summaryConfig = {
  enabled: true,
  position: 'bottom',
  sticky: true,
  summaries: {
    'product_name': {
      label: '合计',
      style: { fontWeight: 'bold' }
    },
    'quantity': {
      calculator: 'sum',
      label: '总数量',
      suffix: ' 件'
    },
    'amount': {
      calculator: 'sum',
      label: '总金额',
      formatter: (value) => `¥${Number(value).toLocaleString()}`,
      style: { fontWeight: 'bold', color: '#52c41a' }
    }
  }
};
</script>
```

## 🎨 样式自定义

### CSS 类名

```css
/* 合计行容器 */
.summary-column {
  background-color: #fafafa;
  border-top: 1px solid #e8e8e8;
  font-weight: 500;
}

/* 强调样式 */
.summary-column.emphasis {
  background-color: #f0f9f0;
  color: #52c41a;
  font-weight: bold;
}

/* 警告样式 */
.summary-column.warning {
  background-color: #fff7e6;
  color: #fa8c16;
}

/* 粘性定位 */
.summary-column.sticky {
  position: sticky;
  bottom: 0;
  z-index: 10;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}
```

### 内联样式配置

```typescript
const summaryConfig = {
  enabled: true,
  style: {
    backgroundColor: '#f0f9f0',
    borderTop: '2px solid #52c41a',
    fontSize: '14px'
  },
  summaries: {
    'amount': {
      calculator: 'sum',
      style: {
        fontWeight: 'bold',
        color: '#52c41a',
        fontSize: '16px'
      }
    }
  }
};
```

## ⚡ 性能优化

### 计算缓存

```typescript
const summaryConfig = {
  enabled: true,
  summaries: {
    'complex_calculation': {
      calculator: (values, data) => {
        // 复杂计算逻辑
        console.log('执行复杂计算');
        return data.reduce((sum, row) => sum + heavyCalculation(row), 0);
      },
      cache: true  // 启用缓存
    }
  }
};
```

### 条件优化

```typescript
// 优化前：每次都计算所有行
const inefficient = {
  calculator: (values, data) => {
    return data.filter(row => expensiveCondition(row)).length;
  }
};

// 优化后：使用条件过滤
const efficient = {
  calculator: 'count',
  condition: (value, row) => expensiveCondition(row)
};
```

## 🔍 调试和监控

```typescript
const summaryConfig = {
  enabled: true,
  summaries: {
    'debug_field': {
      calculator: (values, data) => {
        console.log('计算合计值:', { values, dataLength: data.length });
        const result = values.reduce((sum, val) => sum + (Number(val) || 0), 0);
        console.log('计算结果:', result);
        return result;
      },
      formatter: (value, config) => {
        console.log('格式化值:', { value, config });
        return `总计: ${value}`;
      }
    }
  }
};
```

## 📝 最佳实践

1. **性能考虑** - 对于大数据量表格，使用内置计算函数而非自定义函数
2. **数据类型** - 确保数值字段的数据类型正确
3. **条件筛选** - 使用条件属性而非在计算函数中过滤
4. **样式一致性** - 保持合计行样式与表格整体风格一致
5. **用户体验** - 在合计值旁显示清晰的标签
6. **响应式设计** - 考虑移动端的显示效果

## 🔗 相关文档

- [插件系统介绍](../README.md)
- [表格组件文档](../../README.md)
- [数字列插件](../numberColumn/README.md)
- [状态列插件](../statusColumn/README.md) 