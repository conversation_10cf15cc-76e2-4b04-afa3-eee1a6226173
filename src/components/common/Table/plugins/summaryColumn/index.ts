import type { BaseColumn } from '../../src/types';
import type { TablePlugin } from '../../core/pluginApi';

// 计算函数类型
export type SummaryCalculator = (values: any[], data: any[]) => any;

// 内置计算类型
export type BuiltinCalculatorType =
    | 'sum' // 求和
    | 'avg' // 平均值
    | 'max' // 最大值
    | 'min' // 最小值
    | 'count' // 计数
    | 'countNonNull' // 非空计数
    | 'median' // 中位数
    | 'variance' // 方差
    | 'stddev'; // 标准差

// 合计配置选项
export interface SummaryConfig {
    field?: string; // 要计算的字段
    calculator?: BuiltinCalculatorType | SummaryCalculator; // 计算函数
    label?: string; // 显示标签
    formatter?: (value: any, config: SummaryConfig) => string; // 格式化函数
    condition?: (value: any, row: any) => boolean; // 条件过滤
    precision?: number; // 数字精度
    prefix?: string; // 前缀
    suffix?: string; // 后缀
    style?: any; // 样式
    showEmpty?: boolean; // 是否显示空值
    emptyText?: string; // 空值显示文本
}

// 合计列选项
export interface SummaryColumnOptions extends BaseColumn {
    enabled?: boolean; // 是否启用合计
    position?: 'bottom' | 'top'; // 位置
    summaries?: Record<string, SummaryConfig>; // 各列的合计配置
    style?: any; // 合计行样式
    className?: string; // 合计行CSS类
    height?: number; // 合计行高度
    sticky?: boolean; // 是否固定
    showBorder?: boolean; // 是否显示边框
    background?: string; // 背景色
}

// 内置计算函数
export const BuiltinCalculators: Record<
    BuiltinCalculatorType,
    SummaryCalculator
> = {
    sum: (values: any[], data: any[]) => {
        return values.reduce((sum, val) => {
            const num = extractNumber(val);
            return num !== null ? sum + num : sum;
        }, 0);
    },

    avg: (values: any[], data: any[]) => {
        const nums = values
            .map((val) => extractNumber(val))
            .filter((num) => num !== null) as number[];
        return nums.length > 0
            ? nums.reduce((sum, val) => sum + val, 0) / nums.length
            : 0;
    },

    max: (values: any[], data: any[]) => {
        const nums = values
            .map((val) => extractNumber(val))
            .filter((num) => num !== null) as number[];
        return nums.length > 0 ? Math.max(...nums) : null;
    },

    min: (values: any[], data: any[]) => {
        const nums = values
            .map((val) => extractNumber(val))
            .filter((num) => num !== null) as number[];
        return nums.length > 0 ? Math.min(...nums) : null;
    },

    count: (values: any[], data: any[]) => {
        return values.length;
    },

    countNonNull: (values: any[], data: any[]) => {
        return values.filter((val) => val != null && val !== '').length;
    },

    median: (values: any[], data: any[]) => {
        const nums = values
            .map((val) => extractNumber(val))
            .filter((num) => num !== null) as number[];
        nums.sort((a, b) => a - b);
        if (nums.length === 0) return null;
        const mid = Math.floor(nums.length / 2);
        return nums.length % 2 === 0
            ? (nums[mid - 1] + nums[mid]) / 2
            : nums[mid];
    },

    variance: (values: any[], data: any[]) => {
        const nums = values
            .map((val) => extractNumber(val))
            .filter((num) => num !== null) as number[];
        if (nums.length === 0) return null;
        const mean = nums.reduce((sum, val) => sum + val, 0) / nums.length;
        const variance =
            nums.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) /
            nums.length;
        return variance;
    },

    stddev: (values: any[], data: any[]) => {
        const variance = BuiltinCalculators.variance(values, data);
        return variance !== null ? Math.sqrt(variance) : null;
    },
};

// 计算合计值
export function calculateSummary(
    data: any[],
    field: string,
    config: SummaryConfig
): any {
    // 提取字段值
    let values = data.map((row) => getFieldValue(row, field));

    // 应用条件过滤
    if (config.condition) {
        values = data
            .filter((row) => config.condition!(getFieldValue(row, field), row))
            .map((row) => getFieldValue(row, field));
    }

    // 执行计算
    let result: any;
    if (typeof config.calculator === 'string') {
        const calculator = BuiltinCalculators[config.calculator];
        if (!calculator) {
            throw new Error(`Unknown calculator: ${config.calculator}`);
        }
        result = calculator(values, data);
    } else if (typeof config.calculator === 'function') {
        result = config.calculator(values, data);
    } else {
        // 默认求和
        result = BuiltinCalculators.sum(values, data);
    }

    return result;
}

// 格式化合计值
export function formatSummaryValue(value: any, config: SummaryConfig): string {
    if (value == null || value === '') {
        return config.showEmpty ? config.emptyText || '-' : '';
    }

    // 自定义格式化器
    if (config.formatter) {
        return config.formatter(value, config);
    }

    // 默认格式化
    let formatted = String(value);

    // 数字精度
    if (typeof value === 'number' && config.precision !== undefined) {
        formatted = value.toFixed(config.precision);
    }

    // 添加前缀和后缀
    if (config.prefix) {
        formatted = config.prefix + formatted;
    }
    if (config.suffix) {
        formatted = formatted + config.suffix;
    }

    return formatted;
}

// 提取数值（过滤掉非数字字符）
function extractNumber(value: any): number | null {
    if (value == null || value === '') return null;

    // 如果已经是数字，直接返回
    if (typeof value === 'number' && !isNaN(value)) {
        return value;
    }

    // 转换为字符串并提取数字
    const str = String(value);

    // 匹配数字模式，包括负号、小数点
    const match = str.match(/-?\d+(?:\.\d+)?/);

    if (match) {
        const num = Number(match[0]);
        return isNaN(num) ? null : num;
    }

    return null;
}

// 获取字段值
function getFieldValue(obj: any, path: string): any {
    if (!path) return obj;

    const keys = path.split('.');
    let result = obj;

    for (const key of keys) {
        if (result == null) return null;
        result = result[key];
    }

    return result;
}

// 合计列插件
const SummaryColumnPlugin: TablePlugin = {
    name: 'SummaryColumn',
    version: '1.0.0',

    install(core, options = {}) {
        // 注册渲染器
        core.registerRenderer(
            'SummaryRenderer',
            () => import('./SummaryRenderer.vue')
        );

        // 扩展表格API
        core.extendTableApi(
            'setSummaryConfig',
            function (config: SummaryColumnOptions) {
                // 设置合计配置的逻辑
            }
        );

        core.extendTableApi(
            'getSummaryData',
            function (data: any[], columns: any[]) {
                const summaryConfig = this.getSummaryConfig?.();
                if (!summaryConfig?.enabled || !summaryConfig.summaries) {
                    return null;
                }

                const summaryData: Record<string, any> = {};

                for (const [field, config] of Object.entries(
                    summaryConfig.summaries
                )) {
                    try {
                        const value = calculateSummary(
                            data,
                            field,
                            config as SummaryConfig
                        );
                        summaryData[field] = formatSummaryValue(
                            value,
                            config as SummaryConfig
                        );
                    } catch (error) {
                        console.error(
                            `Failed to calculate summary for field ${field}:`,
                            error
                        );
                        const summaryConfig = config as SummaryConfig;
                        summaryData[field] = summaryConfig.showEmpty
                            ? summaryConfig.emptyText || '-'
                            : '';
                    }
                }

                return summaryData;
            }
        );

        // 扩展列助手
        core.extendColumnHelper(
            'summary',
            (options: SummaryColumnOptions = {}): BaseColumn => ({
                ...options,
                cellRender: { name: 'SummaryRenderer', props: options },
            })
        );

        // console.log('SummaryColumn plugin installed');
    },

    uninstall(core) {
        // console.log('SummaryColumn plugin uninstalled');
    },
};

export default SummaryColumnPlugin;
