<template>
  <div class="summary-column" :class="className" :style="containerStyle">
    <!-- 合计行显示区域 -->
    <div
      v-if="summaryData && summaryData[field]"
      class="summary-cell"
      :style="cellStyle"
    >
      {{ summaryData[field] }}
    </div>
    <div
      v-else-if="summaryConfig?.label"
      class="summary-label"
      :style="labelStyle"
    >
      {{ summaryConfig.label }}
    </div>
    <div v-else class="summary-empty">-</div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject } from 'vue';
import type { SummaryConfig, SummaryColumnOptions } from './index';

// 组件属性
interface Props {
  cellValue?: any;
  row?: any;
  column?: any;
  options?: SummaryColumnOptions;
}

const props = withDefaults(defineProps<Props>(), {
  options: () => ({})
});

// 从表格上下文获取数据
const tableData = inject<any[]>('tableData', []);
const summaryData = inject<Record<string, any>>('summaryData', {});

// 计算属性
const field = computed(() => props.column?.field || props.column?.property);

const summaryConfig = computed((): SummaryConfig | undefined => {
  return props.options?.summaries?.[field.value];
});

const className = computed(() => {
  const classes = ['summary-column'];
  if (props.options?.className) {
    classes.push(props.options.className);
  }
  if (summaryConfig.value?.style?.className) {
    classes.push(summaryConfig.value.style.className);
  }
  return classes.join(' ');
});

const containerStyle = computed(() => {
  const style: any = {};
  
  // 合并样式
  if (props.options?.style) {
    Object.assign(style, props.options.style);
  }
  
  if (props.options?.background) {
    style.backgroundColor = props.options.background;
  }
  
  if (props.options?.height) {
    style.height = typeof props.options.height === 'number' 
      ? `${props.options.height}px` 
      : props.options.height;
  }
  
  return style;
});

const cellStyle = computed(() => {
  const style: any = {};
  
  if (summaryConfig.value?.style) {
    Object.assign(style, summaryConfig.value.style);
  }
  
  return style;
});

const labelStyle = computed(() => {
  const style: any = {
    fontWeight: 'bold',
    color: '#666'
  };
  
  if (summaryConfig.value?.style) {
    Object.assign(style, summaryConfig.value.style);
  }
  
  return style;
});
</script>

<style scoped>
.summary-column {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 8px;
  min-height: 32px;
  background-color: #fafafa;
  border-top: 1px solid #e8e8e8;
  font-weight: 500;
}

.summary-cell {
  color: #333;
}

.summary-label {
  color: #666;
  font-weight: bold;
}

.summary-empty {
  color: #ccc;
}

/* 数字对齐 */
.summary-column[data-type="number"] {
  justify-content: flex-end;
  text-align: right;
}

/* 文本对齐 */
.summary-column[data-type="text"] {
  justify-content: flex-start;
  text-align: left;
}

/* 居中对齐 */
.summary-column[data-type="center"] {
  justify-content: center;
  text-align: center;
}

/* 紧凑模式 */
.summary-column.compact {
  padding: 4px 8px;
  min-height: 24px;
  font-size: 12px;
}

/* 强调样式 */
.summary-column.emphasis {
  background-color: #f0f9f0;
  color: #52c41a;
  font-weight: bold;
}

/* 警告样式 */
.summary-column.warning {
  background-color: #fff7e6;
  color: #fa8c16;
}

/* 错误样式 */
.summary-column.error {
  background-color: #fff1f0;
  color: #f5222d;
}

/* 粘性定位 */
.summary-column.sticky {
  position: sticky;
  bottom: 0;
  z-index: 10;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

/* 边框样式 */
.summary-column.bordered {
  border: 1px solid #e8e8e8;
}

.summary-column.no-border {
  border: none;
}
</style> 