import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import GroupRenderer from './GroupRenderer.vue';

export interface GroupRendererOptions extends BaseColumn {
    onClick?: (row: any) => void; // 点击事件
}

/**
 * 头像插件
 */
const groupColumnPlugin: TablePlugin = {
    name: 'GroupColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 GroupRenderer 渲染器
        core.registerRenderer('GroupRenderer', GroupRenderer);

        // 2. 注册 avatar 列类型
        core.registerColumnType('group', {
            cellRender: { name: 'GroupRenderer' },
            align: 'center',
            sortable: false,
            width: 80,
        });

        // 3. 扩展 column 辅助对象，添加 avatar 方法
        core.extendColumnHelper(
            'group',
            (
                field: string,
                title: string,
                options: Omit<GroupRendererOptions, 'field' | 'title'> = {}
            ): GroupRendererOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'GroupRenderer',
                        props: options,
                    },
                    align: options.align || 'center',
                    sortable: false,
                    width: options.width || 120,
                    ...options,
                } as GroupRendererOptions;
            }
        );

        // console.log('GroupColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('GroupColumn plugin uninstalled');
    },
};

export default groupColumnPlugin;
