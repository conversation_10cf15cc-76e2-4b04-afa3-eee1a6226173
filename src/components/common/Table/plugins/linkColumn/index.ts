import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import LinkRenderer from './LinkRenderer.vue';

export interface LinkColumnOptions extends BaseColumn {
    href?: string | ((row: any) => string); // 链接地址
    target?: '_blank' | '_self' | '_parent' | '_top'; // 打开方式
    textField?: string; // 显示文本字段
    iconField?: string; // 图标字段
    icon?: string; // 固定图标
    iconPosition?: 'left' | 'right'; // 图标位置
    underline?: boolean; // 是否显示下划线
    color?: string; // 链接颜色
    hoverColor?: string; // 悬停颜色
    disabled?: boolean | ((row: any) => boolean); // 是否禁用
    onClick?: (row: any, event: Event) => void; // 点击事件
    onHover?: (row: any, event: Event) => void; // 悬停事件
    maxLength?: number; // 最大显示长度
    ellipsis?: boolean; // 超长省略
    tooltip?: boolean; // 是否显示提示
}

/**
 * 链接插件
 */
const linkColumnPlugin: TablePlugin = {
    name: 'LinkColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 LinkRenderer 渲染器
        core.registerRenderer('LinkRenderer', LinkRenderer);

        // 2. 注册 link 列类型
        core.registerColumnType('link', {
            cellRender: { name: 'LinkRenderer' },
            sortable: true,
        });

        // 3. 扩展 column 辅助对象，添加 link 方法
        core.extendColumnHelper(
            'link',
            (
                field: string,
                title: string,
                options: Omit<LinkColumnOptions, 'field' | 'title'> = {}
            ): LinkColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'LinkRenderer',
                        props: options,
                    },
                    href: options.href,
                    target: options.target || '_blank',
                    underline: options.underline !== false,
                    color: options.color || '#3b82f6',
                    ellipsis: options.ellipsis !== false,
                    tooltip: options.tooltip !== false,
                    ...options,
                } as LinkColumnOptions;
            }
        );

        // 4. 扩展邮箱链接
        core.extendColumnHelper(
            'emailLink',
            (
                field: string,
                title: string = '邮箱',
                options: Omit<
                    LinkColumnOptions,
                    'field' | 'title' | 'href'
                > = {}
            ): LinkColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'LinkRenderer',
                        props: {
                            href: (row: any) => `mailto:${row[field]}`,
                            icon: 'mdi:email',
                            ...options,
                        },
                    },
                    href: (row: any) => `mailto:${row[field]}`,
                    target: '_self',
                    icon: 'mdi:email',
                    underline: false,
                    ...options,
                } as LinkColumnOptions;
            }
        );

        // 5. 扩展电话链接
        core.extendColumnHelper(
            'phoneLink',
            (
                field: string,
                title: string = '电话',
                options: Omit<
                    LinkColumnOptions,
                    'field' | 'title' | 'href'
                > = {}
            ): LinkColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'LinkRenderer',
                        props: {
                            href: (row: any) => `tel:${row[field]}`,
                            icon: 'mdi:phone',
                            ...options,
                        },
                    },
                    href: (row: any) => `tel:${row[field]}`,
                    target: '_self',
                    icon: 'mdi:phone',
                    underline: false,
                    ...options,
                } as LinkColumnOptions;
            }
        );

        // 6. 扩展网址链接
        core.extendColumnHelper(
            'urlLink',
            (
                field: string,
                title: string = '网址',
                options: Omit<
                    LinkColumnOptions,
                    'field' | 'title' | 'href'
                > = {}
            ): LinkColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'LinkRenderer',
                        props: {
                            href: (row: any) => {
                                const url = row[field];
                                if (!url) return '';
                                return url.startsWith('http')
                                    ? url
                                    : `https://${url}`;
                            },
                            icon: 'mdi:link',
                            maxLength: 30,
                            ...options,
                        },
                    },
                    href: (row: any) => {
                        const url = row[field];
                        if (!url) return '';
                        return url.startsWith('http') ? url : `https://${url}`;
                    },
                    target: '_blank',
                    icon: 'mdi:link',
                    maxLength: 30,
                    ...options,
                } as LinkColumnOptions;
            }
        );

        // console.log('LinkColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('LinkColumn plugin uninstalled');
    },
};

export default linkColumnPlugin;
