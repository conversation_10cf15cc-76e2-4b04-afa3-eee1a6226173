import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import DictRenderer from './DictRenderer.vue';

export interface DictRendererOptions extends BaseColumn {
    onClick?: (row: any) => void; // 点击事件
}

/**
 * 头像插件
 */
const dictColumnPlugin: TablePlugin = {
    name: 'DictColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 DictRenderer 渲染器
        core.registerRenderer('DictRenderer', DictRenderer);

        // 2. 注册 avatar 列类型
        core.registerColumnType('dict', {
            cellRender: { name: 'DictRenderer' },
            align: 'center',
            sortable: false,
            width: 80,
        });

        // 3. 扩展 column 辅助对象，添加 avatar 方法
        core.extendColumnHelper(
            'dict',
            (
                field: string,
                title: string,
                options: Omit<DictRendererOptions, 'field' | 'title'> = {}
            ): DictRendererOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'Dict<PERSON><PERSON><PERSON>',
                        props: options,
                    },
                    align: options.align || 'center',
                    sortable: false,
                    width: options.width || 120,
                    ...options,
                } as DictRendererOptions;
            }
        );

        // console.log('DictColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('DictColumn plugin uninstalled');
    },
};

export default dictColumnPlugin;
