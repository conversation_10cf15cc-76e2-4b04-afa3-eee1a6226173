import type { TablePlugin, TableCore } from '../../core/pluginApi';
import CompositeRenderer from './CompositeRenderer.vue';
import { compositeHelpers } from './compositeHelpers';
import type {
    CompositeSubConfig,
    CompositeMainConfig,
    CompositeIconConfig,
    CompositeColumnOptions,
} from './types';

/**
 * 复合列插件
 */
const compositeColumnPlugin: TablePlugin = {
    name: 'CompositeColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 CompositeRenderer 渲染器
        core.registerRenderer('CompositeRenderer', CompositeRenderer);

        // 2. 注册 composite 列类型
        core.registerColumnType('composite', {
            cellRender: { name: 'CompositeRenderer' },
        });

        // 3. 扩展 column 辅助对象，添加 composite 方法
        core.extendColumnHelper(
            'composite',
            (
                field: string,
                title: string,
                options: Omit<CompositeColumnOptions, 'field' | 'title'> = {}
            ): CompositeColumnOptions => {
                // 使用与直接 compositeColumn 相同的机制
                const {
                    main,
                    subs,
                    icon,
                    layout,
                    separator,
                    actions,
                    moreActions,
                    interactive,
                    ...rest
                } = options;

                return {
                    field,
                    title,
                    width: 200,
                    align: 'left',
                    // 使用现有的compositeConfig机制，与直接的compositeColumn保持一致
                    compositeConfig: {
                        main: {
                            field: main?.field || field,
                            formatter: main?.formatter,
                            style: main?.style,
                            tooltip: main?.tooltip,
                            props: main?.props,
                        },
                        subs: subs || [],
                        icon,
                        layout: layout || 'horizontal',
                        separator: separator || {
                            main: ' · ',
                            subs: ' | ',
                            vertical: '\n',
                        },
                        interactive,
                    },
                    // 如果有actions或moreActions，设置cellActionConfig
                    cellActionConfig:
                        actions || moreActions
                            ? {
                                  actions: actions,
                                  moreConfig: moreActions,
                              }
                            : undefined,
                    ...rest,
                } as CompositeColumnOptions;
            }
        );

        // 4. 注册 compositeHelpers 到全局配置
        core.setGlobalConfig('compositeHelpers', compositeHelpers);

        // console.log('CompositeColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // 插件卸载时的清理工作
        // console.log('CompositeColumn plugin uninstalled');
    },
};

export default compositeColumnPlugin;

// 导出相关工具和类型
export { compositeHelpers };
export type {
    CompositeSubConfig,
    CompositeMainConfig,
    CompositeIconConfig,
    CompositeColumnOptions,
};
