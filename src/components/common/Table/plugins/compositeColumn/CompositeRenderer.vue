<template>
    <div
        class="composite-cell"
        :class="{
            'composite-horizontal': isHorizontal,
            'composite-vertical': isVertical,
        }"
    >
        <!-- 图标部分 -->
        <div v-if="hasIcon" class="composite-icon">
            <Icon
                v-if="config.icon?.type === 'icon'"
                :icon="config.icon.iconName"
                class="composite-icon-item"
            />
        </div>

        <!-- 文本内容部分 -->
        <div class="composite-content">
            <!-- 主数据 -->
            <div class="composite-main" v-bind="itemBind(config?.main?.props)">
                {{ getMainText() }}
            </div>

            <!-- 辅助数据 -->
            <div v-if="shouldShowSubs" class="composite-subs">
                <span
                    v-for="(sub, index) in getDisplayedSubs()"
                    :key="index"
                    class="composite-sub-item"
                    :style="sub.style"
                    v-bind="itemBind(sub?.props)"
                >
                    {{ getSubText(sub, index) }}
                </span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Icon } from '@iconify/vue';
import { compositeHelpers } from './compositeHelpers';
import { isFunction } from 'lodash-es';

// Props 定义
const props = defineProps({
    params: {
        type: Object,
        required: true,
    },
});

// 从 params 中获取数据
const { row, column } = props.params;
const config = column.params || {};

// 计算属性
const isHorizontal = computed(() => config.layout !== 'vertical');
const isVertical = computed(() => config.layout === 'vertical');
const hasIcon = computed(() => !!config.icon);
const shouldShowSubs = computed(() => config.subs && config.subs.length > 0);
const itemBind = computed(() => {
    return (item) => {
        if (!item) return {};

        return Object.entries(item).reduce((acc, [key, value]) => {
            if (key.startsWith('on') && isFunction(value)) {
                // 包装事件处理函数，传入 props.params
                acc[key] = () => value(props.params);
            } else {
                // 普通属性直接赋值
                acc[key] = value;
            }
            return acc;
        }, {});
    };
});

// 获取主文本
function getMainText(): string {
    if (!config.main?.field) return '';

    const value = compositeHelpers.getFieldValue(row, config.main.field);

    if (config.main.formatter) {
        return config.main.formatter(value, row);
    }

    return compositeHelpers.formatFieldValue(value);
}

// 获取显示的辅助数据
function getDisplayedSubs() {
    if (!config.subs) return [];

    return config.subs.filter((sub) => {
        if (sub.condition) {
            return sub.condition(row);
        }
        return true;
    });
}

// 获取辅助文本
function getSubText(sub: any, index: number): string {
    // 计算字段
    if (sub.computed) {
        const values = sub.computed.fields.map((field) =>
            compositeHelpers.getFieldValue(row, field)
        );
        const result = sub.computed.formula(values, row);

        if (sub.template) {
            return sub.template.replace('${value}', result);
        }
        return result;
    }

    // 普通字段
    if (sub.field) {
        const value = compositeHelpers.getFieldValue(row, sub.field);

        if (sub.formatter) {
            return sub.formatter(value, row);
        }

        if (sub.template) {
            return sub.template.replace('${value}', value);
        }

        // 智能解析
        if (sub.smartParse?.metadata) {
            return compositeHelpers.smartFieldParser(
                row,
                sub.field,
                sub.smartParse.metadata,
                sub.smartParse.options || {}
            );
        }

        // 关系字段
        if (sub.relation) {
            return compositeHelpers.relationDisplay(
                row,
                sub.field,
                sub.relation
            );
        }

        // 枚举字段
        if (sub.enum?.enumMap) {
            return compositeHelpers.enumDisplay(value, sub.enum.enumMap, {
                showOriginal: sub.enum.showOriginal,
                template: sub.enum.template,
            });
        }

        return compositeHelpers.formatFieldValue(value);
    }

    // 模板字段
    if (sub.template) {
        return sub.template;
    }

    return '';
}
</script>

<style scoped>
.composite-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

.composite-vertical {
    flex-direction: column;
    align-items: flex-start;
}

.composite-icon {
    flex-shrink: 0;
}

.composite-icon-item {
    width: 16px;
    height: 16px;
}

.composite-content {
    flex: 1;
    min-width: 0;
}

.composite-main {
    font-weight: 500;
    color: var(--primary-text-color, #333);
}

.composite-subs {
    display: flex;
    gap: 8px;
    margin-top: 2px;
    flex-wrap: wrap;
}

.composite-vertical .composite-subs {
    flex-direction: column;
    gap: 2px;
}

.composite-sub-item {
    font-size: 12px;
    color: var(--secondary-text-color, #666);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
