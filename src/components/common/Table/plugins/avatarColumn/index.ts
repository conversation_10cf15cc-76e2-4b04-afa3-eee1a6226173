import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import AvatarRenderer from './AvatarRenderer.vue';

export interface AvatarColumnOptions extends BaseColumn {
    nameField?: string; // 姓名字段，用于生成初始字母
    emailField?: string; // 邮箱字段，用于生成 Gravatar
    sizeField?: string; // 尺寸字段
    size?: number | 'small' | 'medium' | 'large'; // 尺寸
    shape?: 'circle' | 'square' | 'rounded'; // 形状
    defaultSrc?: string; // 默认头像
    fallbackType?: 'letter' | 'icon' | 'image'; // 回退类型
    fallbackIcon?: string; // 回退图标
    fallbackBg?: string; // 回退背景色
    showName?: boolean; // 是否显示姓名
    namePosition?: 'right' | 'bottom'; // 姓名位置
    borderColor?: string; // 边框颜色
    borderWidth?: number; // 边框宽度
    clickable?: boolean; // 是否可点击
    onClick?: (row: any) => void; // 点击事件
}

/**
 * 头像插件
 */
const avatarColumnPlugin: TablePlugin = {
    name: 'AvatarColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 AvatarRenderer 渲染器
        core.registerRenderer('AvatarRenderer', AvatarRenderer);

        // 2. 注册 avatar 列类型
        core.registerColumnType('avatar', {
            cellRender: { name: 'AvatarRenderer' },
            align: 'center',
            sortable: false,
            width: 80,
        });

        // 3. 扩展 column 辅助对象，添加 avatar 方法
        core.extendColumnHelper(
            'avatar',
            (
                field: string,
                title: string = '头像',
                options: Omit<AvatarColumnOptions, 'field' | 'title'> = {}
            ): AvatarColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'AvatarRenderer',
                        props: options,
                    },
                    align: options.align || 'center',
                    sortable: false,
                    width: options.width || (options.showName ? 120 : 80),
                    size: options.size || 'medium',
                    shape: options.shape || 'circle',
                    fallbackType: options.fallbackType || 'letter',
                    showName: options.showName || false,
                    namePosition: options.namePosition || 'right',
                    ...options,
                } as AvatarColumnOptions;
            }
        );

        // 4. 扩展用户头像列
        core.extendColumnHelper(
            'userAvatar',
            (
                avatarField: string = 'avatar',
                nameField: string = 'name',
                title: string = '用户',
                options: Omit<
                    AvatarColumnOptions,
                    'field' | 'title' | 'nameField'
                > = {}
            ): AvatarColumnOptions => {
                return {
                    field: avatarField,
                    title,
                    cellRender: {
                        name: 'AvatarRenderer',
                        props: { nameField, showName: true, ...options },
                    },
                    align: 'left',
                    sortable: false,
                    width: 120,
                    nameField,
                    showName: true,
                    namePosition: 'right',
                    size: 'medium',
                    shape: 'circle',
                    fallbackType: 'letter',
                    ...options,
                } as AvatarColumnOptions;
            }
        );

        // 5. 扩展简单头像列
        core.extendColumnHelper(
            'simpleAvatar',
            (
                field: string,
                title: string = '头像',
                options: Omit<AvatarColumnOptions, 'field' | 'title'> = {}
            ): AvatarColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'AvatarRenderer',
                        props: { size: 'small', showName: false, ...options },
                    },
                    align: 'center',
                    sortable: false,
                    width: 60,
                    size: 'small',
                    shape: 'circle',
                    fallbackType: 'icon',
                    showName: false,
                    ...options,
                } as AvatarColumnOptions;
            }
        );

        // console.log('AvatarColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('AvatarColumn plugin uninstalled');
    },
};

export default avatarColumnPlugin;
