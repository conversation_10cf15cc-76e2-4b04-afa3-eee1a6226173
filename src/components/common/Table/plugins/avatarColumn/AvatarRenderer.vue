<template>
  <div class="avatar-renderer" :class="avatarClass" @click="handleClick">
    <!-- 头像 -->
    <div class="avatar-container" :style="avatarStyle">
      <!-- 图片头像 -->
      <img 
        v-if="avatarSrc" 
        :src="avatarSrc" 
        :alt="displayName"
        class="avatar-image"
        @error="handleImageError"
      />
      
      <!-- 回退显示 -->
      <div v-else class="avatar-fallback" :style="fallbackStyle">
        <!-- 字母回退 -->
        <span v-if="fallbackType === 'letter'" class="avatar-letter">
          {{ avatarLetter }}
        </span>
        
        <!-- 图标回退 -->
        <i v-else-if="fallbackType === 'icon'" :class="fallbackIcon" class="avatar-icon"></i>
        
        <!-- 默认图片回退 -->
        <img 
          v-else-if="fallbackType === 'image' && defaultSrc" 
          :src="defaultSrc" 
          :alt="displayName"
          class="avatar-image"
        />
      </div>
    </div>
    
    <!-- 姓名显示 -->
    <div v-if="showName && displayName" class="avatar-name" :class="nameClass">
      {{ displayName }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps<{
  params: {
    row: any;
    column: any;
    rowIndex: number;
  };
}>();

const { row, column } = props.params;
const options = column.options || {};

const imageError = ref(false);

// 头像源
const avatarSrc = computed(() => {
  if (imageError.value) return null;
  
  const src = row[column.field];
  if (src && typeof src === 'string') {
    return src;
  }
  
  return null;
});

// 显示名称
const displayName = computed(() => {
  if (options.nameField) {
    return row[options.nameField] || '';
  }
  return '';
});

// 头像字母
const avatarLetter = computed(() => {
  const name = displayName.value;
  if (!name) return '?';
  
  // 获取第一个字符（支持中文）
  const firstChar = name.charAt(0).toUpperCase();
  return firstChar;
});

// 回退类型
const fallbackType = computed(() => {
  return options.fallbackType || 'letter';
});

// 回退图标
const fallbackIcon = computed(() => {
  return options.fallbackIcon || 'user-icon';
});

// 默认头像
const defaultSrc = computed(() => {
  return options.defaultSrc || '';
});

// 是否显示名称
const showName = computed(() => {
  return options.showName || false;
});

// 尺寸
const avatarSize = computed(() => {
  const size = options.size || 'medium';
  
  if (typeof size === 'number') {
    return size;
  }
  
  const sizeMap = {
    small: 32,
    medium: 40,
    large: 48
  };
  
  return sizeMap[size] || 40;
});

// 样式类
const avatarClass = computed(() => {
  const shape = options.shape || 'circle';
  const namePosition = options.namePosition || 'right';
  const clickable = options.clickable || false;
  
  return {
    [`avatar-shape-${shape}`]: true,
    [`name-position-${namePosition}`]: showName.value,
    'avatar-clickable': clickable,
    'has-name': showName.value
  };
});

const nameClass = computed(() => {
  const namePosition = options.namePosition || 'right';
  return {
    [`name-${namePosition}`]: true
  };
});

// 头像样式
const avatarStyle = computed(() => {
  const size = avatarSize.value;
  const style: any = {
    width: `${size}px`,
    height: `${size}px`
  };
  
  if (options.borderColor) {
    style.borderColor = options.borderColor;
    style.borderWidth = `${options.borderWidth || 2}px`;
    style.borderStyle = 'solid';
  }
  
  return style;
});

// 回退样式
const fallbackStyle = computed(() => {
  const style: any = {};
  
  if (options.fallbackBg) {
    style.backgroundColor = options.fallbackBg;
  } else {
    // 根据名称生成颜色
    const colors = [
      '#f56565', '#ed8936', '#ecc94b', '#48bb78', '#38b2ac',
      '#4299e1', '#667eea', '#9f7aea', '#ed64a6', '#a0aec0'
    ];
    const index = displayName.value.charCodeAt(0) % colors.length;
    style.backgroundColor = colors[index];
  }
  
  return style;
});

// 方法
function handleImageError() {
  imageError.value = true;
}

function handleClick() {
  if (options.clickable && options.onClick) {
    options.onClick(row);
  }
}
</script>

<style scoped>
.avatar-renderer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.avatar-renderer.name-position-bottom {
  flex-direction: column;
  gap: 4px;
}

.avatar-renderer.avatar-clickable {
  cursor: pointer;
}

.avatar-renderer.avatar-clickable:hover .avatar-container {
  transform: scale(1.05);
}

.avatar-container {
  position: relative;
  overflow: hidden;
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.avatar-shape-circle .avatar-container {
  border-radius: 50%;
}

.avatar-shape-square .avatar-container {
  border-radius: 0;
}

.avatar-shape-rounded .avatar-container {
  border-radius: 8px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.avatar-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.avatar-letter {
  line-height: 1;
  font-size: 0.75em;
}

.avatar-icon {
  font-size: 0.6em;
}

.avatar-name {
  font-size: 13px;
  color: #374151;
  font-weight: 500;
  line-height: 1.2;
}

.avatar-name.name-right {
  text-align: left;
}

.avatar-name.name-bottom {
  text-align: center;
  font-size: 11px;
}

/* 小尺寸调整 */
.avatar-renderer:has(.avatar-container[style*="32px"]) .avatar-fallback {
  font-size: 12px;
}

.avatar-renderer:has(.avatar-container[style*="32px"]) .avatar-letter {
  font-size: 10px;
}

.avatar-renderer:has(.avatar-container[style*="32px"]) .avatar-icon {
  font-size: 14px;
}

/* 大尺寸调整 */
.avatar-renderer:has(.avatar-container[style*="48px"]) .avatar-fallback {
  font-size: 16px;
}

.avatar-renderer:has(.avatar-container[style*="48px"]) .avatar-letter {
  font-size: 18px;
}

.avatar-renderer:has(.avatar-container[style*="48px"]) .avatar-icon {
  font-size: 20px;
}
</style> 