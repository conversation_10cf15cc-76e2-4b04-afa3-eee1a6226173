<template>
  <div class="action-renderer" :class="alignClass">
    <!-- 主要操作按钮 -->
    <div class="action-buttons" :class="sizeClass">
      <button
        v-for="(action, index) in visibleActions"
        :key="index"
        :class="getButtonClass(action)"
        :disabled="isActionDisabled(action)"
        @click="handleActionClick(action)"
      >
        <i v-if="action.icon" :class="action.icon" class="action-icon"></i>
        <span>{{ action.text }}</span>
      </button>
    </div>

    <!-- 更多操作下拉菜单 -->
    <div v-if="hasMoreActions" class="more-actions">
      <button
        class="more-button"
        :class="sizeClass"
        @click="toggleMoreMenu"
        @blur="hideMoreMenu"
      >
        <i class="more-icon">⋯</i>
      </button>
      
      <div v-show="showMoreMenu" class="more-menu">
        <div
          v-for="(action, index) in moreActions"
          :key="index"
          :class="getMenuItemClass(action)"
          @click="handleActionClick(action)"
        >
          <i v-if="action.icon" :class="action.icon" class="menu-icon"></i>
          <span>{{ action.text }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

interface ActionConfig {
  text: string;
  icon?: string;
  color?: 'primary' | 'secondary' | 'danger' | 'warning' | 'success';
  disabled?: boolean | ((row: any) => boolean);
  visible?: boolean | ((row: any) => boolean);
  auth?: string | string[];
  onClick?: (row: any, action: ActionConfig) => void;
}

const props = defineProps<{
  params: {
    row: any;
    column: any;
  };
}>();

const { row, column } = props.params;
const options = column.options || {};

const showMoreMenu = ref(false);

// 计算属性
const allActions = computed(() => {
  const actions = options.actions || [];
  const moreActions = options.moreActions || [];
  return [...actions, ...moreActions].filter(action => isActionVisible(action));
});

const maxVisible = computed(() => options.maxVisible || 3);

const visibleActions = computed(() => {
  return allActions.value.slice(0, maxVisible.value);
});

const moreActions = computed(() => {
  return allActions.value.slice(maxVisible.value);
});

const hasMoreActions = computed(() => moreActions.value.length > 0);

const sizeClass = computed(() => {
  const size = options.size || 'medium';
  return {
    'size-small': size === 'small',
    'size-medium': size === 'medium',
    'size-large': size === 'large'
  };
});

const alignClass = computed(() => {
  const align = options.align || 'center';
  return {
    'align-left': align === 'left',
    'align-center': align === 'center',
    'align-right': align === 'right'
  };
});

// 方法
function isActionVisible(action: ActionConfig): boolean {
  if (typeof action.visible === 'function') {
    return action.visible(row);
  }
  return action.visible !== false;
}

function isActionDisabled(action: ActionConfig): boolean {
  if (typeof action.disabled === 'function') {
    return action.disabled(row);
  }
  return action.disabled === true;
}

function getButtonClass(action: ActionConfig) {
  const baseClass = 'action-button';
  const colorClass = `color-${action.color || 'primary'}`;
  const disabledClass = isActionDisabled(action) ? 'disabled' : '';
  
  return [baseClass, colorClass, disabledClass].filter(Boolean).join(' ');
}

function getMenuItemClass(action: ActionConfig) {
  const baseClass = 'menu-item';
  const colorClass = `color-${action.color || 'primary'}`;
  const disabledClass = isActionDisabled(action) ? 'disabled' : '';
  
  return [baseClass, colorClass, disabledClass].filter(Boolean).join(' ');
}

function handleActionClick(action: ActionConfig) {
  if (isActionDisabled(action)) return;
  
  if (action.onClick) {
    action.onClick(row, action);
  }
  
  hideMoreMenu();
}

function toggleMoreMenu() {
  showMoreMenu.value = !showMoreMenu.value;
}

function hideMoreMenu() {
  setTimeout(() => {
    showMoreMenu.value = false;
  }, 200);
}
</script>

<style scoped>
.action-renderer {
  display: flex;
  gap: 8px;
  position: relative;
}

.align-left {
  justify-content: flex-start;
}

.align-center {
  justify-content: center;
}

.align-right {
  justify-content: flex-end;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
}

.size-small .action-button {
  padding: 2px 6px;
  font-size: 11px;
}

.size-medium .action-button {
  padding: 4px 8px;
  font-size: 12px;
}

.size-large .action-button {
  padding: 6px 12px;
  font-size: 13px;
}

.action-button.color-primary {
  background-color: #3b82f6;
  color: white;
}

.action-button.color-primary:hover {
  background-color: #2563eb;
}

.action-button.color-secondary {
  background-color: #6b7280;
  color: white;
}

.action-button.color-danger {
  background-color: #ef4444;
  color: white;
}

.action-button.color-warning {
  background-color: #f59e0b;
  color: white;
}

.action-button.color-success {
  background-color: #10b981;
  color: white;
}

.action-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-icon {
  width: 14px;
  height: 14px;
}

.more-actions {
  position: relative;
}

.more-button {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
}

.more-button:hover {
  border-color: #9ca3af;
  background-color: #f9fafb;
}

.more-icon {
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
}

.more-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  min-width: 120px;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f3f4f6;
}

.menu-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.menu-icon {
  width: 14px;
  height: 14px;
}
</style> 