import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import ActionRenderer from './ActionRenderer.vue';

export interface ActionConfig {
    text: string;
    icon?: string;
    color?: 'primary' | 'secondary' | 'danger' | 'warning' | 'success';
    disabled?: boolean | ((row: any) => boolean);
    visible?: boolean | ((row: any) => boolean);
    auth?: string | string[];
    onClick?: (row: any, action: ActionConfig) => void;
}

export interface ActionColumnOptions extends BaseColumn {
    actions?: ActionConfig[];
    moreActions?: ActionConfig[];
    maxVisible?: number; // 最多显示几个按钮，其余放入更多菜单
    size?: 'small' | 'medium' | 'large';
    align?: 'left' | 'center' | 'right';
}

/**
 * 操作列插件
 */
const actionColumnPlugin: TablePlugin = {
    name: 'ActionColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        // 1. 注册 ActionRenderer 渲染器
        core.registerRenderer('ActionRenderer', ActionRenderer);

        // 2. 注册 action 列类型
        core.registerColumnType('action', {
            cellRender: { name: 'ActionRenderer' },
            width: 120,
            resizable: false,
            sortable: false,
        });

        // 3. 扩展 column 辅助对象，添加 action 方法
        core.extendColumnHelper(
            'action',
            (
                title: string = '操作',
                options: Omit<ActionColumnOptions, 'title'> = {}
            ): ActionColumnOptions => {
                return {
                    title,
                    cellRender: {
                        name: 'ActionRenderer',
                        props: options,
                    },
                    width: options.width || 120,
                    minWidth: options.minWidth || 80,
                    maxVisible: options.maxVisible || 3,
                    size: options.size || 'medium',
                    align: options.align || 'center',
                    fixed: options.fixed || 'right',
                    resizable: false,
                    sortable: false,
                    ...options,
                } as ActionColumnOptions;
            }
        );

        // console.log('ActionColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // console.log('ActionColumn plugin uninstalled');
    },
};

export default actionColumnPlugin;
