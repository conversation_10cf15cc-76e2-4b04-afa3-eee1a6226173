import type { TablePlugin, TableCore } from '../../core/pluginApi';
import type { BaseColumn } from '../../src/types';
import { getAllUsersName } from '@/api/sys/user';
import UserMappingRenderer from './UserMappingRenderer.vue';

export interface UserMappingColumnOptions extends BaseColumn {
    nullDisplay?: string; // 空值显示文本，默认'-'
    unknownDisplay?: string; // 未知用户显示文本，默认'未知用户'
    showId?: boolean; // 是否同时显示ID，默认false
    template?: string; // 显示模板，如 '${username} (${id})'
    clickable?: boolean; // 是否可点击
    onUserClick?: (userId: number, username: string, row: any) => void; // 点击用户回调
    // 样式配置
    userStyle?: any; // 用户名样式
    idStyle?: any; // ID样式
}

// 用户数据缓存管理器
class UserCacheManager {
    private static instance: UserCacheManager;
    private userMap: Map<number, string> = new Map();
    private isLoading = false;
    private hasLoaded = false;
    private loadPromise: Promise<void> | null = null;

    static getInstance(): UserCacheManager {
        if (!UserCacheManager.instance) {
            UserCacheManager.instance = new UserCacheManager();
        }
        return UserCacheManager.instance;
    }

    getUsername(userId: number): string | null {
        return this.userMap.get(userId) || null;
    }

    hasCache(): boolean {
        return this.userMap.size > 0;
    }

    getLoadingState(): boolean {
        return this.isLoading;
    }

    // 加载用户数据
    async loadUserData(force = false): Promise<void> {
        // 如果已经加载过且不强制重新加载，直接返回
        if (this.hasLoaded && !force) {
            return;
        }

        // 如果正在加载，返回现有的Promise
        if (this.isLoading && this.loadPromise) {
            return this.loadPromise;
        }

        this.isLoading = true;
        this.loadPromise = this._fetchUserData();

        try {
            await this.loadPromise;
            this.hasLoaded = true;
        } finally {
            this.isLoading = false;
            this.loadPromise = null;
        }
    }

    private async _fetchUserData(): Promise<void> {
        try {
            const response = await getAllUsersName();

            // 使用any类型来处理多种可能的响应格式
            const anyResponse = response as any;

            // 清空旧数据
            this.userMap.clear();

            let userData = null;

            // 尝试多种可能的数据格式
            if (anyResponse?.items && Array.isArray(anyResponse.items)) {
                // 格式1: ResponseListModel 直接包含items
                userData = anyResponse.items;
            } else if (
                anyResponse?.data?.items &&
                Array.isArray(anyResponse.data.items)
            ) {
                // 格式2: 嵌套在data中的items
                userData = anyResponse.data.items;
            } else if (anyResponse?.data && Array.isArray(anyResponse.data)) {
                // 格式3: data直接是数组
                userData = anyResponse.data;
            } else if (Array.isArray(anyResponse)) {
                // 格式4: 直接返回数组
                userData = anyResponse;
            }

            // 处理用户数据
            if (userData && Array.isArray(userData)) {
                userData.forEach((user: any) => {
                    // 支持多种字段名格式
                    const userId = user.id || user.user_id || user.Id;
                    const username =
                        user.username ||
                        user.name ||
                        user.userName ||
                        user.user_name;

                    if (userId && username) {
                        this.userMap.set(Number(userId), String(username));
                    }
                });
            } else {
                throw new Error(
                    '用户数据格式不正确: 无法找到有效的用户数据数组'
                );
            }
        } catch (error) {
            console.error('UserMappingPlugin: 加载用户数据失败', error);
            throw error;
        }
    }

    // 清除缓存
    clearCache(): void {
        this.userMap.clear();
        this.hasLoaded = false;
    }

    // 获取所有用户数据
    getAllUsers(): Map<number, string> {
        return new Map(this.userMap);
    }

    // 预加载用户数据 - 提供给外部调用
    async preloadUsers(): Promise<void> {
        if (!this.hasLoaded && !this.isLoading) {
            await this.loadUserData();
        }
    }
}

/**
 * 用户映射列插件
 * 将用户ID转换为用户名显示
 */
const userMappingColumnPlugin: TablePlugin = {
    name: 'UserMappingColumn',
    version: '1.0.0',
    install: (core: TableCore, options?: any) => {
        const cacheManager = UserCacheManager.getInstance();

        // 1. 注册 UserMappingRenderer 渲染器
        core.registerRenderer('UserMappingRenderer', UserMappingRenderer);

        // 2. 注册 userMapping 列类型
        core.registerColumnType('userMapping', {
            cellRender: { name: 'UserMappingRenderer' },
            sortable: true,
        });

        // 3. 扩展 column 辅助对象，添加 userMapping 方法
        core.extendColumnHelper(
            'userMapping',
            (
                field: string,
                title: string,
                options: Omit<UserMappingColumnOptions, 'field' | 'title'> = {}
            ): UserMappingColumnOptions => {
                return {
                    field,
                    title,
                    cellRender: {
                        name: 'UserMappingRenderer',
                        props: {
                            cacheManager,
                            nullDisplay: options.nullDisplay || '-',
                            unknownDisplay:
                                options.unknownDisplay || '未知用户',
                            showId: options.showId || false,
                            clickable: options.clickable || false,
                            template: options.template || '',
                            userStyle: options.userStyle,
                            idStyle: options.idStyle,
                            onUserClick: options.onUserClick,
                            ...options,
                        },
                    },
                    align: options.align || 'left',
                    width: options.width || 120,
                    ...options,
                } as UserMappingColumnOptions;
            }
        );

        // 4. 设置全局配置，暴露缓存管理器
        core.setGlobalConfig('userCacheManager', cacheManager);

        // 5. 提供全局方法供外部预加载用户数据
        if (typeof window !== 'undefined') {
            (window as any).__userCacheManager = cacheManager;
        }

        // 6. 监听页面刷新事件，重新加载用户数据
        if (typeof window !== 'undefined') {
            const handlePageRefresh = () => {
                cacheManager.clearCache();
            };

            window.addEventListener('beforeunload', handlePageRefresh);

            // 清理函数
            core.on('plugin-uninstall', () => {
                window.removeEventListener('beforeunload', handlePageRefresh);
                if ((window as any).__userCacheManager) {
                    delete (window as any).__userCacheManager;
                }
            });
        }

        // console.log('UserMappingColumn plugin installed');
    },

    uninstall: (core: TableCore) => {
        // 清理缓存
        const cacheManager = core.getGlobalConfig('userCacheManager');
        if (cacheManager) {
            cacheManager.clearCache();
        }

        // console.log('UserMappingColumn plugin uninstalled');
    },
};

export default userMappingColumnPlugin;
export { UserCacheManager };
