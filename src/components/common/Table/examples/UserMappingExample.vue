<template>
  <div class="user-mapping-example">
    <h2>用户映射插件示例</h2>
    
    <div class="mb-4">
      <h3>功能演示：</h3>
      <ul class="list-disc list-inside text-sm text-gray-600 mb-4">
        <li>自动将用户ID转换为用户名显示</li>
        <li>支持创建人、修改人字段的快捷配置</li>
        <li>支持点击用户名查看用户详情</li>
        <li>支持自定义显示模板</li>
        <li>数据缓存机制，页面刷新时重新获取</li>
      </ul>
    </div>

    <VTable @register="register" />
  </div>
</template>

<script setup lang="ts">
import { VTable, useTable, createPluginManager, PluginPresets } from '@/components/common/Table';
import { ref } from 'vue';

// 创建包含用户映射插件的插件管理器
const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

// 模拟用户数据API
const mockUserData = [
  { 
    id: 1, 
    name: '项目A', 
    description: '这是项目A的描述',
    status: 'active',
    created_by: 1,  // 张三
    updated_by: 2,  // 李四
    assigned_to: 3, // 王五
    created_at: '2024-01-01 10:00:00',
    updated_at: '2024-01-02 15:30:00'
  },
  { 
    id: 2, 
    name: '项目B', 
    description: '这是项目B的描述',
    status: 'pending',
    created_by: 2,  // 李四
    updated_by: 1,  // 张三
    assigned_to: 1, // 张三
    created_at: '2024-01-03 09:15:00',
    updated_at: '2024-01-04 11:20:00'
  },
  { 
    id: 3, 
    name: '项目C', 
    description: '这是项目C的描述',
    status: 'completed',
    created_by: 3,  // 王五
    updated_by: 3,  // 王五
    assigned_to: 2, // 李四
    created_at: '2024-01-05 14:30:00',
    updated_at: '2024-01-06 16:45:00'
  }
];

// 模拟数据获取API
const fetchProjectData = async (searchParams: any, pageParams: any) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    items: mockUserData,
    total: mockUserData.length
  };
};

// 状态映射
const statusMap = {
  'active': { text: '进行中', color: '#52c41a' },
  'pending': { text: '待处理', color: '#faad14' },
  'completed': { text: '已完成', color: '#1890ff' }
};

// 表格列配置
const columns = [
  column.selection({ fixed: 'left' }),
  
  // 基础信息
  column.text('name', '项目名称', { 
    width: 150,
    fixed: 'left' 
  }),
  
  column.text('description', '项目描述', { 
    width: 200,
    ellipsis: true 
  }),
  
  // 状态列
  column.status('status', '状态', statusMap, {
    width: 100
  }),
  
  // 用户映射列 - 快捷方法
  column.createdBy('created_by', '创建人', {
    width: 100
  }),
  
  column.updatedBy('updated_by', '修改人', {
    width: 100
  }),
  
  // 用户映射列 - 完整配置
  column.userMapping('assigned_to', '负责人', {
    width: 120,
    showId: true,  // 显示用户名和ID
    clickable: true,
    onUserClick: (userId, username, row) => {
      alert(`点击了用户: ${username} (ID: ${userId})`);
      console.log('行数据:', row);
    }
  }),
  
  // 时间列
  column.datetime('created_at', '创建时间', {
    width: 160
  }),
  
  column.datetime('updated_at', '更新时间', {
    width: 160
  }),
  
  // 操作列
  column.action('操作', {
    width: 120,
    fixed: 'right',
    actions: [
      {
        text: '编辑',
        icon: 'mdi:pencil',
        color: 'primary',
        onClick: (row) => {
          console.log('编辑项目:', row);
        }
      },
      {
        text: '删除',
        icon: 'mdi:delete',
        color: 'danger',
        onClick: (row) => {
          console.log('删除项目:', row);
        }
      }
    ]
  })
];

// 配置表格
const [register, tableApi] = useTable({
  fetchApi: fetchProjectData,
  columns,
  checkType: 'checkbox',
  showPagination: true,
  tableStyle: {
    striped: true,
    border: 'horizontal',
    compact: true,
    headerStyle: 'gray'
  }
});

defineOptions({
  name: 'UserMappingExample'
});
</script>

<style scoped>
.user-mapping-example {
  padding: 20px;
}

.user-mapping-example h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: #1f2937;
}

.user-mapping-example h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}
</style> 