<template>
  <div class="plugin-example">
    <div class="header">
      <h2>插件化表格示例</h2>
      <p>展示 Table 插件系统的完整功能</p>
      
      <div class="controls">
        <button @click="reloadData" class="btn-primary">
          🔄 刷新数据
        </button>
        <button @click="getSelectedRows" class="btn-secondary">
          ✅ 获取选中
        </button>
        <button @click="showTableInfo" class="btn-info">
          📊 表格信息
        </button>
      </div>
    </div>

    <VTable @register="register" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  VTable,
  useTable,
  createPluginManager,
  PluginPresets
} from '@/components/common/Table';

// 创建插件管理器
const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

// 模拟数据API
const mockApi = async (params: any) => {
  await new Promise(resolve => setTimeout(resolve, 800));
  
  return {
    items: [
      {
        id: 1,
        name: '张三',
        email: 'zhang<PERSON>@example.com',
        phone: '13800138000',
        avatar: 'https://example.com/avatar1.jpg',
        status: 'active',
        grade: 'vip',
        department: { id: 1, name: '技术部' },
        is_active: true,
        salary: 15000,
        join_date: '2023-01-15',
        created_at: '2023-01-15 09:00:00',
        address: '北京市朝阳区',
        orders: [{ id: 1 }, { id: 2 }, { id: 3 }],
        tags: ['前端', 'Vue.js', 'TypeScript']
      },
      {
        id: 2,
        name: '李四',
        email: '<EMAIL>',
        phone: '13900139000',
        avatar: null,
        status: 'pending',
        grade: 'normal',
        department: { id: 2, name: '产品部' },
        is_active: false,
        salary: 12000,
        join_date: '2023-02-20',
        created_at: '2023-02-20 14:30:00',
        address: '上海市浦东新区',
        orders: [{ id: 4 }],
        tags: ['产品', '需求分析']
      },
      {
        id: 3,
        name: '王五',
        email: '<EMAIL>',
        phone: '',
        avatar: 'https://example.com/avatar3.jpg',
        status: 'inactive',
        grade: 'premium',
        department: { id: 1, name: '技术部' },
        is_active: true,
        salary: 18000,
        join_date: '2022-12-01',
        created_at: '2022-12-01 10:15:00',
        address: '深圳市南山区',
        orders: [],
        tags: ['后端', 'Node.js', '数据库']
      }
    ],
    total: 3
  };
};

// 列配置
const columns = [
  // 选择框
  column.selection({ fixed: 'left' }),
  
  // 序号
  column.index('序号'),
  
  // 复合用户信息列
  column.composite('user_info', '用户信息', {
    main: {
      field: 'name',
      style: { fontWeight: 'bold', fontSize: '14px' }
    },
    subs: [
      {
        field: 'email',
        template: '📧 ${value}',
        style: { color: '#3b82f6', fontSize: '12px' },
        condition: (record) => !!record.email
      },
      {
        field: 'department.name',
        template: '🏢 ${value}',
        style: { color: '#8b5cf6', fontSize: '12px' }
      },
      {
        field: 'tags',
        formatter: (value) => value?.join(', ') || '',
        template: '🏷️ ${value}',
        style: { color: '#6b7280', fontSize: '11px' },
        condition: (record) => record.tags?.length > 0
      }
    ],
    icon: {
      type: 'avatar',
      avatarField: 'avatar',
      nameField: 'name',
      size: 36
    },
    actions: [
      {
        icon: 'mdi:phone',
        tooltip: '拨打电话',
        condition: (row) => !!row.phone,
        onClick: (row) => alert(`拨打电话: ${row.phone}`)
      },
      {
        icon: 'mdi:email',
        tooltip: '发送邮件',
        condition: (row) => !!row.email,
        onClick: (row) => alert(`发送邮件: ${row.email}`)
      }
    ],
    width: 280,
    fixed: 'left'
  }),
  
  // 电话链接
  column.phoneLink('phone', '电话', { width: 130 }),
  
  // 状态列
  column.status('status', '状态', {
    'active': { text: '激活', color: '#10b981' },
    'pending': { text: '待审核', color: '#f59e0b' },
    'inactive': { text: '停用', color: '#ef4444' }
  }),
  
  // 等级状态
  column.status('grade', '等级', {
    'vip': { text: 'VIP', color: '#8b5cf6' },
    'premium': { text: '高级', color: '#3b82f6' },
    'normal': { text: '普通', color: '#6b7280' }
  }),
  
  // 布尔开关
  column.boolean('is_active', '启用状态', {
    style: 'switch',
    trueText: '启用',
    falseText: '禁用',
    onChange: async (value, row, field) => {
      // 模拟API调用
      const oldValue = row[field];
      row[field] = value;
      
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        alert(`用户 ${row.name} 状态已${value ? '启用' : '禁用'}`);
      } catch (error) {
        row[field] = oldValue;
        alert('状态更新失败');
      }
    }
  }),
  
  // 关系列 - 部门
  column.manyToOne('department', '部门', {
    displayField: 'name',
    clickable: true,
    onItemClick: (dept) => alert(`查看部门: ${dept.name}`)
  }),
  
  // 货币列 - 工资
  column.currency('salary', '工资', '¥', {
    precision: 0,
    width: 120
  }),
  
  // 关系列 - 订单数量
  column.oneToMany('orders', '订单', {
    itemName: '个',
    clickable: true,
    onItemClick: (orders, row) => {
      if (orders.length > 0) {
        alert(`${row.name} 有 ${orders.length} 个订单`);
      } else {
        alert(`${row.name} 暂无订单`);
      }
    }
  }),
  
  // 日期列 - 入职日期
  column.dateOnly('join_date', '入职日期'),
  
  // 日期时间列 - 创建时间
  column.datetime('created_at', '创建时间'),
  
  // 操作列
  column.action('操作', {
    actions: [
      {
        text: '编辑',
        icon: 'mdi:pencil',
        color: 'primary',
        onClick: (row) => alert(`编辑用户: ${row.name}`)
      },
      {
        text: '查看',
        icon: 'mdi:eye',
        color: 'info',
        onClick: (row) => alert(`查看用户: ${row.name}`)
      },
      {
        text: '删除',
        icon: 'mdi:delete',
        color: 'danger',
        onClick: (row) => {
          if (confirm(`确定删除用户 ${row.name} 吗？`)) {
            alert(`已删除用户: ${row.name}`);
          }
        }
      }
    ],
    width: 180,
    fixed: 'right'
  })
];

// 配置表格
const [register, tableApi] = useTable({
  fetchApi: mockApi,
  columns,
  checkType: 'checkbox',
  showPagination: true,
  height: 600,
  toolbarConfig: {
    isReload: true,
    isZoom: true,
    isFull: true
  },
  on: {
    'checkbox-change': ({ records }) => {
      console.log('选中的行:', records);
    }
  }
});

// 控制方法
const reloadData = () => {
  tableApi.reload();
};

const getSelectedRows = () => {
  const selected = tableApi.getCheckboxRecords();
  console.log('选中的行:', selected);
  alert(`选中了 ${selected.length} 行数据`);
};

const showTableInfo = () => {
  console.log('表格配置:', columns);
  console.log('表格API:', tableApi);
  alert('请查看控制台输出');
};
</script>

<style scoped>
.plugin-example {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.header h2 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.header p {
  margin: 0 0 20px 0;
  color: #6b7280;
  line-height: 1.6;
}

.controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn-primary, .btn-secondary, .btn-info {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-secondary {
  background: #10b981;
  color: white;
}

.btn-info {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover, .btn-secondary:hover, .btn-info:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* 表格样式增强 */
:deep(.vxe-table) {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

:deep(.vxe-table .vxe-header--column .vxe-cell) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #374151;
  font-weight: 600;
}

:deep(.vxe-table .vxe-body--row:hover) {
  background-color: #f8fafc !important;
}

:deep(.vxe-table .vxe-cell) {
  padding: 12px !important;
  line-height: 1.4 !important;
}

/* 固定列样式 */
:deep(.vxe-table .vxe-table--fixed-left) {
  border-right: 2px solid #d1d5db !important;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1) !important;
}

:deep(.vxe-table .vxe-table--fixed-right) {
  border-left: 2px solid #d1d5db !important;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plugin-example {
    padding: 12px;
  }
  
  .header {
    padding: 16px;
  }
  
  .controls {
    flex-direction: column;
  }
  
  .btn-primary, .btn-secondary, .btn-info {
    width: 100%;
    justify-content: center;
  }
}
</style> 