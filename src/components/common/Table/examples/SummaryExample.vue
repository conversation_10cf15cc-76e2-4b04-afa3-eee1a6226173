<template>
  <div class="summary-example-container">
    <div class="example-header">
      <h2>合计列插件示例</h2>
      <p>展示表格底部合计行的各种功能</p>
    </div>

    <!-- 基础合计示例 -->
    <div class="example-section">
      <h3>基础合计示例</h3>
      <VTable
        :columns="basicColumns"
        :data="sampleData"
        :summary-config="basicSummaryConfig"
        height="300"
      />
    </div>

    <!-- 条件计算示例 -->
    <div class="example-section">
      <h3>条件计算示例</h3>
      <VTable
        :columns="conditionalColumns"
        :data="orderData"
        :summary-config="conditionalSummaryConfig"
        height="300"
      />
    </div>

    <!-- 财务报表示例 -->
    <div class="example-section">
      <h3>财务报表示例</h3>
      <VTable
        :columns="financialColumns"
        :data="financialData"
        :summary-config="financialSummaryConfig"
        height="300"
      />
    </div>

    <!-- 配置面板 -->
    <div class="config-panel">
      <h3>配置选项</h3>
      <div class="config-form">
        <div class="form-item">
          <label>位置:</label>
          <select v-model="configOptions.position">
            <option value="bottom">底部</option>
            <option value="top">顶部</option>
          </select>
        </div>
        <div class="form-item">
          <label>粘性定位:</label>
          <input type="checkbox" v-model="configOptions.sticky" />
        </div>
        <div class="form-item">
          <label>显示边框:</label>
          <input type="checkbox" v-model="configOptions.showBorder" />
        </div>
        <div class="form-item">
          <label>背景色:</label>
          <input type="color" v-model="configOptions.background" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { createPluginManager, PluginPresets } from '../plugins';
import type { SummaryColumnOptions } from '../plugins/summaryColumn';
import VTable from '../src/index.vue';

// 确保VTable组件被正确注册
defineOptions({
  components: {
    VTable
  }
});

// 创建插件管理器
const pluginManager = createPluginManager(PluginPresets.analytics);
const column = pluginManager.getColumnHelper();

// 配置选项
const configOptions = ref({
  position: 'bottom' as const,
  sticky: true,
  showBorder: true,
  background: '#f0f9f0'
});

// 基础示例数据
const sampleData = ref([
  { id: 1, name: '商品A', price: 99.99, quantity: 10, amount: 999.9 },
  { id: 2, name: '商品B', price: 149.99, quantity: 5, amount: 749.95 },
  { id: 3, name: '商品C', price: 79.99, quantity: 8, amount: 639.92 },
  { id: 4, name: '商品D', price: 199.99, quantity: 3, amount: 599.97 },
  { id: 5, name: '商品E', price: 129.99, quantity: 7, amount: 909.93 }
]);

// 订单数据
const orderData = ref([
  { id: 1, order_no: 'ORD001', amount: 1200, status: 'paid', customer: '张三' },
  { id: 2, order_no: 'ORD002', amount: 800, status: 'pending', customer: '李四' },
  { id: 3, order_no: 'ORD003', amount: 1500, status: 'paid', customer: '王五' },
  { id: 4, order_no: 'ORD004', amount: 600, status: 'cancelled', customer: '赵六' },
  { id: 5, order_no: 'ORD005', amount: 2000, status: 'paid', customer: '钱七' }
]);

// 财务数据
const financialData = ref([
  { id: 1, project: '项目A', revenue: 50000, cost: 30000, margin: 20000 },
  { id: 2, project: '项目B', revenue: 80000, cost: 60000, margin: 20000 },
  { id: 3, project: '项目C', revenue: 120000, cost: 80000, margin: 40000 },
  { id: 4, project: '项目D', revenue: 200000, cost: 150000, margin: 50000 },
  { id: 5, project: '项目E', revenue: 150000, cost: 100000, margin: 50000 }
]);

// 基础列定义
const basicColumns = [
  column.text('name', '商品名称', { width: 150 }),
  column.number('price', '单价', { precision: 2, prefix: '¥', width: 120 }),
  column.number('quantity', '数量', { width: 100 }),
  column.number('amount', '金额', { precision: 2, prefix: '¥', width: 120 })
];

// 条件计算列定义
const conditionalColumns = [
  column.text('order_no', '订单号', { width: 120 }),
  column.text('customer', '客户', { width: 100 }),
  column.number('amount', '金额', { precision: 2, prefix: '¥', width: 120 }),
  column.status('status', '状态', {
    'paid': { label: '已付款', color: '#52c41a' },
    'pending': { label: '待付款', color: '#fa8c16' },
    'cancelled': { label: '已取消', color: '#f5222d' }
  }, { width: 100 })
];

// 财务列定义
const financialColumns = [
  column.text('project', '项目', { width: 120 }),
  column.number('revenue', '收入', { precision: 0, prefix: '¥', width: 120 }),
  column.number('cost', '成本', { precision: 0, prefix: '¥', width: 120 }),
  column.number('margin', '利润', { precision: 0, prefix: '¥', width: 120 })
];

// 基础合计配置
const basicSummaryConfig = computed((): SummaryColumnOptions => ({
  enabled: true,
  position: configOptions.value.position,
  sticky: configOptions.value.sticky,
  showBorder: configOptions.value.showBorder,
  background: configOptions.value.background,
  summaries: {
    'name': {
      label: '合计',
      style: { fontWeight: 'bold' }
    },
    'quantity': {
      calculator: 'sum',
      label: '总数量',
      suffix: ' 件',
      style: { color: '#1890ff' }
    },
    'amount': {
      calculator: 'sum',
      label: '总金额',
      formatter: (value) => `¥${Number(value).toLocaleString()}`,
      style: { fontWeight: 'bold', color: '#52c41a' }
    }
  }
}));

// 条件计算配置
const conditionalSummaryConfig = computed((): SummaryColumnOptions => ({
  enabled: true,
  position: configOptions.value.position,
  sticky: configOptions.value.sticky,
  summaries: {
    'order_no': {
      label: '统计',
      style: { fontWeight: 'bold' }
    },
    'customer': {
      calculator: 'count',
      label: '客户数',
      suffix: ' 个'
    },
    'amount': {
      calculator: 'sum',
      label: '总金额',
      formatter: (value) => `¥${Number(value).toLocaleString()}`,
      style: { fontWeight: 'bold', color: '#52c41a' }
    },
    'status': {
      calculator: (values, data) => {
        const paidCount = data.filter(row => row.status === 'paid').length;
        const total = data.length;
        return total > 0 ? ((paidCount / total) * 100).toFixed(1) + '%' : '0%';
      },
      label: '付款率',
      style: { color: '#1890ff' }
    }
  }
}));

// 财务报表配置
const financialSummaryConfig = computed((): SummaryColumnOptions => ({
  enabled: true,
  position: configOptions.value.position,
  sticky: configOptions.value.sticky,
  background: '#f0f9f0',
  style: {
    borderTop: '2px solid #52c41a'
  },
  summaries: {
    'project': {
      label: '总计',
      style: { fontWeight: 'bold', fontSize: '16px' }
    },
    'revenue': {
      calculator: 'sum',
      label: '总收入',
      formatter: (value) => `¥${Number(value).toLocaleString()}`,
      style: { fontWeight: 'bold', color: '#52c41a' }
    },
    'cost': {
      calculator: 'sum',
      label: '总成本',
      formatter: (value) => `¥${Number(value).toLocaleString()}`,
      style: { fontWeight: 'bold', color: '#f5222d' }
    },
    'margin': {
      calculator: (values, data) => {
        const totalRevenue = data.reduce((sum, row) => sum + (row.revenue || 0), 0);
        const totalCost = data.reduce((sum, row) => sum + (row.cost || 0), 0);
        const profit = totalRevenue - totalCost;
        return profit;
      },
      label: '净利润',
      formatter: (value) => {
        const color = value >= 0 ? '#52c41a' : '#f5222d';
        const sign = value >= 0 ? '+' : '';
        return `${sign}¥${Number(value).toLocaleString()}`;
      },
      style: { fontWeight: 'bold', fontSize: '16px' }
    }
  }
}));
</script>

<style scoped>
.summary-example-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-header {
  text-align: center;
  margin-bottom: 30px;
}

.example-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.example-header p {
  color: #666;
  font-size: 14px;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fff;
}

.example-section h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 16px;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 5px;
}

.config-panel {
  margin-top: 30px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 8px;
}

.config-panel h3 {
  color: #333;
  margin-bottom: 15px;
}

.config-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-item label {
  font-weight: 500;
  color: #333;
  min-width: 80px;
}

.form-item select,
.form-item input[type="color"] {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  outline: none;
}

.form-item select:focus,
.form-item input[type="color"]:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #1890ff;
}

/* 表格样式调整 */
:deep(.vxe-table) {
  border: 1px solid #e8e8e8;
}

:deep(.summary-column) {
  font-weight: 500;
}

:deep(.summary-column.emphasis) {
  background-color: #f0f9f0;
  font-weight: bold;
}
</style> 