import VTable from './src/index.vue';
import { useTable } from './src/hooks/useTable';
import { useTableDebug } from './src/devtools';

// 导出类型
export * from './src/types';

// 导出预设配置
export * from './src/presets';

export * from './src/infoModel';

// // 导出简化Hook
// export * from './src/hooks/useEasyTable';

// 导出插件系统 - 新的插件化架构
export * from './plugins';

// 导出列配置助手（仅保留复合列相关功能）
export {
    compositeColumn,
    compositeHelpers,
    getFieldValue,
    formatFieldValue,
} from './src/columnHelper';

// 导出主要组件和Hook
export { VTable, useTable, useTableDebug };

// 重新导出插件管理器相关的核心功能
export { createPluginManager, PluginPresets } from './plugins';
