# Table 插件化迁移指南

## 概述

Table 组件现已全面启用插件化架构，提供更好的模块化、可扩展性和开发体验。

## 迁移前后对比

### 旧版本使用方式（已废弃）

```typescript
// ❌ 旧的使用方式 - 已不再支持
import { VTable, useTable } from '@/components/common/Table';
import { 
  textColumn, 
  numberColumn, 
  dateColumn, 
  statusColumn,
  booleanColumn,
  actionColumn
} from '@/components/common/Table';

const columns = [
  textColumn('name', '姓名'),
  numberColumn('age', '年龄'),
  dateColumn('created_at', '创建时间'),
  statusColumn('status', '状态', statusMap),
  booleanColumn('active', '激活状态'),
  actionColumn('操作', { actions: [...] })
];
```

### 新版本使用方式（推荐）

```typescript
// ✅ 新的插件化使用方式
import { 
  VTable, 
  useTable,
  createPluginManager,
  PluginPresets
} from '@/components/common/Table';

// 1. 创建插件管理器
const pluginManager = createPluginManager(PluginPresets.full);

// 2. 获取列助手对象
const column = pluginManager.getColumnHelper();

// 3. 使用插件方法创建列
const columns = [
  column.text('name', '姓名'),
  column.number('age', '年龄'),
  column.datetime('created_at', '创建时间'),
  column.status('status', '状态', statusMap),
  column.boolean('active', '激活状态'),
  column.action('操作', { actions: [...] })
];
```

## 已清理的旧代码

### 删除的文件
- `src/components/common/Table/src/renderer/BooleanRenderer.vue` - 替换为插件版本
- `src/components/common/Table/src/renderer/MyInput.vue` - 空文件
- 所有旧的列助手函数（`textColumn`, `numberColumn`, `dateColumn` 等）

### 清理的代码
- `src/components/common/Table/src/columnHelper.ts` - 移除所有已插件化的函数
- `src/components/common/Table/src/components/TableColumn.vue` - 移除旧的兼容性代码
- `src/components/common/Table/index.ts` - 更新导出，只保留插件化API

## 插件预设包

根据不同场景选择合适的插件包：

```typescript
// 基础功能
createPluginManager(PluginPresets.basic)

// 完整功能
createPluginManager(PluginPresets.full)

// 内容管理系统
createPluginManager(PluginPresets.cms)

// 数据分析
createPluginManager(PluginPresets.analytics)

// 用户管理
createPluginManager(PluginPresets.userManagement)
```

## 可用的插件列表

### 列渲染插件
- **TextColumn** - 文本列
- **NumberColumn** - 数字列（货币、百分比）
- **DateColumn** - 日期时间列
- **LinkColumn** - 链接列（邮箱、电话、网址）
- **StatusColumn** - 状态标签列
- **BooleanColumn** - 布尔值列
- **CompositeColumn** - 复合列
- **AvatarColumn** - 头像列
- **RelationColumn** - 关系列（一对一、一对多等）

### 功能插件
- **ActionColumn** - 操作列
- **PaginationPlugin** - 分页组件

## 迁移步骤

### 1. 更新导入
```typescript
// 旧的导入
import { textColumn, numberColumn } from '@/components/common/Table';

// 新的导入
import { createPluginManager, PluginPresets } from '@/components/common/Table';
```

### 2. 创建插件管理器
```typescript
const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();
```

### 3. 更新列定义
```typescript
// 旧的列定义
const columns = [
  textColumn('name', '姓名'),
  numberColumn('price', '价格', { precision: 2, unit: '元' }),
];

// 新的列定义
const columns = [
  column.text('name', '姓名'),
  column.currency('price', '价格', '¥', { precision: 2 }),
];
```

## 新增功能

### 1. 关系列支持
```typescript
// 多对一关系
column.manyToOne('user', '用户', {
  displayField: 'name',
  clickable: true,
  onItemClick: (user) => console.log(user)
})

// 一对多关系
column.oneToMany('orders', '订单', {
  itemName: '个订单',
  showCount: true
})
```

### 2. 复合列功能
```typescript
column.composite('user_info', '用户信息', {
  main: { field: 'name' },
  subs: [
    { field: 'email', style: { color: '#666' } },
    { field: 'department' }
  ],
  icon: { type: 'avatar', avatarField: 'avatar' },
  actions: [
    { icon: 'mdi:phone', onClick: (row) => call(row.phone) }
  ]
})
```

### 3. 链接列支持
```typescript
// 电话链接
column.phoneLink('phone', '电话')

// 邮箱链接  
column.emailLink('email', '邮箱')

// 网址链接
column.urlLink('website', '官网')
```

## 兼容性说明

- ✅ 所有现有的表格配置和数据格式保持兼容
- ✅ `compositeColumn` 函数保留向后兼容
- ✅ VTable 组件API保持不变
- ✅ useTable Hook保持不变
- ❌ 旧的列助手函数不再可用，需要使用插件版本

## 性能优势

- 📦 **按需加载** - 只加载需要的插件
- 🚀 **更好的性能** - 插件化架构减少不必要的代码
- 🔧 **易于扩展** - 可以轻松添加自定义插件
- 🛡️ **类型安全** - 完整的 TypeScript 支持

## 获取帮助

如果在迁移过程中遇到问题：
1. 查看 `src/components/common/Table/plugins/README.md` 详细文档
2. 参考 `src/components/TextStyleDemo.vue` 示例代码
3. 查看各插件的使用示例：`src/components/common/Table/plugins/USAGE_EXAMPLES.md` 