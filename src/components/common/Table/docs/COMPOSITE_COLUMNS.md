# 复合列插件完整指南

复合列插件是一个强大的表格列配置系统，支持在单个列中显示主数据、多个辅助数据、图标以及操作按钮等。

## 🚀 快速开始

### 基础使用

```typescript
import { 
  createPluginManager, 
  PluginPresets 
} from '@/components/common/Table/plugins';

// 创建包含复合列插件的管理器
const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

// 基础复合列
const columns = [
  column.composite('user_info', '用户信息', {
    main: {
      field: 'name',
      style: { fontWeight: 'bold', fontSize: '14px' }
    },
    subs: [
      {
        field: 'email',
        template: '📧 ${value}',
        style: { color: '#3b82f6', fontSize: '12px' }
      },
      {
        field: 'department',
        template: '🏢 ${value}',
        style: { color: '#8b5cf6', fontSize: '12px' }
      }
    ],
    icon: {
      type: 'avatar',
      avatarField: 'avatar',
      nameField: 'name'
    },
    width: 240
  })
];
```

## 🎨 核心特性

- **多层信息展示** - 主信息 + 多个辅助信息
- **灵活布局** - 水平/垂直/混合布局
- **图标支持** - 头像、图标、图片
- **操作集成** - 内嵌操作按钮和下拉菜单
- **条件显示** - 基于数据的动态显示
- **模板语法** - 强大的模板系统
- **交互功能** - 悬停展开、点击事件
- **样式自定义** - 完全的样式控制

## 📋 配置选项

### 主信息配置 (main)

```typescript
column.composite('customer', '客户信息', {
  main: {
    field: 'name',                    // 数据字段
    formatter: (value, row) => value || '未命名', // 格式化函数
    style: { 
      fontWeight: 'bold', 
      fontSize: '14px',
      color: '#1f2937'
    },
    template: '客户: ${value}'        // 模板字符串
  }
})
```

### 辅助信息配置 (subs)

```typescript
column.composite('order', '订单信息', {
  main: { field: 'order_no' },
  subs: [
    // 基础字段
    {
      field: 'customer.name',
      template: '客户: ${value}',
      style: { color: '#3b82f6' },
      condition: (row) => !!row.customer
    },
    
    // 计算字段
    {
      computed: (row) => `${row.items?.length || 0} 个商品`,
      style: { color: '#10b981' }
    },
    
    // 格式化显示
    {
      field: 'total_amount',
      formatter: (value) => `¥${Number(value).toFixed(2)}`,
      template: '总额: ${value}',
      style: { fontWeight: 'bold', color: '#ef4444' }
    }
  ]
})
```

### 图标配置 (icon)

```typescript
// 头像图标
column.composite('user', '用户', {
  main: { field: 'name' },
  icon: {
    type: 'avatar',
    avatarField: 'avatar',
    nameField: 'name',
    size: 36,
    shape: 'circle'
  }
})

// 字体图标
column.composite('file', '文件', {
  main: { field: 'filename' },
  icon: {
    type: 'icon',
    iconName: 'mdi:file-document',
    size: 24,
    color: '#3b82f6'
  }
})

// 图片图标
column.composite('product', '产品', {
  main: { field: 'name' },
  icon: {
    type: 'image',
    imageField: 'thumbnail',
    size: 40,
    shape: 'rounded'
  }
})
```

### 操作按钮 (actions)

```typescript
column.composite('customer', '客户信息', {
  main: { field: 'name' },
  subs: [{ field: 'phone' }],
  actions: [
    {
      icon: 'mdi:phone',
      tooltip: '拨打电话',
      color: '#10b981',
      condition: (row) => !!row.phone,
      onClick: (row) => {
        window.open(`tel:${row.phone}`);
      }
    },
    {
      icon: 'mdi:email',
      tooltip: '发送邮件',
      color: '#3b82f6',
      condition: (row) => !!row.email,
      onClick: (row) => {
        window.open(`mailto:${row.email}`);
      }
    }
  ]
})
```

### 更多操作菜单 (moreActions)

```typescript
column.composite('user', '用户信息', {
  main: { field: 'name' },
  moreActions: {
    type: 'group',
    trigger: 'click',
    items: [
      {
        text: '查看详情',
        icon: 'mdi:eye',
        onClick: (row) => viewUserDetail(row.id)
      },
      {
        text: '编辑信息',
        icon: 'mdi:pencil',
        onClick: (row) => editUser(row)
      },
      {
        text: '删除用户',
        icon: 'mdi:delete',
        danger: true,
        condition: (row) => row.deletable,
        onClick: (row) => deleteUser(row.id)
      }
    ]
  }
})
```

## 🎯 布局选项

### 水平布局

```typescript
column.composite('info', '信息', {
  main: { field: 'title' },
  subs: [
    { field: 'category' },
    { field: 'status' }
  ],
  layout: 'horizontal',
  separator: {
    main: ' - ',
    subs: ' | '
  }
})
// 输出: 标题 - 分类 | 状态
```

### 垂直布局

```typescript
column.composite('user', '用户', {
  main: { field: 'name' },
  subs: [
    { field: 'email' },
    { field: 'phone' }
  ],
  layout: 'vertical'
})
// 输出:
// 用户名
// 邮箱
// 电话
```

### 混合布局

```typescript
column.composite('contact', '联系信息', {
  main: { field: 'name' },
  subs: [
    { field: 'title' },
    { field: 'phone' },
    { field: 'email' }
  ],
  layout: 'mixed',
  subLayout: {
    main: 'horizontal',  // 主信息水平排列
    subs: 'vertical'     // 辅助信息垂直排列
  }
})
```

## 🔧 高级功能

### 条件显示

```typescript
column.composite('order', '订单信息', {
  main: { field: 'order_no' },
  subs: [
    {
      field: 'urgent_note',
      template: '🚨 ${value}',
      style: { color: '#ef4444', fontWeight: 'bold' },
      condition: (row) => row.is_urgent
    },
    {
      field: 'delivery_date',
      template: '📅 交期: ${value}',
      condition: (row) => !!row.delivery_date
    }
  ]
})
```

### 交互功能

```typescript
column.composite('details', '详细信息', {
  main: { field: 'summary' },
  subs: [
    { field: 'description' },
    { field: 'tags' }
  ],
  interactive: {
    hover: {
      showAll: true,      // 悬停显示全部信息
      highlight: true     // 高亮显示
    },
    click: {
      expandable: true,   // 可点击展开
      onExpand: (row) => {
        console.log('展开详情:', row);
      }
    }
  }
})
```

### 计算字段

```typescript
column.composite('statistics', '统计信息', {
  main: { field: 'name' },
  subs: [
    // 简单计算
    {
      computed: (row) => `${row.orders?.length || 0} 个订单`,
      style: { color: '#3b82f6' }
    },
    
    // 复杂计算
    {
      computed: (row) => {
        const total = row.orders?.reduce((sum, order) => sum + (order.amount || 0), 0) || 0;
        return `总消费: ¥${total.toFixed(2)}`;
      },
      style: { fontWeight: 'bold', color: '#10b981' }
    }
  ]
})
```

### 模板语法

```typescript
column.composite('address', '地址信息', {
  main: { field: 'name' },
  subs: [
    // 基础模板
    {
      field: 'province',
      template: '📍 ${value}'
    },
    
    // 多字段模板
    {
      template: '🏠 ${province} ${city} ${district}',
      fields: ['province', 'city', 'district'],
      condition: (row) => row.province && row.city
    },
    
    // 条件模板
    {
      field: 'zip_code',
      template: (value, row) => {
        return value ? `📮 邮编: ${value}` : '📮 邮编未设置';
      }
    }
  ]
})
```

## 📚 实战示例

### 客户管理表格

```typescript
const customerColumns = [
  column.selection({ fixed: 'left' }),
  
  // 客户基本信息
  column.composite('customer_info', '客户信息', {
    main: {
      field: 'name',
      formatter: (value) => value || '未命名客户',
      style: { fontWeight: 'bold', fontSize: '14px' }
    },
    subs: [
      {
        field: 'code',
        template: '编码: ${value}',
        style: { color: '#3b82f6', fontSize: '12px' },
        condition: (row) => !!row.code
      },
      {
        field: 'type.name',
        template: '类型: ${value}',
        style: { color: '#8b5cf6', fontSize: '12px' }
      }
    ],
    icon: {
      type: 'avatar',
      avatarField: 'avatar',
      nameField: 'name',
      size: 32
    },
    actions: [
      {
        icon: 'mdi:phone',
        tooltip: '拨打电话',
        condition: (row) => !!row.phone,
        onClick: (row) => window.open(`tel:${row.phone}`)
      }
    ],
    moreActions: {
      type: 'group',
      items: [
        {
          text: '查看详情',
          icon: 'mdi:eye',
          onClick: (row) => viewCustomer(row.id)
        },
        {
          text: '编辑客户',
          icon: 'mdi:pencil',
          onClick: (row) => editCustomer(row)
        }
      ]
    },
    width: 200,
    fixed: 'left'
  }),
  
  // 联系方式
  column.composite('contact_info', '联系方式', {
    main: {
      field: 'contact',
      formatter: (value) => value || '未设置联系人'
    },
    subs: [
      {
        field: 'phone',
        template: '📞 ${value}',
        style: { color: '#10b981' },
        condition: (row) => !!row.phone
      },
      {
        field: 'email',
        template: '📧 ${value}',
        style: { color: '#3b82f6' },
        condition: (row) => !!row.email
      },
      {
        field: 'address',
        template: '📍 ${value}',
        style: { color: '#8b5cf6' },
        condition: (row) => !!row.address
      }
    ],
    layout: 'vertical',
    width: 180
  }),
  
  // 业务状态
  column.composite('business_status', '业务状态', {
    main: {
      computed: (row) => {
        const orderCount = row.orders?.length || 0;
        return orderCount > 0 ? `${orderCount} 个订单` : '暂无订单';
      },
      style: { fontWeight: '500' }
    },
    subs: [
      {
        computed: (row) => {
          const total = row.orders?.reduce((sum, order) => sum + (order.amount || 0), 0) || 0;
          return total > 0 ? `总金额: ¥${total.toFixed(2)}` : null;
        },
        style: { color: '#10b981', fontWeight: 'bold' },
        condition: (row) => row.orders?.length > 0
      },
      {
        field: 'last_order_date',
        template: '最近: ${value}',
        style: { color: '#6b7280', fontSize: '12px' },
        condition: (row) => !!row.last_order_date
      }
    ],
    width: 160
  })
];
```

## 🎭 样式自定义

### 容器样式

```typescript
column.composite('styled', '自定义样式', {
  main: { field: 'title' },
  containerStyle: {
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    padding: '8px 12px',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  }
})
```

### 响应式样式

```typescript
column.composite('responsive', '响应式', {
  main: { field: 'title' },
  subs: [
    {
      field: 'description',
      style: {
        fontSize: '12px',
        '@media (max-width: 768px)': {
          display: 'none'  // 移动端隐藏
        }
      }
    }
  ]
})
```

## 🚀 性能优化

### 懒加载

```typescript
column.composite('lazy', '懒加载', {
  main: { field: 'title' },
  subs: [
    {
      field: 'heavy_data',
      lazy: true,
      loader: async (row) => {
        // 异步加载数据
        const data = await fetchHeavyData(row.id);
        return `加载的数据: ${data}`;
      }
    }
  ]
})
```

### 虚拟化支持

```typescript
column.composite('virtual', '虚拟化', {
  main: { field: 'title' },
  virtual: true,  // 启用虚拟化
  estimatedHeight: 60  // 预估高度
})
```

## 📖 最佳实践

1. **合理使用层级** - 避免嵌套过深
2. **性能考虑** - 大数据量时使用懒加载
3. **用户体验** - 提供清晰的视觉层次
4. **响应式设计** - 考虑移动端显示
5. **无障碍访问** - 添加适当的aria标签

## 🔗 相关文档

- [插件系统介绍](./plugins/README.md)
- [插件使用示例](./plugins/USAGE_EXAMPLES.md)
- [类型安全指南](./TYPE_SAFE_GUIDE.md)
