# 表格插件系统 - 类型安全使用指南

## 概述

现在插件系统已经支持完整的 TypeScript 智能提示！通过改进的类型定义，你可以在编辑器中获得完整的方法提示、参数提示和类型检查。

## 🎯 主要改进

1. **完整的智能提示** - `column.` 会显示所有可用的方法
2. **参数类型提示** - 方法参数会有详细的类型提示
3. **插件感知** - 只有已安装的插件方法才会显示
4. **类型安全** - 编译时类型检查，减少运行时错误

## 🚀 基础使用

### 1. 导入类型定义

```typescript
import { 
  createPluginManager, 
  PluginPresets, 
  type ColumnHelper 
} from '@/components/common/Table/plugins';
```

### 2. 创建类型安全的列助手

```typescript
// 创建插件管理器
const pluginManager = createPluginManager(PluginPresets.basic);

// 获取列助手对象，现在有完整的类型支持
const column: ColumnHelper = pluginManager.getColumnHelper();
```

### 3. 享受智能提示

```typescript
const columns = [
  // 输入 column. 时会显示所有可用方法
  column.selection(),           // ✅ 智能提示
  column.index(),              // ✅ 智能提示  
  column.text('name', '姓名'),  // ✅ 参数提示
  column.id('用户ID'),         // ✅ 参数提示
  
  // 插件方法（如果已安装）
  column.activeStatus?.('status', '状态'),  // ✅ 可选链确保安全
  column.composite?.('user', '用户', {}),   // ✅ 可选链确保安全
];
```

## 📋 可用的智能提示方法

### 基础方法（始终可用）
- `column.selection()` - 选择框列
- `column.index()` - 序号列  
- `column.text()` - 文本列
- `column.name()` - 名称列
- `column.id()` - ID列
- `column.column()` - 通用列

### 插件方法（按需可用）

#### StatusColumn 插件
- `column.status()` - 自定义状态列
- `column.activeStatus()` - 启用/禁用状态
- `column.orderStatus()` - 订单状态

#### CompositeColumn 插件  
- `column.composite()` - 复合信息列

#### AvatarColumn 插件
- `column.avatar()` - 头像列
- `column.userAvatar()` - 用户头像+姓名
- `column.simpleAvatar()` - 简单头像

#### LinkColumn 插件
- `column.link()` - 自定义链接
- `column.emailLink()` - 邮箱链接
- `column.phoneLink()` - 电话链接  
- `column.urlLink()` - 网址链接

#### NumberColumn 插件
- `column.number()` - 数字格式化
- `column.currency()` - 货币格式
- `column.percentage()` - 百分比格式

#### DateColumn 插件
- `column.date()` - 日期列
- `column.datetime()` - 日期时间列
- `column.dateOnly()` - 仅日期
- `column.timeOnly()` - 仅时间
- `column.relativeTime()` - 相对时间

#### BooleanColumn 插件
- `column.boolean()` - 布尔值列

#### ActionColumn 插件
- `column.action()` - 操作按钮列

## 🔧 最佳实践

### 1. 类型注解

```typescript
// ✅ 推荐：显式类型注解
const column: ColumnHelper = pluginManager.getColumnHelper();

// ❌ 不推荐：缺少类型注解
const column = pluginManager.getColumnHelper();
```

### 2. 可选链保护

```typescript
// ✅ 推荐：使用可选链确保插件方法存在
const columns = [
  column.text('name', '姓名'),
  ...(column.activeStatus ? [
    column.activeStatus('is_active', '状态')
  ] : []),
];

// ❌ 不推荐：直接调用可能不存在的方法
const columns = [
  column.text('name', '姓名'),
  column.activeStatus('is_active', '状态'), // 可能报错
];
```

### 3. 插件检查

```typescript
// ✅ 推荐：检查插件是否已安装
if (pluginManager.isPluginRegistered('StatusColumn')) {
  columns.push(column.activeStatus('status', '状态'));
}

// 或者使用可选链
if (column.activeStatus) {
  columns.push(column.activeStatus('status', '状态'));
}
```

### 4. 组合配置

```typescript
// ✅ 推荐：组合不同插件的功能
const userColumns = [
  column.selection(),
  column.userAvatar?.('avatar', 'name', '用户'),
  column.emailLink?.('email', '邮箱'),
  column.activeStatus?.('is_active', '状态'),
  column.datetime?.('created_at', '创建时间'),
  column.action?.('操作', { actions: [...] }),
];
```

## 🛠️ 在 Vue 组件中使用

### 单文件组件示例

```vue
<script setup lang="ts">
import { ref } from 'vue';
import { 
  createPluginManager, 
  PluginPresets, 
  type ColumnHelper 
} from '@/components/common/Table/plugins';

// 创建插件管理器
const pluginManager = createPluginManager(PluginPresets.basic);

// 获取类型安全的列助手
const column: ColumnHelper = pluginManager.getColumnHelper();

// 定义列配置 - 现在有完整的智能提示
const columns = ref([
  column.selection({ fixed: 'left' }),
  column.index('序号'),
  column.text('name', '姓名', {
    minWidth: 120,
    sortable: true
  }),
  // 智能提示会显示所有可用选项
]);

// 动态添加列也有智能提示
function addStatusColumn() {
  if (column.activeStatus) {
    columns.value.push(
      column.activeStatus('is_active', '状态', {
        size: 'medium' // 参数选项也有智能提示
      })
    );
  }
}
</script>
```

### Composition API 使用

```typescript
import { computed } from 'vue';
import { createPluginManager, PluginPresets, type ColumnHelper } from '@/components/common/Table/plugins';

export function useTableColumns() {
  const pluginManager = createPluginManager(PluginPresets.userManagement);
  const column: ColumnHelper = pluginManager.getColumnHelper();
  
  const userColumns = computed(() => [
    column.selection(),
    column.userAvatar?.('avatar', 'name', '用户'),
    column.emailLink?.('email', '邮箱'),
    column.phoneLink?.('phone', '电话'),
    column.activeStatus?.('is_active', '状态'),
    column.action?.('操作', {
      actions: [
        { text: '编辑', onClick: editUser },
        { text: '删除', onClick: deleteUser }
      ]
    })
  ]);
  
  return {
    columns: userColumns,
    pluginManager
  };
}
```

## 🔍 调试技巧

### 1. 查看可用方法

```typescript
const debugInfo = pluginManager.getDebugInfo();
console.log('可用的列助手方法:', debugInfo.columnHelpers);
```

### 2. 检查插件状态

```typescript
console.log('已安装插件:', pluginManager.getInstalledPlugins().map(p => p.name));
console.log('StatusColumn 已安装:', pluginManager.isPluginRegistered('StatusColumn'));
```

### 3. 验证列配置

```typescript
const columns = [/* 你的列配置 */];
console.log('列配置验证:', columns.every(col => col.field && col.title));
```

## ⚠️ 注意事项

1. **插件顺序** - 某些插件可能依赖其他插件，确保按正确顺序安装
2. **类型导入** - 确保导入 `type ColumnHelper` 以获得完整的类型支持
3. **可选链** - 插件方法可能不存在，使用可选链（`?.`）确保安全
4. **编辑器支持** - 确保你的编辑器支持 TypeScript 智能提示（VSCode、WebStorm等）

## 🎨 配置示例

查看 `examples/TypeSafeExample.vue` 文件获取完整的使用示例。

现在你可以享受完整的智能提示支持，让开发更加高效和安全！ 🚀 