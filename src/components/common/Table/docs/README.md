# Table 组件库 - 插件化架构

## 🚀 概述

Table 组件库是一个基于 VxeTable 构建的现代化插件化表格解决方案。通过插件化架构，提供模块化、可扩展、类型安全的表格开发体验。

## ✨ 核心特性

- 🧩 **插件化架构** - 按需加载，减少包体积
- 🔧 **TypeScript 支持** - 完整的类型定义和智能提示
- 📦 **预设包** - 针对不同场景的预设插件组合
- 🎨 **丰富的列类型** - 文本、数字、日期、状态、关系、复合列等
- 🔄 **响应式数据** - 完整的 Vue 3 响应式支持
- 🛠️ **调试工具** - 内置开发调试和性能监控
- 🎨 **美观的默认样式** - 内置TailwindCSS样式，支持斑马纹、紧凑布局、响应式设计
- 📱 **响应式设计** - 完美适配PC、平板、手机等设备
- 🎯 **类型安全** - 完整的TypeScript支持
- ⚡ **高性能** - 虚拟滚动，支持大数据量
- 🎪 **ShadcnUI 集成** - 使用现代化的 ShadcnUI Tooltip 组件，提供更好的用户体验和一致的设计语言

## 🚀 快速开始

### 安装和基础使用

```vue
<template>
  <VTable @register="register" />
</template>

<script setup lang="ts">
import { 
  VTable, 
  useTable,
  createPluginManager,
  PluginPresets
} from '@/components/common/Table';

// 1. 创建插件管理器
const pluginManager = createPluginManager(PluginPresets.full);

// 2. 获取列助手对象
const column = pluginManager.getColumnHelper();

// 3. 定义列配置
const columns = [
  column.selection(),
  column.index('序号'),
  column.text('name', '姓名'),
  column.emailLink('email', '邮箱'),
  column.status('status', '状态', statusMap),
  column.datetime('created_at', '创建时间'),
  column.action('操作', { actions: [...] })
];

// 4. 配置表格
const [register, tableApi] = useTable({
  fetchApi: getUserList,
  columns,
  checkType: 'checkbox',
  showPagination: true
});
</script>
```

## 📦 插件预设包

根据不同业务场景选择合适的插件包：

```typescript
// 基础功能 - 文本、数字、日期列
createPluginManager(PluginPresets.basic)

// 完整功能 - 包含所有插件
createPluginManager(PluginPresets.full)

// 内容管理系统
createPluginManager(PluginPresets.cms)

// 数据分析场景
createPluginManager(PluginPresets.analytics)

// 用户管理场景
createPluginManager(PluginPresets.userManagement)
```

## 🧩 可用插件

### 列渲染插件

#### TextColumn - 文本列
    ```typescript
column.text('name', '姓名', { 
  width: 120,
  ellipsis: true 
})
```

#### NumberColumn - 数字列  
```typescript
// 货币格式
column.currency('price', '价格', '¥', { precision: 2 })

// 百分比格式
column.percentage('rate', '费率', { precision: 1 })

// 普通数字
column.number('count', '数量', { thousandsSeparator: true })
```

#### DateColumn - 日期时间列
```typescript
// 完整日期时间
column.datetime('created_at', '创建时间')

// 仅日期
column.dateOnly('birth_date', '生日')

// 相对时间
column.relativeTime('last_login', '最后登录')
```

#### LinkColumn - 链接列
    ```typescript
// 电话链接
column.phoneLink('phone', '电话')

// 邮箱链接
column.emailLink('email', '邮箱')

// 网址链接
column.urlLink('website', '官网')

// 自定义链接
column.link('profile', '个人资料', (row) => `/user/${row.id}`)
```

#### StatusColumn - 状态列
```typescript
column.status('status', '状态', {
  'active': { text: '激活', color: 'success' },
  'inactive': { text: '禁用', color: 'warning' },
  'deleted': { text: '已删除', color: 'danger' }
})
```

#### BooleanColumn - 布尔值列
```typescript
column.boolean('is_active', '启用状态', {
  style: 'switch',
  onChange: (value, row, field) => updateStatus(row.id, value)
})
```

#### RelationColumn - 关系列
```typescript
// 多对一关系
column.manyToOne('user', '用户', {
  displayField: 'name',
  clickable: true,
  onItemClick: (user) => viewUserDetail(user)
})

// 一对多关系
column.oneToMany('orders', '订单', {
  itemName: '个订单',
  showCount: true,
  onItemClick: (orders, row) => viewOrderList(row.id)
})
```

#### UserMappingColumn - 用户映射列
```typescript
// 创建人/修改人快捷方法
column.createdBy('created_by', '创建人')
column.updatedBy('updated_by', '修改人')

// 完整配置
column.userMapping('sales_id', '销售员', {
  showId: true,  // 显示 "张三 (123)"
  clickable: true,
  onUserClick: (userId, username, row) => {
    viewUserProfile(userId)
  }
})

// 自定义模板
column.userMapping('assigned_to', '负责人', {
  template: '负责人: ${username}'
})
```

#### CompositeColumn - 复合列
```typescript
column.composite('user_info', '用户信息', {
  main: { 
    field: 'name',
    style: { fontWeight: 'bold' }
  },
  subs: [
    { field: 'email', style: { color: '#666' } },
    { field: 'department' },
    { field: 'phone', template: '📞 ${value}' }
  ],
  icon: {
    type: 'avatar',
    avatarField: 'avatar'
  },
  actions: [
    {
      icon: 'mdi:phone',
      tooltip: '拨打电话',
      onClick: (row) => callUser(row.phone)
    }
  ],
  layout: 'vertical'
})
```

#### AvatarColumn - 头像列
```typescript
column.avatar('avatar', '头像', {
  nameField: 'name',
  size: 'medium',
  showName: true
})
```

### 功能插件

#### ActionColumn - 操作列
```typescript
column.action('操作', {
  actions: [
    {
      text: '编辑',
      icon: 'mdi:pencil',
      color: 'primary',
      onClick: (row) => editUser(row)
    },
    {
      text: '删除',
      icon: 'mdi:delete',
      color: 'danger',
      onClick: (row) => deleteUser(row.id)
    }
  ],
  width: 120,
  fixed: 'right'
})
```

## 🔧 高级用法

### 动态插件加载

    ```typescript
// 运行时动态加载插件
import { TextColumnPlugin } from '@/components/common/Table/plugins/textColumn';

const pluginManager = createPluginManager();
pluginManager.use(TextColumnPlugin);

const column = pluginManager.getColumnHelper();
```

### 自定义插件

```typescript
// 创建自定义插件
const CustomColumnPlugin = {
  name: 'CustomColumn',
  install(core, options) {
    // 注册渲染器
    core.registerRenderer('CustomRenderer', CustomComponent);
    
    // 扩展列助手
    core.extendColumnHelper('custom', (field, title, options = {}) => {
                return {
                    field,
                    title,
        cellRender: {
          name: 'CustomRenderer',
          props: options
        }
      };
    });
  }
};

// 使用自定义插件
pluginManager.use(CustomColumnPlugin);
```

### 复杂业务场景

    ```typescript
// 客户管理表格示例
const customerColumns = [
  column.selection({ fixed: 'left' }),
  
  // 复合客户信息列
  column.composite('customer', '客户信息', {
    main: { field: 'name', style: { fontWeight: 'bold' } },
    subs: [
      { field: 'code', template: '编码: ${value}' },
      { field: 'type.name', template: '类型: ${value}' }
    ],
    icon: { type: 'icon', iconName: 'mdi:account' },
    width: 200,
    fixed: 'left'
  }),
  
  // 联系方式
  column.composite('contact', '联系方式', {
    main: { field: 'contact', formatter: v => v || '未设置' },
    subs: [
      { field: 'phone', template: '📞 ${value}' },
      { field: 'email', template: '📧 ${value}' }
    ],
    layout: 'vertical'
  }),
  
  // 客户等级
  column.status('grade', '等级', {
    'Intention': { text: '意向客户', color: '#f59e0b' },
    'Transaction': { text: '成交客户', color: '#10b981' },
    'Important': { text: '重要客户', color: '#8b5cf6' }
  }),
  
  // 启用状态
  column.boolean('valid', '启用', {
    style: 'switch',
    onChange: async (value, row) => {
      await updateCustomerStatus(row.id, value);
    }
  }),
  
  // 业务员（关系列）
  column.manyToOne('salesman', '业务员', {
    displayField: 'nick_name',
    clickable: true
  }),
  
  // 信用额度
  column.currency('quota', '信用额度', '¥'),
  
  // 创建时间
  column.datetime('created_at', '创建时间'),
  
  // 操作列
  column.action('操作', {
    actions: [
      { text: '编辑', onClick: editCustomer },
      { text: '查看', onClick: viewCustomer }
    ],
    fixed: 'right'
  })
];
```

## 🛠️ 调试和开发

### 开发调试工具

```typescript
import { useTableDebug } from '@/components/common/Table';

// 启用调试
const debug = useTableDebug('my-table');

// 记录性能
debug.recordRenderTime(Date.now());

// 查看调试信息
debug.printDebugInfo();
```

### 性能监控

```typescript
// 性能监控Hook
const [register, tableApi] = useTable({
  // ... 其他配置
  on: {
    'data-loaded': (params) => {
      console.log('数据加载耗时:', params.loadTime);
    }
  }
});
```

## 📚 相关文档

- [插件迁移指南](./PLUGIN_MIGRATION.md) - 从旧版本迁移到插件化架构
- [类型安全指南](./TYPE_SAFE_GUIDE.md) - TypeScript 最佳实践
- [插件使用示例](./plugins/USAGE_EXAMPLES.md) - 各插件详细用法
- [复合列指南](./COMPOSITE_COLUMNS.md) - 复合列高级用法

## 🤝 贡献

欢迎贡献新的插件或改进现有功能！请遵循插件开发规范。

## 📄 许可证

MIT License 

### 表格样式配置

Table组件提供了强大的样式配置能力，支持以下配置项：

#### 默认样式配置

```javascript
// 表格的默认样式配置
const defaultTableStyle = {
  striped: true,           // 斑马纹效果
  border: 'horizontal',    // 边框配置：'horizontal' | 'vertical' | 'all' | 'none' | true | false
  compact: true,           // 紧凑模式
  headerStyle: 'gray',     // 表头样式：'gray' | 'dark' | 'default' | 'none'
  rowHeight: 'medium',     // 行高：'small' | 'medium' | 'large' | number(px)
  rounded: 'md',           // 圆角：'sm' | 'md' | 'lg' | true | false
  shadow: 'sm',            // 阴影：'sm' | 'md' | 'lg' | true | false
  responsive: true,        // 响应式设计
  theme: 'light'           // 主题：'light' | 'dark' | 'auto'
};

const [register, tableApi] = useTable({
  columns: customerColumns,
  fetchApi: fetchCustomerData,
  // 使用自定义样式配置
  tableStyle: {
    striped: true,
    border: 'horizontal',  // 不显示竖向边框，符合用户要求
    compact: true,
    headerStyle: 'gray',
    rowHeight: 'medium',
    rounded: 'md', 
    shadow: 'sm',
    responsive: true,
    theme: 'light'
  },
  // 启用默认样式
  enableDefaultStyle: true,
  // 自定义CSS类
  customClass: 'my-custom-table'
});
```

#### 样式配置属性详解

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `striped` | `boolean` | `true` | 是否启用斑马纹效果 |
| `border` | `'horizontal' \| 'vertical' \| 'all' \| 'none' \| boolean` | `'horizontal'` | 边框显示方式。`horizontal`仅显示水平边框，`vertical`仅显示垂直边框，`all`显示全部边框，`none`不显示边框 |
| `compact` | `boolean` | `true` | 是否启用紧凑布局，减少内边距 |
| `headerStyle` | `'gray' \| 'dark' \| 'default' \| 'none'` | `'gray'` | 表头样式。`gray`为灰色背景，`dark`为深色背景，`default`为默认样式 |
| `rowHeight` | `'small' \| 'medium' \| 'large' \| number` | `'medium'` | 行高设置。可使用预设值或自定义像素值 |
| `rounded` | `'sm' \| 'md' \| 'lg' \| boolean` | `'md'` | 表格圆角大小 |
| `shadow` | `'sm' \| 'md' \| 'lg' \| boolean` | `'sm'` | 表格阴影效果 |
| `responsive` | `boolean` | `true` | 是否启用响应式设计，在不同设备上自动调整显示 |
| `theme` | `'light' \| 'dark' \| 'auto'` | `'light'` | 表格主题 |
| `customClass` | `string` | - | 自定义CSS类名 |

#### 响应式适配

表格组件内置了完善的响应式适配：

```css
/* 移动端适配 */
@media (max-width: 768px) {
  .v-table-styled.table-responsive :deep(.vxe-cell) {
    font-size: 12px;
    padding: 8px;
  }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .v-table-styled.table-responsive :deep(.vxe-cell) {
    font-size: 14px;
    padding: 12px;
  }
}
```

### 样式自定义示例

#### 1. 暗色主题表格

```javascript
const darkTableStyle = {
  striped: true,
  border: 'horizontal',
  compact: false,
  headerStyle: 'dark',
  rowHeight: 'large',
  rounded: 'lg',
  shadow: 'md',
  responsive: true,
  theme: 'dark'
};
```

#### 2. 简洁无边框表格

```javascript
const minimalTableStyle = {
  striped: false,
  border: 'none',
  compact: true,
  headerStyle: 'none',
  rowHeight: 'small',
  rounded: false,
  shadow: false,
  responsive: true,
  theme: 'light'
};
```

#### 3. 移动端优化表格

```javascript
const mobileTableStyle = {
  striped: true,
  border: 'horizontal',
  compact: true,
  headerStyle: 'gray',
  rowHeight: 'small',
  rounded: 'sm',
  shadow: false,
  responsive: true,
  theme: 'light',
  customClass: 'mobile-optimized'
};
```

### 禁用默认样式

如果需要完全自定义样式，可以禁用默认样式：

```javascript
const [register, tableApi] = useTable({
  // ... 其他配置
  enableDefaultStyle: false,  // 禁用默认样式
  customClass: 'my-custom-table-class'
});
```

## 插件系统

### 可用插件

- **StyleColumn** - 样式管理插件（默认启用）
- **CompositeColumn** - 复合列插件
- **BooleanColumn** - 布尔值列插件
- **ActionColumn** - 操作列插件
- **NumberColumn** - 数字列插件
- **DateColumn** - 日期列插件
- **StatusColumn** - 状态列插件
- **AvatarColumn** - 头像列插件
- **LinkColumn** - 链接列插件
- **TextColumn** - 文本列插件
- **RelationColumn** - 关联列插件
- **UserMappingColumn** - 用户映射列插件
- **PaginationPlugin** - 分页插件

### 插件预设

```javascript
// 基础插件包（包含样式插件）
const pluginManager = createPluginManager(PluginPresets.basic);

// 完整插件包
const pluginManager = createPluginManager(PluginPresets.full);

// 内容管理系统配置
const pluginManager = createPluginManager(PluginPresets.cms);
```

### 高级用法

使用插件扩展列功能：

```javascript
const columns = [
  // 使用复合列显示多个字段
  column.composite('user', '用户信息')
    .withMain('name', (value) => value)
    .withSub('email', (value) => `📧 ${value}`)
    .withSub('phone', (value) => `📱 ${value}`)
    .withStyle({
      compact: false,
      headerStyle: 'default'
    }),
    
  // 使用状态列
  column.status('status', '状态')
    .withConfig({
      active: { text: '活跃', color: 'green' },
      inactive: { text: '非活跃', color: 'red' }
    }),
    
  // 使用操作列
  column.action('actions', '操作')
    .withActions([
      { text: '编辑', type: 'primary', onClick: (row) => editUser(row) },
      { text: '删除', type: 'danger', onClick: (row) => deleteUser(row) }
    ])
];
```

## API 参考

### Table Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `columns` | `BaseColumn[]` | `[]` | 表格列配置 |
| `fetchApi` | `Function` | - | 数据获取API |
| `tableStyle` | `TableStyleOptions` | - | 表格样式配置 |
| `enableDefaultStyle` | `boolean` | `true` | 是否启用默认样式 |
| `customClass` | `string` | `''` | 自定义CSS类名 |
| `showSearch` | `boolean` | `false` | 是否显示搜索框 |
| `showPagination` | `boolean` | `true` | 是否显示分页 |
| `checkType` | `'radio' \| 'checkbox' \| false` | `false` | 选择模式 |

### Table Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `data-loaded` | `{success, loadTime, data?, total?, error?}` | 数据加载完成 |
| `checkbox-change` | `{records}` | 复选框状态变化 |
| `cell-click` | `{row, column}` | 单元格点击 |

## 更多示例

查看 `examples/PluginExample.vue` 获取完整的使用示例。 

## 🔄 最新优化

### v2.1.0 - ShadcnUI Tooltip 集成
- ✅ **移除 VxeTooltip 依赖** - 不再依赖 vxe-pc-ui 的 Tooltip 组件
- ✅ **ShadcnUI Tooltip 替代** - 使用项目统一的 ShadcnUI Tooltip 组件
- ✅ **更好的类型安全** - 完整的 TypeScript 类型支持
- ✅ **样式一致性** - 与项目整体 UI 设计保持一致
- ✅ **性能优化** - 减少不必要的依赖，提升加载性能
- ✅ **增强的交互体验** - 复合列支持 hover 交互显示详细信息

### 使用示例

```typescript
// 复合列中的 Tooltip 功能
column.composite('customer_info', '客户信息', {
  main: { field: 'name' },
  subs: [
    { field: 'email' },
    { field: 'phone' }
  ],
  actions: [
    {
      icon: 'mdi:phone',
      tooltip: '拨打电话', // 使用 ShadcnUI Tooltip
      onClick: (row) => callUser(row.phone)
    }
  ],
  interactive: {
    hover: {
      showAll: true,  // 鼠标悬停显示完整信息
      highlight: true
    }
  }
})
``` 