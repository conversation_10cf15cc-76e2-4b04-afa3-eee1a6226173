# 表格工具栏性能优化指南

## 🚀 性能优化策略

### 1. 虚拟化优化

#### 组件懒加载
```typescript
// 条件导入工具栏组件
const TableToolbar = defineAsyncComponent(() => 
  import('./Toolbar/index.vue')
);

// 仅在需要时渲染
<Suspense v-if="getProps.useNewToolbar">
  <TableToolbar />
  <template #fallback>
    <div>加载中...</div>
  </template>
</Suspense>
```

#### 智能渲染控制
```typescript
// 基于表格数据量动态调整工具栏功能
const shouldShowAdvancedFeatures = computed(() => {
  return props.totalItems > 1000;
});

// 分批渲染大量操作按钮
const visibleActions = computed(() => {
  if (props.config?.leftZone?.customActions?.length > 5) {
    return props.config.leftZone.customActions.slice(0, 5);
  }
  return props.config?.leftZone?.customActions || [];
});
```

### 2. 事件优化

#### 防抖搜索
```typescript
// 在 SearchInput 组件中使用防抖
import { debounce } from 'lodash-es';

const debouncedSearch = debounce((query: string) => {
  emit('search', query);
}, 300);
```

#### 事件委托
```typescript
// 统一事件处理器，减少事件监听器数量
const handleToolbarAction = (actionType: string, payload?: any) => {
  switch (actionType) {
    case 'new':
      emit('new');
      break;
    case 'search':
      emit('search', payload);
      break;
    // ... 其他操作
  }
};
```

### 3. 内存优化

#### 响应式数据优化
```typescript
// 使用 shallowRef 和 shallowReactive 减少深度监听
import { shallowRef, shallowReactive } from 'vue';

const toolbarState = shallowReactive({
  isFullscreen: false,
  selectedRows: [],
  searchQuery: ''
});

// 对于大型配置对象使用 markRaw
const toolbarConfig = markRaw({
  // ... 配置对象
});
```

#### 计算属性缓存
```typescript
// 优化计算属性，避免不必要的重计算
const expensiveComputation = computed(() => {
  // 使用记忆化缓存复杂计算结果
  return memoize(() => {
    return processLargeDataSet(props.data);
  })();
});
```

### 4. 渲染优化

#### v-memo 使用
```vue
<!-- 对于复杂的工具栏区域使用 v-memo -->
<div 
  v-memo="[config?.leftZone, selectedRows.length]"
  class="toolbar-left"
>
  <!-- 左侧工具栏内容 -->
</div>
```

#### 条件渲染优化
```vue
<!-- 使用 v-show 而不是 v-if 对于频繁切换的元素 -->
<div v-show="shouldShowBatchActions" class="batch-actions">
  <!-- 批量操作按钮 -->
</div>

<!-- 对于很少改变的元素使用 v-if -->
<ToolbarTitle v-if="config?.title" :config="titleConfig" />
```

### 5. 数据优化

#### 分页数据处理
```typescript
// 仅处理当前页面的数据，避免全量数据操作
const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return fullDataSet.value.slice(start, end);
});

// 智能预加载下一页数据
const preloadNextPage = () => {
  if (currentPage.value < totalPages.value) {
    // 预加载逻辑
  }
};
```

#### 选择状态优化
```typescript
// 使用 Set 优化选择状态检查
const selectedRowIds = new Set<string>();

const isRowSelected = (rowId: string) => {
  return selectedRowIds.has(rowId);
};

// 批量操作优化
const performBatchOperation = async (operation: string) => {
  const batchSize = 100;
  const selectedIds = Array.from(selectedRowIds);
  
  for (let i = 0; i < selectedIds.length; i += batchSize) {
    const batch = selectedIds.slice(i, i + batchSize);
    await processBatch(operation, batch);
    
    // 让出执行权，避免阻塞 UI
    await nextTick();
  }
};
```

### 6. 网络优化

#### 请求合并
```typescript
// 合并多个搜索请求
const batchSearchRequests = () => {
  const pendingRequests = new Map();
  
  return (query: string) => {
    if (pendingRequests.has(query)) {
      return pendingRequests.get(query);
    }
    
    const request = fetch(`/api/search?q=${query}`)
      .finally(() => {
        pendingRequests.delete(query);
      });
    
    pendingRequests.set(query, request);
    return request;
  };
};
```

#### 缓存策略
```typescript
// 实现智能缓存
const cache = new Map();
const CACHE_EXPIRY = 5 * 60 * 1000; // 5分钟

const getCachedData = (key: string) => {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_EXPIRY) {
    return cached.data;
  }
  return null;
};
```

## 📊 性能监控

### 1. 关键指标监控
```typescript
// 组件渲染性能监控
const renderingMetrics = {
  renderTime: 0,
  reRenderCount: 0,
  lastRenderTime: Date.now()
};

const trackRenderPerformance = () => {
  const start = performance.now();
  
  nextTick(() => {
    const end = performance.now();
    renderingMetrics.renderTime = end - start;
    renderingMetrics.reRenderCount++;
    
    if (renderingMetrics.renderTime > 16) {
      console.warn(`Toolbar render took ${renderingMetrics.renderTime}ms`);
    }
  });
};
```

### 2. 内存泄漏检测
```typescript
// 监控内存使用
const memoryTracker = {
  initialMemory: 0,
  checkInterval: null as NodeJS.Timeout | null,
  
  start() {
    this.initialMemory = performance.memory?.usedJSHeapSize || 0;
    this.checkInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, 30000); // 每30秒检查一次
  },
  
  checkMemoryUsage() {
    const currentMemory = performance.memory?.usedJSHeapSize || 0;
    const growth = currentMemory - this.initialMemory;
    
    if (growth > 10 * 1024 * 1024) { // 10MB 增长
      console.warn('Potential memory leak detected');
    }
  },
  
  stop() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }
  }
};
```

## 🛠️ 开发工具

### 1. 性能分析工具
```typescript
// Vue DevTools 性能标记
const markStart = (name: string) => {
  if (import.meta.env.DEV) {
    performance.mark(`${name}-start`);
  }
};

const markEnd = (name: string) => {
  if (import.meta.env.DEV) {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
  }
};
```

### 2. 调试辅助
```typescript
// 性能调试开关
const PerformanceDebugger = {
  enabled: import.meta.env.DEV,
  
  log(message: string, data?: any) {
    if (this.enabled) {
      console.log(`[Toolbar Performance] ${message}`, data);
    }
  },
  
  time(label: string) {
    if (this.enabled) {
      console.time(`[Toolbar] ${label}`);
    }
  },
  
  timeEnd(label: string) {
    if (this.enabled) {
      console.timeEnd(`[Toolbar] ${label}`);
    }
  }
};
```

## 📈 最佳实践

### 1. 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **最小依赖**: 减少不必要的依赖注入
- **懒加载**: 非关键组件延迟加载
- **缓存友好**: 设计利于缓存的数据结构

### 2. 状态管理优化
- **局部状态**: 优先使用组件内部状态
- **状态分片**: 将大状态拆分为小块
- **计算缓存**: 合理使用 computed 和 memo
- **事件节流**: 控制高频事件的触发频率

### 3. 渲染优化技巧
- **虚拟列表**: 大量数据使用虚拟滚动
- **分时渲染**: 大量 DOM 操作分批执行
- **关键路径**: 优先渲染用户可见内容
- **预加载策略**: 智能预测用户需求

### 4. 内存管理
- **及时清理**: 组件卸载时清理监听器
- **弱引用**: 使用 WeakMap/WeakSet 避免内存泄漏
- **对象池**: 复用对象减少 GC 压力
- **监控告警**: 设置内存使用告警阈值

## 🔧 故障排查

### 常见性能问题及解决方案

1. **工具栏渲染缓慢**
   - 检查是否有不必要的深度监听
   - 优化计算属性的依赖项
   - 使用 v-memo 缓存复杂渲染

2. **内存持续增长**
   - 检查事件监听器是否正确清理
   - 验证定时器是否及时取消
   - 确认没有循环引用

3. **搜索响应慢**
   - 实现搜索防抖
   - 使用 Web Workers 处理复杂搜索
   - 实现搜索结果缓存

4. **批量操作卡顿**
   - 分批处理大量数据
   - 使用 requestIdleCallback 优化执行时机
   - 显示进度反馈给用户

通过以上优化策略，新工具栏系统能够在各种场景下保持优秀的性能表现。
