// Toolbar/ToolbarLeft.vue
<template>
  <div :class="cn('flex items-center gap-2', className)">
    <!-- 标题区域 -->
    <ToolbarTitle
      v-if="titleConfig"
      :icon="typeof titleConfig.icon === 'string' ? titleConfig.icon : titleConfig.icon?.name"
      :main="titleConfig.main"
      :sub="titleConfig.sub"
      :badge="titleConfig.badge"
      :class="titleConfig.className"
    />
    
    <!-- 操作按钮区域 -->
    <div class="flex items-center gap-1 ml-4">
      <!-- 新增按钮 -->
      <ActionButton
        v-if="showNewButton"
        :text="newButtonText"
        :icon="typeof newButtonIcon === 'string' ? newButtonIcon : newButtonIcon?.name"
        :variant="newButtonVariant"
        tooltip="新增记录"
        @click="handleNew"
      />
      
      <!-- 批量删除按钮 -->
      <ActionButton
        v-if="shouldShowBatchDelete"
        :text="batchDeleteText"
        :icon="typeof batchDeleteIcon === 'string' ? batchDeleteIcon : batchDeleteIcon?.name"
        :variant="batchDeleteVariant"
        tooltip="删除选中项"
        @click="handleBatchDelete"
      />
      
      <!-- 自定义操作按钮 -->
      <template v-if="leftActions?.length">
        <!-- 直接展示的按钮 -->
        <ActionButton
          v-for="action in directActions"
          :key="action.id"
          :text="action.text"
          :icon="typeof action.icon === 'string' ? action.icon : action.icon?.name"
          :tooltip="action.tooltip"
          :variant="action.variant"
          :size="action.size"
          :disabled="getActionDisabled(action)"
          :class="action.className"
          @click="() => handleActionClick(action)"
        />
        
        <!-- 更多操作下拉菜单 -->
        <DropdownMenu v-if="dropdownActions.length">
          <DropdownMenuTrigger as-child>
            <ActionButton
              text="更多操作"
              icon="lucide:more-horizontal"
              variant="outline"
            />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem
              v-for="action in dropdownActions"
              :key="action.id"
              :disabled="getActionDisabled(action)"
              @click="() => handleActionClick(action)"
            >
              <Icon
                v-if="action.icon"
                :icon="typeof action.icon === 'string' ? action.icon : action.icon?.name || ''"
                class="mr-2 h-4 w-4"
              />
              {{ action.text }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Icon } from '@iconify/vue';
import { cn } from '@/lib/utils';
import ToolbarTitle from './components/ToolbarTitle.vue';
import ActionButton from './components/ActionButton.vue';
import type { 
  ToolbarTitleConfig, 
  ActionConfig, 
  IconType, 
  ButtonVariant 
} from './types';

interface Props {
  titleConfig?: ToolbarTitleConfig | null;
  tableApi: any;
  selectedRows: any[];
  tableProps: Record<string, any>;
  className?: string;
  
  // 新增按钮相关
  showNewButton?: boolean;
  newButtonText?: string;
  newButtonIcon?: IconType;
  newButtonVariant?: ButtonVariant;
  onNewClick?: (tableApi: any) => void;
  
  // 批量删除相关
  showBatchDelete?: boolean;
  batchDeleteText?: string;
  batchDeleteIcon?: IconType;
  batchDeleteVariant?: ButtonVariant;
  onBatchDeleteClick?: (tableApi: any, selectedRows: any[]) => void;
  
  // 左侧自定义动作
  leftActions?: ActionConfig[];
}

const props = withDefaults(defineProps<Props>(), {
  showNewButton: true,
  newButtonText: '新增',
  newButtonIcon: 'lucide:plus',
  newButtonVariant: 'default',
  showBatchDelete: true,
  batchDeleteText: '批量删除',
  batchDeleteIcon: 'lucide:trash-2',
  batchDeleteVariant: 'destructive'
});

const emits = defineEmits<{
  new: [];
  batchDelete: [];
}>();

// 计算是否显示批量删除按钮
const shouldShowBatchDelete = computed(() => {
  return (
    props.showBatchDelete &&
    props.tableProps.checkType === 'checkbox' &&
    props.selectedRows.length > 0
  );
});

// 分离直接显示的按钮和下拉菜单中的按钮
const directActions = computed(() => {
  return props.leftActions?.filter(action => {
    if ('children' in action) return false;
    return !(action as any).isDropdownItem;
  }) || [];
});

const dropdownActions = computed(() => {
  const dropdown: any[] = [];
  props.leftActions?.forEach(action => {
    if ('children' in action) {
      dropdown.push(...action.children);
    } else if ((action as any).isDropdownItem) {
      dropdown.push(action);
    }
  });
  return dropdown;
});

// 计算按钮禁用状态
const getActionDisabled = (action: any) => {
  if (typeof action.disabled === 'function') {
    return action.disabled(props.tableApi, props.selectedRows);
  }
  return action.disabled || false;
};

const handleNew = () => {
  if (props.onNewClick) {
    props.onNewClick(props.tableApi);
  } else {
    emits('new');
  }
};

const handleBatchDelete = () => {
  if (props.onBatchDeleteClick) {
    props.onBatchDeleteClick(props.tableApi, props.selectedRows);
  } else {
    emits('batchDelete');
  }
};

const handleActionClick = (action: any) => {
  if (typeof action.onClick === 'function') {
    action.onClick(props.tableApi, props.selectedRows);
  }
};
</script>
