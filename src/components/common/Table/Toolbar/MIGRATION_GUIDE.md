# 表格工具栏迁移完整指南

## 📋 迁移概览

本指南将帮助您从旧版表格工具栏平滑迁移到新版现代化工具栏系统。

### 迁移收益
- ✅ **现代化 UI**: 基于 Tailwind CSS 4 和 Shadcn/UI
- ✅ **更好的性能**: 优化的渲染和事件处理
- ✅ **类型安全**: 完整的 TypeScript 支持
- ✅ **高度可配置**: 灵活的配置选项
- ✅ **无缝集成**: 向后兼容现有代码

## 🚀 快速迁移

### 步骤 1: 启用新工具栏

#### 最简单的迁移
```vue
<!-- 之前 -->
<VTable
  :columns="columns"
  :data="data"
  :button-list="buttonList"
  show-search
  show-pagination
/>

<!-- 之后 -->
<VTable
  :columns="columns"
  :data="data"
  :use-new-toolbar="true"
  :new-toolbar-config="migratedConfig"
/>

<script setup lang="ts">
import { generateToolbarConfig } from '@/components/common/Table/Toolbar/utils/legacyMigration';

// 自动生成新配置
const migratedConfig = generateToolbarConfig({
  buttonList: buttonList,
  showSearch: true,
  showPagination: true
});
</script>
```

### 步骤 2: 渐进式增强

#### 添加现代化功能
```typescript
const enhancedConfig: ToolbarConfig = {
  // 从旧配置自动生成的基础配置
  ...migratedConfig,
  
  // 添加新功能
  title: {
    icon: 'lucide:users',
    main: '用户管理',
    sub: '系统用户信息管理',
    badge: {
      text: 'Active',
      variant: 'default'
    }
  },
  
  leftZone: {
    ...migratedConfig.leftZone,
    // 增强左侧区域
    newButtonIcon: 'lucide:user-plus',
    customActions: [
      {
        id: 'import',
        text: '导入用户',
        icon: 'lucide:upload',
        tooltip: '从 Excel 导入用户数据'
      }
    ]
  }
};
```

## 📊 详细迁移映射

### 1. 按钮配置迁移

#### 旧版配置
```typescript
const legacyConfig = {
  buttonList: [
    {
      name: '新增',
      icon: 'plus',
      on: {
        click: () => handleAdd()
      }
    },
    {
      name: '导出',
      icon: 'download',
      permission: 'user:export',
      on: {
        click: () => handleExport()
      }
    }
  ]
};
```

#### 新版配置
```typescript
const newConfig: ToolbarConfig = {
  leftZone: {
    showNewButton: true,
    newButtonText: '新增',
    newButtonIcon: 'lucide:plus',
    onNew: () => handleAdd(),
    
    customActions: [
      {
        id: 'export',
        text: '导出',
        icon: 'lucide:download',
        permission: 'user:export',
        onClick: () => handleExport()
      }
    ]
  }
};
```

### 2. 搜索配置迁移

#### 旧版
```typescript
{
  showSearch: true,
  searchConfig: {
    placeholder: '请输入搜索关键词'
  }
}
```

#### 新版
```typescript
{
  centerZone: {
    showSearch: true,
    searchPlaceholder: '请输入搜索关键词',
    onSearch: (query: string) => handleSearch(query)
  }
}
```

### 3. 工具栏配置迁移

#### 旧版
```typescript
{
  toolbarConfig: {
    isReload: true,
    isFull: true,
    isZoom: true
  }
}
```

#### 新版
```typescript
{
  rightZone: {
    showRefreshButton: true,
    showFullscreenButton: true,
    showColumnSettingsButton: true
  }
}
```

## 🔄 自动化迁移工具

### 使用迁移助手

```typescript
import { 
  generateToolbarConfig, 
  createSimpleToolbarConfig,
  TOOLBAR_PRESETS 
} from '@/components/common/Table/Toolbar/utils/legacyMigration';

// 方式 1: 自动转换现有配置
const autoMigrated = generateToolbarConfig(legacyTableProps);

// 方式 2: 使用简单配置
const simpleConfig = createSimpleToolbarConfig('用户管理', {
  showExport: true,
  showImport: true
});

// 方式 3: 使用预设配置
const presetConfig = TOOLBAR_PRESETS.CRUD_MANAGEMENT;
```

### 批量迁移脚本

```typescript
// scripts/migrate-toolbars.ts
import { readFileSync, writeFileSync } from 'fs';
import { glob } from 'glob';

const migrateVTableComponents = async () => {
  const vueFiles = await glob('src/**/*.vue');
  
  for (const file of vueFiles) {
    const content = readFileSync(file, 'utf8');
    
    // 检查是否包含 VTable
    if (!content.includes('<VTable') && !content.includes('<v-table')) {
      continue;
    }
    
    let updatedContent = content;
    
    // 1. 添加导入
    if (!content.includes('generateToolbarConfig')) {
      updatedContent = updatedContent.replace(
        /<script setup lang="ts">/,
        `<script setup lang="ts">
import { generateToolbarConfig } from '@/components/common/Table/Toolbar/utils/legacyMigration';`
      );
    }
    
    // 2. 生成迁移配置
    updatedContent = updatedContent.replace(
      /const \[register\] = useTable\(([\s\S]*?)\);/,
      `const tableConfig = $1;
const migratedToolbarConfig = generateToolbarConfig(tableConfig);
const [register] = useTable({
  ...tableConfig,
  useNewToolbar: true,
  newToolbarConfig: migratedToolbarConfig
});`
    );
    
    // 3. 更新模板
    updatedContent = updatedContent.replace(
      /<VTable([^>]*?)@register="register"/,
      '<VTable$1:use-new-toolbar="true" :new-toolbar-config="migratedToolbarConfig" @register="register"'
    );
    
    writeFileSync(file, updatedContent);
    console.log(`Migrated: ${file}`);
  }
};

migrateVTableComponents();
```

## 📋 迁移检查清单

### 准备阶段
- [ ] 备份现有代码
- [ ] 确认依赖版本兼容
- [ ] 运行现有测试确保基准功能正常

### 迁移阶段
- [ ] 安装新工具栏依赖
- [ ] 使用自动迁移工具转换配置
- [ ] 逐个页面测试功能
- [ ] 调整样式和布局

### 验证阶段
- [ ] 功能完整性测试
- [ ] 性能对比测试
- [ ] 用户体验测试
- [ ] 兼容性测试

### 优化阶段
- [ ] 利用新功能增强用户体验
- [ ] 清理旧代码和依赖
- [ ] 更新文档和培训材料

## 🐛 常见迁移问题

### 1. 事件处理不一致

**问题**: 旧版事件回调无法正常触发

**解决方案**:
```typescript
// 确保事件正确映射
const handleTableEvents = {
  'toolbar:new': () => handleAdd(),
  'toolbar:batchDelete': (rows) => handleBatchDelete(rows),
  'toolbar:search': (query) => handleSearch(query)
};

// 在模板中绑定
<VTable
  @toolbar:new="handleTableEvents['toolbar:new']"
  @toolbar:batch-delete="handleTableEvents['toolbar:batchDelete']"
  @toolbar:search="handleTableEvents['toolbar:search']"
/>
```

### 2. 样式冲突

**问题**: 新旧样式产生冲突

**解决方案**:
```css
/* 添加命名空间隔离 */
.new-toolbar {
  /* 新工具栏样式 */
}

.legacy-toolbar {
  /* 保留旧工具栏样式 */
}
```

### 3. 类型错误

**问题**: TypeScript 类型不匹配

**解决方案**:
```typescript
// 使用类型断言或更新类型定义
const config = migratedConfig as ToolbarConfig;

// 或者更新接口定义
interface ExtendedTableProps extends BaseTableProps {
  useNewToolbar?: boolean;
  newToolbarConfig?: ToolbarConfig;
}
```

### 4. 性能问题

**问题**: 迁移后性能下降

**解决方案**:
```typescript
// 使用 shallowRef 优化配置对象
const toolbarConfig = shallowRef(generateToolbarConfig(props));

// 使用 v-memo 缓存渲染
<div v-memo="[config.title, selectedRows.length]">
  <!-- 工具栏内容 -->
</div>
```

## 📈 迁移最佳实践

### 1. 分阶段迁移
- **第一阶段**: 核心功能页面
- **第二阶段**: 次要功能页面
- **第三阶段**: 管理后台页面

### 2. 功能对等原则
- 确保所有现有功能在新版本中都有对应实现
- 保持用户操作习惯的一致性
- 渐进式引入新功能

### 3. 测试驱动迁移
- 为每个迁移的组件编写测试
- 进行对比测试确保功能一致
- 性能测试确保无退化

### 4. 用户反馈循环
- 内部测试团队先行体验
- 收集用户反馈并及时调整
- 建立问题反馈渠道

## 🎯 迁移时间表

### 建议迁移周期: 2-4 周

#### 第 1 周: 准备和工具
- 环境准备和依赖更新
- 自动迁移工具开发和测试
- 核心页面迁移

#### 第 2 周: 批量迁移
- 使用工具批量迁移剩余页面
- 功能测试和问题修复
- 样式调整和优化

#### 第 3 周: 测试和优化
- 全面功能测试
- 性能测试和优化
- 用户体验改进

#### 第 4 周: 上线和监控
- 灰度发布
- 线上监控和问题修复
- 用户反馈收集和处理

## 📞 支持和帮助

### 技术支持
- 📧 技术邮箱: [email protected]
- 💬 内部群组: #table-toolbar-migration
- 📖 文档中心: `/docs/table-toolbar`

### 培训资源
- 🎥 视频教程: 新工具栏使用指南
- 📚 最佳实践文档
- 🛠️ 迁移工具使用说明

通过遵循本指南，您可以顺利完成从旧版工具栏到新版工具栏的迁移，享受更好的开发体验和用户体验。
