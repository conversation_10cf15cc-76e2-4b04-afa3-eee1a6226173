// Toolbar/utils/configMapper.ts
import type { ToolbarConfig, ActionButtonConfig, ActionConfig, NewButtonConfig, SearchConfig } from '../types';

/**
 * 将现有的 VTable 配置映射为新工具栏配置
 * @param legacyConfig 现有的表格配置
 * @returns 新的工具栏配置
 */
export function mapLegacyConfig(legacyConfig: {
  buttonList?: Array<{
    key?: string;
    name: string;
    icon?: string;
    onClick?: Function;
    disabled?: boolean;
    type?: string;
    list?: any[];
  }>;
  showSearch?: boolean;
  showPagination?: boolean;
  toolbarConfig?: {
    isReload?: boolean;
    isFull?: boolean;
    isZoom?: boolean;
  };
  title?: string;
}): ToolbarConfig {
  
  // 处理按钮列表，转换为新的左侧动作按钮
  const leftActions: ActionConfig[] = legacyConfig.buttonList?.map((btn, index) => {
    // 处理按钮组
    if (btn.type === 'group' && btn.list) {
      return {
        id: btn.key || `group-${index}`,
        text: btn.name,
        icon: btn.icon,
        disabled: btn.disabled,
        children: btn.list.map((subBtn, subIndex) => ({
          id: subBtn.key || `group-${index}-${subIndex}`,
          text: subBtn.name,
          icon: subBtn.icon,
          disabled: subBtn.disabled,
          onClick: (tableApi: any, selectedRows: any[]) => {
            if (subBtn.onClick) {
              subBtn.onClick();
            }
          }
        }))
      };
    }
    
    // 处理普通按钮
    return {
      id: btn.key || `btn-${index}`,
      text: btn.name,
      icon: btn.icon,
      disabled: btn.disabled,
      onClick: (tableApi: any, selectedRows: any[]) => {
        if (btn.onClick) {
          btn.onClick();
        }
      }
    };
  }) || [];

  return {
    title: legacyConfig.title,
    // 左侧功能
    newButton: true,
    batchDelete: true,
    leftActions,
    // 搜索功能
    search: legacyConfig.showSearch !== false,
    // 右侧功能
    refresh: legacyConfig.toolbarConfig?.isReload !== false,
    fullscreen: legacyConfig.toolbarConfig?.isFull !== false,
    columnSettings: legacyConfig.toolbarConfig?.isZoom !== false,
    pagination: legacyConfig.showPagination !== false
  };
}

/**
 * 创建默认的工具栏配置
 * @param title 标题
 * @returns 默认配置
 */
export function createDefaultConfig(title?: string): ToolbarConfig {
  return {
    title,
    newButton: true,
    batchDelete: true,
    search: true,
    refresh: true,
    fullscreen: true,
    columnSettings: true,
    pagination: true
  };
}

/**
 * 合并工具栏配置
 * @param baseConfig 基础配置
 * @param customConfig 自定义配置
 * @returns 合并后的配置
 */
export function mergeToolbarConfig(
  baseConfig: ToolbarConfig,
  customConfig: Partial<ToolbarConfig>
): ToolbarConfig {
  return {
    ...baseConfig,
    ...customConfig,
    // 合并左侧动作按钮
    leftActions: [
      ...(baseConfig.leftActions || []),
      ...(customConfig.leftActions || [])
    ],
    // 合并右侧动作按钮
    rightActions: [
      ...(baseConfig.rightActions || []),
      ...(customConfig.rightActions || [])
    ],
    // 处理搜索配置的合并
    search: mergeSearchConfig(baseConfig.search, customConfig.search),
    // 处理新增按钮配置的合并
    newButton: mergeButtonConfig(baseConfig.newButton, customConfig.newButton),
    // 处理批量删除配置的合并
    batchDelete: mergeButtonConfig(baseConfig.batchDelete, customConfig.batchDelete)
  };
}

/**
 * 合并搜索配置
 */
function mergeSearchConfig(
  base?: boolean | SearchConfig,
  custom?: boolean | SearchConfig
): boolean | SearchConfig | undefined {
  if (custom === undefined) return base;
  if (typeof custom === 'boolean') return custom;
  if (typeof base === 'boolean') return custom;
  if (!base) return custom;
  
  return { ...base, ...custom };
}

/**
 * 合并按钮配置
 */
function mergeButtonConfig<T extends object>(
  base?: boolean | T,
  custom?: boolean | T
): boolean | T | undefined {
  if (custom === undefined) return base;
  if (typeof custom === 'boolean') return custom;
  if (typeof base === 'boolean') return custom;
  if (!base) return custom;
  
  return { ...base, ...custom };
}

/**
 * 配置构建器类 - 提供流式 API 构建工具栏配置
 */
export class ToolbarConfigBuilder {
  private config: ToolbarConfig = {};

  constructor(initialConfig?: Partial<ToolbarConfig>) {
    if (initialConfig) {
      this.config = { ...initialConfig };
    }
  }

  /**
   * 设置标题
   */
  title(title: string | ToolbarConfig['title']): this {
    this.config.title = title;
    return this;
  }

  /**
   * 设置新增按钮
   */
  newButton(config: boolean | NewButtonConfig = true): this {
    this.config.newButton = config;
    return this;
  }

  /**
   * 设置批量删除按钮
   */
  batchDelete(config: boolean | ToolbarConfig['batchDelete'] = true): this {
    this.config.batchDelete = config;
    return this;
  }

  /**
   * 添加左侧动作按钮
   */
  addLeftAction(action: ActionButtonConfig): this {
    if (!this.config.leftActions) {
      this.config.leftActions = [];
    }
    this.config.leftActions.push(action);
    return this;
  }

  /**
   * 设置搜索功能
   */
  search(config: boolean | SearchConfig = true): this {
    this.config.search = config;
    return this;
  }

  /**
   * 设置刷新按钮
   */
  refresh(config: boolean | ToolbarConfig['refresh'] = true): this {
    this.config.refresh = config;
    return this;
  }

  /**
   * 设置全屏按钮
   */
  fullscreen(config: boolean | ToolbarConfig['fullscreen'] = true): this {
    this.config.fullscreen = config;
    return this;
  }

  /**
   * 设置列设置按钮
   */
  columnSettings(config: boolean | ToolbarConfig['columnSettings'] = true): this {
    this.config.columnSettings = config;
    return this;
  }

  /**
   * 设置分页
   */
  pagination(config: boolean | ToolbarConfig['pagination'] = true): this {
    this.config.pagination = config;
    return this;
  }

  /**
   * 添加右侧动作按钮
   */
  addRightAction(action: ActionButtonConfig): this {
    if (!this.config.rightActions) {
      this.config.rightActions = [];
    }
    this.config.rightActions.push(action);
    return this;
  }

  /**
   * 设置主题
   */
  theme(theme: ToolbarConfig['theme']): this {
    this.config.theme = theme;
    return this;
  }

  /**
   * 设置尺寸
   */
  size(size: ToolbarConfig['size']): this {
    this.config.size = size;
    return this;
  }

  /**
   * 设置紧凑模式
   */
  compact(compact = true): this {
    this.config.compact = compact;
    return this;
  }

  /**
   * 设置响应式配置
   */
  responsive(responsive: ToolbarConfig['responsive']): this {
    this.config.responsive = responsive;
    return this;
  }

  /**
   * 构建最终配置
   */
  build(): ToolbarConfig {
    return { ...this.config };
  }
}

/**
 * 预设配置模板
 */
export const presetConfigs = {
  // 数据管理页面
  dataManagement: (title: string): ToolbarConfig => ({
    title: {
      icon: 'lucide:database',
      main: title
    },
    newButton: {
      text: '新增数据',
      icon: 'lucide:plus'
    },
    leftActions: [
      {
        id: 'import',
        text: '导入',
        icon: 'lucide:upload',
        tooltip: '从 Excel 导入数据',
        onClick: (tableApi, selectedRows) => {
          console.log('Import data');
        }
      },
      {
        id: 'export',
        text: '导出',
        icon: 'lucide:download',
        tooltip: '导出数据到 Excel',
        onClick: (tableApi, selectedRows) => {
          console.log('Export data', selectedRows);
        }
      }
    ],
    search: {
      placeholder: '搜索数据...',
      advanced: {
        show: true,
        title: '高级搜索'
      }
    }
  }),

  // 审核流程页面
  approval: (title: string): ToolbarConfig => ({
    title: {
      icon: 'lucide:check-circle',
      main: title
    },
    newButton: false, // 审核页面通常不需要新增按钮
    leftActions: [
      {
        id: 'batch-approve',
        text: '批量通过',
        icon: 'lucide:check',
        variant: 'default',
        showOnlyWhenSelected: true,
        minSelectedRows: 1,
        confirm: {
          title: '确认批量通过',
          description: '确定要通过所选的审核项目吗？',
          confirmText: '确认通过',
          cancelText: '取消'
        },
        onClick: (tableApi, selectedRows) => {
          console.log('Batch approve', selectedRows);
        }
      },
      {
        id: 'batch-reject',
        text: '批量拒绝',
        icon: 'lucide:x',
        variant: 'destructive',
        showOnlyWhenSelected: true,
        minSelectedRows: 1,
        confirm: {
          title: '确认批量拒绝',
          description: '确定要拒绝所选的审核项目吗？',
          confirmText: '确认拒绝',
          cancelText: '取消'
        },
        onClick: (tableApi, selectedRows) => {
          console.log('Batch reject', selectedRows);
        }
      }
    ],
    batchDelete: false, // 审核页面通常不需要删除功能
    search: {
      placeholder: '搜索审核项目...'
    }
  }),

  // 简单列表页面
  simpleList: (title: string): ToolbarConfig => ({
    title,
    newButton: true,
    batchDelete: true,
    search: true,
    refresh: true,
    columnSettings: false,
    fullscreen: false,
    pagination: {
      type: 'simple'
    },
    compact: true
  }),

  // 只读展示页面
  readonly: (title: string): ToolbarConfig => ({
    title,
    newButton: false,
    batchDelete: false,
    search: true,
    refresh: true,
    columnSettings: true,
    fullscreen: true,
    pagination: true,
    rightActions: [
      {
        id: 'export',
        text: '导出',
        icon: 'lucide:download',
        onClick: (tableApi, selectedRows) => {
          console.log('Export readonly data');
        }
      }
    ]
  }),

  // 移动端优化
  mobile: (title: string): ToolbarConfig => ({
    title,
    newButton: {
      text: '',
      icon: 'lucide:plus',
      size: 'sm'
    },
    batchDelete: false,
    search: {
      placeholder: '搜索...'
    },
    refresh: {
      icon: 'lucide:refresh-cw'
    },
    columnSettings: false,
    fullscreen: false,
    pagination: {
      type: 'simple'
    },
    size: 'sm',
    compact: true,
    responsive: {
      hideOnMobile: ['columnSettings', 'fullscreen'],
      tabletBehavior: 'wrap'
    }
  })
};

/**
 * 获取预设配置
 * @param preset 预设名称
 * @param title 标题
 * @returns 预设配置
 */
export function getPresetConfig(
  preset: keyof typeof presetConfigs,
  title: string
): ToolbarConfig {
  return presetConfigs[preset](title);
}

/**
 * 创建配置构建器
 * @param preset 可选的预设配置
 * @param title 标题
 * @returns 配置构建器实例
 */
export function createBuilder(preset?: keyof typeof presetConfigs, title?: string): ToolbarConfigBuilder {
  if (preset && title) {
    return new ToolbarConfigBuilder(getPresetConfig(preset, title));
  }
  return new ToolbarConfigBuilder();
}

// ========== 配置验证和类型守卫 ==========

/**
 * 验证工具栏配置的有效性
 * @param config 工具栏配置
 * @returns 验证结果
 */
export function validateToolbarConfig(config: ToolbarConfig): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查左侧动作按钮
  if (config.leftActions) {
    config.leftActions.forEach((action, index) => {
      if (!action.id) {
        errors.push(`左侧动作按钮[${index}]缺少必需的 id 字段`);
      }
      if (!action.text && !action.icon) {
        warnings.push(`左侧动作按钮[${index}]建议设置 text 或 icon`);
      }
    });
  }

  // 检查右侧动作按钮
  if (config.rightActions) {
    config.rightActions.forEach((action, index) => {
      if (!action.id) {
        errors.push(`右侧动作按钮[${index}]缺少必需的 id 字段`);
      }
    });
  }

  // 检查响应式配置
  if (config.responsive?.hideOnMobile) {
    const validOptions = ['search', 'pagination', 'columnSettings', 'fullscreen'];
    const invalidOptions = config.responsive.hideOnMobile.filter(
      option => !validOptions.includes(option)
    );
    if (invalidOptions.length > 0) {
      errors.push(`响应式配置中的无效选项: ${invalidOptions.join(', ')}`);
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 工具栏配置的类型守卫函数
 */
export const isToolbarConfig = (config: any): config is ToolbarConfig => {
  return typeof config === 'object' && config !== null;
};

/**
 * 规范化工具栏配置 - 确保所有配置项都有合理的默认值
 * @param config 原始配置
 * @returns 规范化后的配置
 */
export function normalizeToolbarConfig(config: Partial<ToolbarConfig> = {}): ToolbarConfig {
  return {
    // 默认启用基础功能
    newButton: config.newButton ?? true,
    batchDelete: config.batchDelete ?? true,
    search: config.search ?? true,
    refresh: config.refresh ?? true,
    fullscreen: config.fullscreen ?? true,
    columnSettings: config.columnSettings ?? true,
    pagination: config.pagination ?? true,
    
    // 保留用户自定义配置
    title: config.title,
    leftActions: config.leftActions || [],
    rightActions: config.rightActions || [],
    className: config.className,
    size: config.size ?? 'md',
    compact: config.compact ?? false,
    responsive: config.responsive,
    theme: config.theme
  };
}

/**
 * 迁移指南：从旧配置结构到新配置结构
 * 
 * ## 主要变化
 * 1. **去掉嵌套结构**：不再使用 leftZone、centerZone、rightZone
 * 2. **扁平化配置**：所有功能配置直接在根级别
 * 3. **统一命名**：功能命名更简洁明确
 * 4. **更强类型支持**：提供更丰富的 TypeScript 类型定义
 * 
 * ## 配置映射对照表
 * 
 * ### 左侧区域 (leftZone → 根级配置)
 * ```typescript
 * // 旧配置
 * leftZone: {
 *   showNewButton: true,
 *   newButtonText: '新增',
 *   newButtonIcon: 'lucide:plus',
 *   onNew: (api) => {},
 *   
 *   showBatchDeleteButton: true,
 *   batchDeleteButtonText: '批量删除',
 *   onBatchDelete: (api, rows) => {},
 *   
 *   customActions: [...]
 * }
 * 
 * // 新配置
 * newButton: {
 *   text: '新增',
 *   icon: 'lucide:plus',
 *   onClick: (api) => {}
 * },
 * batchDelete: {
 *   text: '批量删除',
 *   onClick: (api, rows) => {}
 * },
 * leftActions: [...]
 * ```
 * 
 * ### 中央区域 (centerZone → search)
 * ```typescript
 * // 旧配置
 * centerZone: {
 *   showSearch: true,
 *   searchPlaceholder: '搜索...',
 *   onSearch: (query, api) => {}
 * }
 * 
 * // 新配置
 * search: {
 *   placeholder: '搜索...',
 *   onSearch: (query, api) => {},
 *   debounce: 300,
 *   advanced: { show: true }
 * }
 * ```
 * 
 * ### 右侧区域 (rightZone → 多个根级配置)
 * ```typescript
 * // 旧配置
 * rightZone: {
 *   showPagination: true,
 *   showRefreshButton: true,
 *   refreshButtonIcon: 'lucide:refresh-cw',
 *   onRefresh: (api) => {},
 *   
 *   showFullscreenButton: true,
 *   fullscreenButtonIcon: 'lucide:maximize',
 *   onFullscreenToggle: (isFs, api) => {},
 *   
 *   showColumnSettingsButton: true,
 *   columnSettingsButtonIcon: 'lucide:settings',
 *   
 *   customActions: [...]
 * }
 * 
 * // 新配置
 * pagination: true,
 * refresh: {
 *   icon: 'lucide:refresh-cw',
 *   onClick: (api) => {}
 * },
 * fullscreen: {
 *   enterIcon: 'lucide:maximize',
 *   exitIcon: 'lucide:minimize',
 *   onToggle: (isFs, api) => {}
 * },
 * columnSettings: {
 *   icon: 'lucide:settings'
 * },
 * rightActions: [...]
 * ```
 * 
 * ## 快速迁移工具
 * 可以使用 `mapLegacyConfig()` 函数自动迁移旧配置：
 * 
 * ```typescript
 * import { mapLegacyConfig } from './utils/configMapper';
 * 
 * const oldConfig = {
 *   title: '用户管理',
 *   showSearch: true,
 *   showPagination: true,
 *   toolbarConfig: {
 *     isReload: true,
 *     isFull: true,
 *     isZoom: true
 *   }
 * };
 * 
 * const newConfig = mapLegacyConfig(oldConfig);
 * ```
 * 
 * ## 新增功能
 * 
 * ### 1. 构建器模式
 * ```typescript
 * const config = createBuilder('dataManagement', '数据管理')
 *   .addLeftAction({
 *     id: 'import',
 *     text: '导入',
 *     onClick: () => {}
 *   })
 *   .search({ advanced: { show: true } })
 *   .theme({ background: 'muted' })
 *   .build();
 * ```
 * 
 * ### 2. 预设配置
 * ```typescript
 * const config = getPresetConfig('approval', '审核管理');
 * ```
 * 
 * ### 3. 主题和响应式支持
 * ```typescript
 * const config: ToolbarConfig = {
 *   theme: {
 *     background: 'muted',
 *     border: 'bottom',
 *     shadow: 'sm'
 *   },
 *   responsive: {
 *     hideOnMobile: ['columnSettings', 'fullscreen'],
 *     tabletBehavior: 'wrap'
 *   }
 * };
 * ```
 * 
 * ### 4. 增强的动作按钮配置
 * ```typescript
 * leftActions: [
 *   {
 *     id: 'batch-approve',
 *     text: '批量通过',
 *     showOnlyWhenSelected: true,
 *     minSelectedRows: 1,
 *     confirm: {
 *       title: '确认操作',
 *       description: '确定要批量通过吗？'
 *     },
 *     onClick: (api, rows) => {}
 *   }
 * ]
 * ```
 */

// ========== 实用配置示例 ==========

/**
 * 配置示例集合 - 展示新简化配置的各种用法
 */
export const CONFIG_EXAMPLES = {
  
  // 示例1：最简配置 - 快速启用基础功能
  minimal: {
    title: '简单表格',
    newButton: true,
    search: true,
    pagination: true
  } as ToolbarConfig,

  // 示例2：禁用特定功能
  customized: {
    title: '自定义表格',
    newButton: true,
    batchDelete: false,     // 禁用批量删除
    search: true,
    refresh: true,
    fullscreen: false,      // 禁用全屏
    columnSettings: false,  // 禁用列设置
    pagination: true
  } as ToolbarConfig,

  // 示例3：详细配置 - 精细控制
  detailed: {
    title: {
      icon: 'lucide:users',
      main: '用户管理系统',
      sub: '完整功能展示',
      badge: { text: 'v2.0', variant: 'secondary' }
    },
    newButton: {
      text: '创建用户',
      icon: 'lucide:user-plus',
      variant: 'default'
    },
    batchDelete: {
      text: '批量删除',
      icon: 'lucide:trash-2',
      confirm: {
        title: '确认删除',
        description: '此操作不可撤销，确定要删除选中的用户吗？'
      }
    },
    leftActions: [
      {
        id: 'import',
        text: '导入',
        icon: 'lucide:upload',
        tooltip: '从 Excel 导入用户',
        onClick: () => console.log('导入用户')
      },
      {
        id: 'export',
        text: '导出',
        icon: 'lucide:download',
        tooltip: '导出用户数据',
        onClick: () => console.log('导出用户')
      }
    ],
    search: {
      placeholder: '搜索用户名、邮箱或手机号...',
      debounce: 500,
      advanced: {
        show: true,
        title: '高级搜索'
      }
    },
    refresh: {
      icon: 'lucide:refresh-cw'
    },
    fullscreen: {
      enterIcon: 'lucide:expand',
      exitIcon: 'lucide:compress'
    },
    columnSettings: true,
    pagination: {
      type: 'full',
      showSizeChanger: true,
      showTotal: true
    },
    rightActions: [
      {
        id: 'settings',
        icon: 'lucide:settings',
        tooltip: '高级设置',
        onClick: () => console.log('打开设置')
      }
    ]
  } as ToolbarConfig,

  // 示例4：移动端优化
  mobile: {
    title: '移动端表格',
    newButton: {
      text: '',  // 只显示图标
      icon: 'lucide:plus',
      size: 'sm'
    },
    batchDelete: false,  // 移动端隐藏批量删除
    search: {
      placeholder: '搜索...'
    },
    refresh: true,
    fullscreen: false,   // 移动端不需要全屏
    columnSettings: false, // 移动端隐藏列设置
    pagination: {
      type: 'simple'
    },
    size: 'sm',
    compact: true,
    responsive: {
      hideOnMobile: ['columnSettings', 'fullscreen'],
      tabletBehavior: 'wrap'
    }
  } as ToolbarConfig,

  // 示例5：只读表格
  readonly: {
    title: '数据展示',
    newButton: false,     // 只读模式，禁用新增
    batchDelete: false,   // 只读模式，禁用删除
    search: true,         // 保留搜索功能
    refresh: true,        // 保留刷新功能
    fullscreen: true,     // 保留全屏查看
    columnSettings: true, // 保留列配置
    pagination: true,
    rightActions: [
      {
        id: 'export',
        text: '导出',
        icon: 'lucide:download',
        tooltip: '导出数据',
        onClick: () => console.log('导出数据')
      }
    ]
  } as ToolbarConfig,

  // 示例6：审核工作流
  approval: {
    title: {
      icon: 'lucide:clipboard-check',
      main: '审核管理',
      badge: { text: '待审核', variant: 'destructive' }
    },
    newButton: false,  // 审核页面不需要新增
    batchDelete: false,
    leftActions: [
      {
        id: 'batch-approve',
        text: '批量通过',
        icon: 'lucide:check-circle',
        variant: 'default',
        showOnlyWhenSelected: true,
        minSelectedRows: 1,
        confirm: {
          title: '确认批量通过',
          description: '确定要通过选中的审核项目吗？'
        },
        onClick: () => console.log('批量通过')
      },
      {
        id: 'batch-reject',
        text: '批量拒绝',
        icon: 'lucide:x-circle',
        variant: 'destructive',
        showOnlyWhenSelected: true,
        minSelectedRows: 1,
        confirm: {
          title: '确认批量拒绝',
          description: '确定要拒绝选中的审核项目吗？'
        },
        onClick: () => console.log('批量拒绝')
      }
    ],
    search: {
      placeholder: '搜索审核项目...'
    },
    refresh: true,
    pagination: true
  } as ToolbarConfig,

  // 示例7：数据分析页面
  analytics: {
    title: {
      icon: 'lucide:bar-chart-3',
      main: '数据分析',
      sub: '实时数据展示'
    },
    newButton: false,
    batchDelete: false,
    leftActions: [
      {
        id: 'refresh-data',
        text: '刷新数据',
        icon: 'lucide:refresh-cw',
        onClick: () => console.log('刷新分析数据')
      },
      {
        id: 'export-report',
        text: '导出报告',
        icon: 'lucide:file-text',
        onClick: () => console.log('导出分析报告')
      }
    ],
    search: false,  // 分析页面可能不需要搜索
    refresh: {
      icon: 'lucide:rotate-ccw'
    },
    fullscreen: true,  // 分析页面适合全屏查看
    columnSettings: true,
    pagination: true,
    rightActions: [
      {
        id: 'chart-view',
        icon: 'lucide:pie-chart',
        tooltip: '图表视图',
        onClick: () => console.log('切换到图表视图')
      },
      {
        id: 'filter',
        icon: 'lucide:filter',
        tooltip: '数据筛选',
        onClick: () => console.log('打开筛选器')
      }
    ]
  } as ToolbarConfig

};
