// Toolbar/example/ToolbarExample.vue
<template>
  <div class="p-4">
    <h1 class="text-2xl font-bold mb-4">表格工具栏示例</h1>
    
    <!-- 基础示例 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-2">基础工具栏</h2>
      <TableToolbar
        :table-api="mockTableApi"
        :table-props="mockTableProps"
        :selected-rows="selectedRows"
        :config="basicConfig"
        :total-items="100"
        :current-page="1"
        :page-size="10"
        @new="handleNew"
        @batch-delete="handleBatchDelete"
        @search="handleSearch"
        @refresh="handleRefresh"
      />
    </div>

    <!-- 完整配置示例 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-2">完整配置工具栏</h2>
      <TableToolbar
        :table-api="mockTableApi"
        :table-props="mockTableProps"
        :selected-rows="selectedRows"
        :config="fullConfig"
        :total-items="200"
        :current-page="2"
        :page-size="20"
        @new="handleNew"
        @batch-delete="handleBatchDelete"
        @search="handleSearch"
        @refresh="handleRefresh"
      />
    </div>

    <!-- 自定义按钮示例 -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold mb-2">自定义按钮工具栏</h2>
      <TableToolbar
        :table-api="mockTableApi"
        :table-props="mockTableProps"
        :selected-rows="selectedRows"
        :config="customActionsConfig"
        @new="handleNew"
        @batch-delete="handleBatchDelete"
        @search="handleSearch"
        @refresh="handleRefresh"
      />
    </div>

    <!-- 控制台输出 -->
    <div class="bg-gray-100 p-4 rounded">
      <h3 class="font-semibold mb-2">控制台输出：</h3>
      <pre class="text-sm">{{ JSON.stringify(logs, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import TableToolbar from '../index.vue';
import type { ToolbarConfig } from '../types';

// Mock 数据
const selectedRows = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' }
]);

const mockTableApi = {
  reload: () => console.log('Table reloaded'),
  clearCheckboxRow: () => console.log('Selection cleared'),
  getData: () => ({ list: [], total: 0 })
};

const mockTableProps = {
  checkType: 'checkbox',
  showPagination: true
};

const logs = ref<string[]>([]);

// 基础配置
const basicConfig: ToolbarConfig = {
  title: '用户管理',
  leftZone: {
    showNewButton: true,
    showBatchDeleteButton: true
  },
  centerZone: {
    showSearch: true,
    searchPlaceholder: '搜索用户...'
  },
  rightZone: {
    showPagination: true,
    showRefreshButton: true,
    showFullscreenButton: true,
    showColumnSettingsButton: true
  }
};

// 完整配置
const fullConfig: ToolbarConfig = {
  title: {
    icon: 'lucide:users',
    main: '用户管理系统',
    sub: '管理系统中的所有用户信息',
    badge: {
      text: 'Beta',
      variant: 'secondary'
    }
  },
  leftZone: {
    newButtonText: '添加用户',
    newButtonIcon: 'lucide:user-plus',
    batchDeleteButtonText: '批量删除',
    batchDeleteButtonIcon: 'lucide:trash',
    onNew: (tableApi) => {
      addLog('执行新增操作');
    },
    onBatchDelete: (tableApi, selectedRows) => {
      addLog(`批量删除 ${selectedRows.length} 个用户`);
    }
  },
  centerZone: {
    searchPlaceholder: '输入用户名或邮箱搜索...',
    onSearch: (query, tableApi) => {
      addLog(`搜索: ${query}`);
    }
  },
  rightZone: {
    onRefresh: (tableApi) => {
      addLog('刷新表格数据');
    },
    onFullscreenToggle: (isFullscreen, tableApi) => {
      addLog(`切换全屏: ${isFullscreen}`);
    },
    onColumnSettings: (tableApi) => {
      addLog('打开列配置');
    }
  }
};

// 自定义按钮配置
const customActionsConfig: ToolbarConfig = {
  title: '订单管理',
  leftZone: {
    customActions: [
      {
        id: 'export',
        text: '导出',
        icon: 'lucide:download',
        tooltip: '导出数据',
        variant: 'outline',
        onClick: (tableApi, selectedRows) => {
          addLog('导出数据');
        }
      },
      {
        id: 'import',
        text: '导入',
        icon: 'lucide:upload',
        tooltip: '导入数据',
        variant: 'outline',
        onClick: (tableApi, selectedRows) => {
          addLog('导入数据');
        }
      },
      {
        id: 'approve',
        text: '批量审核',
        icon: 'lucide:check-circle',
        tooltip: '批量审核选中项',
        variant: 'outline',
        disabled: (tableApi, selectedRows) => selectedRows.length === 0,
        isDropdownItem: true,
        onClick: (tableApi, selectedRows) => {
          addLog(`批量审核 ${selectedRows.length} 个订单`);
        }
      },
      {
        id: 'reject',
        text: '批量拒绝',
        icon: 'lucide:x-circle',
        tooltip: '批量拒绝选中项',
        variant: 'destructive',
        disabled: (tableApi, selectedRows) => selectedRows.length === 0,
        isDropdownItem: true,
        onClick: (tableApi, selectedRows) => {
          addLog(`批量拒绝 ${selectedRows.length} 个订单`);
        }
      }
    ]
  },
  rightZone: {
    customActions: [
      {
        id: 'settings',
        icon: 'lucide:settings',
        tooltip: '高级设置',
        variant: 'ghost',
        onClick: (tableApi, selectedRows) => {
          addLog('打开高级设置');
        }
      }
    ]
  }
};

// 工具函数
const addLog = (message: string) => {
  logs.value.unshift(`[${new Date().toLocaleTimeString()}] ${message}`);
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10);
  }
};

// 事件处理
const handleNew = () => {
  addLog('默认新增事件触发');
};

const handleBatchDelete = () => {
  addLog(`默认批量删除事件触发，选中 ${selectedRows.value.length} 项`);
};

const handleSearch = (query: string) => {
  addLog(`默认搜索事件触发: ${query}`);
};

const handleRefresh = () => {
  addLog('默认刷新事件触发');
};
</script>
