// Toolbar/ToolbarCenter.vue
<template>
  <div :class="cn('flex justify-center w-full', className)">
    <SearchInput
      v-if="showSearch"
      v-model="searchQuery"
      :placeholder="searchPlaceholder"
      class="w-full max-w-lg"
      @search="handleSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { cn } from '@/lib/utils';
import SearchInput from './components/SearchInput.vue';

interface Props {
  tableApi: any;
  className?: string;
  showSearch?: boolean;
  searchPlaceholder?: string;
  onSearchChange?: (query: string, tableApi: any) => void;
}

const props = withDefaults(defineProps<Props>(), {
  showSearch: true,
  searchPlaceholder: '搜索...'
});

const emits = defineEmits<{
  search: [query: string];
}>();

const searchQuery = ref('');

const handleSearch = (query: string) => {
  if (props.onSearchChange) {
    props.onSearchChange(query, props.tableApi);
  } else {
    emits('search', query);
  }
};
</script>
