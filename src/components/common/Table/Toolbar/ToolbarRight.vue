// Toolbar/ToolbarRight.vue
<template>
  <div :class="cn('flex items-center gap-2', className)">
    <!-- 分页信息 -->
    <SimplePagination
      v-if="showPagination && paginationData"
      :start="paginationData.start"
      :limit="paginationData.limit"
      :total="paginationData.total"
      @page-change="handlePageChange"
    />
    
    <!-- 控制按钮组 -->
    <div class="flex items-center gap-1 ml-2">
      <!-- 全屏按钮 -->
      <ActionButton
        v-if="showFullscreen"
        :icon="isFullscreen ? fullscreenExitIcon : fullscreenEnterIcon"
        :tooltip="isFullscreen ? '退出全屏' : '全屏'"
        size="sm"
        variant="outline"
        @click="handleFullscreen"
      />
      
      <!-- 刷新按钮 -->
      <ActionButton
        v-if="showRefresh"
        :icon="typeof refreshIcon === 'string' ? refreshIcon : refreshIcon?.name"
        tooltip="刷新数据"
        size="sm"
        variant="outline"
        @click="handleRefresh"
      />
      
      <!-- 列配置按钮 -->
      <ColumnSettings
        v-if="showColumnSettings"
        :icon="typeof columnSettingsIcon === 'string' ? columnSettingsIcon : columnSettingsIcon?.name"
        @column-settings="handleColumnSettings"
      />
      
      <!-- 自定义操作按钮 -->
      <template v-if="rightActions?.length">
        <!-- 直接展示的按钮 -->
        <ActionButton
          v-for="action in directActions"
          :key="action.id"
          :text="action.text"
          :icon="typeof action.icon === 'string' ? action.icon : action.icon?.name"
          :tooltip="action.tooltip"
          :variant="action.variant"
          :size="action.size"
          :disabled="getActionDisabled(action)"
          :class="action.className"
          @click="() => handleActionClick(action)"
        />
        
        <!-- 更多操作下拉菜单 -->
        <DropdownMenu v-if="dropdownActions.length">
          <DropdownMenuTrigger as-child>
            <ActionButton
              icon="lucide:more-horizontal"
              tooltip="更多操作"
              variant="outline"
              size="sm"
            />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem
              v-for="action in dropdownActions"
              :key="action.id"
              :disabled="getActionDisabled(action)"
              @click="() => handleActionClick(action)"
            >
              <Icon
                v-if="action.icon"
                :icon="typeof action.icon === 'string' ? action.icon : action.icon?.name || ''"
                class="mr-2 h-4 w-4"
              />
              {{ action.text }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Icon } from '@iconify/vue';
import { cn } from '@/lib/utils';
import ActionButton from './components/ActionButton.vue';
import SimplePagination from './components/SimplePagination.vue';
import ColumnSettings from './components/ColumnSettings.vue';
import type { ActionConfig, IconType } from './types';
import type { PageQuery } from './components/SimplePagination.vue';

interface Props {
  tableApi: any;
  selectedRows: any[];
  isFullscreen?: boolean;
  paginationData?: {
    start: number;
    limit: number;
    total: number;
  } | null;
  className?: string;
  
  // 分页相关
  showPagination?: boolean;
  
  // 刷新相关
  showRefresh?: boolean;
  refreshIcon?: IconType;
  onRefreshClick?: (tableApi: any) => void;
  
  // 全屏相关
  showFullscreen?: boolean;
  fullscreenEnterIcon?: string;
  fullscreenExitIcon?: string;
  onFullscreenToggle?: (isFullscreen: boolean) => void;
  
  // 列设置相关
  showColumnSettings?: boolean;
  columnSettingsIcon?: IconType;
  onColumnSettingsClick?: (tableApi: any) => void;
  
  // 右侧自定义动作
  rightActions?: ActionConfig[];
}

const props = withDefaults(defineProps<Props>(), {
  isFullscreen: false,
  showPagination: true,
  showRefresh: true,
  refreshIcon: 'lucide:refresh-cw',
  showFullscreen: true,
  fullscreenEnterIcon: 'lucide:maximize',
  fullscreenExitIcon: 'lucide:minimize',
  showColumnSettings: true,
  columnSettingsIcon: 'lucide:settings'
});

const emits = defineEmits<{
  fullscreenToggle: [isFullscreen: boolean];
  refresh: [];
  columnSettings: [];
  pageChange: [query: PageQuery];
}>();

// 分离直接显示的按钮和下拉菜单中的按钮
const directActions = computed(() => {
  return props.rightActions?.filter(action => {
    if ('children' in action) return false;
    return !(action as any).isDropdownItem;
  }) || [];
});

const dropdownActions = computed(() => {
  const dropdown: any[] = [];
  props.rightActions?.forEach(action => {
    if ('children' in action) {
      dropdown.push(...action.children);
    } else if ((action as any).isDropdownItem) {
      dropdown.push(action);
    }
  });
  return dropdown;
});

// 计算按钮禁用状态
const getActionDisabled = (action: any) => {
  if (typeof action.disabled === 'function') {
    return action.disabled(props.tableApi, props.selectedRows);
  }
  return action.disabled || false;
};

const handleActionClick = (action: any) => {
  if (typeof action.onClick === 'function') {
    action.onClick(props.tableApi, props.selectedRows);
  }
};

const handleFullscreen = () => {
  const newFullscreenState = !props.isFullscreen;
  if (props.onFullscreenToggle) {
    props.onFullscreenToggle(newFullscreenState);
  } else {
    emits('fullscreenToggle', newFullscreenState);
  }
};

const handleRefresh = () => {
  if (props.onRefreshClick) {
    props.onRefreshClick(props.tableApi);
  } else {
    emits('refresh');
  }
};

const handleColumnSettings = () => {
  if (props.onColumnSettingsClick) {
    props.onColumnSettingsClick(props.tableApi);
  } else {
    emits('columnSettings');
  }
};

const handlePageChange = (query: PageQuery) => {
  emits('pageChange', query);
};
</script>
