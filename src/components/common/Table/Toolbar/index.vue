// Toolbar/index.vue
<template>
  <TooltipProvider>
    <div :class="toolbarClasses">
      <!-- 响应式网格容器 -->
      <div class="toolbar-grid w-full">
        <!-- 左侧区域 -->
        <div class="toolbar-left">
          <ToolbarLeft
            :title-config="titleConfig"
            :table-api="tableApi"
            :selected-rows="selectedRows"
            :table-props="tableProps"
            :show-new-button="getNewButtonShow"
            :new-button-text="getNewButtonText"
            :new-button-icon="getNewButtonIcon"
            :new-button-variant="getNewButtonVariant"
            :on-new-click="getNewButtonClick"
            :show-batch-delete="getBatchDeleteShow"
            :batch-delete-text="getBatchDeleteText"
            :batch-delete-icon="getBatchDeleteIcon"
            :batch-delete-variant="getBatchDeleteVariant"
            :on-batch-delete-click="getBatchDeleteClick"
            :left-actions="config?.leftActions"
            @new="handleNew"
            @batch-delete="handleBatchDelete"
          />
        </div>
        
        <!-- 中间区域 -->
        <div class="toolbar-center">
          <ToolbarCenter
            :show-search="getSearchShow"
            :search-placeholder="getSearchPlaceholder"
            :on-search-change="getSearchChange"
            :table-api="tableApi"
            @search="handleSearch"
          />
        </div>
        
        <!-- 右侧区域 -->
        <div class="toolbar-right">
          <ToolbarRight
            :show-pagination="getPaginationShow"
            :show-refresh="getRefreshShow"
            :refresh-icon="getRefreshIcon"
            :on-refresh-click="getRefreshClick"
            :show-fullscreen="getFullscreenShow"
            :fullscreen-enter-icon="typeof getFullscreenEnterIcon === 'string' ? getFullscreenEnterIcon : getFullscreenEnterIcon?.name || 'lucide:maximize'"
            :fullscreen-exit-icon="typeof getFullscreenExitIcon === 'string' ? getFullscreenExitIcon : getFullscreenExitIcon?.name || 'lucide:minimize'"
            :on-fullscreen-toggle="getFullscreenToggle"
            :show-column-settings="getColumnSettingsShow"
            :column-settings-icon="getColumnSettingsIcon"
            :on-column-settings-click="getColumnSettingsClick"
            :right-actions="config?.rightActions"
            :table-api="tableApi"
            :selected-rows="selectedRows"
            :is-fullscreen="isFullscreen"
            :pagination-data="paginationData"
            @fullscreen-toggle="handleFullscreenToggle"
            @refresh="handleRefresh"
            @column-settings="handleColumnSettings"
            @page-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </TooltipProvider>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { cn } from '@/lib/utils';
import ToolbarLeft from './ToolbarLeft.vue';
import ToolbarCenter from './ToolbarCenter.vue';
import ToolbarRight from './ToolbarRight.vue';
import type { 
  MainToolbarProps, 
  ToolbarTitleConfig,
  ActionButtonConfig,
  IconType
} from './types';
import type { PageQuery } from './components/SimplePagination.vue';

interface Props extends MainToolbarProps {}

const props = defineProps<Props>();

const emits = defineEmits<{
  new: [];
  batchDelete: [];
  search: [query: string];
  fullscreenToggle: [isFullscreen: boolean];
  refresh: [];
  columnSettings: [];
  pageChange: [page: number];
}>();

// 内部状态
const isFullscreen = ref(false);

// 计算工具栏样式类
const toolbarClasses = computed(() => {
  const baseClasses = 'w-full p-3 border-b bg-background';
  const sizeClasses = {
    'sm': 'p-2 text-sm',
    'md': 'p-3',
    'lg': 'p-4'
  };
  const themeClasses = {
    'muted': 'bg-muted/50',
    'transparent': 'bg-transparent',
    'default': 'bg-background'
  };
  
  const size = props.config?.size || 'md';
  const background = props.config?.theme?.background || 'default';
  const compact = props.config?.compact ? 'py-2' : '';
  
  return cn(
    baseClasses,
    sizeClasses[size],
    themeClasses[background],
    compact,
    props.config?.className
  );
});

// 计算标题配置
const titleConfig = computed<ToolbarTitleConfig | null>(() => {
  if (!props.config?.title) return null;
  
  if (typeof props.config.title === 'string') {
    return {
      main: props.config.title
    };
  }
  
  return props.config.title;
});

// 新增按钮相关计算属性
const getNewButtonShow = computed(() => {
  const config = props.config?.newButton;
  if (typeof config === 'boolean') return config;
  if (typeof config === 'object') return config.show !== false;
  return true; // 默认显示
});

const getNewButtonText = computed(() => {
  const config = props.config?.newButton;
  if (typeof config === 'object') return config.text || '新增';
  return '新增';
});

const getNewButtonIcon = computed(() => {
  const config = props.config?.newButton;
  if (typeof config === 'object') return config.icon || 'lucide:plus';
  return 'lucide:plus';
});

const getNewButtonVariant = computed(() => {
  const config = props.config?.newButton;
  if (typeof config === 'object') return config.variant || 'default';
  return 'default';
});

const getNewButtonClick = computed(() => {
  const config = props.config?.newButton;
  if (typeof config === 'object') return config.onClick;
  return undefined;
});

// 批量删除相关计算属性
const getBatchDeleteShow = computed(() => {
  const config = props.config?.batchDelete;
  if (typeof config === 'boolean') return config;
  if (typeof config === 'object') return config.show !== false;
  return true; // 默认显示
});

const getBatchDeleteText = computed(() => {
  const config = props.config?.batchDelete;
  if (typeof config === 'object') return config.text || '批量删除';
  return '批量删除';
});

const getBatchDeleteIcon = computed(() => {
  const config = props.config?.batchDelete;
  if (typeof config === 'object') return config.icon || 'lucide:trash-2';
  return 'lucide:trash-2';
});

const getBatchDeleteVariant = computed(() => {
  const config = props.config?.batchDelete;
  if (typeof config === 'object') return config.variant || 'destructive';
  return 'destructive';
});

const getBatchDeleteClick = computed(() => {
  const config = props.config?.batchDelete;
  if (typeof config === 'object') return config.onClick;
  return undefined;
});

// 搜索相关计算属性
const getSearchShow = computed(() => {
  const config = props.config?.search;
  if (typeof config === 'boolean') return config;
  if (typeof config === 'object') return config.show !== false;
  return true; // 默认显示
});

const getSearchPlaceholder = computed(() => {
  const config = props.config?.search;
  if (typeof config === 'object') return config.placeholder || '搜索...';
  return '搜索...';
});

const getSearchChange = computed(() => {
  const config = props.config?.search;
  if (typeof config === 'object') return config.onSearch;
  return undefined;
});

// 分页相关计算属性
const getPaginationShow = computed(() => {
  const config = props.config?.pagination;
  if (typeof config === 'boolean') return config;
  if (typeof config === 'object') return config.show !== false;
  return true; // 默认显示
});

// 刷新相关计算属性
const getRefreshShow = computed(() => {
  const config = props.config?.refresh;
  if (typeof config === 'boolean') return config;
  if (typeof config === 'object') return config.show !== false;
  return true; // 默认显示
});

const getRefreshIcon = computed(() => {
  const config = props.config?.refresh;
  if (typeof config === 'object') return config.icon || 'lucide:refresh-cw';
  return 'lucide:refresh-cw';
});

const getRefreshClick = computed(() => {
  const config = props.config?.refresh;
  if (typeof config === 'object') return config.onClick;
  return undefined;
});

// 全屏相关计算属性
const getFullscreenShow = computed(() => {
  const config = props.config?.fullscreen;
  if (typeof config === 'boolean') return config;
  if (typeof config === 'object') return config.show !== false;
  return true; // 默认显示
});

const getFullscreenEnterIcon = computed(() => {
  const config = props.config?.fullscreen;
  if (typeof config === 'object') return config.enterIcon || 'lucide:maximize';
  return 'lucide:maximize';
});

const getFullscreenExitIcon = computed(() => {
  const config = props.config?.fullscreen;
  if (typeof config === 'object') return config.exitIcon || 'lucide:minimize';
  return 'lucide:minimize';
});

const getFullscreenToggle = computed(() => {
  const config = props.config?.fullscreen;
  if (typeof config === 'object' && config.onToggle) {
    // 包装原函数以提供 tableApi 参数
    return (isFullscreen: boolean) => {
      config.onToggle!(isFullscreen, props.tableApi);
    };
  }
  return undefined;
});

// 列设置相关计算属性
const getColumnSettingsShow = computed(() => {
  const config = props.config?.columnSettings;
  if (typeof config === 'boolean') return config;
  if (typeof config === 'object') return config.show !== false;
  return true; // 默认显示
});

const getColumnSettingsIcon = computed(() => {
  const config = props.config?.columnSettings;
  if (typeof config === 'object') return config.icon || 'lucide:settings';
  return 'lucide:settings';
});

const getColumnSettingsClick = computed(() => {
  const config = props.config?.columnSettings;
  if (typeof config === 'object') return config.onClick;
  return undefined;
});

// 计算分页数据
const paginationData = computed(() => {
  if (!props.totalItems || !props.currentPage || !props.pageSize) {
    return null;
  }
  
  const start = (props.currentPage - 1) * props.pageSize + 1;
  
  return {
    start: start,
    limit: props.pageSize,
    total: props.totalItems
  };
});

// 事件处理器
const handleNew = () => {
  emits('new');
};

const handleBatchDelete = () => {
  emits('batchDelete');
};

const handleSearch = (query: string) => {
  emits('search', query);
};

const handleFullscreenToggle = (newIsFullscreen: boolean) => {
  isFullscreen.value = newIsFullscreen;
  emits('fullscreenToggle', newIsFullscreen);
};

const handleRefresh = () => {
  emits('refresh');
};

const handleColumnSettings = () => {
  emits('columnSettings');
};

const handlePageChange = (query: PageQuery) => {
  // 不在这里调用 tableApi.reload，因为这会导致重复请求
  // 只是通知外部组件页码变化，让Table组件的 @queryPage 事件统一处理
  const newPage = Math.floor(query.offset / query.limit) + 1;
  emits('pageChange', newPage);
};
</script>

<style scoped>
/* 响应式网格布局 */
.toolbar-grid {
  display: grid;
  gap: 0.75rem;
  align-items: center;
}

/* 默认状态：大屏幕 - 水平三栏布局 */
@media (min-width: 1200px) {
  .toolbar-grid {
    grid-template-columns: auto 1fr auto;
    grid-template-areas: "left center right";
    gap: 1rem;
  }
  
  .toolbar-left {
    grid-area: left;
    justify-self: start;
  }
  
  .toolbar-center {
    grid-area: center;
    justify-self: center;
    width: 100%;
    max-width: 600px;
  }
  
  .toolbar-right {
    grid-area: right;
    justify-self: end;
  }
}

/* 第一个断点：中大屏幕 - 左右在上，中间在下 */
@media (min-width: 768px) and (max-width: 1199px) {
  .toolbar-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-areas: 
      "left right"
      "center center";
    gap: 0.75rem;
  }
  
  .toolbar-left {
    grid-area: left;
    justify-self: start;
  }
  
  .toolbar-center {
    grid-area: center;
    justify-self: stretch;
    width: 100%;
  }
  
  .toolbar-right {
    grid-area: right;
    justify-self: end;
  }
}

/* 第二个断点：小屏幕 - 三个区域各占一行 */
@media (max-width: 767px) {
  .toolbar-grid {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "left"
      "right"
      "center";
    gap: 0.75rem;
  }
  
  .toolbar-left {
    grid-area: left;
    justify-self: center;
  }
  
  .toolbar-center {
    grid-area: center;
    justify-self: stretch;
    width: 100%;
  }
  
  .toolbar-right {
    grid-area: right;
    justify-self: center;
  }
}

/* 确保子组件在小屏幕下能正确适应 */
.toolbar-left :deep(.flex) {
  flex-wrap: wrap;
  justify-content: center;
}

.toolbar-right :deep(.flex) {
  flex-wrap: wrap;
  justify-content: center;
}

/* 中间搜索区域样式调整 - 确保占满可用空间 */
.toolbar-center :deep(.flex) {
  width: 100%;
}

.toolbar-center :deep(.max-w-lg) {
  max-width: none !important;
  width: 100%;
}

/* 在不同断点下的搜索框宽度调整 */
@media (min-width: 1200px) {
  .toolbar-center :deep(.max-w-lg) {
    max-width: 500px !important;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .toolbar-center :deep(.max-w-lg) {
    max-width: none !important;
    width: 100%;
  }
}

@media (max-width: 767px) {
  .toolbar-center :deep(.max-w-lg) {
    max-width: none !important;
    width: 100%;
  }
}
</style>
