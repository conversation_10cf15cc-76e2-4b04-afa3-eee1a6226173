# 表格工具栏组件 (Table Toolbar)

基于 Tailwind CSS 4 和 Shadcn/UI 的现代化表格工具栏组件，提供完整的表格操作功能。

## 📋 特性

- ✅ **模块化设计** - 左中右三区域独立配置
- ✅ **TypeScript 支持** - 完整的类型定义
- ✅ **Shadcn/UI 集成** - 使用统一的 UI 组件库
- ✅ **Tailwind CSS** - 响应式设计和现代样式
- ✅ **高度可配置** - 灵活的配置选项
- ✅ **可扩展性** - 支持自定义按钮和操作

## 🏗️ 组件结构

```
Toolbar/
├── index.vue                 # 主工具栏组件
├── ToolbarLeft.vue           # 左侧区域
├── ToolbarCenter.vue         # 中间区域
├── ToolbarRight.vue          # 右侧区域
├── components/               # 原子组件
│   ├── ActionButton.vue      # 通用操作按钮
│   ├── ToolbarTitle.vue      # 标题组件
│   ├── SearchInput.vue       # 搜索输入框
│   ├── SimplePagination.vue  # 简易分页
│   └── ColumnSettings.vue    # 列配置 (占位)
├── composables/              # 组合式函数
│   └── useToolbarFeatures.ts # 工具栏功能
├── types.ts                  # TypeScript 类型
└── example/                  # 使用示例
    └── ToolbarExample.vue
```

## 🚀 快速开始

### 基础用法

```vue
<template>
  <TableToolbar
    :table-api="tableApi"
    :table-props="tableProps"
    :selected-rows="selectedRows"
    :config="toolbarConfig"
    @new="handleNew"
    @batch-delete="handleBatchDelete"
    @search="handleSearch"
    @refresh="handleRefresh"
  />
</template>

<script setup lang="ts">
import TableToolbar from '@/components/common/Table/Toolbar';
import type { ToolbarConfig } from '@/components/common/Table/Toolbar/types';

const toolbarConfig: ToolbarConfig = {
  title: '用户管理',
  leftZone: {
    showNewButton: true,
    showBatchDeleteButton: true
  },
  centerZone: {
    showSearch: true
  },
  rightZone: {
    showPagination: true,
    showRefreshButton: true
  }
};

// 事件处理
const handleNew = () => {
  console.log('新增');
};

const handleBatchDelete = () => {
  console.log('批量删除');
};
</script>
```

### 完整配置示例

```typescript
const fullConfig: ToolbarConfig = {
  title: {
    icon: 'lucide:users',
    main: '用户管理系统',
    sub: '管理系统中的所有用户信息',
    badge: {
      text: 'Beta',
      variant: 'secondary'
    }
  },
  leftZone: {
    newButtonText: '添加用户',
    newButtonIcon: 'lucide:user-plus',
    newButtonVariant: 'default',
    batchDeleteButtonText: '批量删除',
    customActions: [
      {
        id: 'export',
        text: '导出',
        icon: 'lucide:download',
        tooltip: '导出数据',
        onClick: (tableApi, selectedRows) => {
          // 导出逻辑
        }
      }
    ]
  },
  centerZone: {
    searchPlaceholder: '搜索用户...',
    onSearch: (query, tableApi) => {
      // 搜索逻辑
    }
  },
  rightZone: {
    showPagination: true,
    onRefresh: (tableApi) => {
      // 刷新逻辑
    },
    customActions: [
      {
        id: 'settings',
        icon: 'lucide:settings',
        tooltip: '设置',
        onClick: (tableApi, selectedRows) => {
          // 设置逻辑
        }
      }
    ]
  }
};
```

## 📖 API 文档

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `tableApi` | `any` | - | 表格 API 实例 |
| `tableProps` | `Record<string, any>` | - | 表格属性 |
| `selectedRows` | `any[]` | `[]` | 选中的行数据 |
| `config` | `ToolbarConfig` | - | 工具栏配置 |
| `totalItems` | `number` | - | 总条目数 |
| `currentPage` | `number` | `1` | 当前页码 |
| `pageSize` | `number` | `10` | 每页条数 |
| `className` | `string` | - | 额外的样式类 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `new` | - | 新增按钮点击 |
| `batchDelete` | - | 批量删除按钮点击 |
| `search` | `query: string` | 搜索事件 |
| `fullscreenToggle` | `isFullscreen: boolean` | 全屏切换 |
| `refresh` | - | 刷新事件 |
| `columnSettings` | - | 列配置事件 |
| `pageChange` | `page: number` | 页码变化 |

## 🎨 配置选项

### ToolbarConfig

```typescript
interface ToolbarConfig {
  title?: string | ToolbarTitleConfig;
  leftZone?: ToolbarLeftZoneConfig;
  centerZone?: ToolbarCenterZoneConfig;
  rightZone?: ToolbarRightZoneConfig;
  className?: string;
}
```

### 左侧区域配置 (ToolbarLeftZoneConfig)

```typescript
interface ToolbarLeftZoneConfig {
  showNewButton?: boolean;           // 显示新增按钮
  newButtonText?: string;            // 新增按钮文本
  newButtonIcon?: string;            // 新增按钮图标
  newButtonVariant?: ButtonVariant;  // 新增按钮样式变体
  onNew?: (tableApi: any) => void;   // 新增回调

  showBatchDeleteButton?: boolean;   // 显示批量删除按钮
  batchDeleteButtonText?: string;    // 批量删除按钮文本
  onBatchDelete?: (tableApi: any, selectedRows: any[]) => void;

  customActions?: ActionButtonConfig[]; // 自定义操作按钮
  className?: string;
}
```

### 中间区域配置 (ToolbarCenterZoneConfig)

```typescript
interface ToolbarCenterZoneConfig {
  showSearch?: boolean;              // 显示搜索框
  searchPlaceholder?: string;        // 搜索框占位符
  onSearch?: (query: string, tableApi: any) => void; // 搜索回调
  className?: string;
}
```

### 右侧区域配置 (ToolbarRightZoneConfig)

```typescript
interface ToolbarRightZoneConfig {
  showPagination?: boolean;          // 显示分页
  showFullscreenButton?: boolean;    // 显示全屏按钮
  showRefreshButton?: boolean;       // 显示刷新按钮
  showColumnSettingsButton?: boolean; // 显示列配置按钮
  
  onFullscreenToggle?: (isFullscreen: boolean, tableApi: any) => void;
  onRefresh?: (tableApi: any) => void;
  onColumnSettings?: (tableApi: any) => void;
  
  customActions?: ActionButtonConfig[]; // 自定义操作按钮
  className?: string;
}
```

### 自定义操作按钮 (ActionButtonConfig)

```typescript
interface ActionButtonConfig {
  id: string;                        // 唯一标识
  text?: string;                     // 按钮文本
  icon?: string;                     // 图标名称 (Lucide)
  tooltip?: string;                  // 提示文本
  disabled?: boolean | ((tableApi: any, selectedRows: any[]) => boolean);
  variant?: ButtonVariant;           // 按钮样式变体
  size?: ButtonSize;                 // 按钮尺寸
  className?: string;                // 额外样式类
  isDropdownItem?: boolean;          // 是否作为下拉菜单项
  onClick: (tableApi: any, selectedRows: any[]) => void; // 点击回调
}
```

## 🎯 使用场景

### 1. 数据管理页面

```typescript
const dataManagementConfig: ToolbarConfig = {
  title: {
    icon: 'lucide:database',
    main: '数据管理',
    badge: '1,234'
  },
  leftZone: {
    customActions: [
      {
        id: 'import',
        text: '导入',
        icon: 'lucide:upload',
        onClick: () => openImportDialog()
      },
      {
        id: 'export',
        text: '导出',
        icon: 'lucide:download',
        onClick: () => exportData()
      }
    ]
  }
};
```

### 2. 审核流程页面

```typescript
const approvalConfig: ToolbarConfig = {
  leftZone: {
    customActions: [
      {
        id: 'batch-approve',
        text: '批量通过',
        icon: 'lucide:check',
        variant: 'default',
        disabled: (api, rows) => rows.length === 0,
        onClick: (api, rows) => batchApprove(rows)
      },
      {
        id: 'batch-reject',
        text: '批量拒绝',
        icon: 'lucide:x',
        variant: 'destructive',
        disabled: (api, rows) => rows.length === 0,
        onClick: (api, rows) => batchReject(rows)
      }
    ]
  }
};
```

## 🔧 自定义扩展

### 添加自定义按钮组

当按钮较多时，可以使用 `isDropdownItem: true` 将按钮放入下拉菜单：

```typescript
const customActions: ActionButtonConfig[] = [
  // 直接显示的按钮
  {
    id: 'primary-action',
    text: '主要操作',
    icon: 'lucide:zap',
    onClick: () => {}
  },
  // 下拉菜单中的按钮
  {
    id: 'secondary-action-1',
    text: '次要操作1',
    icon: 'lucide:settings',
    isDropdownItem: true,
    onClick: () => {}
  },
  {
    id: 'secondary-action-2',
    text: '次要操作2',
    icon: 'lucide:info',
    isDropdownItem: true,
    onClick: () => {}
  }
];
```

### 动态禁用按钮

```typescript
{
  id: 'conditional-action',
  text: '条件操作',
  disabled: (tableApi, selectedRows) => {
    // 自定义禁用逻辑
    return selectedRows.length === 0 || 
           selectedRows.some(row => row.status === 'locked');
  },
  onClick: (api, rows) => {}
}
```

## 🎨 样式定制

组件完全基于 Tailwind CSS 构建，可以通过 `className` 属性进行样式定制：

```vue
<TableToolbar
  :config="{
    className: 'border-2 border-blue-200 rounded-lg shadow-lg',
    leftZone: { className: 'bg-blue-50 p-2 rounded' },
    rightZone: { className: 'bg-gray-50 p-2 rounded' }
  }"
/>
```

## 🔄 与现有表格集成

工具栏设计为与现有的 VTable 组件无缝集成：

```vue
<template>
  <div class="table-container">
    <TableToolbar
      :table-api="tableInstance"
      :table-props="tableProps"
      :selected-rows="selectedRows"
      :config="toolbarConfig"
    />
    <VTable
      @register="registerTable"
      v-bind="tableProps"
    />
  </div>
</template>

<script setup lang="ts">
import { useTable } from '@/components/common/Table';

const [registerTable, tableInstance] = useTable(tableConfig);
</script>
```

## 📝 注意事项

1. **图标使用**: 推荐使用 Lucide 图标，与 Shadcn/UI 保持一致
2. **类型安全**: 充分利用 TypeScript 类型定义，避免运行时错误
3. **性能优化**: 大量自定义按钮时考虑使用下拉菜单
4. **响应式**: 组件已内置响应式设计，在移动端会自动适配

## 🚧 未来计划

- [ ] 实现完整的列配置功能
- [ ] 添加高级筛选器支持
- [ ] 支持视图保存与切换
- [ ] 国际化支持
- [ ] 主题定制功能
