# 表格工具栏测试指南

## 🧪 测试策略

### 1. 单元测试

#### 组件测试
```typescript
// tests/components/Toolbar/ActionButton.test.ts
import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import ActionButton from '@/components/common/Table/Toolbar/components/ActionButton.vue';

describe('ActionButton', () => {
  it('renders with basic props', () => {
    const wrapper = mount(ActionButton, {
      props: {
        text: 'Test Button',
        icon: 'lucide:plus'
      }
    });
    
    expect(wrapper.text()).toContain('Test Button');
    expect(wrapper.find('[data-icon="lucide:plus"]')).toBeTruthy();
  });

  it('emits click event when clicked', async () => {
    const mockClick = vi.fn();
    const wrapper = mount(ActionButton, {
      props: {
        text: 'Click Me',
        onClick: mockClick
      }
    });
    
    await wrapper.trigger('click');
    expect(mockClick).toHaveBeenCalled();
  });

  it('shows disabled state correctly', () => {
    const wrapper = mount(ActionButton, {
      props: {
        text: 'Disabled',
        disabled: true
      }
    });
    
    expect(wrapper.attributes('disabled')).toBeDefined();
  });
});
```

#### 工具函数测试
```typescript
// tests/utils/configMapper.test.ts
import { describe, it, expect } from 'vitest';
import { generateToolbarConfig, createSimpleToolbarConfig } from '@/components/common/Table/Toolbar/utils/legacyMigration';

describe('configMapper', () => {
  it('generates toolbar config from legacy props', () => {
    const legacyProps = {
      buttonList: [
        { name: '新增', icon: 'plus' },
        { name: '删除', icon: 'trash' }
      ],
      showSearch: true,
      showPagination: true
    };

    const config = generateToolbarConfig(legacyProps);
    
    expect(config.leftZone?.customActions).toHaveLength(2);
    expect(config.centerZone?.showSearch).toBe(true);
    expect(config.rightZone?.showPagination).toBe(true);
  });

  it('creates simple config with title', () => {
    const config = createSimpleToolbarConfig('用户管理');
    
    expect(config.title).toBe('用户管理');
    expect(config.leftZone?.showNewButton).toBe(true);
    expect(config.centerZone?.showSearch).toBe(true);
  });
});
```

### 2. 集成测试

#### VTable 集成测试
```typescript
// tests/components/VTable.integration.test.ts
import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import VTable from '@/components/common/Table/src/index.vue';
import type { ToolbarConfig } from '@/components/common/Table/Toolbar/types';

describe('VTable with New Toolbar', () => {
  const mockColumns = [
    { field: 'id', title: 'ID' },
    { field: 'name', title: '名称' }
  ];
  
  const mockData = [
    { id: 1, name: '测试1' },
    { id: 2, name: '测试2' }
  ];

  const toolbarConfig: ToolbarConfig = {
    title: '测试表格',
    leftZone: {
      showNewButton: true,
      showBatchDeleteButton: true
    },
    centerZone: {
      showSearch: true
    },
    rightZone: {
      showPagination: true
    }
  };

  it('renders new toolbar when enabled', () => {
    const wrapper = mount(VTable, {
      props: {
        columns: mockColumns,
        data: mockData,
        useNewToolbar: true,
        newToolbarConfig: toolbarConfig
      }
    });
    
    expect(wrapper.find('.toolbar-container')).toBeTruthy();
    expect(wrapper.text()).toContain('测试表格');
  });

  it('emits toolbar events correctly', async () => {
    const wrapper = mount(VTable, {
      props: {
        columns: mockColumns,
        data: mockData,
        useNewToolbar: true,
        newToolbarConfig: toolbarConfig
      }
    });
    
    // 模拟新增按钮点击
    await wrapper.find('[data-testid="new-button"]').trigger('click');
    expect(wrapper.emitted('toolbar:new')).toBeTruthy();
  });

  it('falls back to legacy toolbar when new toolbar disabled', () => {
    const wrapper = mount(VTable, {
      props: {
        columns: mockColumns,
        data: mockData,
        useNewToolbar: false,
        buttonList: [{ name: '旧按钮' }]
      }
    });
    
    expect(wrapper.text()).toContain('旧按钮');
  });
});
```

### 3. E2E 测试

#### Playwright 测试
```typescript
// tests/e2e/toolbar.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Table Toolbar E2E', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/demo/new-toolbar');
  });

  test('basic toolbar functionality', async ({ page }) => {
    // 验证工具栏渲染
    await expect(page.locator('.toolbar-container')).toBeVisible();
    
    // 验证标题显示
    await expect(page.locator('h1')).toContainText('用户管理');
    
    // 测试新增按钮
    await page.click('[data-testid="new-button"]');
    await expect(page.locator('.toast')).toContainText('点击新增按钮');
  });

  test('search functionality', async ({ page }) => {
    // 输入搜索关键词
    await page.fill('[data-testid="search-input"]', '张三');
    await page.keyboard.press('Enter');
    
    // 验证搜索事件触发
    await expect(page.locator('.operation-logs')).toContainText('搜索: 张三');
  });

  test('batch operations', async ({ page }) => {
    // 选中多行数据
    await page.check('[data-testid="row-checkbox-1"]');
    await page.check('[data-testid="row-checkbox-2"]');
    
    // 点击批量删除
    await page.click('[data-testid="batch-delete-button"]');
    
    // 验证批量删除事件
    await expect(page.locator('.operation-logs')).toContainText('批量删除 2 条记录');
  });

  test('responsive design', async ({ page }) => {
    // 测试移动端布局
    await page.setViewportSize({ width: 375, height: 667 });
    
    // 验证工具栏在小屏幕上的布局
    await expect(page.locator('.toolbar-container')).toBeVisible();
    
    // 检查是否有响应式调整
    const toolbar = page.locator('.toolbar-container');
    await expect(toolbar).toHaveCSS('flex-direction', 'column');
  });

  test('accessibility features', async ({ page }) => {
    // 键盘导航测试
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="new-button"]')).toBeFocused();
    
    // ARIA 属性检查
    await expect(page.locator('[data-testid="new-button"]')).toHaveAttribute('role', 'button');
    
    // 颜色对比度检查
    const button = page.locator('[data-testid="new-button"]');
    const contrast = await page.evaluate(() => {
      // 实现颜色对比度检查逻辑
      return true; // 简化示例
    });
    expect(contrast).toBe(true);
  });
});
```

### 4. 性能测试

#### 渲染性能测试
```typescript
// tests/performance/toolbar.perf.test.ts
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import VTable from '@/components/common/Table/src/index.vue';

describe('Toolbar Performance', () => {
  it('renders within acceptable time', async () => {
    const start = performance.now();
    
    const wrapper = mount(VTable, {
      props: {
        columns: Array.from({ length: 20 }, (_, i) => ({
          field: `field${i}`,
          title: `Column ${i}`
        })),
        data: Array.from({ length: 1000 }, (_, i) => ({
          id: i,
          name: `Item ${i}`
        })),
        useNewToolbar: true,
        newToolbarConfig: {
          title: '性能测试',
          leftZone: { showNewButton: true },
          centerZone: { showSearch: true },
          rightZone: { showPagination: true }
        }
      }
    });
    
    await nextTick();
    
    const end = performance.now();
    const renderTime = end - start;
    
    // 渲染时间应该小于 100ms
    expect(renderTime).toBeLessThan(100);
  });

  it('handles large datasets efficiently', async () => {
    const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      value: Math.random() * 1000
    }));

    const wrapper = mount(VTable, {
      props: {
        columns: [
          { field: 'id', title: 'ID' },
          { field: 'name', title: 'Name' },
          { field: 'value', title: 'Value' }
        ],
        data: largeDataset,
        useNewToolbar: true,
        newToolbarConfig: {
          leftZone: {
            customActions: Array.from({ length: 10 }, (_, i) => ({
              id: `action${i}`,
              text: `Action ${i}`,
              onClick: () => {}
            }))
          }
        }
      }
    });

    // 验证内存使用没有显著增长
    const initialMemory = performance.memory?.usedJSHeapSize || 0;
    
    // 模拟一些操作
    for (let i = 0; i < 100; i++) {
      wrapper.vm.$forceUpdate();
      await nextTick();
    }
    
    const finalMemory = performance.memory?.usedJSHeapSize || 0;
    const memoryGrowth = finalMemory - initialMemory;
    
    // 内存增长应该小于 10MB
    expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024);
  });
});
```

### 5. 兼容性测试

#### 浏览器兼容性
```typescript
// tests/compatibility/browser.test.ts
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';

describe('Browser Compatibility', () => {
  it('works with different user agents', () => {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ];

    userAgents.forEach(userAgent => {
      Object.defineProperty(navigator, 'userAgent', {
        value: userAgent,
        configurable: true
      });

      const wrapper = mount(VTable, {
        props: {
          columns: [{ field: 'test', title: 'Test' }],
          data: [{ test: 'value' }],
          useNewToolbar: true
        }
      });

      expect(wrapper.exists()).toBe(true);
    });
  });
});
```

## 📊 测试报告

### 测试覆盖率目标
- **单元测试**: > 90%
- **集成测试**: > 80%
- **E2E 测试**: > 70%

### 关键指标
- **渲染时间**: < 100ms
- **内存使用**: < 50MB
- **首次交互**: < 200ms
- **可访问性评分**: > 95

### 测试自动化

#### CI/CD 集成
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: pnpm install
      - run: pnpm test:unit --coverage
      - uses: codecov/codecov-action@v3

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: pnpm install
      - run: pnpm build
      - run: pnpm test:e2e

  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: pnpm install
      - run: pnpm test:performance
```

### 质量门禁
- 所有测试必须通过
- 代码覆盖率不能下降
- 性能指标不能退化
- 可访问性检查通过

## 🐛 调试指南

### 常见问题排查

1. **工具栏不显示**
   ```typescript
   // 检查配置是否正确传递
   console.log('useNewToolbar:', props.useNewToolbar);
   console.log('newToolbarConfig:', props.newToolbarConfig);
   ```

2. **事件未触发**
   ```typescript
   // 检查事件监听器
   console.log('Emitted events:', wrapper.emitted());
   ```

3. **样式问题**
   ```typescript
   // 检查 CSS 类名
   const toolbar = wrapper.find('.toolbar-container');
   console.log('Classes:', toolbar.classes());
   ```

4. **性能问题**
   ```typescript
   // 监控渲染次数
   let renderCount = 0;
   const originalRender = wrapper.vm.$forceUpdate;
   wrapper.vm.$forceUpdate = () => {
     renderCount++;
     console.log('Render count:', renderCount);
     return originalRender.call(wrapper.vm);
   };
   ```

通过这套完整的测试体系，可以确保新工具栏系统的质量和稳定性。
