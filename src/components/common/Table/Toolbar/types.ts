// Toolbar/types.ts
import type { VariantProps } from 'class-variance-authority';
import type { ButtonVariants } from '@/components/ui/button';
import type { BadgeVariants } from '@/components/ui/badge';

// ========== 基础类型定义 ==========

/** 图标类型 - 支持 Lucide 图标或自定义图标 */
export type IconType = string | { name: string; library?: 'lucide' | 'tabler' | 'heroicons' };

/** 按钮尺寸类型 */
export type ButtonSize = NonNullable<ButtonVariants['size']>;

/** 按钮变体类型 */
export type ButtonVariant = NonNullable<ButtonVariants['variant']>;

/** 徽章变体类型 */
export type BadgeVariant = NonNullable<BadgeVariants['variant']>;

// ========== 动作按钮配置 ==========

/** 动作按钮基础配置 */
export interface BaseActionConfig {
  /** 唯一标识符 */
  id: string;
  /** 按钮文本 */
  text?: string;
  /** 图标 */
  icon?: IconType;
  /** 提示信息 */
  tooltip?: string;
  /** 是否禁用 */
  disabled?: boolean | ((tableApi: any, selectedRows: any[]) => boolean);
  /** 是否加载中 */
  loading?: boolean | ((tableApi: any, selectedRows: any[]) => boolean);
  /** 按钮变体 */
  variant?: ButtonVariant;
  /** 按钮尺寸 */
  size?: ButtonSize;
  /** 自定义类名 */
  className?: string;
  /** 是否仅在选中行时显示 */
  showOnlyWhenSelected?: boolean;
  /** 最小选中行数要求 */
  minSelectedRows?: number;
  /** 最大选中行数要求 */
  maxSelectedRows?: number;
}

/** 单个动作按钮配置 */
export interface ActionButtonConfig extends BaseActionConfig {
  /** 点击事件处理函数 */
  onClick: (tableApi: any, selectedRows: any[]) => void | Promise<void>;
  /** 确认提示配置 */
  confirm?: {
    title?: string;
    description?: string;
    confirmText?: string;
    cancelText?: string;
  };
}

/** 下拉菜单动作组配置 */
export interface ActionGroupConfig extends BaseActionConfig {
  /** 子动作列表 */
  children: ActionButtonConfig[];
  /** 触发方式 */
  trigger?: 'click' | 'hover';
}

/** 动作配置联合类型 */
export type ActionConfig = ActionButtonConfig | ActionGroupConfig;

// ========== 标题配置 ==========

/** 工具栏标题配置 */
export interface ToolbarTitleConfig {
  /** 标题图标 */
  icon?: IconType;
  /** 主标题 */
  main: string;
  /** 副标题 */
  sub?: string;
  /** 徽章配置 */
  badge?: string | {
    text: string;
    variant?: BadgeVariant;
    className?: string;
  };
  /** 自定义类名 */
  className?: string;
  /** 点击事件 */
  onClick?: () => void;
}

// ========== 搜索配置 ==========

/** 搜索功能配置 */
export interface SearchConfig {
  /** 是否显示搜索框 */
  show?: boolean;
  /** 搜索框占位符 */
  placeholder?: string;
  /** 搜索框图标 */
  icon?: IconType;
  /** 搜索防抖延迟（毫秒） */
  debounce?: number;
  /** 最小搜索字符数 */
  minLength?: number;
  /** 搜索事件处理函数 */
  onSearch?: (query: string, tableApi: any) => void | Promise<void>;
  /** 清空搜索事件 */
  onClear?: (tableApi: any) => void;
  /** 自定义类名 */
  className?: string;
  /** 高级搜索配置 */
  advanced?: {
    /** 是否显示高级搜索按钮 */
    show?: boolean;
    /** 高级搜索弹窗标题 */
    title?: string;
    /** 搜索字段配置 */
    fields?: Array<{
      key: string;
      label: string;
      type: 'text' | 'select' | 'date' | 'daterange';
      options?: Array<{ label: string; value: any }>;
    }>;
    /** 高级搜索事件 */
    onAdvancedSearch?: (filters: Record<string, any>, tableApi: any) => void;
  };
}

// ========== 分页配置 ==========

/** 分页配置 */
export interface PaginationConfig {
  /** 是否显示分页 */
  show?: boolean;
  /** 分页类型 */
  type?: 'simple' | 'full';
  /** 每页大小选项 */
  pageSizeOptions?: number[];
  /** 是否显示每页大小选择器 */
  showSizeChanger?: boolean;
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean;
  /** 是否显示总数信息 */
  showTotal?: boolean | ((total: number, range: [number, number]) => string);
  /** 自定义类名 */
  className?: string;
}

// ========== 内置功能按钮配置 ==========

/** 新增按钮配置 */
export interface NewButtonConfig {
  /** 是否显示 */
  show?: boolean;
  /** 按钮文本 */
  text?: string;
  /** 按钮图标 */
  icon?: IconType;
  /** 按钮变体 */
  variant?: ButtonVariant;
  /** 按钮尺寸 */
  size?: ButtonSize;
  /** 点击事件 */
  onClick?: (tableApi: any) => void | Promise<void>;
  /** 下拉菜单选项 */
  dropdown?: Array<{
    key: string;
    text: string;
    icon?: IconType;
    onClick: (tableApi: any) => void | Promise<void>;
  }>;
}

/** 批量删除按钮配置 */
export interface BatchDeleteConfig {
  /** 是否显示 */
  show?: boolean;
  /** 按钮文本 */
  text?: string;
  /** 按钮图标 */
  icon?: IconType;
  /** 按钮变体 */
  variant?: ButtonVariant;
  /** 点击事件 */
  onClick?: (tableApi: any, selectedRows: any[]) => void | Promise<void>;
  /** 确认提示配置 */
  confirm?: {
    title?: string;
    description?: string | ((count: number) => string);
    confirmText?: string;
    cancelText?: string;
  };
}

/** 刷新按钮配置 */
export interface RefreshButtonConfig {
  /** 是否显示 */
  show?: boolean;
  /** 按钮图标 */
  icon?: IconType;
  /** 点击事件 */
  onClick?: (tableApi: any) => void | Promise<void>;
  /** 是否显示加载状态 */
  showLoading?: boolean;
}

/** 全屏按钮配置 */
export interface FullscreenButtonConfig {
  /** 是否显示 */
  show?: boolean;
  /** 进入全屏图标 */
  enterIcon?: IconType;
  /** 退出全屏图标 */
  exitIcon?: IconType;
  /** 切换事件 */
  onToggle?: (isFullscreen: boolean, tableApi: any) => void;
}

/** 列设置按钮配置 */
export interface ColumnSettingsConfig {
  /** 是否显示 */
  show?: boolean;
  /** 按钮图标 */
  icon?: IconType;
  /** 点击事件 */
  onClick?: (tableApi: any) => void;
  /** 设置选项 */
  options?: {
    /** 是否允许拖拽排序 */
    sortable?: boolean;
    /** 是否允许重置 */
    resettable?: boolean;
    /** 是否显示密度设置 */
    showDensity?: boolean;
  };
}

// ========== 主配置类型 ==========

/** 优化后的工具栏配置 */
export interface ToolbarConfig {
  // ========== 标题区域 ==========
  /** 工具栏标题配置 */
  title?: string | ToolbarTitleConfig;

  // ========== 左侧功能区域 ==========
  /** 新增按钮配置 */
  newButton?: boolean | NewButtonConfig;
  
  /** 批量删除按钮配置 */
  batchDelete?: boolean | BatchDeleteConfig;
  
  /** 自定义左侧动作按钮 */
  leftActions?: ActionConfig[];

  // ========== 中央搜索区域 ==========
  /** 搜索功能配置 */
  search?: boolean | SearchConfig;

  // ========== 右侧功能区域 ==========
  /** 刷新按钮配置 */
  refresh?: boolean | RefreshButtonConfig;
  
  /** 全屏按钮配置 */
  fullscreen?: boolean | FullscreenButtonConfig;
  
  /** 列设置按钮配置 */
  columnSettings?: boolean | ColumnSettingsConfig;
  
  /** 分页配置 */
  pagination?: boolean | PaginationConfig;
  
  /** 自定义右侧动作按钮 */
  rightActions?: ActionConfig[];

  // ========== 全局配置 ==========
  /** 工具栏整体类名 */
  className?: string;
  
  /** 工具栏尺寸 */
  size?: 'sm' | 'md' | 'lg';
  
  /** 是否紧凑模式 */
  compact?: boolean;
  
  /** 响应式断点配置 */
  responsive?: {
    /** 小屏幕时隐藏的功能 */
    hideOnMobile?: ('search' | 'pagination' | 'columnSettings' | 'fullscreen')[];
    /** 平板时的行为 */
    tabletBehavior?: 'collapse' | 'wrap' | 'scroll';
  };
  
  /** 主题配置 */
  theme?: {
    /** 背景变体 */
    background?: 'default' | 'muted' | 'transparent';
    /** 边框样式 */
    border?: boolean | 'top' | 'bottom' | 'both';
    /** 阴影 */
    shadow?: boolean | 'sm' | 'md' | 'lg';
  };
}

// ========== 组件 Props 类型 ==========

/** 主工具栏组件属性 */
export interface MainToolbarProps {
  /** 表格 API 实例 */
  tableApi: any;
  /** 表格属性 */
  tableProps: Record<string, any>;
  /** 当前选中的行 */
  selectedRows: any[];
  /** 工具栏配置 */
  config?: ToolbarConfig;
  /** 分页相关属性 */
  totalItems?: number;
  currentPage?: number;
  pageSize?: number;
  /** 组件类名 */
  className?: string;
  /** 加载状态 */
  loading?: boolean;
}

// ========== 工具类型 ==========

/** 类型守卫：判断动作是否为按钮 */
export function isActionButton(action: ActionConfig): action is ActionButtonConfig {
  return 'onClick' in action && typeof action.onClick === 'function';
}

/** 类型守卫：判断动作是否为组 */
export function isActionGroup(action: ActionConfig): action is ActionGroupConfig {
  return 'children' in action && Array.isArray(action.children);
}

/** 获取按钮配置的辅助函数类型 */
export type GetButtonConfig<T> = T extends boolean 
  ? T extends true 
    ? {} 
    : never
  : T extends object 
    ? T 
    : never;
