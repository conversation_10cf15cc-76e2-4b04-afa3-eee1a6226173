# 表格工具栏集成指南

## 集成到现有 VTable

### 1. 替换现有工具栏

现有的 VTable 组件中有内置的工具栏实现，我们可以选择性地替换或扩展它。

#### 当前工具栏位置
```vue
<!-- src/components/common/Table/src/index.vue -->
<div class="flex justify-between items-center w-full" ref="proTableHeader">
  <div class="flex-1">
    <ButtonGroup :list="getProps.buttonList" />
  </div>
  <Search v-if="getProps.showSearch" />
  <div class="flex-1 flex justify-end">
    <Pagination v-if="getProps.showPagination" />
    <div class="toolbar flex items-center ml-2">
      <!-- 现有工具栏按钮 -->
    </div>
  </div>
</div>
```

#### 集成新工具栏

##### 方式一：完全替换（推荐）

在 VTable 组件中添加 props 来控制使用新工具栏：

```vue
<!-- src/components/common/Table/src/index.vue -->
<template>
  <div class="v-table flex-grow flex flex-col gap-2 w-full">
    <!-- 新工具栏 -->
    <TableToolbar
      v-if="useNewToolbar"
      :table-api="tableInstance"
      :table-props="getProps"
      :selected-rows="selectedRows"
      :config="toolbarConfig"
      :total-items="pagination.total"
      :current-page="pagination.current"
      :page-size="pagination.pageSize"
      @new="handleNew"
      @batch-delete="handleBatchDelete"
      @search="handleSearch"
      @refresh="reload"
      @fullscreen-toggle="toggleFull"
      @column-settings="openColumnsZoom"
      @page-change="handlePageChange"
    />
    
    <!-- 原有工具栏（条件渲染） -->
    <div
      v-else-if="getProps.buttonList || getProps.showSearch || getProps.showPagination"
      class="flex justify-between items-center w-full"
    >
      <!-- ...existing code... -->
    </div>
    
    <!-- 表格主体 -->
    <div class="table-wrap h-full">
      <!-- ...existing code... -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { TableToolbar } from './Toolbar';
import type { ToolbarConfig } from './Toolbar/types';

// 添加新的 props
interface VTableProps {
  // ...existing props...
  useNewToolbar?: boolean;
  toolbarConfig?: ToolbarConfig;
}

// 处理工具栏事件
const handleNew = () => {
  // 触发新增事件或调用默认逻辑
};

const handleBatchDelete = () => {
  // 处理批量删除
  const selected = getSelectedRows();
  // 执行删除逻辑
};

const handleSearch = (query: string) => {
  // 处理搜索
  // 可以集成到现有的搜索逻辑中
};

const handlePageChange = (page: number) => {
  pagination.current = page;
  reload();
};
</script>
```

##### 方式二：并存使用

用户可以选择使用哪种工具栏：

```vue
<VTable
  :use-new-toolbar="true"
  :toolbar-config="toolbarConfig"
  @register="register"
/>
```

### 2. 在具体页面中使用

#### 用户管理页面示例

```vue
<!-- src/views/sys/user/index.vue -->
<template>
  <div>
    <VTable 
      :use-new-toolbar="true"
      :toolbar-config="userToolbarConfig"
      @register="register" 
    />
  </div>
</template>

<script setup lang="ts">
import type { ToolbarConfig } from '@/components/common/Table/Toolbar/types';

const userToolbarConfig: ToolbarConfig = {
  title: {
    icon: 'lucide:users',
    main: '用户管理',
    sub: '系统用户信息管理',
    badge: {
      text: 'Active',
      variant: 'default'
    }
  },
  leftZone: {
    newButtonText: '添加用户',
    newButtonIcon: 'lucide:user-plus',
    customActions: [
      {
        id: 'import-users',
        text: '导入用户',
        icon: 'lucide:upload',
        tooltip: '从 Excel 导入用户数据',
        onClick: (tableApi, selectedRows) => {
          // 打开导入对话框
          openImportDialog();
        }
      },
      {
        id: 'export-users',
        text: '导出用户',
        icon: 'lucide:download',
        tooltip: '导出用户数据到 Excel',
        onClick: (tableApi, selectedRows) => {
          // 执行导出
          exportUsers(selectedRows);
        }
      },
      {
        id: 'reset-password',
        text: '重置密码',
        icon: 'lucide:key',
        tooltip: '重置选中用户的密码',
        variant: 'outline',
        disabled: (tableApi, selectedRows) => selectedRows.length === 0,
        isDropdownItem: true,
        onClick: (tableApi, selectedRows) => {
          batchResetPassword(selectedRows);
        }
      }
    ]
  },
  centerZone: {
    searchPlaceholder: '搜索用户名、邮箱、手机号...',
    onSearch: (query, tableApi) => {
      // 可以自定义搜索逻辑
      // 或者让工具栏触发默认搜索事件
    }
  },
  rightZone: {
    onRefresh: (tableApi) => {
      // 自定义刷新逻辑
      tableApi.reload();
      toast.success('数据已刷新');
    },
    customActions: [
      {
        id: 'user-stats',
        icon: 'lucide:bar-chart',
        tooltip: '用户统计',
        onClick: (tableApi, selectedRows) => {
          openUserStatsDialog();
        }
      }
    ]
  }
};

// 事件处理函数
const openImportDialog = () => {
  // 实现导入对话框
};

const exportUsers = (selectedRows: any[]) => {
  // 实现导出功能
};

const batchResetPassword = (selectedRows: any[]) => {
  // 实现批量重置密码
};

const openUserStatsDialog = () => {
  // 显示用户统计信息
};
</script>
```

### 3. 渐进式迁移策略

#### 第一阶段：兼容现有 API
- 保持现有 VTable 的 API 不变
- 新增 `useNewToolbar` 和 `toolbarConfig` props
- 默认使用原有工具栏

#### 第二阶段：逐步迁移
- 在新页面中使用新工具栏
- 逐步迁移现有页面
- 收集用户反馈

#### 第三阶段：完全替换
- 当新工具栏稳定后，可以考虑完全替换
- 提供迁移工具协助配置转换

### 4. 配置映射

为了便于从旧配置迁移到新配置，可以提供一个映射函数：

```typescript
// src/components/common/Table/Toolbar/utils/configMapper.ts
export function mapLegacyConfig(legacyConfig: any): ToolbarConfig {
  return {
    leftZone: {
      showNewButton: true,
      customActions: legacyConfig.buttonList?.map((btn: any) => ({
        id: btn.key || btn.name,
        text: btn.name,
        icon: btn.icon,
        onClick: btn.onClick
      })) || []
    },
    centerZone: {
      showSearch: legacyConfig.showSearch
    },
    rightZone: {
      showPagination: legacyConfig.showPagination,
      showRefreshButton: legacyConfig.toolbarConfig?.isReload,
      showFullscreenButton: legacyConfig.toolbarConfig?.isFull,
      showColumnSettingsButton: legacyConfig.toolbarConfig?.isZoom
    }
  };
}
```

### 5. 使用示例

```vue
<template>
  <VTable
    :use-new-toolbar="true"
    :toolbar-config="toolbarConfig"
    @register="register"
  />
</template>

<script setup lang="ts">
import { useTable } from '@/components/common/Table';
import type { ToolbarConfig } from '@/components/common/Table/Toolbar/types';

const toolbarConfig: ToolbarConfig = {
  title: '订单管理',
  leftZone: {
    customActions: [
      {
        id: 'export',
        text: '导出',
        icon: 'lucide:download',
        onClick: () => exportData()
      }
    ]
  }
};

const [register] = useTable({
  columns: [],
  fetchApi: getOrderList
});
</script>
```

这样就可以平滑地将新工具栏集成到现有系统中，既保持了向后兼容性，又提供了更强大和灵活的工具栏功能。
