// Toolbar/components/ToolbarTitle.vue
<template>
  <div :class="cn('flex items-center gap-2', className)">
    <Icon
      v-if="icon"
      :icon="icon"
      class="h-5 w-5 text-muted-foreground"
    />
    <div class="flex flex-col">
      <div class="flex items-center gap-2">
        <h2 class="text-base font-medium text-foreground">{{ main }}</h2>
        <Badge
          v-if="badgeConfig"
          :variant="badgeConfig.variant"
          :class="badgeConfig.className"
        >
          {{ badgeConfig.text }}
        </Badge>
      </div>
      <p
        v-if="sub"
        class="text-sm text-muted-foreground"
      >
        {{ sub }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Badge, type BadgeVariants } from '@/components/ui/badge';
import { Icon } from '@iconify/vue';
import { cn } from '@/lib/utils';

interface Props {
  icon?: string;
  main: string;
  sub?: string;
  badge?: string | { text: string; variant?: BadgeVariants['variant']; className?: string };
  className?: string;
}

const props = defineProps<Props>();

const badgeConfig = computed(() => {
  if (!props.badge) return null;
  
  if (typeof props.badge === 'string') {
    return {
      text: props.badge,
      variant: 'default' as const,
      className: ''
    };
  }
  
  return {
    text: props.badge.text,
    variant: props.badge.variant || 'default',
    className: props.badge.className || ''
  };
});
</script>
