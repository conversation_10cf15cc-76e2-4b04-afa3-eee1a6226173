// Toolbar/components/SimplePagination.vue
<template>
  <div :class="cn('flex items-center gap-2 text-sm text-muted-foreground', className)">
    <span class="flex items-center gap-1">
      <input 
        v-model.number="editableStart" 
        @blur="handleStartBlur"
        @keyup.enter="handleStartBlur"
        class="outline-none bg-transparent text-center border-b border-slate-200 w-fit min-w-[30px] max-w-[50px] text-sm no-spinner"
        type="number"
        :min="1"
        :max="total"
      />
      -
      <input 
        v-model.number="editableEnd" 
        @blur="handleEndBlur"
        @keyup.enter="handleEndBlur"
        class="outline-none bg-transparent text-center border-b border-slate-200 w-fit min-w-[30px] max-w-[50px] text-sm no-spinner"
        type="number"
        :min="1"
        :max="total"
      />
      / 共{{ total }}条
    </span>
    <div class="flex items-center gap-1">
      <ActionButton
        icon="lucide:chevron-left"
        tooltip="上一页"
        :disabled="start <= 1"
        size="sm"
        variant="outline"
        @click="handlePrevPage"
      />
      <ActionButton
        icon="lucide:chevron-right"
        tooltip="下一页"
        :disabled="end >= total"
        size="sm"
        variant="outline"
        @click="handleNextPage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import ActionButton from './ActionButton.vue';
import { cn } from '@/lib/utils';

// 分页相关的接口定义
export interface PageQuery {
  offset: number; // 从第几条开始（0-based）
  limit: number;  // 取多少条
}

interface Props {
  start: number;  // 当前显示的起始记录编号（1-based）
  limit: number;  // 每页显示的记录数
  total: number;  // 总记录数
  className?: string;
}

const props = withDefaults(defineProps<Props>(), {
  start: 1,
  limit: 20,
  total: 0
});

const emits = defineEmits<{
  pageChange: [query: PageQuery];
}>();

// 可编辑的 start 和 end 值
const editableStart = ref(props.start);
const editableEnd = ref(0);

// 存储上一次有效的值，用于回滚
const lastValidStart = ref(props.start);
const lastValidEnd = ref(0);

// 监听props变化，确保组件状态与外部同步
watch(
  () => [props.start, props.limit, props.total],
  () => {
    editableStart.value = props.start;
    editableEnd.value = Math.min(props.start + props.limit - 1, props.total);
    lastValidStart.value = props.start;
    lastValidEnd.value = Math.min(props.start + props.limit - 1, props.total);
  },
  { immediate: true }
);

// 计算当前显示的结束记录编号
const end = computed(() => {
  const calculatedEnd = props.start + props.limit - 1;
  return Math.min(calculatedEnd, props.total);
});

// 计算是否可以上一页
const canGoPrev = computed(() => props.start > 1);

// 计算是否可以下一页
const canGoNext = computed(() => end.value < props.total);

// 处理 start 输入框失焦
const handleStartBlur = () => {
  // 验证输入值
  if (isNaN(editableStart.value) || editableStart.value < 1) {
    editableStart.value = lastValidStart.value;
    return;
  }
  
  if (editableStart.value > props.total) {
    editableStart.value = props.total;
  }
  
  // 确保 end 不小于 start
  if (editableEnd.value < editableStart.value) {
    editableEnd.value = Math.min(editableStart.value + props.limit - 1, props.total);
  }
  
  // 触发页面变更
  triggerPageChange();
};

// 处理 end 输入框失焦
const handleEndBlur = () => {
  // 验证输入值
  if (isNaN(editableEnd.value) || editableEnd.value < 1) {
    editableEnd.value = lastValidEnd.value;
    return;
  }
  
  if (editableEnd.value > props.total) {
    editableEnd.value = props.total;
  }
  
  // 确保 end 不小于 start
  if (editableEnd.value < editableStart.value) {
    editableEnd.value = editableStart.value;
  }
  
  // 触发页面变更
  triggerPageChange();
};

// 触发页面变更事件
const triggerPageChange = () => {
  console.log('triggerPageChange - current values:', {
    editableStart: editableStart.value,
    editableEnd: editableEnd.value,
    lastValidStart: lastValidStart.value,
    lastValidEnd: lastValidEnd.value
  });
  
  if (editableStart.value !== lastValidStart.value || editableEnd.value !== lastValidEnd.value) {
    const newLimit = editableEnd.value - editableStart.value + 1;
    const query: PageQuery = {
      offset: editableStart.value - 1, // 转换为 0-based
      limit: newLimit
    };
    
    console.log('emitting pageChange:', query);
    
    lastValidStart.value = editableStart.value;
    lastValidEnd.value = editableEnd.value;
    
    emits('pageChange', query);
  }
};

// 处理上一页
const handlePrevPage = () => {
  if (!canGoPrev.value) return;
  
  const newStart = Math.max(1, props.start - props.limit);
  const query: PageQuery = {
    offset: newStart - 1, // 转换为 0-based
    limit: props.limit
  };
  
  emits('pageChange', query);
};

// 处理下一页
const handleNextPage = () => {
  if (!canGoNext.value) return;
  
  const newStart = props.start + props.limit;
  const query: PageQuery = {
    offset: newStart - 1, // 转换为 0-based
    limit: props.limit
  };
  console.log('pageChange', query)
  emits('pageChange', query);
};

// 暴露工具函数
const getCurrentQuery = (): PageQuery => {
  console.log('getCurrentQuery called, returning:', {
    offset: props.start - 1,
    limit: props.limit
  });
  return {
    offset: props.start - 1,
    limit: props.limit
  };
};

defineExpose({
  getCurrentQuery
});
</script>

<style scoped>
/* 隐藏数字输入框的上下箭头 */
.no-spinner::-webkit-outer-spin-button,
.no-spinner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
.no-spinner[type=number] {
  -moz-appearance: textfield;
}
</style>
