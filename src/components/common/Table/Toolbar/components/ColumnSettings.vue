// Toolbar/components/ColumnSettings.vue
<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <ActionButton
        :icon="icon"
        :tooltip="tooltip"
        size="sm"
        variant="outline"
      />
    </DropdownMenuTrigger>
    <DropdownMenuContent class="min-w-[200px]">
      <DropdownMenuLabel>列配置</DropdownMenuLabel>
      <DropdownMenuSeparator />
      <!-- 占位内容 - 未来实现列配置功能 -->
      <DropdownMenuItem disabled>
        <Icon icon="lucide:eye" class="mr-2 h-4 w-4" />
        显示/隐藏列
      </DropdownMenuItem>
      <DropdownMenuItem disabled>
        <Icon icon="lucide:pin" class="mr-2 h-4 w-4" />
        固定列
      </DropdownMenuItem>
      <DropdownMenuItem disabled>
        <Icon icon="lucide:arrows-up-down" class="mr-2 h-4 w-4" />
        列排序
      </DropdownMenuItem>
      <DropdownMenuSeparator />
      <DropdownMenuItem disabled>
        <Icon icon="lucide:rotate-ccw" class="mr-2 h-4 w-4" />
        重置配置
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Icon } from '@iconify/vue';
import ActionButton from './ActionButton.vue';

interface Props {
  icon?: string;
  tooltip?: string;
}

withDefaults(defineProps<Props>(), {
  icon: 'lucide:settings-2',
  tooltip: '列配置'
});

const emits = defineEmits<{
  columnSettings: [];
}>();

// 占位 - 未来实现实际的列配置功能
const handleColumnSettings = () => {
  emits('columnSettings');
};
</script>
