// Toolbar/components/ActionButton.vue
<template>
  <Tooltip v-if="tooltip" :delay-duration="200">
    <TooltipTrigger as-child>
      <Button
        :variant="variant"
        :size="size"
        :disabled="disabled"
        :class="cn('', className)"
        @click="handleClick"
      >
        <Icon
          v-if="icon && !text"
          :icon="icon"
          class="h-4 w-4"
        />
        <template v-if="text">
          <Icon
            v-if="icon"
            :icon="icon"
            class="h-4 w-4 mr-1"
          />
          {{ text }}
        </template>
      </Button>
    </TooltipTrigger>
    <TooltipContent>{{ tooltip }}</TooltipContent>
  </Tooltip>
  
  <Button
    v-else
    :variant="variant"
    :size="size"
    :disabled="disabled"
    :class="cn('', className)"
    @click="handleClick"
  >
    <Icon
      v-if="icon && !text"
      :icon="icon"
      class="h-4 w-4"
    />
    <template v-if="text">
      <Icon
        v-if="icon"
        :icon="icon"
        class="h-4 w-4 mr-1"
      />
      {{ text }}
    </template>
  </Button>
</template>

<script setup lang="ts">
import { Button, type ButtonVariants } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Icon } from '@iconify/vue';
import { cn } from '@/lib/utils';

interface Props {
  icon?: string;
  text?: string;
  tooltip?: string;
  disabled?: boolean;
  loading?: boolean;
  variant?: ButtonVariants['variant'];
  size?: ButtonVariants['size'];
  className?: string;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'outline',
  size: 'sm',
  disabled: false,
  loading: false
});

const emits = defineEmits<{
  click: [event: MouseEvent];
}>();

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emits('click', event);
  }
};
</script>
