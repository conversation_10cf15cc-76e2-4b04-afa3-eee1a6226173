// Toolbar/components/SearchInput.vue
<template>
  <div class="relative">
    <Icon
      icon="lucide:search"
      class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
    />
    <Input
      :model-value="modelValue"
      :placeholder="placeholder"
      :class="cn('pl-9', className)"
      @update:model-value="$emit('update:modelValue', $event)"
      @keydown.enter="handleSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { Input } from '@/components/ui/input';
import { Icon } from '@iconify/vue';
import { cn } from '@/lib/utils';

interface Props {
  modelValue: string;
  placeholder?: string;
  className?: string;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索...'
});

const emits = defineEmits<{
  'update:modelValue': [value: string];
  search: [query: string];
}>();

const handleSearch = () => {
  emits('search', props.modelValue);
};
</script>
