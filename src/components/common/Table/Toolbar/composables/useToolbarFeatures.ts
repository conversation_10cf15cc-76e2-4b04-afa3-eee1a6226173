// Toolbar/composables/useToolbarFeatures.ts
import { computed, ref, type Ref } from 'vue';
import type { ActionButtonConfig } from '../types';

export function useToolbarFeatures(
  tableApi: any,
  selectedRows: Ref<any[]>,
  tableProps: Record<string, any>
) {
  // 计算批量删除按钮的禁用状态
  const batchDeleteDisabled = computed(() => {
    return tableProps.checkType !== 'checkbox' || selectedRows.value.length === 0;
  });

  // 防抖处理按钮点击
  const debounceMap = new Map<string, number>();
  
  const debounceAction = (actionId: string, callback: Function, delay = 300) => {
    const existingTimer = debounceMap.get(actionId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }
    
    const timer = window.setTimeout(() => {
      callback();
      debounceMap.delete(actionId);
    }, delay);
    
    debounceMap.set(actionId, timer);
  };

  // 处理操作按钮点击
  const handleActionClick = (action: ActionButtonConfig) => {
    if (action.disabled) return;
    
    debounceAction(action.id, () => {
      action.onClick(tableApi, selectedRows.value);
    });
  };

  // 获取选中行数据
  const getSelectedRowsData = () => {
    return selectedRows.value;
  };

  // 刷新表格数据
  const refreshTable = () => {
    if (tableApi?.reload) {
      tableApi.reload();
    }
  };

  // 清除选中状态
  const clearSelection = () => {
    if (tableApi?.clearCheckboxRow) {
      tableApi.clearCheckboxRow();
    }
  };

  return {
    batchDeleteDisabled,
    debounceAction,
    handleActionClick,
    getSelectedRowsData,
    refreshTable,
    clearSelection
  };
}
