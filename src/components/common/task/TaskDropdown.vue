<template>
  <div>
    <Popover v-model:open="isOpen">
      <!-- 审批任务按钮触发器 -->
      <PopoverTrigger as-child>
        <Button
          variant="ghost"
          size="icon"
          class="relative w-9 h-9 rounded-full text-white hover:text-theme-accent hover:bg-white border border-white/20"
        >
          <ListTodo class="h-5 w-5" />

          <!-- 未完成任务红点 (来源于websocket) -->
          <Badge
            v-if="uncompletedCount > 0"
            variant="destructive"
            class="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
          >
            {{ uncompletedCount > 99 ? '99+' : uncompletedCount }}
          </Badge>
        </Button>
      </PopoverTrigger>

      <!-- 审批任务下拉面板 -->
      <PopoverContent class="w-80 p-0" side="bottom" align="end">
        <!-- 头部 -->
        <div class="flex items-center justify-between p-4 border-b">
          <div class="flex items-center space-x-2">
            <h3 class="font-semibold">待审批任务</h3>
            <!-- 连接状态 -->
          </div>
          <div class="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              @click="refreshApprovalList"
              :disabled="isRefreshing"
              class="text-xs"
            >
              <RotateCcw :class="['h-3 w-3', isRefreshing && 'animate-spin']" />
              刷新
            </Button>
          </div>
        </div>

        <!-- 审批任务列表 -->
        <ScrollArea class="h-96">
          <div v-if="isLoading" class="p-8 text-center">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"
            ></div>
            <p class="text-sm text-muted-foreground">加载中...</p>
          </div>

          <div
            v-else-if="pendingApprovals.length === 0"
            class="p-8 text-center"
          >
            <InboxIcon class="mx-auto h-12 w-12 text-muted-foreground mb-2" />
            <p class="text-sm text-muted-foreground">暂无待审批任务</p>
          </div>

          <div v-else class="space-y-1">
            <div
              v-for="approval in pendingApprovals"
              :key="approval.id"
              class="p-3 hover:bg-accent cursor-pointer border-b last:border-b-0"
              @click="handleApprovalClick(approval)"
            >
              <!-- 审批任务项内容 - 上下布局 -->
              <div class="space-y-3">
                <!-- 第一行：标题和状态 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <!-- 状态指示点 -->
                    <div
                      class="w-2 h-2 rounded-full flex-shrink-0 bg-primary"
                    />
                    <p class="text-xs font-medium">
                      审批任务#单号:{{ approval.relation_no }}
                    </p>
                  </div>
                  <Badge variant="default" class="text-xs font-light">
                    {{ approval.approve_status }}
                  </Badge>
                </div>

                <!-- 第二行：审批人和备注 -->
                <div class="text-xs flex text-muted-foreground">
                  <div class="flex-1">
                    <p>审批人：{{ approval.approve.name }}</p>
                    <p class="mt-1">
                      <Badge variant="secondary" class="text-xs">
                        {{ approval.document_type }}
                      </Badge>
                    </p>
                  </div>
                  <div>
                    <span class="text-xs text-muted-foreground">
                      {{ formatTime(approval.created_at) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>

        <!-- 底部 -->
        <div class="p-3 border-t">
          <Button
            variant="ghost"
            size="sm"
            @click="viewAllApprovals"
            class="w-full text-xs"
          >
            查看全部审批任务
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { getWorkflowInstanceLog } from '@/api/workflow/workflow_instance'
import type {
  WorkflowInstanceLog,
  WorkflowInstanceLogResponse,
} from '@/views/workflow/types'

import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'
import { ListTodo, InboxIcon, RotateCcw } from 'lucide-vue-next'

const router = useRouter()

// 响应式数据
const isOpen = ref(false)
const isLoading = ref(false)
const isRefreshing = ref(false)
const approvalList = ref<WorkflowInstanceLog[]>([])

// 本地任务通知状态
const uncompletedCount = ref(0)

// 只显示状态为"审批中"的数据
const pendingApprovals = computed(() =>
  approvalList.value.filter((item) => item.approve_status === '审批中')
)
// 监听下拉菜单是否打开，如果打开则加载审批任务列表
watch(isOpen, (newVal) => {
  if (newVal) {
    loadApprovalList()
  }
})

//监听路由变化，路由变化则刷新一下任务的数据
watch(
  () => router.currentRoute.value.path,
  () => {
    loadApprovalList()
  }
)

// 加载审批任务列表
const loadApprovalList = async () => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  try {
    isLoading.value = true
    const response = await getWorkflowInstanceLog({
      filters: {
        conditions: [
          {
            field: 'approve_status',
            op: 'eq',
            value: '审批中',
          },
          {
            field: 'approve_id',
            op: 'eq',
            value: userInfo.id,
          },
        ],
      },
      offset: 0,
      limit: 100,
    })

    approvalList.value = response.items || []
    uncompletedCount.value = approvalList.value.length
  } catch (error) {
    console.error('加载审批任务列表失败:', error)
    approvalList.value = []
  } finally {
    isLoading.value = false
  }
}

// 刷新审批任务列表
const refreshApprovalList = async () => {
  isRefreshing.value = true
  await loadApprovalList()
  isRefreshing.value = false
}

// 处理审批任务点击
const handleApprovalClick = (approval: WorkflowInstanceLog) => {
  // 触发全局审批弹窗事件
  window.dispatchEvent(
    new CustomEvent('approval-show-main-modal', {
      detail: { approval },
    })
  )

  // 关闭下拉菜单
  isOpen.value = false
}

// 查看全部审批任务
const viewAllApprovals = () => {
  router.push('/sys/task')
  isOpen.value = false
}

// 时间格式化
const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
    })
  }
}

// 组件挂载时加载数据和初始化
onMounted(async () => {
  // 加载审批任务列表
  await loadApprovalList()
})
</script>
