<template>
    <button
        @click="toggleTheme"
        class="inline-flex items-center justify-center rounded-full text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 w-9"
    >
        <Icon
            :icon="theme === 'dark' ? 'lucide:sun' : 'lucide:moon'"
            class="h-5 w-5 text-white hover:text-theme-accent"
        />
        <span class="sr-only">Toggle theme</span>
    </button>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue';
import { useThemeStore } from '@/store/theme';

const { theme, toggleTheme } = useThemeStore();
</script>
