<template>
    <div>
        <!-- 主模态框 -->
        <Dialog :open="visible" @update:open="handleOpenChange">
            <DialogContent
                class="max-w-4xl w-[95vw] h-[85vh] flex flex-col p-0"
            >
                <!-- 头部 -->
                <DialogHeader class="px-6 py-4 border-b">
                    <DialogTitle class="text-lg font-semibold">
                        {{ approvalData?.document_type }}审批任务
                    </DialogTitle>
                    <div class="text-sm text-muted-foreground">
                        {{ approvalData?.approve_status }}
                    </div>
                </DialogHeader>

                <!-- 内容区域 -->
                <div class="flex-1 flex flex-col overflow-hidden">
                    <!-- 上半部分：单据信息 -->
                    <div class="flex-1 overflow-y-auto">
                        <Card class="m-6 mb-3">
                            <CardHeader class="pb-4">
                                <div class="flex items-center justify-between">
                                    <CardTitle class="text-lg"
                                        >单据信息</CardTitle
                                    >
                                    <Button
                                        @click="viewDocumentDetail"
                                        variant="outline"
                                        size="sm"
                                        class="ml-auto"
                                    >
                                        <Icon
                                            icon="lucide:external-link"
                                            class="w-4 h-4 mr-2"
                                        />
                                        查看详情
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <!-- 关键信息三列布局 -->
                                <div
                                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                                >
                                    <div
                                        v-for="(info, index) in documentInfo"
                                        :key="index"
                                        class="space-y-2"
                                    >
                                        <div
                                            class="text-sm text-muted-foreground"
                                        >
                                            {{ info.label }}
                                        </div>
                                        <div class="font-medium">
                                            {{ info.value }}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <!-- 下半部分：审批表单 -->
                    <div class="border-t bg-muted/30">
                        <Card class="m-6 mt-3">
                            <CardHeader class="pb-4">
                                <CardTitle class="text-lg">审批意见</CardTitle>
                            </CardHeader>
                            <CardContent class="space-y-4">
                                <!-- 审批意见输入框 -->
                                <div class="space-y-2">
                                    <Label for="approval-comment"
                                        >请输入审批意见</Label
                                    >
                                    <Textarea
                                        id="approval-comment"
                                        v-model="approvalComment"
                                        placeholder="请输入您的审批意见..."
                                        class="min-h-[100px] resize-none"
                                        :maxlength="500"
                                    />
                                    <div
                                        class="text-xs text-muted-foreground text-right"
                                    >
                                        {{ approvalComment.length }}/500
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="flex justify-end space-x-3 pt-4">
                                    <Button
                                        @click="handleReject"
                                        variant="destructive"
                                        :disabled="loading"
                                        class="min-w-[100px]"
                                    >
                                        <Icon
                                            v-if="
                                                loading &&
                                                actionType === 'reject'
                                            "
                                            icon="lucide:loader-2"
                                            class="w-4 h-4 mr-2 animate-spin"
                                        />
                                        <Icon
                                            v-else
                                            icon="lucide:x-circle"
                                            class="w-4 h-4 mr-2"
                                        />
                                        拒绝
                                    </Button>
                                    <Button
                                        @click="handleApprove"
                                        :disabled="loading"
                                        class="min-w-[100px]"
                                    >
                                        <Icon
                                            v-if="
                                                loading &&
                                                actionType === 'approve'
                                            "
                                            icon="lucide:loader-2"
                                            class="w-4 h-4 mr-2 animate-spin"
                                        />
                                        <Icon
                                            v-else
                                            icon="lucide:check-circle"
                                            class="w-4 h-4 mr-2"
                                        />
                                        同意
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </DialogContent>
        </Dialog>

        <!-- 全局浮动审批表单 -->
        <div
            v-if="globalFloatingForm && approvalData"
            class="fixed bottom-2 left-1/2 transform -translate-x-1/2 z-[9999] w-96 max-w-[calc(100vw-2rem)]"
        >
            <Card class="shadow-2xl border-2">
                <CardHeader class="pb-1">
                    <div class="flex items-center justify-between">
                        <div class="flex-1 min-w-0">
                            <CardTitle class="text-base truncate">
                                {{ approvalData.title }}
                            </CardTitle>
                            <p class="text-sm text-muted-foreground mt-1">
                                审批意见
                            </p>
                        </div>
                        <div class="flex items-center space-x-2 ml-2">
                            <!-- 返回按钮 -->
                            <Button
                                @click="backToMainModalFromGlobal"
                                variant="ghost"
                                size="sm"
                                class="h-8 w-8 p-0"
                                title="返回完整审批窗口"
                            >
                                <Icon
                                    icon="lucide:maximize-2"
                                    class="w-4 h-4"
                                />
                            </Button>
                            <!-- 关闭按钮 -->
                            <Button
                                @click="closeGlobalFloatingForm"
                                variant="ghost"
                                size="sm"
                                class="h-8 w-8 p-0"
                                title="关闭审批窗口"
                            >
                                <Icon icon="lucide:x" class="w-4 h-4" />
                            </Button>
                        </div>
                    </div>
                </CardHeader>
                <CardContent class="space-y-4">
                    <!-- 审批意见输入框 -->
                    <div class="space-y-2">
                        <Textarea
                            v-model="approvalComment"
                            placeholder="请输入您的审批意见..."
                            class="min-h-[80px] resize-none text-sm"
                            :maxlength="500"
                        />
                        <div class="text-xs text-muted-foreground text-right">
                            {{ approvalComment.length }}/500
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end space-x-2">
                        <Button
                            @click="handleReject"
                            variant="destructive"
                            size="sm"
                            :disabled="loading"
                            class="min-w-[80px]"
                        >
                            <Icon
                                v-if="loading && actionType === 'reject'"
                                icon="lucide:loader-2"
                                class="w-3 h-3 mr-1 animate-spin"
                            />
                            <Icon
                                v-else
                                icon="lucide:x-circle"
                                class="w-3 h-3 mr-1"
                            />
                            拒绝
                        </Button>
                        <Button
                            @click="handleApprove"
                            size="sm"
                            :disabled="loading"
                            class="min-w-[80px]"
                        >
                            <Icon
                                v-if="loading && actionType === 'approve'"
                                icon="lucide:loader-2"
                                class="w-3 h-3 mr-1 animate-spin"
                            />
                            <Icon
                                v-else
                                icon="lucide:check-circle"
                                class="w-3 h-3 mr-1"
                            />
                            同意
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>

        <!-- 页脚浮动按钮 -->
        <div
            v-if="minimizedModals.length > 0"
            class="fixed bottom-4 left-4 z-50 flex flex-col space-y-2"
        >
            <div
                v-for="modal in minimizedModals"
                :key="modal.id"
                class="group relative"
            >
                <Button
                    @click="restoreModal(modal)"
                    variant="default"
                    class="shadow-lg hover:shadow-xl transition-all duration-200 pr-8 max-w-[300px]"
                >
                    <Icon icon="lucide:file-text" class="w-4 h-4 mr-2" />
                    <span class="truncate">{{ modal.title }}</span>
                </Button>
                <!-- 关闭按钮 -->
                <Button
                    @click="closeMinimizedModal(modal.id)"
                    variant="ghost"
                    size="sm"
                    class="absolute -top-1 -right-1 w-5 h-5 p-0 bg-destructive text-destructive-foreground hover:bg-destructive/90 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                >
                    <Icon icon="lucide:x" class="w-3 h-3" />
                </Button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Icon } from '@iconify/vue';
import { checkApproval } from '@/api/workflow/workflow_instance';
import { useRequest } from 'alova/client';
import { useToast } from '@/components/ui/toast/use-toast';
import type { AlertFunction } from '@/components/common/AlertDialog/type.ts';
import { inject } from 'vue';
import { getOrder } from '@/api/sal/order/index';
import { getDelivery } from '@/api/sal/delivery/index';
import { getWorkflowInstance } from '@/api/workflow/workflow_instance';
import dayjs from 'dayjs';

// import { useApprovalStore } from '@/store/approval';

const toast = useToast();
const alertDialog = inject<AlertFunction>('alertDialog');
// 审批数据接口
interface ApprovalData {
    id: string;
    title: string;
    approve_status?: string;
    document_type: string;
    business_type?: string; // 添加业务类型属性
    business_id: string;
    relation_id?: string | number; // 添加关联ID属性
    document_info: Record<string, any>;
    created_at: string;
    priority: 'low' | 'normal' | 'high' | 'urgent';
}

// 最小化模态框接口
interface MinimizedModal {
    id: string;
    title: string;
    data: ApprovalData;
}

const router = useRouter();
const route = useRoute();
// const approvalStore = useApprovalStore();

// 响应式数据
const visible = ref(false);
const approvalData = ref<ApprovalData | null>(null);
const approvalComment = ref('');
const loading = ref(false);
const actionType = ref<'approve' | 'reject' | null>(null);
const minimizedModals = ref<MinimizedModal[]>([]);
const documentInfo = ref([]);
// 全局浮动表单状态
const globalFloatingForm = ref(false);

const routeMap: Record<string, string> = {
    销售订单: '/sal/order/edit',
    销售送货: '/sal/delivery/edit',
    销售报价: '/sal/quotation/edit',
    采购申请: '/scr/purchase_apply/edit',
    入库: '/wms/inbound/detail',
    出库: '/wms/outbound/detail',
    收款单: '/fin/collection/edit',
    采购收货: '/wms/purchase-receipt/detail',
    销售合同: '/sal/contract',
    付款单: '/fin/payment/detail',
    流程卡: '/mes/flow-card/detail',
    采购订单: '/scr/purchase/detail',
    工艺路线: '/mes/workmanship-route/detail',
};

// 方法
const showApprovalModal = async (
    data: ApprovalData,
    options?: { clearComment?: boolean }
) => {
    try {
        const res = await getWorkflowInstance(Number(data.instance_id));
        console.log('🚀🚀🚀🚀🚀 ~ res:', res);
        console.log('showApprovalModal', data)
        documentInfo.value = [
            {label: '业务类型', value: data?.document_type},
            {label: '业务单号', value: data?.relation_no},
            {label: '发起人', value: data?.initiator?.name},
            {label: '发起时间', value: dayjs(data?.initiate_time)?.format('YYYY-MM-DD HH:mm:ss') },
        ]

        if (res) {
            // documentInfo.value = Object.entries(res.relation_model).map(
            //     ([key, value]) => {
            //         if (key.includes('日期')) {
            //             let tempValue = '';
            //             if (value && typeof value === 'string') {
            //                 // 处理ISO格式的日期字符串
            //                 const date = new Date(value);
            //                 if (!isNaN(date.getTime())) {
            //                     // 格式化为 YYYY-MM-DD HH:mm:ss
            //                     const year = date.getFullYear();
            //                     const month = String(
            //                         date.getMonth() + 1
            //                     ).padStart(2, '0');
            //                     const day = String(date.getDate()).padStart(
            //                         2,
            //                         '0'
            //                     );
            //                     tempValue = `${year}-${month}-${day}`;
            //                 }
            //             }
            //             return {
            //                 label: key,
            //                 value: tempValue,
            //             };
            //         } else {
            //             return {
            //                 label: key,
            //                 value: value,
            //             };
            //         }
            //     }
            // );
        }
    } catch (error) {
        console.error('获取单据详情失败:', error);
        // 即使API调用失败也继续显示审批模态框
    }

    approvalData.value = data;
    // 默认清空评论（新审批任务），除非明确指定保持评论（切换场景）
    if (options?.clearComment !== false) {
        approvalComment.value = '';
    }
    visible.value = true;
};

const handleOpenChange = (open: boolean) => {
    if (!open) {
        visible.value = false;
    }
};

const viewDocumentDetail = () => {
    if (!approvalData.value) return;

    // 显示浮动审批表单
    globalFloatingForm.value = true;

    // 关闭主模态框
    visible.value = false;

    // 跳转到对应的单据详情页（演示模式下跳转到演示页面）
    const route = routeMap[approvalData.value.document_type];
    if (route) {
        // 使用全局事件通知页面将要显示浮动审批表单
        window.dispatchEvent(
            new CustomEvent('approval-floating-show', {
                detail: {
                    approvalData: approvalData.value,
                    targetRoute: route,
                },
            })
        );

        router.push({
            path: route,
            query: {
                id: approvalData.value.relation_id,
                approval: 'floating', // 添加标识参数
            },
        });
    }
};

const minimizeModal = () => {
    if (!approvalData.value) return;

    // 添加到最小化列表
    const minimized: MinimizedModal = {
        id: approvalData.value.id,
        title: approvalData.value.title,
        data: approvalData.value,
    };

    // 检查是否已存在
    const existingIndex = minimizedModals.value.findIndex(
        (m) => m.id === minimized.id
    );
    if (existingIndex === -1) {
        minimizedModals.value.push(minimized);
    }

    // 关闭模态框
    visible.value = false;
};

const restoreModal = (modal: MinimizedModal) => {
    // 恢复模态框，保持当前的审批意见
    showApprovalModal(modal.data, { clearComment: false });

    // 从最小化列表中移除
    const index = minimizedModals.value.findIndex((m) => m.id === modal.id);
    if (index > -1) {
        minimizedModals.value.splice(index, 1);
    }
};

const closeMinimizedModal = (modalId: string) => {
    const index = minimizedModals.value.findIndex((m) => m.id === modalId);
    if (index > -1) {
        minimizedModals.value.splice(index, 1);
    }
};

const backToMainModal = () => {
    // 隐藏浮动审批表单
    globalFloatingForm.value = false;

    // 显示主模态框
    visible.value = true;
};

const closeFloatingForm = () => {
    // 隐藏浮动审批表单
    closeGlobalFloatingForm();
};

const handleApprove = async () => {
    if (!approvalData.value) return;

    loading.value = true;
    actionType.value = 'approve';

    try {
        //调用审批接口
        const { onSuccess, onError } = useRequest(
            checkApproval({
                log_id: Number(approvalData.value.id),
                approve_result: '同意',
                notes: approvalComment.value,
            }),
            {
                immediate: true,
            }
        );
        onSuccess(() => {
            // 关闭模态框或浮动表单
            if (globalFloatingForm.value) {
                closeGlobalFloatingForm();
            } else {
                visible.value = false;
                approvalComment.value = '';
            }
            toast.toast({
                title: '提交成功',
                description: `审批已成功提交`,
                variant: 'default',
                duration: 2000,
            });
        });
        onError((error) => {
            toast.toast({
                title: '提交失败',
                description: `审批提交失败:${error}`,
                variant: 'destructive',
                duration: 2000,
            });
        });
    } catch (error) {
        toast.toast({
            title: '提交失败',
            description: `审批提交失败:${error}`,
            variant: 'destructive',
            duration: 2000,
        });
    } finally {
        loading.value = false;
        actionType.value = null;
    }
};

const handleReject = async () => {
    if (!approvalData.value) return;

    if (!approvalComment.value.trim()) {
        // TODO: 添加提示 - 拒绝时必须填写意见
        toast.toast({
            title: '操作提示',
            description: `拒绝时必须填写审批意见`,
            variant: 'destructive',
        });
        return;
    }

    loading.value = true;
    actionType.value = 'reject';

    try {
        //调用审批接口
        const { onSuccess, onError } = useRequest(
            checkApproval({
                log_id: Number(approvalData.value.id),
                approve_result: '拒绝',
                notes: approvalComment.value,
            }),
            {
                immediate: true,
            }
        );
        onSuccess(() => {
            // 关闭模态框或浮动表单
            if (globalFloatingForm.value) {
                closeGlobalFloatingForm();
            } else {
                visible.value = false;
                approvalComment.value = '';
            }
            toast.toast({
                title: '提交成功',
                description: `审批已成功提交`,
                variant: 'default',
                duration: 2000,
            });
        });
        onError((error) => {
            toast.toast({
                title: '提交失败',
                description: `审批提交失败:${error}`,
                variant: 'destructive',
                duration: 2000,
            });
        });
    } catch (error) {
        toast.toast({
            title: '提交失败',
            description: `审批提交失败:${error}`,
            variant: 'destructive',
            duration: 2000,
        });
    } finally {
        loading.value = false;
        actionType.value = null;
    }
};

// 暴露方法给外部调用
defineExpose({
    showApprovalModal,
    minimizeModal,
    restoreModal,
    closeMinimizedModal,
    backToMainModal,
    closeFloatingForm,
});

// 全局浮动表单相关方法
const handleApprovalFloatingShow = (event: CustomEvent) => {
    const { approvalData: data } = event.detail;
    approvalData.value = data;
    // 不清空审批意见，保持切换时的同步
    // approvalComment.value = ''; // 移除这行，保持审批意见
    globalFloatingForm.value = true;
};

const backToMainModalFromGlobal = () => {
    if (!approvalData.value) return;

    // 隐藏全局浮动表单
    globalFloatingForm.value = false;

    // 移除路由参数
    const query = { ...route.query };
    delete query.approval;
    router.replace({ query });

    // 显示主模态框，保持审批意见不清空
    visible.value = true;
};

const closeGlobalFloatingForm = () => {
    globalFloatingForm.value = false;
    approvalData.value = null;
    approvalComment.value = '';

    // 移除路由参数
    const query = { ...route.query };
    delete query.approval;
    router.replace({ query });
};

// 监听路由变化
watch(
    () => route.query,
    (newQuery) => {
        // 如果路由中没有 approval=floating 参数，隐藏全局浮动表单
        if (newQuery.approval !== 'floating') {
            globalFloatingForm.value = false;
            // 如果是浮动模式下清除数据，主模态框模式不清除
            if (globalFloatingForm.value) {
                approvalData.value = null;
                approvalComment.value = '';
            }
        }
    },
    { immediate: true }
);

// 监听全局事件 - 从浮动表单返回主模态框
const handleShowMainModal = (event: CustomEvent) => {
    const { approval: data } = event.detail;
    if (data) {
        showApprovalModal(data, { clearComment: false });
    }
};

// 生命周期
onMounted(() => {
    window.addEventListener(
        'approval-show-main-modal',
        handleShowMainModal as EventListener
    );
    window.addEventListener(
        'approval-floating-show',
        handleApprovalFloatingShow as EventListener
    );
});

onUnmounted(() => {
    window.removeEventListener(
        'approval-show-main-modal',
        handleShowMainModal as EventListener
    );
    window.removeEventListener(
        'approval-floating-show',
        handleApprovalFloatingShow as EventListener
    );
});
</script>

<style scoped>
/* 确保对话框在移动设备上的适配 */
@media (max-width: 768px) {
    .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3 {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3 {
        grid-template-columns: 1fr 1fr;
    }
}

@media (min-width: 1024px) {
    .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3 {
        grid-template-columns: 1fr 1fr 1fr;
    }
}
</style>
