<template>
    <div class="collapsible-item">
        <div
            class="collapsible-header flex justify-between p-6 pt-0 pb-0"
            :class="{ disabled: disabled }"
        >
            <span>
                <slot name="header" />
            </span>

            <h3
                class="cursor-pointer flex items-center text-muted-foreground"
                @click="!disabled && collapsible.handleItemClick(name)"
            >
                <Icon v-show="isActive" icon="ant-design:up-outlined" />
                <Icon v-show="!isActive" icon="ant-design:down-outlined" />
            </h3>
        </div>
        <transition name="slide">
            <div v-show="isActive" ref="contentRef" class="collapsible-content">
                <div class="collapsible-body">
                    <slot />
                </div>
            </div>
        </transition>
    </div>
</template>

<script setup lang="ts">
import { ref, inject, onMounted, computed } from 'vue';
import { CollapsibleContext } from '../types';
import { Icon } from '@iconify/vue';

interface Props {
    name: string;
    title?: string;
    disabled?: boolean;
}

const props = defineProps<Props>();
const collapsible = inject<CollapsibleContext>('collapsible')!;

const contentRef = ref<HTMLElement | null>(null);
const height = ref<number | null>(null);

// 计算内容高度
const updateHeight = () => {
    if (contentRef.value) {
        height.value = contentRef.value.scrollHeight;
    }
};

onMounted(() => {
    // window.addEventListener('resize', updateHeight);
    // updateHeight();
});

// 判断是否激活
const isActive = computed(() => {
    return collapsible.activeNames.value.includes(props.name);
});
</script>
