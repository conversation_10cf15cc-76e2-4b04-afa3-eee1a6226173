<template>
    <div class="collapsible">
        <slot />
    </div>
</template>

<script setup lang="ts">
import { ref, provide, inject } from 'vue';
import { CollapsibleContext } from './types';

const props = defineProps<{
    modelValue: string[] | string;
    accordion?: boolean;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: string[] | string): void;
    (e: 'change', value: string[] | string): void;
}>();

// 状态管理
const activeNames = ref(props.modelValue || []);

// 注入上下文
provide<CollapsibleContext>('collapsible', {
    activeNames: activeNames,
    accordion: !!props.accordion,
    handleItemClick: (name: string) => {
        if (props.accordion) {
            // 手风琴模式
            const isActive = activeNames.value.includes(name);
            activeNames.value = isActive ? [] : [name];
        } else {
            // 多开模式
            const index = activeNames.value.indexOf(name);
            index === -1
                ? activeNames.value?.push(name)
                : activeNames.value?.splice(index, 1);
        }
        emit('update:modelValue', activeNames.value);
        emit('change', activeNames.value);
    },
});
</script>

<style lang="scss" scoped>
.collapsible {
    border-top: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;

    &-item {
        // border-bottom: 1px solid #ebeef5;

        &:last-child {
            border-bottom: none;
        }
    }

    &-header {
        padding: 16px;
        background: #f5f7fa;
        cursor: pointer;
        position: relative;
        transition: all 0.3s ease;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .arrow {
            transition: transform 0.3s ease;
            transform: rotate(90deg);

            &.active {
                transform: rotate(270deg);
            }
        }
    }

    &-content {
        overflow: hidden;
        transition: max-height 0.3s ease;
        max-height: 0;

        .collapsible-body {
            padding: 16px;
        }
    }
}

.slide-enter-active,
.slide-leave-active {
    transition: max-height 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
    max-height: 0;
}
</style>
