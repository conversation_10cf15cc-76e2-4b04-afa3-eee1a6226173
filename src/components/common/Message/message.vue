<template>
    <div
        v-if="visible"
        class="flex items-center fixed messageBox"
        :class="[positionClass]"
        :style="{ backgroundColor: background, color: textColor }"
    >
        <span class="message-content p-3 flex items-center">
            <img :src="iconSvg" width="20" height="20" /><span class="pl-1">{{
                message
            }}</span>
        </span>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import Success from '@/assets/image/success.svg';
import Error from '@/assets/image/error.svg';
import Warn from '@/assets/image/warn.svg';
const props = defineProps({
    message: {
        type: String,
        required: true,
    },
    type: {
        type: String,
        default: 'success',
    },
    duration: {
        type: Number,
        default: 2000,
    },
    background: {
        type: String,
        default: '#333',
    },
    textColor: {
        type: String,
        default: '#fff',
    },
    position: {
        type: String,
        default: 'center',
    },
});

const visible = ref(true);
const iconSvg = ref();
const positionClass = computed(() => {
    return `message-${props.position}`;
});
onMounted(() => {
    if (props.type === 'success') {
        iconSvg.value = Success;
    } else if (props.type === 'error') {
        iconSvg.value = Error;
    } else if (props.type === 'warn') {
        iconSvg.value = Warn;
    }
});
</script>

<style scoped>
.messageBox {
    z-index: 70;
}
.message-bottom {
    bottom: 20px;
}

.message-center {
    top: 30%;
    left: 45%;
}
.message-content {
    box-shadow: 2px 2px 2px 2px rgba(9, 9, 9, 0.1);
}
</style>
