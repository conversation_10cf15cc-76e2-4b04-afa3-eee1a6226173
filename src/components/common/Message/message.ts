import { render, createVNode } from 'vue';
import Message from './message.vue';
const container = document.createElement('div');
document.body.appendChild(container);
export function useMessage() {
    return (
        content: string,
        options: {
            type?: string;
            duration?: number;
            background?: string;
            textColor?: string;
            position?: string;
        } = {}
    ) => {
        displayMessages(content, options);
    };
}

function displayMessages(content: any, options: any) {
    const vnodes = createVNode(Message, {
        message: content,
        type: options.type || 'success',
        duration: options.duration || 2000,
        background: options.background || '#fff',
        textColor: options.textColor || '#333',
        position: options.position || 'center', // 默认显示在底部
    });
    render(vnodes, container);

    setTimeout(() => {
        render(null, container);
    }, options.duration);
}
