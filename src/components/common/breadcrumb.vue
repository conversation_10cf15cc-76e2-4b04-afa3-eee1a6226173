<template>
    <header class="flex h-12 shrink-0 items-center gap-2 border-b px-4">
        <SidebarTrigger class="-ml-1" />
        <Separator orientation="vertical" class="mr-2 h-4" />
        <Breadcrumb class="w-full">
            <BreadcrumbList class="flex-nowrap overflow-hidden">
                <BreadcrumbItem
                    v-for="(item, index) in breadcrumbs"
                    :key="index"
                    class="hidden md:flex items-center whitespace-nowrap overflow-hidden"
                >
                    <BreadcrumbLink
                        v-if="item.path"
                        :href="item.path"
                        class="truncate max-w-[120px]"
                    >
                        {{ item.name }}
                    </BreadcrumbLink>
                    <BreadcrumbPage v-else class="truncate max-w-[120px]">
                        {{ item.name }}
                    </BreadcrumbPage>
                    <BreadcrumbSeparator
                        v-if="index < breadcrumbs.length - 1"
                    />
                </BreadcrumbItem>
            </BreadcrumbList>
        </Breadcrumb>
    </header>
</template>

<script setup lang="ts">
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { useSidebar } from '@/components/ui/sidebar';
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

defineOptions({
    name: 'AppBreadcrumb',
});

const route = useRoute();
const { open } = useSidebar();

interface BreadcrumbItem1 {
    name: string;
    path?: string;
}

const breadcrumbs = ref<BreadcrumbItem1[]>([]);

const updateBreadcrumbs = () => {
    const paths = route.path.split('/').filter(Boolean);
    breadcrumbs.value = [
        { name: '首页', path: '/' },
        ...paths.map((path, index) => {
            const fullPath = '/' + paths.slice(0, index + 1).join('/');
            return {
                name: path,
                path: index === paths.length - 1 ? undefined : fullPath,
            };
        }),
    ];
};

watch(() => route.path, updateBreadcrumbs, { immediate: true });

onMounted(() => {
    open.value = true;
});
</script>
