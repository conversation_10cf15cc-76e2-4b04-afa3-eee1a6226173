<template>
    <!-- 占位元素防止布局抖动 -->
    <div :style="{ height: headerHeight + 'px' }" v-show="isSticky"></div>

    <!-- 吸顶头部 -->
    <header
        ref="headerRef"
        :class="['detail-header', { 'header-sticky': isSticky }]"
    >
        <div class="left">
            <slot name="header-left">
                <h3 class="font-semibold leading-none tracking-tight">
                    {{ headerTitle }}
                </h3>
                <!-- <button @click="$emit('back')">←</button> -->
            </slot>
        </div>

        <div class="center">
            <div
                v-if="!$slots['header-center']"
                class="grid grid-cols-4 justify-center gap-4"
            >
                <DetailCard
                    :record="item"
                    v-for="(item, index) in statistics"
                    :key="index"
                ></DetailCard>
            </div>
            <slot name="header-center"></slot>
        </div>

        <div class="right">
            <slot name="header-right"></slot>
        </div>
    </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import DetailCard from './DetailCard.vue';

const route = useRoute();

console.log('route', route);

const props = defineProps({
    title: String,
    statistics: Array,
});

// const emit = defineEmits(['back']);

const headerTitle = props.title || route.meta.title || '';

// 响应式状态
const headerRef = ref(null);
const isSticky = ref(false);
const headerHeight = ref(0);
const stickyOffset = ref(0);

// 吸顶逻辑（网页6方案优化）
const handleScroll = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    isSticky.value = scrollTop > stickyOffset.value;
};

// Composition API 组织逻辑
const useSticky = () => {
    onMounted(() => {
        // 获取元素初始位置（网页7关键实现）
        stickyOffset.value =
            headerRef.value.getBoundingClientRect().top + window.pageYOffset;
        headerHeight.value = headerRef.value.offsetHeight;

        window.addEventListener('scroll', handleScroll, { passive: true });
    });

    onUnmounted(() => {
        window.removeEventListener('scroll', handleScroll);
    });
};

useSticky();
</script>

<style scoped>
.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    height: 64px;
    will-change: transform, opacity, box-shadow;
    backdrop-filter: blur(6px);
    transform: translateZ(0);
    margin-bottom: 10px;

    position: sticky;
    top: 60px;
}

.header-sticky {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    z-index: 9;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.58);
    /* transform: translateY(10px) scale(1.02);  */
    backdrop-filter: blur(6px);
    padding: 0 30px;
    /* animation: headerSticky 0.1s ease-out; */
}

@keyframes headerSticky {
    0% {
        transform: translateY(-100%) scale(0.98);
        opacity: 0;
    }
    80% {
        transform: translateY(15px) scale(1.01);
    }
    100% {
        transform: translateY(10px) scale(1.02);
    }
}

.left,
.right {
    flex: 0 0 auto;
}

.center {
    flex: 1;
    text-align: center;
    font-weight: bold;
}

.card-wrap {
    display: grid;
    /* grid-template-columns: repeat(4, auto); */
    justify-content: center;
    gap: 20px;
}
.grid-cols-1 {
    grid-template-columns: repeat(1, auto);
}
.grid-cols-2 {
    grid-template-columns: repeat(2, auto);
}
.grid-cols-3 {
    grid-template-columns: repeat(3, auto);
}
.grid-cols-4 {
    grid-template-columns: repeat(4, auto);
}
.grid-cols-5 {
    grid-template-columns: repeat(5, auto);
}
.grid-cols-6 {
    grid-template-columns: repeat(6, auto);
}
.grid-cols-7 {
    grid-template-columns: repeat(7, auto);
}
.grid-cols-8 {
    grid-template-columns: repeat(8, auto);
}
</style>
