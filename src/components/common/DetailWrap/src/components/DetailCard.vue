<template>
    <div class="grid">
        <div class="flex items-center border rounded-md pl-2 pr-2">
            <span v-if="record.icon">
                <Icon
                    :icon="record.icon"
                    size="24"
                    class="inline-block w-[32px] h-[32px] mr-[8px]"
                    :class="
                        record.color
                            ? 'text-' + record.color + '-500'
                            : 'text-green-500'
                    "
                ></Icon>
            </span>
            <span
                class="inline-block w-[32px] h-[32px] mr-[8px] rounded-full br"
                v-else
            >
            </span>
            <div class="p-1 pt-0">
                <div class="text-2xl font-bold text-left">
                    {{ record.count }}
                </div>
                <p class="text-xs text-muted-foreground">
                    {{ record.title }}
                </p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue';
import {} from 'vue';

defineProps({
    record: Object,
});
</script>

<style scoped>
.br {
    background: linear-gradient(106.3532427531deg, #4c07f2 0%, #fa0006 100%);
}
</style>
