<template>
    <DetailHeader :title="title" :statistics="statistics">
        <template v-for="item in Object.keys($slots)" #[item]="data">
            <slot :name="item" v-bind="data || {}" />
        </template>
    </DetailHeader>
    <Scroll :scroll="scroll" class="mb-[12px]" v-if="scroll?.length" />
    <div class="overflow-y-auto custom-scrollable">
        <div class="main">
            <slot />
        </div>
    </div>
</template>

<script setup>
import {} from 'vue';
import DetailHeader from './components/DetailHeader.vue';
import { Scroll } from '@/components';

defineProps({
    title: String,
    statistics: Array,
    scroll: Array,
});
</script>

<style lang="sass" scoped></style>

<style>
::-webkit-scrollbar {
    width: 10px; /* 垂直滚动条宽度 */
    height: 6; /* 水平滚动条高度 */
}
::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
}
::-webkit-scrollbar-thumb {
    background: #acacac;
    border-radius: 6px;
    border: 2px solid transparent;
    background-clip: content-box;
}
::-webkit-scrollbar-thumb:hover {
    background: #666;
}
</style>
