import type { ButtonVariants } from '@/components/ui/button';

// 对话框配置类型
export interface AlertDialogConfig {
    title: string;
    description?: string;
    confirmText?: string;
    cancelText?: string;
    hideCancel?: boolean;
    confirmVariant?: ButtonVariants['variant'];
    cancelVariant?: ButtonVariants['variant'];
    onConfirm?: () => void | undefined;
    onCancel?: () => void | undefined;
    confirmClass?: string;
    cancelClass?: string;
}

// 对话框状态类型
export interface AlertDialogState {
    isOpen: boolean;
    config: AlertDialogConfig;
}

// 定义 alert 函数的类型
export type AlertFunction = (config: AlertDialogConfig) => void;
