import { ref } from 'vue';
import type {
    AlertDialogConfig,
    AlertDialogState,
    AlertFunction,
} from './type';
import { $t } from '@/locales';

const state = ref<AlertDialogState>({
    isOpen: false,
    config: {
        title: '',
        confirmText: $t('common.button.confirm'),
        cancelText: $t('common.button.cancel'),
        hideCancel: false,
        cancelVariant: 'outline',
        confirmVariant: 'default',
        onConfirm: undefined,
        onCancel: undefined,
        confirmClass: '',
        cancelClass: '',
    },
});

export function useAlertDialog() {
    const alertDialog: AlertFunction = (config: AlertDialogConfig) => {
        state.value.config = {
            ...state.value.config,
            ...config,
        };
        state.value.isOpen = true;
    };
    const loading = ref(false);

    const onConfirm = async (): Promise<void> => {
        if (state.value.config.onConfirm) {
            loading.value = true;
            await state.value.config.onConfirm();
            loading.value = false;
            state.value.isOpen = false;
        }
    };

    const onCancel = (): void => {
        state.value.isOpen = false;
        if (state.value.config.onCancel) {
            state.value.config.onCancel();
        }
    };

    return {
        state: state as Readonly<typeof state>,
        alertDialog,
        loading,
        onConfirm,
        onCancel,
    };
}
