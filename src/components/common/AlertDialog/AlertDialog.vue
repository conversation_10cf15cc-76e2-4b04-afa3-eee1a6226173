<template>
    <AlertDialog v-model:open="state.isOpen" class="z-[9998]">
        <AlertDialogContent>
            <AlertDialogHeader>
                <AlertDialogTitle>{{ currentConfig.title }}</AlertDialogTitle>
                <AlertDialogDescription v-if="currentConfig.description">
                    {{ currentConfig.description }}
                </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
                <Button :variant="currentConfig.cancelVariant || 'outline'" v-if="!currentConfig.hideCancel"
                    :class="currentConfig.cancelClass" @click="onCancel" :disabled="loading">{{ currentConfig.cancelText
                    }}</Button>
                <Button :variant="currentConfig.confirmVariant || 'default'" :class="currentConfig.confirmClass"
                    @click="onConfirm" v-loading="loading">{{ currentConfig.confirmText }}</Button>
            </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>
</template>

<script setup lang="ts">
import {
    AlertDialog,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogDescription,
    AlertDialogFooter,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { useAlertDialog } from './useAlertDialog';
import type { AlertDialogConfig } from './type';
import { computed } from 'vue';

const { state, onConfirm, onCancel, loading } = useAlertDialog();

// 类型推导检查
const currentConfig = computed<AlertDialogConfig>(() => {
    return state.value.config;
});

/**
 * 使用示例
 * 在要用的文件中
 * const alertDialog = inject<AlertFunction>('alertDialog')!
 * alertDialog({
 *   title: '提示',
 *   description: '是否确认删除？',
 *   confirmText: '确认',
 *   cancelText: '取消',
 *   confirmVariant: 'destructive', // 确认按钮样式
 *   cancelVariant: 'outline',
 *   hideCancel: true, // 是否隐藏取消按钮
 *   onConfirm: () => {
 *     console.log('确认')
 *   },
 *   onCancel: () => {
 *     console.log('取消')
 *   },
 *   confirmClass: 'bg-destructive text-destructive-foreground hover:bg-destructive/90', // 确认按钮样式
 *   cancelClass: 'bg-outline text-outline-foreground hover:bg-outline/90', // 取消按钮样式
 */
</script>
