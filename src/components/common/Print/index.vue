<template>
    <Dialog v-model:visible="internalVisible" :title="title">
        <div class="space-y-4">
            <div>
                <Button v-permission="['bas:printtemplate:create']" class="py-4" @click="() => openEditDialog()">
                    <Icon icon="lucide:plus" />
                    新增
                </Button>
            </div>
            <Table>
                <TableCaption>
                    <div v-if="templateList.length === 0"
                        class="w-full py-4 pb-6 bg-background rounded-lg flex items-center justify-center space-x-4">
                        <div class="text-center space-y-2">
                            <h3 class="text-lg text-zinc-400 text-foreground">暂无打印模板数据</h3>
                        </div>
                    </div>
                </TableCaption>
                <TableHeader class="bg-gray-100">
                    <TableHead>模板名称</TableHead>
                    <TableHead>操作</TableHead>
                </TableHeader>
                <TableBody>
                    <TableRow v-for="temp in templateList" :key="temp.id">
                        <TableCell class="font-medium w-[45%]">{{ temp.name }}</TableCell>
                        <TableCell>
                            <div class="flex items-center gap-1">
                                <Button v-permission="['common:preview:print']" class="py-4" variant="outline"
                                    @click="sendRequest(temp, '1')">预览</Button>
                                <Button v-permission="['common:print']" class="py-4"
                                    @click="sendRequest(temp, '3')">打印</Button>
                                <Button v-permission="['design:printtemplate']" class="py-4" variant="outline"
                                    @click="sendRequest(temp, '2')">设计</Button>
                                <Button v-permission="['bas:printtemplate:create']" class="py-4" variant="outline"
                                    @click="openCopyDialog(temp)">复制</Button>
                                <Button v-permission="['bas:printtemplate:update']" class="py-4" variant="outline"
                                    @click="openEditDialog(temp)">修改</Button>
                                <Button v-permission="['bas:printtemplate:delete']" class="py-4" variant="destructive"
                                    @click="deleteTemplate(temp.id)">删除</Button>
                            </div>
                        </TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
        <div>

        </div>
    </Dialog>
    <Dialog v-model:visible="editVisible" :title="editTitle">
        <div class="space-y-4">
            <div class="space-y-1">
                <Label class="before:content-['*'] before:text-red-500 before:mr-1">模板名称</Label>
                <Input v-model="formData.name" placeholder="请输入模板名称" />
            </div>
            <div class="max-h-[60vh] overflow-y-auto space-y-4 pb-4 pr-4">
                <div v-for="(item, index) in formData.data_source" :key="item.id" class="flex gap-2">
                    <div class="space-y-1 flex-1">
                        <Label class="before:content-['*'] before:text-red-500 before:mr-1">数据源名称{{ index + 1 }}</Label>
                        <Input v-model="item.name" placeholder="请输入数据源名称" />
                    </div>
                    <div class="space-y-1 flex-1">
                        <Label>SQL语句</Label>
                        <Input v-model="item.query_sql" placeholder="请输入SQL语句" />
                    </div>
                    <div class="space-y-1 flex-1">
                        <Label>JSON数据源</Label>
                        <Input v-model="item.json_data" placeholder="请输入JSON数据源" />
                    </div>
                    <Button size="icon" class="w-[38px] h-[38px] mt-[24px]" variant="destructive"
                        :title="$t('common.button.delete')" @click="deleteDataSource(index)">
                        <Icon icon="lucide:trash-2" />
                    </Button>
                </div>
                <div class="col-span-3">
                    <Button class=" py-4 w-[100%]" @click="addDataSource">新增模板语句</Button>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex justify-end gap-2">
                <Button :disabled="loading" class="py-4" variant="outline" @click="closeEditDialog()">取消</Button>
                <Button v-loading="loading" class="py-4" @click="submit()">保存</Button>
            </div>
        </template>
        <Loading :loading="loading" />
    </Dialog>
</template>
<script setup lang='ts'>
import { inject, onMounted, ref, watch } from 'vue';
import { createPrintTemplate, getPrintTemplate, getPrintTemplateList, removePrintTemplate, updatePrintTemplate } from '@/api/bas/print_template';
import Dialog from '../Dialog.vue';
import Button from '@/components/ui/button/Button.vue';
import { Table, TableHead, TableRow, TableCell, TableBody, TableHeader, TableCaption } from "@/components/ui/table";
import Label from '@/components/ui/label/Label.vue';
import Input from '@/components/ui/input/Input.vue';
import Loading from '../Loading.vue';
import { Icon } from '@iconify/vue';
import { useToast } from '@/components/ui/toast';
import { AlertFunction } from '../AlertDialog/type';
import { FilterOperator } from '@/types/api/queryParams';
import { PrintRequest, PrintTemplate } from '@/api/bas/print_template/types';
import { useUserStore } from '@/store/user';

type Size = 'large' | 'middle' | 'small' | 'mini';
// Dialog组件的Props类型定义
interface DialogProps {
    /** 控制显隐 */
    visible: boolean;
    /** 选中的ids */
    selectedIds: number[];
    /** 模板标记 */
    tag: string;
    /** 所属模块 */
    module: string;
    /** 弹窗尺寸 */
    size?: Size;
    /** 标题 */
    title?: string;
    /** 弹窗层级 */
    zIndex?: number;

}
const props = withDefaults(defineProps<DialogProps>(), {
    visible: false,
    tag: '',
    module: '',
    title: '打印模板',
    size: 'small',
    zIndex: 45,
});

const toast = useToast()
const alertDialog = inject<AlertFunction>('alertDialog')!;
// 定义 emits，包括 update:visible 事件
const emit = defineEmits<{
    close: [];
    'update:visible': [value: boolean];
}>();
const loading = ref(false);
const userStore = useUserStore();

// 内部状态管理
const internalVisible = ref(props.visible);

// 监听 props.visible 的变化，同步到内部状态
watch(() => props.visible, (newValue) => {
    console.log("🚀 ~ watch ~ newValue:", newValue)
    internalVisible.value = newValue;
});

// 监听内部状态的变化，向外发射事件
watch(internalVisible, (newValue) => {
    emit('update:visible', newValue);
    if (!newValue) {
        emit('close');
    }
});

/** == 打印模板增删改查 ================================================================== */
/** 打印模板列表 */
const templateList = ref([])
/** 获取打印模板列表 */
const getTemplateList = async () => {
    try {
        if (!props.tag || !props.module) {
            return;
        }
        const res = await getPrintTemplateList({
            offset: 0, limit: 9999, filters: {
                couple: 'and',
                conditions: [
                    { field: 'tag', op: FilterOperator.EQUAL, value: props.tag },
                    { field: 'module', op: FilterOperator.EQUAL, value: props.module }
                ]
            }
        });
        templateList.value = res.items;
    } catch (error) {
        console.log("🚀 ~ getTemplateList ~ error:", error)
    }
}
/** 获取模板详情 */
const getTemplateDetail = async (id: number, isCopy?: boolean) => {
    try {
        loading.value = true;
        const res = await getPrintTemplate({ id: id, max_depth: 2 });
        formData.value = res;
        if (isCopy) {
            formData.value.id = undefined;
            formData.value.name = '';
        }
    } catch (error) {
        console.log("🚀 ~ getTemplateDetail ~ error:", error)
    } finally {
        loading.value = false;
    }
}
const defaultFormData = {
    name: '',
    tag: props.tag,
    module: props.module,
    data_source: [
        {
            name: '',
            query_sql: '',
            json_data: '',
        }
    ]
}
/** 新增/编辑表单 */
const formData = ref<PrintTemplate>(defaultFormData)
/** 新增/编辑弹窗visible */
const editVisible = ref(false)
/** 标题 */
const editTitle = ref('新增打印模板');

/** 打开弹窗 */
const openEditDialog = (row?: PrintTemplate) => {
    formData.value = defaultFormData;
    editTitle.value = '新增打印模板';
    if (row) {
        editTitle.value = '编辑打印模板';
        getTemplateDetail(row.id);
    }
    editVisible.value = true
}
/** 打开复制弹窗 */
const openCopyDialog = (row: PrintTemplate) => {
    getTemplateDetail(row.id, true);
    editTitle.value = '复制打印模板';
    editVisible.value = true
}

/** 新增模板语句 */
const addDataSource = () => {
    formData.value.data_source.push({
        name: '',
        query_sql: '',
        json_data: '',
    })
}

/** 删除模板语句 */
const deleteDataSource = (index: number) => {
    formData.value.data_source.splice(index, 1);
}

/** 关闭弹窗 */
const closeEditDialog = () => {
    editVisible.value = false;
}

/** 提交新增打印模板 */
const submit = async () => {
    try {
        if (!formData.value.name) {
            toast.toast({
                title: '请输入模板名称',
                duration: 2000,
            });
            return;
        }
        if (formData.value.data_source.length === 0) {
            toast.toast({
                title: '请添加模板语句',
                duration: 2000,
            });
            return;
        }
        for (let i = 0; i < formData.value.data_source.length; i++) {
            const source = formData.value.data_source[i];
            if (!source.name) {
                toast.toast({
                    title: `请输入数据源名称${i + 1}的名称`,
                    duration: 2000,
                });
                return;
            }
            if (!source.query_sql && !source.json_data) {
                toast.toast({
                    title: `请至少输入数据源名称${i + 1}的SQL或JSON中的一个`,
                    duration: 2000,
                });
                return;
            }
        }
        loading.value = true;
        if (formData.value.id) {
            await updatePrintTemplate(formData.value);
        } else {
            await createPrintTemplate(formData.value);
        }
        toast.toast({
            title: '操作成功',
            duration: 2000,
        });
        closeEditDialog();
        getTemplateList();
    } catch (error) {
        console.log("🚀 ~ submit: ~ error:", error)
    } finally {
        loading.value = false;
    }
}

/** 删除模板 */
const deleteTemplate = async (id: number) => {
    alertDialog({
        title: '删除模板',
        description: '是否删除当前打印模板？',
        confirmVariant: 'destructive',
        onConfirm: async () => {
            try {
                loading.value = true;
                await removePrintTemplate(id);
                toast.toast({
                    title: '删除模板成功',
                    duration: 2000,
                });
                getTemplateList();
            } catch (error) {
                console.log("🚀 ~ deleteTemplate: ~ error:", error)
            } finally {
                loading.value = false;
            }
        }
    })
}

/** 预览1、设计2、打印3 */
const sendRequest = async (temp: PrintTemplate, type: '1' | '2' | '3') => {
    const url = 'http://zbkj.com:9000/api/Command/SendPrintCmd';
    const params: PrintRequest = {
        code: type,
        template_id: temp.id,
        ids: props.selectedIds,
        print_by: userStore.userInfo.id,
        user_name: userStore.userInfo.name,
        token: localStorage.getItem('access_token'),
        is_record: true
    }
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
    }).then(res => res.json()).then(data => {
        console.log("🚀 ~ sendRequest ~ data:", data)
    })
}

onMounted(() => {
    getTemplateList();
})
</script>