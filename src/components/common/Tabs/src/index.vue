<template>
  <Tabs v-model="activeId" v-bind="getBind">
    <TabsList>
      <TabsTrigger
        @click.stop="() => handleClick(item)"
        v-for="item in options"
        :value="item.value"
        :key="item.value"
      >
        {{ item.label }}
      </TabsTrigger>
    </TabsList>
  </Tabs>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, PropType, computed, watch } from 'vue'
import { isArray, isFunction, isString, omit, throttle } from 'lodash-es'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'

const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean],
  },
  active: {
    type: [String, Number],
  },
  record: {
    type: Object,
  },
  options: {
    type: Array as PropType<
      | { label: string; value: string | number | boolean }[]
      | (string | number | boolean)[]
    >,
  },
  onClick: {
    type: Function,
  },
})

const emits = defineEmits(['change', 'update:modelValue'])

const activeId = ref(props.modelValue)

const options = computed(() => {
  if (!isArray(props?.options)) return []
  return props?.options?.map((item) => {
    if (isString(item)) {
      return {
        label: item,
        value: item,
      }
    } else {
      return item
    }
  })
})

const getBind = computed(() => {
  return {
    ...omit(props, ['modelValue', 'active', 'record', 'options', 'on']),
  }
})

watch(
  () => props.modelValue,
  (n) => {
    activeId.value = n
  }
)

watch(
  () => activeId.value,
  (n) => {
    emits('change', n)
    emits('update:modelValue', n)

    const { onClick } = props
    if (isFunction(onClick)) onClick(activeId.value)
  }
)

const handleClick = (item) => {
  // activeId.value = item.value;
  // emits('change', item);
  // const { onClick } = props;
  // if (isFunction(onClick)) onClick(activeId.value);
}
</script>

<style scoped></style>
