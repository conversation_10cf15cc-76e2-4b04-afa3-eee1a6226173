<template>
    <Sheet :open="modelValue" @update:open="updateVisible">
        <SheetContent :class="contentClass">
            <div class="h-full flex flex-col bg-white rounded-l-lg shadow-lg">
                <SheetHeader v-if="$slots.header">
                    <slot name="header" />
                </SheetHeader>
                <div class="flex-1 overflow-y-auto">
                    <slot />
                </div>
                <SheetFooter v-if="$slots.footer">
                    <slot name="footer" />
                </SheetFooter>
            </div>
        </SheetContent>
    </Sheet>
</template>

<script setup lang="ts">
import {
    Sheet,
    Sheet<PERSON>ontent,
    SheetHeader,
    SheetFooter,
} from '@/components/ui/sheet';

defineProps<{
    modelValue: boolean;
    contentClass?: string;
}>();
const emit = defineEmits(['update:modelValue']);

const updateVisible = (value: boolean) => {
    emit('update:modelValue', value);
};
</script>

<style scoped></style>
