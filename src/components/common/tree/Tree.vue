<template>
    <!-- 树形组件的根容器 -->
    <div class="tree">
        <!-- 遍历数据源中的每个节点 -->
        <div v-for="node in data" :key="node.id" class="tree-node">
            <!-- 节点的内容部分 -->
            <div class="tree-node-content">
                <!-- 展开/折叠图标区域 -->
                <div class="tree-node-icon" @click="toggleExpand(node.id)">
                    <!-- 如果节点有子节点，显示可点击的展开/折叠图标 -->
                    <ChevronRight
                        v-if="node.children?.length"
                        class="h-4 w-4 transition-transform duration-200"
                        :class="{ 'rotate-90': expandedKeys.includes(node.id) }"
                    />
                    <!-- 如果节点没有子节点，显示一个占位符以保持对齐 -->
                    <div v-else class="w-4"></div>
                </div>
                <!-- 节点的标签区域，包含复选框和文本 -->
                <div class="tree-node-label">
                    <!-- 复选框组件，用于选择/取消选择节点 -->
                    <!-- 根据checkedKeys判断是否选中 -->
                    <!-- 处理选中状态变化 -->
                    <!-- 支持禁用状态 -->
                    <Checkbox
                        :checked="checkedKeys.includes(node.id)"
                        @update:checked="
                            (checked) => handleCheck(node, checked)
                        "
                        :disabled="node.disabled"
                    />
                    <!-- 显示节点名称 -->
                    <span class="text-xs"
                        >{{ getNodeType(node) }}{{ node.name }}</span
                    >
                </div>
            </div>
            <!-- 子节点容器，仅当节点有子节点且处于展开状态时显示 -->
            <div
                v-if="node.children?.length && expandedKeys.includes(node.id)"
                class="tree-node-children"
            >
                <!-- 递归渲染子节点，形成树形结构 -->
                <!-- :data="node.children" 传递子节点数据 -->
                <!--:check-strictly="checkStrictly"  传递选择模式配置 -->
                <!--:expanded-keys="expandedKeys" 传递展开状态 -->
                <!--:checked-keys="checkedKeys" 传递选中状态 -->
                <!-- @update:expanded-keys="updateExpandedKeys" 子组件展开状态变化时的回调 -->
                <!-- @update:checked-keys="updateCheckedKeys" 子组件选中状态变化时的回调 -->
                <!-- 这个组件是递归引用自身的Tree组件，用于渲染子节点 -->
                <!-- 这是一个自定义的树形组件，来源于当前文件本身，通过递归方式构建多层级树结构 -->
                <Tree
                    :data="node.children"
                    :check-strictly="checkStrictly"
                    :expanded-keys="expandedKeys"
                    :checked-keys="checkedKeys"
                    @update:expanded-keys="updateExpandedKeys"
                    @update:checked-keys="updateCheckedKeys"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ChevronRight } from 'lucide-vue-next';
import { Checkbox } from '@/components/ui/checkbox';
import type { TreeNode } from './types';

const props = defineProps<{
    data: TreeNode[];
    checkStrictly: boolean;
    expandedKeys: number[];
    checkedKeys: number[];
}>();

const emit = defineEmits<{
    'update:expanded-keys': [value: number[]];
    'update:checked-keys': [value: number[]];
}>();

// 获取所有节点的 ID
const getAllKeys = (nodes: TreeNode[]): number[] => {
    return nodes.reduce((keys: number[], node) => {
        keys.push(node.id);
        if (node.children) {
            keys.push(...getAllKeys(node.children));
        }
        return keys;
    }, []);
};

// 获取所有子节点的 ID
const getChildrenKeys = (node: TreeNode): number[] => {
    const keys: number[] = [];
    if (node.children) {
        node.children.forEach((child) => {
            keys.push(child.id);
            if (child.children) {
                keys.push(...getChildrenKeys(child));
            }
        });
    }
    return keys;
};

// 更新展开状态
const updateExpandedKeys = (keys: number[]) => {
    emit('update:expanded-keys', keys);
};

// 更新选中状态
const updateCheckedKeys = (keys: number[]) => {
    emit('update:checked-keys', keys);
};

// 展开/折叠节点
const toggleExpand = (nodeId: number): void => {
    const newValue = [...props.expandedKeys];
    const index = newValue.indexOf(nodeId);
    if (index > -1) {
        newValue.splice(index, 1);
    } else {
        newValue.push(nodeId);
    }
    emit('update:expanded-keys', newValue);
};

// 展开所有节点
const expandAll = (value: boolean): void => {
    console.log('expandAll', value);
    const newValue = value ? getAllKeys(props.data) : [];
    emit('update:expanded-keys', newValue);
};

const getNodeType = (node: TreeNode): string => {
    console.log('node', node);
    if (node.type === 'menu' && node.route_component) {
        return '[菜单] ';
    } else if (node.type === 'api') {
        return '[按钮] ';
    } else if (node.type === 'menu' && !node.route_component) {
        return '[目录] ';
    }
};

// 处理节点选中状态
const handleCheck = (node: TreeNode, checked: boolean) => {
    if (node.disabled) return;

    const newCheckedKeys = [...props.checkedKeys];
    const index = newCheckedKeys.indexOf(node.id);

    if (!props.checkStrictly && node.children?.length) {
        // 非严格模式下，选中父节点时同时选中所有子节点
        const childrenKeys = getChildrenKeys(node);
        if (checked) {
            // 选中当前节点和所有子节点
            if (index === -1) {
                newCheckedKeys.push(node.id);
            }
            childrenKeys.forEach((key) => {
                if (!newCheckedKeys.includes(key)) {
                    newCheckedKeys.push(key);
                }
            });
        } else {
            // 取消选中当前节点和所有子节点
            if (index > -1) {
                newCheckedKeys.splice(index, 1);
            }
            childrenKeys.forEach((key) => {
                const childIndex = newCheckedKeys.indexOf(key);
                if (childIndex > -1) {
                    newCheckedKeys.splice(childIndex, 1);
                }
            });
        }
    } else {
        // 严格模式下，只处理当前节点
        if (checked && index === -1) {
            newCheckedKeys.push(node.id);
        } else if (!checked && index > -1) {
            newCheckedKeys.splice(index, 1);
        }
    }

    emit('update:checked-keys', newCheckedKeys);
};

// 设置选中状态
const setCheckedKeys = (keys: number[]): void => {
    emit('update:checked-keys', keys);
};

// 获取选中的节点
const getCheckedKeys = (): number[] => {
    return [...props.checkedKeys];
};

// 暴露方法给父组件
defineExpose({
    expandAll,
    setCheckedKeys,
    getCheckedKeys,
});
</script>

<style>
.tree {
    @apply space-y-1;
}

.tree-node {
    @apply relative;
}

.tree-node-content {
    @apply flex items-center gap-2 py-1.5 px-2 hover:bg-accent/50 rounded-md transition-colors;
}

.tree-node-icon {
    @apply flex items-center justify-center w-4 h-4 cursor-pointer text-muted-foreground hover:text-foreground;
}

.tree-node-label {
    @apply flex items-center gap-2 flex-1;
}

.tree-node-children {
    @apply pl-6;
}
</style>
