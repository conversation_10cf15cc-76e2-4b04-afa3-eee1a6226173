<template>
    <div class="space-y-4">
        <div class="flex flex-col sm:flex-row gap-4">
            <div class="flex items-center">
                <Switch
                    :checked="treeConfig.expandAll"
                    @update:checked="updateExpandAll"
                    id="expand-all"
                />
                <Label for="expand-all" class="ml-2 text-xs">展开/折叠</Label>
            </div>

            <div class="flex items-center">
                <Switch
                    :checked="treeConfig.selectAll"
                    @update:checked="updateSelectAll"
                    id="select-all"
                />
                <Label for="select-all" class="ml-2 text-xs">全选/全不选</Label>
            </div>

            <div class="flex items-center">
                <Switch
                    :checked="treeConfig.checkStrictly"
                    @update:checked="updateCheckStrictly"
                    id="check-strictly"
                />
                <Label for="check-strictly" class="ml-2 text-xs"
                    >父子级联动</Label
                >
            </div>
        </div>

        <div class="border rounded-md p-4">
            <Tree
                ref="permissionTree"
                :data="permissionData"
                :check-strictly="!treeConfig.checkStrictly"
                v-model:expanded-keys="expandedKeys"
                v-model:checked-keys="checkedKeys"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tree } from '@/components/common/tree';
import { useForm } from '@/components/common/FormEngine/hooks/useForm';
import { permissionGet, permissionGetTree } from '@/api/sys/permission';
import { useRequest } from 'alova/client';
interface PermissionNode {
    id: number;
    name: string;
    children?: PermissionNode[];
}

interface TreeConfig {
    expandAll: boolean;
    selectAll: boolean;
    checkStrictly: boolean;
}

const emit = defineEmits<{
    'update:modelValue': [value: boolean];
    submit: [
        data: {
            id: number | null;
            name: string;
            sort_order: number;
            description: string | null;
            code: string;
            status: '启用' | '禁用';
            permissions: number[];
        },
    ];
}>();
// 树形配置
const treeConfig = reactive<TreeConfig>({
    expandAll: false,
    selectAll: false,
    checkStrictly: true,
});

// 权限树数据
const permissionData = ref<PermissionNode[]>();

const { onSuccess } = useRequest(permissionGetTree(1, -1), {
    immediate: true,
});
onSuccess((response) => {
    permissionData.value = response.data || [];
});

const permissionTree = ref();
const expandedKeys = ref<number[]>([]);
const checkedKeys = ref<number[]>([]);

// 获取所有节点的 ID
const getAllKeys = (nodes: PermissionNode[]): number[] => {
    return nodes.reduce((keys: number[], node) => {
        keys.push(node.id);
        if (node.children) {
            keys.push(...getAllKeys(node.children));
        }
        return keys;
    }, []);
};

const updateExpandAll = (value: boolean) => {
    treeConfig.expandAll = value;

    if (treeConfig.expandAll) {
        // 如果treeConfig.expandAll为true，表示需要展开所有节点
        // 调用getAllKeys函数获取权限树中所有节点的ID
        const allKeys = getAllKeys(permissionData.value);
        // 将所有节点ID设置到expandedKeys中，使所有节点展开
        expandedKeys.value = allKeys;
    } else {
        // 如果treeConfig.expandAll为false，表示需要折叠所有节点
        // 清空expandedKeys，使所有节点折叠
        expandedKeys.value = [];
    }
};

const updateSelectAll = (value: boolean) => {
    treeConfig.selectAll = value;
    if (value) {
        const allKeys = getAllKeys(permissionData.value);
        checkedKeys.value = allKeys;
    } else {
        checkedKeys.value = [];
    }
};

const updateChecked = (permissionData: PermissionNode[]) => {
    checkedKeys.value = getAllKeys(permissionData);
};

const updateCheckStrictly = (value: boolean) => {
    treeConfig.checkStrictly = value;
};

defineExpose({
    updateChecked,
});
</script>
