import type { FormActions, FormEngineProps } from '@/components';

export type Size = 'large' | 'middle' | 'small' | 'mini';

export interface BaseDialogConfig {
    size?: Size;
    title?: string;
    description?: string;
    headerClass?: string;
    bodyClass?: string;
    footerClass?: string;
    showFooter?: boolean;
    showClose?: boolean;
    disableClickOutside?: boolean;
    height?: number;
    submitText?: string;
    cancelText?: string;
    zIndex?: number;
}

export interface BaseDialogFormConfig extends FormEngineProps {
    dialogProps?: BaseDialogConfig;
    submit?: (
        formData: Recordable | undefined
    ) => Promise<boolean | undefined> | boolean | undefined;
    cancel?: () => void;
}

export interface DialogFormActions extends FormActions {
    open: (params?: Recordable) => void;
    close: () => void;
    init: (props: BaseDialogFormConfig) => void;
    getVisible: () => boolean;
}
