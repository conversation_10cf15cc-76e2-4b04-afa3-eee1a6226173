// services/dialog.js
import { createApp, h, ref, render, inject, nextTick } from 'vue';
import { BaseDialogFormConfig, DialogFormActions } from '../../types';
import { useDialogFormStoreWithOut } from '@/store/dialogForm';

export function useGlobDialogForm() {
    try {
        const { getDialogFormApi } = useDialogFormStoreWithOut();

        const openDialogForm = (
            options: BaseDialogFormConfig
        ): DialogFormActions => {
            const globDialogFormApi = getDialogFormApi();
            globDialogFormApi?.init(null);
            globDialogFormApi?.init(options);
            globDialogFormApi?.open();

            return globDialogFormApi;
        };
        return {
            openDialogForm,
        };
    } catch (error) {
        console.error(error);

        return {
            openDialogForm: () => {
                console.error(error);
                console.error('openDialogForm is not available');
            },
        };
    }
}
