<!-- eslint-disable no-useless-catch -->
<script lang="ts">
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog'
import {
  computed,
  ref,
  unref,
  type ComputedRef,
  defineComponent,
  nextTick,
  type PropType,
} from 'vue'
import type { BaseDialogFormConfig, DialogFormActions, Size } from '../types'
import { FormEngine, useForm } from '@/components'
import { cloneDeep, isFunction, omit } from 'lodash-es'
import { deepMerge } from '@/utils/object'

export default defineComponent({
  name: '',
  components: {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
    DialogClose,
    Button,
    // FormEngine,
  },
  emits: ['register', 'open', 'close', 'submit', 'cancel'],
  props: {
    open: {
      type: Boolean,
    },
    size: {
      type: String as PropType<Size>,
    },
    height: {
      type: Number,
    },
    title: {
      type: String,
    },
    description: {
      type: String,
    },
    submitText: {
      type: String,
    },
    cancelText: {
      type: String,
    },
    headerClass: {
      type: String,
      default: '',
    },
    bodyClass: {
      type: String,
      default: '',
    },
    footerClass: {
      type: String,
      default: '',
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    showClose: {
      type: Boolean,
      default: true,
    },
    disableClickOutside: {
      type: Boolean,
      default: false,
    },
    zIndex: {
      type: Number,
    },
  },
  setup(props, { emit, slots }) {
    const propsRef = ref<Partial<BaseDialogFormConfig>>()
    const loading = ref(false)

    const getBind = computed(() => {
      return unref(propsRef)
    }) as ComputedRef<BaseDialogFormConfig>

    const visible = ref(props.open)

    const sizeClass = computed(() => {
      return {
        'w-[90%]': unref(getBind).dialogProps?.size === 'large',
        'w-[70%]': unref(getBind).dialogProps?.size === 'middle',
        'w-[50%]': unref(getBind).dialogProps?.size === 'small',
        'w-[30%]': unref(getBind).dialogProps?.size === 'mini',
        'min-h-[420px]': unref(getBind).dialogProps?.size !== 'mini',
      }
    })

    init()

    const open = (params?: Recordable) => {
      visible.value = true

      nextTick(() => {
        formApi.setProps(
          omit(unref(getBind), ['cancel', 'open', 'init', 'dialogProps'])
        )

        if (params) formApi.setValues(params)
      })
    }
    const close = () => {
      visible.value = false
    }

    emit('open', open)
    emit('close', close)

    const [register, formApi] = useForm({})

    async function handleSubmit() {
      const submit = unref(getBind).submit
      loading.value = true

      if (submit && isFunction(submit)) {
        if (slots['main']) {
          submit(null)
          loading.value = false
          close()
          return
        }

        try {
          const params = await formApi?.submit()

          const res = await submit(cloneDeep(params))
          loading.value = false

          if (res === false) return
          close()
        } catch (error) {
          // close();
          loading.value = false
          throw error
        }
      } else {
        loading.value = false
        close()
      }
    }

    function handleCancel() {
      const { cancel } = unref(getBind)
      if (cancel && isFunction(cancel)) cancel()
      emit('cancel')
      close()
    }

    // 初始化表单
    // 1.通过useForm方式使用表单 formProps 必然有值
    // 2.常规方式使用表单 formProps 必然无值，直接取值props
    function init(formProps?: Partial<BaseDialogFormConfig>) {
      if (!formProps) {
        propsRef.value = props
      } else {
        propsRef.value = deepMerge(unref(propsRef) || {}, {
          ...formProps,
          validateModel: formProps?.validateModel
            ? formProps.validateModel
            : 'default',
        })
      }
    }

    const dialogMethods: DialogFormActions = {
      // dialogProps: {},
      open,
      close,
      init,
      getVisible: () => visible.value,
      ...formApi,
    }

    emit('register', dialogMethods)

    return {
      getBind,
      visible,
      sizeClass,
      register,
      handleSubmit,
      handleCancel,
      open,
      close,
      loading,
    }
  },
})
</script>

<template>
  <Dialog v-model:open="visible" append-to-body>
    <DialogContent
      class="max-w-[90%] grid-rows-[auto_1fr]"
      :class="sizeClass"
      :zIndex="getBind.dialogProps?.zIndex || getBind?.zIndex"
    >
      <DialogClose
        v-if="showClose"
        class="absolute right-3 top-3 rounded-sm w-6 h-6 bg-transparent z-[99]"
        @click="close"
      >
      </DialogClose>
      <DialogHeader :class="headerClass">
        <DialogTitle>{{ getBind.dialogProps?.title }}</DialogTitle>
        <DialogDescription v-if="$slots.description">
          <slot name="description" />
        </DialogDescription>
        <DialogDescription v-else>
          {{ getBind.dialogProps?.description }}
        </DialogDescription>
      </DialogHeader>
      <div class="max-h-[80vh] overflow-y-auto">
        <div
          :class="{
            [`h-[${getBind.dialogProps?.height}px]`]:
              getBind.dialogProps?.height,
            [bodyClass]: true,
          }"
        >
          <template v-if="$slots['main']">
            <slot name="main"></slot>
          </template>
          <template v-else>
            <FormEngine @register="register">
              <template v-for="item in Object.keys($slots)" #[item]="data">
                <slot :name="item" v-bind="data || {}" />
              </template>
            </FormEngine>
          </template>
        </div>
      </div>
      <DialogFooter
        v-if="getBind?.dialogProps?.showFooter !== false"
        :class="footerClass"
      >
        <div class="flex justify-end space-x-3">
          <Button variant="outline" @click="handleCancel">
            {{
              getBind.dialogProps?.cancelText || $t?.('common.button.cancel')
            }}
          </Button>
          <Button variant="default" @click="handleSubmit" v-loading="loading">
            {{
              getBind.dialogProps?.submitText || $t?.('common.button.confirm')
            }}
          </Button>
        </div>

        <!-- 底部插槽 -->
        <slot name="footer"></slot>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
