import type { EditContext, IEditor } from '@visactor/vtable-editors';
import { createApp, defineComponent, h, ref } from 'vue';
import { Switch } from '@/components/ui/switch';

class SwitchEditor implements IEditor {
    editorConfig: any;
    element: HTMLElement | undefined;
    container: HTMLElement | undefined;
    successCallback: Function | undefined;

    constructor(editorConfig: any) {
        this.editorConfig = editorConfig;
    }

    onStart({ container, value, referencePosition, endEdit }: EditContext) {
        this.container = container;
        this.successCallback = endEdit;
        const _this = this;

        // 创建Vue组件实例
        const switchComponent = defineComponent({
            setup() {
                const isChecked = ref(Boolean(value));

                const toggleSwitch = () => {
                    isChecked.value = !isChecked.value;
                    _this.successCallback && _this.successCallback();
                };
                return () =>
                    h(Switch, {
                        modelValue: isChecked.value,
                        'onUpdate:modelValue': toggleSwitch,
                        style: {
                            position: 'absolute',
                            top: referencePosition?.rect.top + 'px',
                            left: referencePosition?.rect.left + 'px',
                            width: referencePosition?.rect.width * 0.4 + 'px',
                            height: referencePosition?.rect.height * 0.8 + 'px',
                            marginTop:
                                referencePosition?.rect.height * 0.1 + 'px',
                            marginLeft:
                                referencePosition?.rect.height * 0.1 + 'px',
                        },
                    });
            },
        });

        // 挂载组件到DOM
        const app = createApp(switchComponent);
        const mountNode = document.createElement('div');
        container.appendChild(mountNode);
        app.mount(mountNode);
        this.element = mountNode;
    }

    getValue() {
        // 获取Switch组件的值
        if (!this.element) return false;
        const app = this.element.querySelector('.switch');
        return app ? app.getAttribute('aria-checked') === 'true' : false;
    }

    onEnd() {
        // 卸载Vue组件
        if (!this.element) return;
        // 获取挂载的Vue应用实例
        const app = (this.element as any).__vueParentComponent?.appContext.app;
        if (app) {
            app.unmount();
        }
        this.container?.removeChild(this.element);
    }

    isEditorElement(target: HTMLElement) {
        return target === this.element || this.element!.contains(target);
    }
}

export default SwitchEditor;
