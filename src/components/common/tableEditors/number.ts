import type { EditContext, IEditor, RectProps } from '@visactor/vtable-editors';
import { nextTick } from 'vue';

class NumberEditor implements IEditor {
    editorConfig: any;
    container: HTMLElement | undefined;
    inputContainer: HTMLElement | undefined;
    input: HTMLInputElement | undefined;
    value: number | undefined;
    successCallback: Function | undefined;

    constructor(editorConfig: any) {
        this.editorConfig = editorConfig;
    }

    onStart({
        container,
        value,
        referencePosition,
        endEdit,
        table,
        row,
        col,
    }: EditContext) {
        this.container = container;
        this.successCallback = endEdit;
        this.value = value as number;

        // 创建输入框
        const input = document.createElement('input');
        this.input = input;
        input.type = 'number';
        input.className = 'vtable-number-input';
        input.style.width = '100%';
        input.style.boxSizing = 'border-box';
        input.style.border = '1px solid #d9d9d9';
        input.style.backgroundColor = '#FFFFFF';
        input.style.borderRadius = '0px';
        input.style.border = '2px solid #d9d9d9';
        input.addEventListener('focus', () => {
            input.style.borderColor = '#4A90E2';
            input.style.outline = 'none';
        });
        input.style.padding = '4px 8px';
        input.style.height = referencePosition?.rect?.height - 2 + 'px';
        input.style.fontSize = '14px';

        // 设置初始值
        if (this.value !== undefined) {
            input.value = this.value.toString();
        }

        // 设置输入限制
        if (this.editorConfig.min !== undefined) {
            input.min = this.editorConfig.min.toString();
        }
        if (this.editorConfig.max !== undefined) {
            input.max = this.editorConfig.max.toString();
        }
        if (this.editorConfig.step !== undefined) {
            input.step = this.editorConfig.step.toString();
        }

        // 输入事件
        input.addEventListener('input', this.handleInput);
        // 键盘事件
        input.addEventListener('keydown', this.handleKeyDown);
        // 失焦事件
        input.addEventListener('blur', this.handleBlur);

        // inputContainer.appendChild(input);
        container.appendChild(input);

        // 调整位置
        if (referencePosition?.rect) {
            this.adjustPosition(referencePosition.rect);
        }
        this.input.focus();
    }

    handleInput = (e: Event) => {
        const input = e.target as HTMLInputElement;
        const newValue = input.value === '' ? undefined : Number(input.value);

        // 验证输入值是否在范围内
        if (newValue !== undefined) {
            if (
                this.editorConfig.min !== undefined &&
                newValue < this.editorConfig.min
            ) {
                input.value = this.editorConfig.min.toString();
            }
            if (
                this.editorConfig.max !== undefined &&
                newValue > this.editorConfig.max
            ) {
                input.value = this.editorConfig.max.toString();
            }
        }

        this.value = newValue;
    };

    handleKeyDown = (e: KeyboardEvent) => {
        switch (e.key) {
            case 'Enter':
                e.stopPropagation();
                if (this.successCallback) {
                    this.successCallback();
                }
                break;
            case 'Escape':
                e.preventDefault();
                if (this.successCallback) {
                    this.successCallback();
                }
                break;
        }
    };

    handleBlur = () => {
        if (this.successCallback) {
            this.successCallback();
        }
    };

    adjustPosition(rect: RectProps) {
        if (!this.input) return;
        this.input.style.top = rect.top + 'px';
        this.input.style.left = rect.left + 'px';
        this.input.style.width = rect.width + 'px';
        this.input.style.height = rect.height + 'px';
    }

    getValue() {
        return this.value;
    }

    onEnd() {
        if (this.input && this.container) {
            this.container.removeChild(this.input);
        }
    }

    isEditorElement(target: HTMLElement) {
        return this.input ? this.input.contains(target) : false;
    }
}

export default NumberEditor;
