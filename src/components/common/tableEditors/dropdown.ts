import { $t } from '@/locales';
import type { EditContext, IEditor, RectProps } from '@visactor/vtable-editors';
import { nextTick } from 'vue';

class SearchDropdownEditor implements IEditor {
    editorConfig: any;
    container: HTMLElement | undefined;
    dropdownContainer: HTMLElement | undefined;
    dropdownList: HTMLElement | undefined;
    searchInput: HTMLInputElement | undefined;
    selectedValue: string | number = '';
    searchText: string = '';
    successCallback: Function | undefined;
    isOpen: boolean = true;
    options: any[] = [];
    filteredOptions: any[] = [];
    highlightedIndex: number = -1;
    optionElements: HTMLElement[] = [];

    constructor(editorConfig: any) {
        this.editorConfig = editorConfig;
    }

    onStart({
        container,
        value,
        referencePosition,
        endEdit,
        table,
        row,
        col,
    }: EditContext) {
        this.container = container;
        this.successCallback = endEdit;
        this.selectedValue = value as string | number;
        const rowData = table.getRecordByCell(col, row);
        /** 有可能有非columns配置生成的列，这里根据实际columns的长度减去实际列长度进行换算得到正确的列信息 */
        const colInfo =
            table.columns[table.columns.length - table.colCount + col];

        // 获取下拉选项
        this.options = this.getOptions(rowData, colInfo);
        this.filteredOptions = [...this.options];

        // 创建自定义下拉框容器
        const dropdownContainer = document.createElement('div');
        this.dropdownContainer = dropdownContainer;
        dropdownContainer.className =
            'vtable-dropdown-editor vtable-search-dropdown-editor';
        dropdownContainer.style.position = 'absolute';
        dropdownContainer.style.width = '100%';
        dropdownContainer.style.boxSizing = 'border-box';
        dropdownContainer.style.backgroundColor = '#fff';
        dropdownContainer.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
        dropdownContainer.style.borderRadius = '2px';
        dropdownContainer.style.zIndex = '1000';

        // 创建搜索输入框
        const searchContainer = document.createElement('div');
        searchContainer.className = 'vtable-dropdown-search';
        searchContainer.style.padding = '0px 8px';
        searchContainer.style.borderBottom = '1px solid #e8e8e8';
        searchContainer.style.position = 'relative';
        searchContainer.style.height = referencePosition?.rect?.height + 'px';

        // 搜索图标
        const searchIcon = document.createElement('span');
        searchIcon.innerHTML = '🔍';
        searchIcon.style.position = 'absolute';
        searchIcon.style.left = '8px';
        searchIcon.style.top = '50%';
        searchIcon.style.transform = 'translateY(-50%)';
        searchIcon.style.fontSize = '12px';
        searchIcon.style.color = '#999';
        searchContainer.appendChild(searchIcon);

        // 搜索输入框
        const searchInput = document.createElement('input');
        this.searchInput = searchInput;
        searchInput.type = 'text';
        searchInput.className = 'vtable-dropdown-search-input';
        searchInput.style.width = '100%';
        searchInput.style.boxSizing = 'border-box';
        searchInput.style.border = 'none';
        searchInput.style.outline = 'none';
        searchInput.style.paddingLeft = '24px';
        searchInput.style.height = referencePosition?.rect?.height - 1 + 'px';

        // 获取当前选中项的标签文本作为初始搜索文本
        const selectedOption = this.options.find((opt) => opt.value === value);
        if (selectedOption) {
            searchInput.value = selectedOption.label;
            this.searchText = selectedOption.label;
        }

        // 输入事件
        searchInput.addEventListener('input', this.handleSearch);

        // 键盘事件
        searchInput.addEventListener('keydown', this.handleKeyDown);

        searchContainer.appendChild(searchInput);
        dropdownContainer.appendChild(searchContainer);

        // 创建选项列表
        const dropdownList = document.createElement('div');
        this.dropdownList = dropdownList;
        dropdownList.className = 'vtable-dropdown-list';
        dropdownList.style.maxHeight = '200px';
        dropdownList.style.overflowY = 'auto';
        dropdownList.style.display = 'block'; // 默认显示，实现自动展开效果

        // 渲染选项列表
        this.renderOptions();

        dropdownContainer.appendChild(dropdownList);
        container.appendChild(dropdownContainer);

        // 点击外部关闭下拉框
        document.addEventListener('mousedown', this.handleOutsideClick);

        if (referencePosition?.rect) {
            this.adjustPosition(referencePosition.rect);
            nextTick(() => {
                if (this.searchInput) {
                    this.searchInput.focus();
                }
            });
        }
    }

    renderOptions() {
        if (!this.dropdownList) return;

        // 清空当前列表
        this.dropdownList.innerHTML = '';
        this.optionElements = [];

        // 添加选项
        this.filteredOptions.forEach((optionItem, index) => {
            const optionElement = document.createElement('div');
            optionElement.className = 'vtable-dropdown-option';

            // 如果有搜索文本且不为空，高亮显示匹配的部分
            if (this.searchText && this.searchText.trim() !== '') {
                const label = optionItem.label;
                const searchText = this.searchText.toLowerCase();
                const labelLower = label.toLowerCase();

                if (labelLower.includes(searchText)) {
                    const startIndex = labelLower.indexOf(searchText);
                    const endIndex = startIndex + searchText.length;

                    // 创建突出显示的HTML
                    const beforeMatch = label.substring(0, startIndex);
                    const match = label.substring(startIndex, endIndex);
                    const afterMatch = label.substring(endIndex);

                    optionElement.innerHTML = `${beforeMatch}<span style="color: #1890ff; font-weight: bold;">${match}</span>${afterMatch}`;
                } else {
                    optionElement.textContent = label;
                }
            } else {
                optionElement.textContent = optionItem.label;
            }

            optionElement.dataset.value = optionItem.value;
            optionElement.dataset.index = index.toString();
            optionElement.style.padding = '4px 8px';
            optionElement.style.cursor = 'pointer';
            optionElement.style.marginBottom = '1px';

            // 高亮当前选中项或者当前高亮项
            if (optionItem.value === this.selectedValue) {
                optionElement.style.backgroundColor = '#e6f7ff';
                optionElement.style.color = '#1890ff';
                this.highlightedIndex = index;
            } else if (index === this.highlightedIndex) {
                optionElement.style.backgroundColor = '#f5f5f5';
            }

            // 鼠标悬停效果
            optionElement.addEventListener('mouseover', () => {
                this.highlightOption(index);
            });

            // 点击选项事件
            optionElement.addEventListener('click', () => {
                this.selectOption(optionItem);
            });

            this.dropdownList?.appendChild(optionElement);
            this.optionElements.push(optionElement);
        });

        // 没有选项时显示无数据
        if (this.filteredOptions.length === 0) {
            const noDataElement = document.createElement('div');
            noDataElement.className = 'vtable-dropdown-no-data';
            noDataElement.textContent = $t('common.common.noData');
            noDataElement.style.padding = '4px 8px';
            noDataElement.style.color = '#999';
            noDataElement.style.textAlign = 'center';
            this.dropdownList.appendChild(noDataElement);
        }
    }

    handleSearch = (e: Event) => {
        const input = e.target as HTMLInputElement;
        this.searchText = input.value;

        // 筛选选项
        this.filteredOptions = this.options.filter((option) =>
            option.label.toLowerCase().includes(this.searchText.toLowerCase())
        );

        // 重置高亮索引
        this.highlightedIndex = -1;

        // 重新渲染选项
        this.renderOptions();
    };

    handleKeyDown = (e: KeyboardEvent) => {
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.highlightNextOption();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.highlightPrevOption();
                break;
            case 'Enter':
                e.preventDefault();
                this.selectHighlightedOption();
                break;
            case 'Escape':
                e.preventDefault();
                if (this.successCallback) {
                    this.successCallback();
                }
                break;
        }
    };

    highlightNextOption() {
        if (this.filteredOptions.length === 0) return;

        this.highlightedIndex =
            (this.highlightedIndex + 1) % this.filteredOptions.length;
        this.updateHighlight();
    }

    highlightPrevOption() {
        if (this.filteredOptions.length === 0) return;

        this.highlightedIndex =
            this.highlightedIndex <= 0
                ? this.filteredOptions.length - 1
                : this.highlightedIndex - 1;
        this.updateHighlight();
    }

    highlightOption(index: number) {
        this.highlightedIndex = index;
        this.updateHighlight();
    }

    updateHighlight() {
        // 更新所有选项的样式
        this.optionElements.forEach((element, index) => {
            const optionValue = element.dataset.value;
            const isSelected = optionValue === this.selectedValue;
            const isHighlighted = index === this.highlightedIndex;

            // 重置样式
            element.style.backgroundColor = '';
            element.style.color = '';

            // 应用合适的样式
            if (isSelected) {
                element.style.backgroundColor = '#e6f7ff';
                element.style.color = '#1890ff';
            } else if (isHighlighted) {
                element.style.backgroundColor = '#e6f7ff';
            }
        });

        // 确保高亮的选项在视图内
        if (
            this.highlightedIndex >= 0 &&
            this.dropdownList &&
            this.optionElements[this.highlightedIndex]
        ) {
            const highlightedElement =
                this.optionElements[this.highlightedIndex];
            const containerTop = this.dropdownList.scrollTop;
            const containerHeight = this.dropdownList.clientHeight;
            const elementTop = highlightedElement.offsetTop;
            const elementHeight = highlightedElement.clientHeight;

            // 如果元素在视图外，滚动到合适位置
            if (elementTop < containerTop) {
                this.dropdownList.scrollTop = elementTop;
            } else if (
                elementTop + elementHeight >
                containerTop + containerHeight
            ) {
                this.dropdownList.scrollTop =
                    elementTop + elementHeight - containerHeight;
            }
        }
    }

    selectHighlightedOption() {
        if (
            this.highlightedIndex >= 0 &&
            this.highlightedIndex < this.filteredOptions.length
        ) {
            const option = this.filteredOptions[this.highlightedIndex];
            this.selectOption(option, true);
        }
    }

    selectOption(option: any, isEnter: boolean = false) {
        console.log(
            '🚀 ~ SearchDropdownEditor ~ selectOption ~ option:',
            option
        );
        this.selectedValue = option.value;
        if (this.searchInput) {
            this.searchInput.value = option.label;
        }
        if (this.editorConfig.onSelect) {
            this.editorConfig.onSelect(this.selectedValue);
        }
        // 如果按下Enter键，则不执行successCallback回调
        if (isEnter) return;
        if (this.successCallback) {
            this.successCallback();
        }
    }

    handleOutsideClick = (e: MouseEvent) => {
        if (
            this.dropdownContainer &&
            e.target instanceof Node &&
            !this.dropdownContainer.contains(e.target)
        ) {
            if (this.successCallback) {
                this.successCallback();
            }
        }
    };

    /** 获取下拉options */
    getOptions(rowData: any, colInfo: any): any[] {
        // 如果配置了自定义返回options的函数，则使用自定义的函数
        if (this.editorConfig.generateOptions) {
            return this.editorConfig.generateOptions(rowData, colInfo);
        }
        // 如果配置了editorConfig.options，则使用配置的选项
        if (colInfo?.editorConfig?.options) {
            return colInfo.editorConfig.options;
        }
        // 否则使用默认选项
        return this.editorConfig.values?.length ? this.editorConfig.values : [];
    }

    adjustPosition(rect: RectProps) {
        if (!this.dropdownContainer) return;
        this.dropdownContainer.style.top = rect.top + 'px';
        this.dropdownContainer.style.left = rect.left + 'px';
        this.dropdownContainer.style.width = rect.width + 'px';
        // 最小高度设置为输入框高度 + 下拉列表最小高度
        // const minHeight = 32 + 100; // 32是输入框高度，100是下拉列表最小高度
        // this.dropdownContainer.style.minHeight = `${minHeight}px`;
    }

    getValue() {
        return this.selectedValue;
    }

    onEnd() {
        if (this.dropdownContainer && this.container) {
            document.removeEventListener('mousedown', this.handleOutsideClick);
            this.container.removeChild(this.dropdownContainer);
        }
    }

    isEditorElement(target: HTMLElement) {
        return this.dropdownContainer
            ? this.dropdownContainer.contains(target)
            : false;
    }
}

export default SearchDropdownEditor;
