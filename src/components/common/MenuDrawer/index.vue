<template>
    <div
        class="hidden lg:block md:hidden sm:hidden w-[200px] tabmain relative after:bg-[#e4e9f2] after:absolute after:w-[1px] after:h-[100%] after:right-[8.5px] after:top-[10px]"
    >
        <div
            v-for="(item, index) in props.detailTab"
            class="flex align-items justify-center py-[5px] pr-[20px] relative min-w-[200px]"
            style="z-index: 1"
            :class="{ iscurrent: currentIndex === index }"
            @click="tabchange(index)"
        >
            <span
                class="tabspan w-[100%] bg-[#ebecf0] text-right cursor-pointer rounded-[4px] h-[35px] leading-[35px] px-[10px] relative hover:bg-[#20469B] hover:text-[#ffffff] iscurrent:bg-[#20469B] iscurrent:text-[#ffffff] after:bg-[#e4e9f2] after:rounded-full after:h-[8px] after:w-[8px] after:absolute after:right-[-15px] after:top-[8px] after:hover:bg-[#20469B] iscurrent:after:bg-[#20469B]"
                :class="{ iscurrent: currentIndex === index }"
                >{{ item.name }}</span
            >
        </div>
    </div>

    <!-- 移动端时左侧显示 -->
    <div
        ref="menuRef"
        class="fixed lg:hidden left-0 top-[60px] z-40 h-[calc(100vh-60px)]"
    >
        <button
            class="fixed lg:hidden left-0 top-[10%] z-50 p-2 bg-gray-200 rounded"
            v-if="!isMenuOpen"
        >
            {{ isMenuOpen ? '×' : '≡' }}
        </button>
        <div
            v-if="isMenuOpen"
            :class="{ iscurrent: isMenuOpen === true }"
            class="w-0 bg-[#ffffff] h-[100%] pl-0 lg:hidden opacity-0 shadow-lg iscurrent:w-[200px] iscurrent:opacity-100 transition-all duration-200"
            @click="isMenuOpen = false"
        >
            <div
                v-for="(item, index) in detailTab"
                class="bg-[#ebecf0] text-right cursor-pointer mb-2 h-[35px] leading-[35px] px-[10px] hover:bg-[#20469B] hover:text-[#ffffff] iscurrent:bg-[#20469B] iscurrent:text-[#ffffff]"
                :class="{ iscurrent: currentIndex === index }"
                @click="tabchange(index)"
            >
                {{ item.name }}
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
// 定义组件接收的props
const props = defineProps({
    detailTab: Array,
    current: Number,
});
// 定义组件发出的事件
const emit = defineEmits(['success']);

const menuRef = ref<HTMLElement | null>(null);
const isMenuOpen = ref(false); // 控制菜单的打开和关闭状态
const currentIndex = ref(0);
//tab切换
const tabchange = (index: number) => {
    isMenuOpen.value = false;
    currentIndex.value = index;
    emit('success', index);
};
// 处理全局点击事件
const handleClickOutside = (event: MouseEvent) => {
    if (menuRef.value && !menuRef.value.contains(event.target as Node)) {
        if (isMenuOpen.value === true) {
            isMenuOpen.value = false;
        }
    } else {
        isMenuOpen.value = !isMenuOpen.value;
    }
};
onMounted(() => {
    currentIndex.value = props.current;
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});
</script>
