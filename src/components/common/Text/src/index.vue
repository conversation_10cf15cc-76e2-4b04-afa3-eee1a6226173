<template>
    <component :is="tag" :class="textClasses">
        {{ valueRef }}
    </component>
</template>

<script setup lang="ts">
import { isFunction } from 'lodash-es';
import { computed, ref, watch } from 'vue';
import type { PropType } from 'vue';

// 定义组件名称
defineOptions({
    name: 'TextDisplay',
});

// 定义支持的文本类型
type TextType =
    | 'title' // 主标题
    | 'subTitle' // 副标题
    | 'content' // 正文内容
    | 'description' // 描述说明
    | 'label' // 标签
    | 'quotation' // 引用
    | 'success' // 成功状态
    | 'warning' // 警告状态
    | 'error' // 错误状态
    | 'info' // 信息提示
    | 'tip' // 小贴士
    | 'disabled' // 禁用状态
    | 'link' // 链接
    | 'code' // 代码片段
    | 'metadata'; // 元数据

// 定义组件props
const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    record: {
        type: Object,
    },
    type: {
        type: String as PropType<TextType>,
        default: 'content',
        validator: (value: TextType) =>
            [
                'title',
                'subTitle',
                'content',
                'description',
                'label',
                'quotation',
                'success',
                'warning',
                'error',
                'info',
                'tip',
                'disabled',
                'link',
                'code',
                'metadata',
            ].includes(value),
    },
    bold: {
        type: Boolean,
        default: false,
    },
    italic: {
        type: Boolean,
        default: false,
    },
    underline: {
        type: Boolean,
        default: false,
    },
    clickable: {
        type: Boolean,
        default: false,
    },
    formatter: {
        type: Function,
    },
    class: {
        type: String,
    },
});

// 根据类型确定HTML标签
const tag = computed(() => {
    switch (props.type) {
        case 'title':
            return 'h1';
        case 'subTitle':
            return 'h2';
        case 'quotation':
            return 'blockquote';
        case 'code':
            return 'code';
        default:
            return 'span';
    }
});

const valueRef = ref(props.modelValue);

watch(
    () => props.record,
    (n) => {
        if (props.formatter && isFunction(props.formatter)) {
            valueRef.value = props.formatter(props.record);
        } else {
            valueRef.value = props.modelValue;
        }
    },
    {
        deep: true,
    }
);

// const getValue = computed(() => {
//     if (props.formatter && isFunction(props.formatter)) {
//         return props.formatter(props.record);
//     }
//     return props.modelValue;
// });

// 文本样式类
const textClasses = computed(() => {
    const baseClasses = [
        'transition-colors',
        'duration-200',
        props.bold ? 'font-bold' : '',
        props.italic ? 'italic' : '',
        props.underline ? 'underline' : '',
        props.clickable ? 'cursor-pointer hover:opacity-80' : '',
    ];

    const typeClasses = {
        title: 'text-2xl font-bold text-gray-900 font-semibold text-base',
        subTitle: 'text-xl font-semibold text-gray-800',
        content: 'text-base text-gray-700',
        description: 'text-sm text-gray-500',
        label: 'font-medium uppercase tracking-wider text-gray-600 bg-gray-100 px-2 py-1 rounded',
        quotation: 'text-gray-600 italic border-l-4 border-gray-300 pl-4',
        success: 'text-green-600 bg-green-50 px-2 py-1 rounded',
        warning: 'text-amber-600 bg-amber-50 px-2 py-1 rounded',
        error: 'text-red-600 bg-red-50 px-2 py-1 rounded',
        info: 'text-blue-600 bg-blue-50 px-2 py-1 rounded',
        tip: 'text-purple-600 bg-purple-50 px-2 py-1 rounded',
        disabled: 'text-gray-400 cursor-not-allowed',
        link: 'text-blue-600 hover:text-blue-800 hover:underline',
        code: 'font-mono text-sm bg-gray-800 text-gray-100 px-2 py-1 rounded',
        metadata: 'text-gray-400',
    }[props.type];

    return [...baseClasses, typeClasses, props.class].filter(Boolean).join(' ');
});
</script>
