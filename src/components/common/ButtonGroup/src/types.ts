export interface ButtonGroupProps {
    type?: 'group';
    name?: string;
    icon?: string;
    variant?:
        | 'default'
        | 'primary'
        | 'link'
        | 'destructive'
        | 'outline'
        | 'secondary'
        | 'ghost'
        | 'success'
        | 'info'
        | 'warning'
        | 'danger'
    permission?: string | string[];
    on?: Recordable;
    onClick?: (row: Recordable) => void;
    props?: Recordable;
    disabled?: () => boolean;
    list?: ButtonGroupProps[];
    ifShow?: boolean | ((record: Recordable) => boolean);
}
