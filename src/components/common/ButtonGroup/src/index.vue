<template>
    <div class="flex">
        <!-- <div class="flex-shrink-0 flex-grow-0 flex items-center mr-2 text-lg">
            {{ $route.meta.title || '未知页面' }}
        </div> -->
        <slot name="before" />
        <div class="flex-1 flex flex-wrap items-center cursor-pointer">
            <template v-for="(btnItem, index) in btnList">
                <template v-if="btnItem?.type === 'group'">
                    <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                            <slot v-if="$slots.trigger" name="trigger" />
                            <Button
                                v-else
                                variant="outline"
                                class="px-3 py-2 text-[12px] mr-2"
                                :disabled="btnDisabled(btnItem.disabled)"
                            >
                                {{ btnItem?.name || $t('common.button.more') }}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent class="min-w-[100px]">
                            <DropdownMenuItem
                                class="cursor-pointer hover:bg-gray-100 px-3 py-2 text-[12px]"
                                v-bind="getBind(subBtn)"
                                v-for="subBtn in btnItem.list"
                            >
                                {{ subBtn.name }}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </template>

                <template v-else>
                    <Button
                        :key="index"
                        class="mr-2 px-2 text-[12px] flex items-center gap-1"
                        v-bind="getBind(btnItem)"
                        :disabled="btnDisabled(btnItem.disabled)"
                    >
                        <!-- 渲染图标 -->
                        <Icon
                            v-if="btnItem.icon"
                            :icon="btnItem.icon"
                            class="h-6 w-6"
                        ></Icon>
                        <!-- <img
                            v-if="btnItem.icon"
                            :src="btnItem.icon"
                            width="14"
                            height="14"
                        /> -->
                        {{ btnItem.name }}
                    </Button>
                </template>
            </template>
        </div>
    </div>
</template>

<script lang="ts">
import { computed, defineComponent, PropType } from 'vue';
import {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { convertToOnEventName } from '../../Table/src/helper';
import { ButtonGroupProps } from './types';
import { get, isBoolean, isFunction, isObject } from 'lodash-es';
import { hasPermission } from '@/directives/modules/permission';
import { Icon } from '@iconify/vue';

export default defineComponent({
    name: 'ButtonGroup',
    components: {
        DropdownMenu,
        DropdownMenuTrigger,
        DropdownMenuContent,
        DropdownMenuItem,
        Button,
        Icon,
    },
    props: {
        list: {
            type: Array as PropType<ButtonGroupProps[]>,
        },
        record: {
            type: Object,
        },

        ifShow: {
            type: [Function, Boolean],
            default: true,
        },
    },
    emits: [],
    setup(props, { emit, attrs }) {
        const modeMps = {
            primary:
                'p-2 bg-primary text-primary-foreground shadow hover:bg-primary/90 rounded-md transition-colors duration-200',
            info: 'p-2 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors duration-200',
            success:
                'p-2 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors duration-200',
            // info: 'p-2 bg-cyan-100 text-cyan-600 rounded-md hover:bg-cyan-200 transition-colors duration-200',
            warning:
                'p-2 bg-amber-100 text-amber-600 rounded-md hover:bg-amber-200 transition-colors duration-200',
            danger: 'p-2 bg-red-100 text-red-600 rounded-md hover:bg-red-200 transition-colors duration-200',
            link: 'p-2 text-blue-600 hover:bg-blue-50 hover:underline underline-offset-4 transition-colors duration-200',
            destructive:
                'p-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors duration-200',
            outline:
                'p-2 bg-transparent border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200',
            secondary:
                'p-2 bg-gray-50 text-gray-500 rounded-md hover:bg-gray-100 transition-colors duration-200',
            ghost: 'p-2 bg-transparent text-gray-700 hover:bg-gray-100 transition-colors duration-200',
        };

        const btnList = computed(() => {
            return props.list?.filter((btn) => {
                if (btn.ifShow) {
                    const show = isBoolean(btn.ifShow)
                        ? btn.ifShow
                        : isFunction(btn.ifShow)
                          ? btn.ifShow(props.record)
                          : true;

                    return show;
                }

                if (btn.type === 'group') {
                    return true;
                }

                if (!btn.permission) {
                    return true;
                    console.error(
                        `按钮: ${btn.name} 需要配置权限字段 permission`
                    );
                    return false;
                }
                return hasPermission(btn.permission);
            });
        });

        const getBind = computed(() => {
            // 缓存事件处理逻辑
            const createEventHandlers = (
                on: Record<string, Function>,
                record: any
            ) => {
                return Object.keys(on || {}).reduce(
                    (events, event) => {
                        const convertedEvent = convertToOnEventName(event);
                        if (convertedEvent) {
                            events[convertedEvent] = (...args: any[]) =>
                                on[event](...args, props.record);
                        }
                        return events;
                    },
                    {} as Record<string, Function>
                );
            };

            return (item: any) => {
                const { on, props } = item;
                return {
                    ...props, // 优先展开 props
                    ...item, // item 属性可覆盖 props
                    type: item.type || 'button',
                    variant:
                        item.variant === 'primary' ? 'default' : item.variant,
                    class: get(modeMps, item.variant || 'default'),
                    ...createEventHandlers(on, props?.record),
                };
            };
        });

        // 判断按钮是否禁用
        const btnDisabled = (
            disabled: boolean | ((...args: any[]) => boolean) | undefined
        ) => {
            if (!disabled) return false;
            if (typeof disabled === 'function') {
                return disabled();
            }
            return disabled;
        };
        return {
            getBind,
            btnList,
            btnDisabled,
        };
    },
});
</script>
