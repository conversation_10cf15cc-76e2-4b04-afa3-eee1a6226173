import z from 'zod';
import { $t } from '@/locales';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get } from 'lodash-es';
import { getDict, updateDict } from '@/utils/dict';
import { create } from '@/api/scr/supplier';

const { openDialogForm } = useGlobDialogForm();
const { openDrawerForm } = useGlobDrawerForm();

export const addSupplier = async (opt, field, { updateSchema }) => {
    console.log('addSupplier', opt, field, this);

    const api = openDialogForm({
        dialogProps: {
            size: 'middle',
            title: $t('menu.bas.paymentTerms.add'),
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 2,
            xl: 2,
            '2xl': 2,
        },
        labelPosition: 'top',
        fields: [
            {
                label: '供应商编码',
                field: 'code',
                required: true,
                component: 'input',
                validation: z.string({
                    required_error: '供应商编码不能为空',
                }),
            },
            {
                label: '供应商名称',
                field: 'name',
                component: 'input',
                required: true,
                validation: z
                    .string({ required_error: '供应商名称不能为空' })
                    .refine((val) => val !== undefined, '供应商名称不能为空'),
            },
            {
                label: '供应商类型',
                field: 'type_id',
                component: 'select',
                dict: 'SUPPLIER_TYPE',
                required: true,
                componentProps: {
                    showFilter: true,
                },
                validation: z.number({
                    required_error: '供应商类型不能为空',
                }),
            },
            {
                label: '社会统一信用代码',
                field: 'uscc',
                component: 'input',
            },
            {
                label: '供应商属性',
                field: 'attribute',
                component: 'select',
                required: true,
                componentProps: {
                    options: [
                        { label: '经销商', value: '经销商' },
                        { label: '委外商', value: '委外商' },
                    ],
                },
                validation: z.string({
                    required_error: '供应商属性不能为空',
                }),
            },
            {
                label: '供应商地址',
                field: 'address',
                component: 'input',
            },
            {
                label: '供应商标签',
                field: 'label_id',
                component: 'select',
                dict: 'LABEL',
                required: true,
                componentProps: {
                    showFilter: true,
                },
                validation: z.number({
                    required_error: '供应商标签不能为空',
                }),
            },
            {
                label: '备注',
                field: 'notes',
                component: 'textarea',
                span: true,
            },
        ],
        submit: async (params) => {
            await create(params);
            await updateDict(opt.code);

            const options = await getDict(opt.code);
            if (field && updateSchema) {
                const dictAlias = {
                    label: 'name',
                    value: 'id',
                };

                updateSchema({
                    field,
                    componentProps: {
                        options: options?.map((item) => {
                            return {
                                ...item,
                                label: get(item, dictAlias.label),
                                value: get(item, dictAlias.value),
                            };
                        }),
                    },
                });
            }
        },
    });
};
