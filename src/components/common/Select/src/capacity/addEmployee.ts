import z from 'zod';
import { $t } from '@/locales';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get } from 'lodash-es';
import { getDict, updateDict } from '@/utils/dict';
import { createEmployee } from '@/api/hr/employee';

export const addEmployee = async (opt, field, { updateSchema }) => {
    console.log('addEmployee', opt, field, this);
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const api = openDialogForm({
        dialogProps: {
            size: 'middle',
            title: '新增员工',
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 2,
            xl: 2,
            '2xl': 2,
        },
        labelPosition: 'top',
        fields: [
            {
                label: '员工工号',
                field: 'employee_no',
                component: 'input',
                validation: z.string({
                    required_error: '员工工号不能为空',
                }),
            },
            {
                label: '员工姓名',
                field: 'nick_name',
                component: 'input',

                validation: z.string({
                    required_error: '员工姓名不能为空',
                }),
            },
            {
                label: '所属组织',
                field: 'factory_id',
                component: 'select',
                dict: 'factoryList',
                defaultValue: 468734683393344,
                validation: z.number({
                    required_error: '所属组织不能为空',
                }),
            },
            {
                label: '所属部门',
                field: 'dept_id',
                component: 'select',
                dict: 'deptList',
                componentProps: {
                    showFilter: true,
                    onSelect(val, option) {
                        api.setValues(
                            {
                                dept_id: val,
                            },
                            false
                        );
                    },
                },
                validation: z.number({
                    required_error: '所属部门不能为空',
                }),
            },
            {
                label: '所属岗位',
                field: 'post_id',
                component: 'select',
                dict: 'POST',
                validation: z.number({
                    required_error: '所属岗位不能为空',
                }),
            },
            {
                label: '用工类型',
                field: 'employee_type_id',
                dict: 'EMPLOYEE_TYPE',
                component: 'select',
            },
            {
                label: '人员状态',
                field: 'employee_state_id',
                dict: 'EMPLOYEE_STATE',
                component: 'select',
                defaultValue: 81,
                validation: z.number({
                    required_error: '人员状态不能为空',
                }),
            },
        ],
        submit: async (params) => {
            await createEmployee(params);
            await updateDict(opt.code);

            const options = await getDict(opt.code);
            if (field && updateSchema) {
                const dictAlias = {
                    label: 'name',
                    value: 'id',
                };

                updateSchema({
                    field,
                    componentProps: {
                        options: options?.map((item) => {
                            return {
                                ...item,
                                label: get(item, dictAlias.label),
                                value: get(item, dictAlias.value),
                            };
                        }),
                    },
                });
            }
        },
    });
};
