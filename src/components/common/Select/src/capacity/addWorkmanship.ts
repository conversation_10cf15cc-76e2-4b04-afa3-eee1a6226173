import z from 'zod';
import { $t } from '@/locales';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get, isFunction } from 'lodash-es';
import { enumDict } from '@/enums/dict';
import {
    createWorkmanship,
} from '@/api/bas/workmanship/index';

export const addWorkmanship = async (opt, field, Options) => {
    console.log('addWorkmanship', opt, field, Options);
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const api = openDialogForm({
        dialogProps: {
            size: 'middle',
            title: '新增工序',
            zIndex: 50,
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 2,
            xl: 2,
            '2xl': 2,
        },
        //labelPosition: 'top',
        fields: [
            {
                label: $t('product.table.factory'),
                field: 'factory_id',
                component: 'select',
                required: true,
                dict: 'factoryList',
                defaultValue: 468734683393344,
                validation: z.number({
                    required_error: $t('common.validation.required', { field: $t('product.table.factory') }),
                }),
            },
            {
                label: '工序编号',
                field: 'code',
                required: true,
                component: 'input',
                validation: z.string({
                    required_error: '工序编号不能为空',
                }),
            },
            {
                label: '工序名称',
                field: 'name',
                component: 'input',
                required: true,
                validation: z.string({
                    required_error: '工序名称不能为空',
                }),
            },

            {
                label: '工序类型',
                field: 'wip_type',
                component: 'select',
                dict: 'workmanship_enum',
                componentProps: {
                    showFilter: true,
                    onSelect(val, option) {
                        // api.setValues(
                        //     {
                        //         wip_type: val,
                        //     },
                        //     false
                        // );
                    },
                }
            },
            {
                label: '检验否',
                field: 'is_check',
                component: 'checkbox',
            },
            {
                label: '委外否',
                field: 'is_out',
                component: 'checkbox',
                componentProps: {},
            },
            {
                label: '交接否',
                field: 'is_handover',
                component: 'checkbox',
            },
            {
                label: '多品种并行加工否',
                field: 'is_multiple',
                component: 'checkbox',
            },
            {
                label: '启用否',
                field: 'valid',
                component: 'checkbox',
                defaultValue: true,
                required: true,
                validation: z
                    .any()
                    .refine((val) => val !== undefined, '请选择'),
            },
            {
                label: '备注',
                field: 'notes',
                component: 'textarea',
                span: true,
            },
        ],
        submit: async (params) => {
            const res = await createWorkmanship(params);
            //await updateDict(opt.code);
            if (isFunction(opt.callback)) {
                opt.callback(res);
            }

        },
    });
};
