import z from 'zod';
import { $t } from '@/locales';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get } from 'lodash-es';
import { getDict, updateDict } from '@/utils/dict';
import { createDept } from '@/api/hr/dept';

const { openDialogForm } = useGlobDialogForm();
const { openDrawerForm } = useGlobDrawerForm();

export const addDept = async (opt, field, { updateSchema, setValues }) => {
    console.log('addDept', opt, field, this);

    const api = openDialogForm({
        dialogProps: {
            size: 'middle',
            title: '新增部门',
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 2,
            xl: 2,
            '2xl': 2,
        },
        labelPosition: 'top',
        fields: [
            {
                field: 'parent_name',
                label: '上级部门',
                component: 'input',
                componentProps: {
                    disabled: true,
                },
            },
            {
                field: 'name',
                label: '部门名称',
                component: 'input',
                required: true,
                validation: z
                    .string({ required_error: '部门名称不能为空' })
                    .nonempty('部门名称不能为空'),
            },
            {
                field: 'code',
                label: '部门编码',
                component: 'input',
                required: true,
                validation: z
                    .string({ required_error: '部门编码不能为空' })
                    .nonempty('部门编码不能为空'),
            },
            {
                field: 'notes',
                label: '部门描述',
                component: 'input',
                validation: z.string().nonempty(),
            },
            {
                field: 'sort_order',
                label: '部门排序',
                component: 'numberInput',
                validation: z.number().optional(),
            },
        ],
        submit: async (params) => {
            await createDept(params);
            await updateDict(opt.code);

            const options = await getDict(opt.code);
            if (field && updateSchema) {
                const dictAlias = {
                    label: 'name',
                    value: 'id',
                };

                updateSchema({
                    field,
                    componentProps: {
                        options: options?.map((item) => {
                            return {
                                ...item,
                                label: get(item, dictAlias.label),
                                value: get(item, dictAlias.value),
                            };
                        }),
                    },
                });

                const item = findItemByBFS(options, params.code);

                setValues({ [field]: get(item, dictAlias.value) }, false);
            }

            return undefined;
        },
    });
};

function findItemByBFS(data, targetId, childrenKey = 'children') {
    const queue = [...data]; // 初始化队列，加入所有根节点
    while (queue.length > 0) {
        const node = queue.shift(); // 取出队首节点
        if (node.code === targetId) return node; // 找到目标项
        if (node[childrenKey]?.length) {
            queue.push(...node[childrenKey]); // 将子节点加入队列尾部
        }
    }
    return null; // 未找到
}
