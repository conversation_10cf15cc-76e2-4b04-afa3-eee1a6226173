import z from 'zod';
import { $t } from '@/locales';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get, isFunction } from 'lodash-es';
import {
    createMaterialInfo,
} from '@/api/ops/material_list/main';
export const addMaterials = async (opt, field, Options) => {
    console.log('addMaterials', opt, field, Options);
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const api = openDialogForm({
        dialogProps: {
            size: 'middle',
            title: '新增物料',
            zIndex: 50,
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 2,
            xl: 2,
            '2xl': 2,
        },
        //labelPosition: 'top',
        fields: [
            {
                label: '物料类别',
                field: 'type_id',
                component: 'select',
                required: true,
                defaultValue: opt.type_id,
                dict: 'materialTypeList',
                componentProps: {
                    disabled: true,
                    showFilter: true,
                    onSelect(val, option) {
                        console.log("物料类别", val, option)
                        api.setValues(
                            {
                                opt_type: option.opt_type,
                            },
                            false
                        );
                    },
                },
                validation: z.number({
                    required_error: '物料类别不能为空',
                }),
            },
            {
                label: '物料编码',
                field: 'code',
                required: true,
                component: 'input',
                validation: z.string({
                    required_error: '物料编码不能为空',
                }),
            },
            {
                label: '物料名称',
                field: 'name',
                component: 'input',
                required: true,
                validation: z.string({
                    required_error: '物料名称不能为空',
                }),
            },
            {
                label: '物料别名',
                field: 'alias',
                component: 'input',
            },

            {
                label: '品牌',
                field: 'brand_id',
                component: 'select',
                dict: 'BRAND',
                componentProps: {
                    customItems: [
                        {
                            type: 'addDict',
                            field: 'brand_id',
                            params: {
                                code: 'BRAND',
                            },
                        },
                    ],
                },
            },
            {
                label: '产地',
                field: 'place_origin_id',
                component: 'select',
                dict: 'PLACE_ORIGIN',
                componentProps: {
                    customItems: [
                        {
                            type: 'addDict',
                            field: 'place_origin_id',
                            params: {
                                code: 'PLACE_ORIGIN',
                            },
                        },
                    ],
                },
            },
            {
                label: '材质',
                field: 'material_quality',
                component: 'input',
            },
            {
                label: '颜色',
                field: 'color_id',
                component: 'select',
                dict: 'COLOR',
                componentProps: {
                    customItems: [
                        {
                            type: 'addDict',
                            field: 'color_id',
                            params: {
                                code: 'COLOR',
                            },
                        },
                    ],
                },
            },
            {
                label: '规格型号',
                field: 'model',
                component: 'input',
            },
            {
                label: '优化',
                field: 'opt_type',
                component: 'radio',
                defaultValue: opt.opt_type,
                componentProps: {
                    disabled: true,
                    options: [
                        { label: '长度优化', value: 2 },
                        { label: '面积优化', value: 1 },
                        { label: '不优化', value: 0 },
                    ],
                },
            },
            {
                label: '厚度(mm)',
                field: 'deep',
                component: 'numberInput',
                componentProps: {
                    min: 0,
                },
            },
            {
                label: '等级',
                field: 'grade_id',
                component: 'select',
                dict: 'GRADE',
            },
            {
                label: '使用位置',
                field: 'position',
                component: 'input',
            },
            {
                label: '备注',
                field: 'notes',
                component: 'textarea',
                span: 4,
                componentProps: {
                    rows: 3,
                },
            },
        ],
        submit: async (params) => {
            const res = await createMaterialInfo(params);
            //await updateDict(opt.code);
            if (isFunction(opt.callback)) {
                opt.callback(res);
            }

        },
    });

};
