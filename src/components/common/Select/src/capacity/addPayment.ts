import z from 'zod';
import { $t } from '@/locales';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get } from 'lodash-es';
import { getDict, updateDict } from '@/utils/dict';
import { create } from '@/api/bas/payment_terms';

export const addPayment = async (opt, field, { updateSchema }) => {
    console.log('addPayment', opt, field, this);
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const api = openDialogForm({
        dialogProps: {
            size: 'middle',
            title: $t('menu.bas.paymentTerms.add'),
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 2,
            xl: 2,
            '2xl': 2,
        },
        labelPosition: 'top',
        fields: [
            {
                field: 'name',
                label: '付款条款名称',
                component: 'input',
                required: true,
                validation: z
                    .string({ required_error: '付款条款不能为空' })
                    .nonempty('付款条款不能为空'),
            },
            {
                field: 'days',
                label: '账期天数',
                component: 'numberInput',
                componentProps: {
                    min: 0,
                },
                // validation: z.number().gt(0).optional(), //正整数
                validation: z.preprocess(
                    (value) => {
                        console.log('账期天数', value);
                        if (typeof value === 'string') {
                            return parseFloat(value);
                        }
                        return value;
                    },
                    z.number({ required_error: '账期天数不能为负数' })
                ),
            },
            {
                field: 'monthly_settlement',
                label: '是否月结',
                component: 'radio',
                componentProps: {
                    options: [
                        { label: '是', value: true },
                        { label: '否', value: false },
                    ],
                },
                defaultValue: false,
                validation: z.boolean().optional(),
            },
            {
                field: 'sign',
                label: '签收后才付款',
                component: 'radio',
                componentProps: {
                    options: [
                        { label: '是', value: true },
                        { label: '否', value: false },
                    ],
                },
                defaultValue: false,
                validation: z.boolean().optional(),
            },
            {
                field: 'payment_before_shipment',
                label: '是否先款后货',
                component: 'radio',
                componentProps: {
                    options: [
                        { label: '是', value: true },
                        { label: '否', value: false },
                    ],
                },
                defaultValue: false,
                validation: z.boolean().optional(),
            },
            {
                field: 'valid',
                label: '状态',
                component: 'radio',
                componentProps: {
                    options: [
                        { label: '正常', value: true },
                        { label: '停用', value: false },
                    ],
                },
                defaultValue: true,
                validation: z.boolean().optional(),
            },
            {
                field: 'notes',
                label: '备注',
                component: 'textarea',
                validation: z.string().optional(),
                span: true,
            },
        ],
        submit: async (params) => {
            await create(params);
            await updateDict(opt.code);

            const options = await getDict(opt.code);
            if (field && updateSchema) {
                const dictAlias = {
                    label: 'name',
                    value: 'id',
                };

                updateSchema({
                    field,
                    componentProps: {
                        options: options?.map((item) => {
                            return {
                                ...item,
                                label: get(item, dictAlias.label),
                                value: get(item, dictAlias.value),
                            };
                        }),
                    },
                });
            }
        },
    });
};
