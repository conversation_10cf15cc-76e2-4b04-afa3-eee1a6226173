import z from 'zod';
import { $t } from '@/locales';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get } from 'lodash-es';
import { getDict, updateDict } from '@/utils/dict';
import { createCurrency } from '@/api/bas/currency';

export const addCurrency = async (opt, field, { updateSchema }) => {
    console.log('addCurrency', opt, field, this);
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const api = openDialogForm({
        dialogProps: {
            size: 'middle',
            title: $t('common.button.create'),
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 2,
            xl: 2,
            '2xl': 2,
        },
        labelPosition: 'top',
        fields: [
            {
                field: 'code',
                label: '货币编码',
                component: 'input',
                required: true,
                validation: z
                    .string({ required_error: '货币编码不能为空' })
                    .nonempty('货币编码不能为空'),
            },
            {
                field: 'name',
                label: '货币名称',
                component: 'input',
                required: true,
                validation: z
                    .string({ required_error: '货币名称不能为空' })
                    .nonempty('货币名称不能为空'),
            },
            {
                field: 'alias',
                label: '货币别名',
                component: 'input',
                validation: z.string().optional(),
            },
            {
                field: 'symbol',
                label: '货币符号',
                component: 'input',
                validation: z.string().optional(),
            },
            {
                field: 'exchange_rate',
                label: '汇率',
                component: 'numberInput',
                componentProps: {
                    min: 0,
                    step: 0.1,
                },
                required: true,
                /** 校验后转数字 */
                validation: z.preprocess((value) => {
                    if (typeof value === 'string') {
                        return parseFloat(value);
                    }
                    return value;
                }, z.number()),
            },
            {
                field: 'standard_currency',
                label: '是否本币',
                component: 'radio',
                componentProps: {
                    options: [
                        { label: '是', value: true },
                        { label: '否', value: false },
                    ],
                },
                validation: z.boolean().optional(),
                defaultValue: false,
            },
            {
                field: 'valid',
                label: '状态',
                component: 'radio',
                componentProps: {
                    options: [
                        { label: '正常', value: true },
                        { label: '停用', value: false },
                    ],
                },
                validation: z.boolean().optional(),
                defaultValue: true,
            },
            {
                field: 'notes',
                label: '备注',
                component: 'textarea',
                validation: z.string().optional(),
                span: true,
            },
        ],
        submit: async (params) => {
            await createCurrency(params);
            await updateDict(opt.code);

            const options = await getDict(opt.code);
            if (field && updateSchema) {
                const dictAlias = {
                    label: 'name',
                    value: 'id',
                };

                updateSchema({
                    field,
                    componentProps: {
                        options: options?.map((item) => {
                            return {
                                ...item,
                                label: get(item, dictAlias.label),
                                value: get(item, dictAlias.value),
                            };
                        }),
                    },
                });
            }
        },
    });
};
