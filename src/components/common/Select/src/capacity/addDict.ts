import z from 'zod';
import { $t } from '@/locales';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get } from 'lodash-es';
import {
    createDictData,
    getDictTypeList,
    updateDictData,
} from '@/api/bas/dict';
import { nextTick } from 'vue';
import { getDict, updateDict } from '@/utils/dict';

export const dictSchemas = [
    {
        field: 'dict_type_name',
        label: '字典类型',
        component: 'input',
        componentProps: {
            disabled: true,
        },
        validation: z.any().optional(),
    },
    {
        field: 'name',
        label: '字典数据名',
        component: 'input',
        required: true,
        validation: z
            .string({ required_error: '字典数据名不能为空' })
            .nonempty('字典数据名不能为空'),
    },
    {
        field: 'value',
        label: '字典数据值',
        component: 'input',
        // required: true,
        // validation: z
        //     .string({ required_error: '字典数据值不能为空' })
        //     .nonempty('字典数据值不能为空'),
    },
    {
        field: 'sort',
        label: '排序',
        component: 'numberInput',
        componentProps: {
            min: 0,
        },
        validation: z.preprocess(
            (value) => {
                if (typeof value === 'string') {
                    return parseFloat(value);
                }
                return value;
            },
            z
                .number({ required_error: '排序不能为空' })
                .min(0, { message: '排序不能小于0' })
        ),
    },
    {
        field: 'valid',
        label: '状态',
        component: 'radio',
        componentProps: {
            options: [
                { label: '正常', value: true },
                { label: '停用', value: false },
            ],
        },
        validation: z.boolean().optional(),
    },
    {
        field: 'cssClass',
        label: '样式属性',
        component: 'select',
        componentProps: {
            options: [
                { label: 'primary', value: 'primary' },
                { label: 'text-navy', value: 'text-navy' },
                { label: '成功', value: '成功' },
                { label: '信息', value: '信息' },
                { label: '警告', value: '警告' },
                { label: '危险', value: '危险' },
                { label: '橘红色', value: 'text-purple' },
                { label: '粉红色', value: '粉红色' },
                { label: '绿色', value: '绿色' },
                { label: '黄绿色', value: '黄绿色' },
            ],
        },
        validation: z.string().optional(),
    },
    {
        field: 'listClass',
        label: '回显样式',
        component: 'select',
        componentProps: {
            options: [
                { label: '默认', value: '默认' },
                { label: '主要', value: '主要' },
                { label: '成功', value: '成功' },
                { label: '信息', value: '信息' },
                { label: '警告', value: '警告' },
                { label: '危险', value: '危险' },
            ],
        },
        validation: z.string().optional(),
    },
    {
        field: 'notes',
        label: '备注',
        component: 'textarea',
        // validation: z.string().optional(),
        span: true,
    },
];

export const addDict = async (
    opt,
    field,
    { updateSchema },
    row?: Recordable
) => {
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    return new Promise(async (resolve, reject) => {
        console.log('addDict', opt, field, this);
        const params = {
            offset: 0,
            limit: 20,
            filters: {
                couple: 'and',
                conditions: [
                    {
                        id: '0',
                        couple: 'and',
                        conditions: [
                            {
                                id: '0-0',
                                field: 'code',
                                op: 'eq',
                                value: opt.code,
                            },
                        ],
                    },
                ],
            },
        };
        const res = await getDictTypeList(params);
        // if (!res.items?.length) return;

        const { id: dict_type_id, code, name: dict_type_name } = res.items?.[0];
        const { name, value, sort, valid, id } = row || {};
        const api = openDialogForm({
            dialogProps: {
                size: 'small',
                title: $t('menu.bas.dict.add'),
                zIndex: 50,
            },
            cols: {
                sm: 1,
                md: 1,
                lg: 1,
                xl: 2,
                '2xl': 2,
            },
            labelPosition: 'top',
            fields: dictSchemas,
            submit: async (params) => {
                params.id
                    ? await updateDictData(params)
                    : await createDictData(params);
                code && (await updateDict(code));
                const options = await getDict(opt.code);
                if (field && updateSchema) {
                    const dictAlias = {
                        label: 'name',
                        value: 'id',
                    };

                    updateSchema({
                        field,
                        componentProps: {
                            options: options?.map((item) => {
                                return {
                                    ...item,
                                    label: get(item, dictAlias.label),
                                    value: get(item, dictAlias.value),
                                };
                            }),
                        },
                    });
                }
                resolve(null);

                return undefined;
            },
        });
        await nextTick();
        api.setValues({
            dict_type_name,
            dict_type_id,
            name,
            value,
            sort,
            valid,
            id,
        });
    });
};
