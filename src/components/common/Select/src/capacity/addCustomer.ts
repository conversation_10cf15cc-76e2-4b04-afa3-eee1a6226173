import z from 'zod';
import { $t } from '@/locales';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get } from 'lodash-es';
import { getDict, updateDict } from '@/utils/dict';
import { createCustomer } from '@/api/sal/customer';

export const addCustomer = async (opt, field, { updateSchema }) => {
    console.log('addCustomer', opt, field, this);
    const { openDialogForm } = useGlobDialogForm();
    const { openDrawerForm } = useGlobDrawerForm();

    const api = openDialogForm({
        dialogProps: {
            size: 'middle',
            title: '新增客户',
        },
        cols: {
            sm: 1,
            md: 1,
            lg: 2,
            xl: 2,
            '2xl': 2,
        },
        labelPosition: 'top',
        fields: [
            {
                label: '客户编码',
                field: 'code',
                component: 'input',
                required: true,
                validation: z
                    .string({ required_error: '客户编码不能为空' })
                    .nonempty('客户编码不能为空'),
            },
            {
                label: '客户名称',
                field: 'name',
                component: 'input',
                required: true,
                validation: z
                    .string({ required_error: '客户名称不能为空' })
                    .nonempty('客户名称不能为空'),
            },
            {
                label: '客户属性',
                field: 'attribute',
                component: 'radio',
                required: true,
                defaultValue: '公司',
                componentProps: {
                    options: [
                        { label: '公司', value: '公司' },
                        { label: '个人', value: '个人' },
                    ],
                },
                validation: z
                    .string({ required_error: '客户属性不能为空' })
                    .nonempty('客户属性不能为空'),
            },
            {
                label: '客户类型',
                field: 'type_id',
                component: 'select',
                required: true,
                dict: 'CUSTOMER_TYPE',
                validation: z.union([
                    z
                        .string({ required_error: '客户类型不能为空' })
                        .nonempty('客户类型不能为空'),
                    z
                        .number()
                        .refine((val) => val !== undefined, '客户类型不能为空'),
                ]),
            },
            {
                label: '客户等级',
                field: 'grade',
                component: 'select',
                dict: 'grade_enum',
                required: true,
                componentProps: {
                    showFilter: true,
                },
                validation: z.union([
                    z
                        .string({ required_error: '客户等级不能为空' })
                        .nonempty('客户等级不能为空'),
                    z
                        .number()
                        .refine((val) => val !== undefined, '客户等级不能为空'),
                ]),
            },
            {
                label: '客户来源',
                field: 'source_id',
                component: 'select',
                dict: 'QUOTE_TYPE',
            },
            {
                label: '业务员',
                field: 'salesman_id',
                component: 'select',
                required: true,
                dict: 'employeeList',
                dictAlias: { label: 'nick_name', value: 'id' },
                componentProps: {
                    showFilter: true,
                },
                validation: z.union([
                    z
                        .string({ required_error: '业务员不能为空' })
                        .nonempty('业务员不能为空'),
                    z
                        .number()
                        .refine((val) => val !== undefined, '业务员不能为空'),
                ]),
            },
            {
                label: '地址',
                field: 'address',
                component: 'input',
            },
            {
                label: '邮箱',
                field: 'mailbox',
                component: 'input',
            },
            {
                label: '传真',
                field: 'fax',
                component: 'input',
            },
            {
                label: '电话',
                field: 'phone',
                component: 'input',
            },
            {
                label: '备注',
                field: 'notes',
                component: 'textarea',
                span: true,
            },
        ],
        submit: async (params) => {
            await createCustomer(params);
            await updateDict(opt.code);

            const options = await getDict(opt.code);
            if (field && updateSchema) {
                const dictAlias = {
                    label: 'name',
                    value: 'id',
                };

                updateSchema({
                    field,
                    componentProps: {
                        options: options?.map((item) => {
                            return {
                                ...item,
                                label: get(item, dictAlias.label),
                                value: get(item, dictAlias.value),
                            };
                        }),
                    },
                });
            }
        },
    });
};
