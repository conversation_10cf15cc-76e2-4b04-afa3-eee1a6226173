import z from 'zod';
import { useGlobDialogForm, useGlobDrawerForm } from '@/components';
import { get, isNil, set, omit } from 'lodash-es';
import { getDict, updateDict } from '@/utils/dict';
import { createStore, updateStore } from '@/api/wms/store';
import { useUserStore } from '@/store/user';
import { toNum, math } from '@/utils/math';
import { nextTick } from 'vue';
import { $t } from '@/locales';

const { openDialogForm } = useGlobDialogForm();
const userInfo = useUserStore();

export const addStore = async (opt, field, { updateSchema, setValues }) => {
    return new Promise((resolve, reject) => {
        const storeTypes = [
            { value: 'is_validate', label: $t('product.table.is_validate') },
            { value: 'is_waste', label: $t('product.table.is_waste') },
            { value: 'is_mrp', label: $t('product.table.is_mrp') },
            { value: 'is_manage', label: $t('product.table.is_manage', { field: $t('product.table.store') }) },
            { value: 'is_track', label: $t('product.table.is_track') },
            { value: 'is_sale', label: $t('product.table.is_sale') },
            { value: 'is_cost', label: $t('product.table.is_cost') },
            { value: 'is_fifo', label: $t('product.table.is_fifo') },
            { value: 'is_average', label: $t('product.table.is_average') },
            { value: 'is_remote', label: $t('product.table.is_remote') },
            { value: 'is_transit', label: $t('product.table.is_transit') },
        ];
        const api = openDialogForm({
            dialogProps: {
                size: 'middle',
                title: opt?.defaultValues?.id ? $t('product.tooltip.edit', { field: $t('product.table.store') }) : $t('product.tooltip.add', { field: $t('product.table.store') }),
            },
            cols: {
                sm: 1,
                md: 1,
                lg: 2,
                xl: 2,
                '2xl': 2,
            },
            labelPosition: 'top',
            fields: [
                {
                    field: 'factory_id',
                    label: $t('product.table.factory'),
                    component: 'select',
                    dict: 'factoryList',
                    required: true,
                    validation: z
                        .any()
                        .refine((val) => !isNil(val), $t('common.validation.required', { field: $t('product.table.factory') })),
                    afterSetDict(options) {
                        const selectedFactory =
                            // @ts-ignore
                            options.find(
                                (f) => f.id === userInfo.$state.userInfo.dept_id
                            ) || options[0];

                        // @ts-ignore
                        api?.setValues(
                            {
                                factory_id: selectedFactory?.id,
                                factory_name: selectedFactory?.name,
                            },
                            false
                        );
                    },
                    componentProps: {
                        showFilter: true,
                    },
                },
                {
                    field: 'parent_id',
                    label: $t('product.table.storageUp'),
                    component: 'TreeSelect',
                    dict: 'storeList',
                    componentProps: {
                        showFilter: true,
                        treeProps: {
                            children: 'children',
                            label: 'name',
                            value: 'id',
                        },
                    },
                },
                {
                    field: 'code',
                    label: $t('product.table.code', { field: $t('product.table.store') }),
                    component: 'input',
                    required: true,
                    validation: z
                        .any()
                        .refine((val) => !isNil(val), $t('common.validation.required', { field: $t('product.table.code', { field: $t('product.table.store') }) })),
                },
                {
                    field: 'name',
                    label: $t('product.table.name', { field: $t('product.table.store') }),
                    component: 'input',
                    required: true,
                    validation: z
                        .any()
                        .refine((val) => !isNil(val), $t('common.validation.required', { field: $t('product.table.name', { field: $t('product.table.store') }) })),
                },
                {
                    field: 'position',
                    label: $t('product.table.site'),
                    component: 'input',
                },
                {
                    field: 'length',
                    label: $t('product.table.length') + '(m)',
                    component: 'numberInput',
                    // required: true,
                    // validation: z.any().refine((val) => !isNil(val), '长不能为空'),
                    componentProps: {
                        onUpdate(val, values) {
                            const { width, length } = values;
                            // @ts-ignore
                            api?.setValues(
                                {
                                    volume: math.multiply(
                                        toNum(width),
                                        toNum(length)
                                    ),
                                },
                                false
                            );
                        },
                    },
                },
                {
                    field: 'width',
                    label: $t('product.table.width') + '(m)',
                    component: 'numberInput',
                    // required: true,
                    // validation: z.any().refine((val) => !isNil(val), '宽不能为空'),
                    componentProps: {
                        onUpdate(val, values) {
                            const { width, length } = values;
                            // @ts-ignore
                            api?.setValues(
                                {
                                    volume: math.multiply(
                                        toNum(width),
                                        toNum(length)
                                    ),
                                },
                                false
                            );
                        },
                    },
                },
                {
                    field: 'volume',
                    label: $t('product.form.area'),
                    component: 'numberInput',
                    // required: true,
                    // validation: z.any().refine((val) => !isNil(val), '宽不能为空'),
                    componentProps: {
                        disabled: true,
                    },
                },
                {
                    field: 'store_type',
                    label: $t('project.list.type', { field: $t('product.table.store') }),
                    component: 'select',
                    dict: 'store_type_enum',
                    componentProps: {
                        onUpdate(val) {
                            if (val !== '部门超领仓')
                                // @ts-ignore
                                api?.setValues({ dept_id: undefined }, false);
                            if (val !== '外协超领仓')
                                // @ts-ignore
                                api?.setValues({ odm_id: undefined }, false);
                        },
                    },
                },
                {
                    field: 'dept_id',
                    label: $t('product.table.dept'),
                    component: 'TreeSelect',
                    dict: 'deptList',
                    show(values) {
                        return values.store_type === '部门超领仓';
                    },
                    componentProps: {
                        treeProps: {
                            children: 'children',
                            label: 'name',
                            value: 'id',
                        },
                    },
                },
                {
                    field: 'odm_id',
                    label: $t('product.table.foreign_investor'),
                    component: 'select',
                    dict: 'supplierList',
                    show(values) {
                        return values.store_type === '外协超领仓';
                    },
                },
                {
                    field: 'turnover_rate',
                    label: $t('product.table.turnover_rate') + '(%)',
                    component: 'numberInput',
                    componentProps: {
                        min: 0,
                    },
                },
                {
                    field: 'safe_stock',
                    label: $t('product.table.safe_stock'),
                    component: 'numberInput',
                    componentProps: {
                        min: 0,
                    },
                },
                {
                    field: 'max_stock_value',
                    label: $t('product.table.max_stock_value'),
                    component: 'numberInput',
                    componentProps: {
                        min: 0,
                    },
                },
                {
                    field: 'valid',
                    label: $t('product.table.valid'),
                    component: 'checkbox',
                },

                {
                    field: 'store_types',
                    label: $t('product.table.attributes', { field: $t('product.table.store') }),
                    component: 'checkGroup',
                    span: true,
                    componentProps: {
                        class: 'w-full h-full',
                        isAll: false,
                        options: storeTypes,
                    },
                },
                {
                    field: 'notes',
                    label: $t('product.table.notes'),
                    component: 'textarea',
                    span: true,
                },
            ],
            submit: async (params) => {
                const { store_types } = params;
                const keys = storeTypes?.map((option) => option.value);
                console.log('store_types', store_types);
                const obj = {
                    ...omit(params, ['store_types']),
                };

                keys?.forEach((key) => {
                    if (store_types?.includes(key)) {
                        set(obj, key, true);
                    } else {
                        set(obj, key, false);
                    }
                });
                const server = params?.id ? updateStore : createStore;
                const res = await server(obj);
                resolve(res);
                if (!opt.code) return;
                await updateDict(opt.code);

                const options = await getDict(opt.code);
                if (field && updateSchema) {
                    const dictAlias = {
                        label: 'name',
                        value: 'id',
                    };

                    updateSchema({
                        field,
                        componentProps: {
                            options: options?.map((item) => {
                                return {
                                    ...item,
                                    label: get(item, dictAlias.label),
                                    value: get(item, dictAlias.value),
                                };
                            }),
                        },
                    });

                    const item = findItemByBFS(options, params.code);
                    setValues({ [field]: get(item, dictAlias.value) }, false);
                }

                return undefined;
            },
        });

        nextTick(() => {
            if (opt.defaultValues) {
                const keys = storeTypes?.map((option) => option.value);
                const params = {
                    store_types: [],
                    ...omit(opt.defaultValues, keys),
                };
                keys.forEach((key) => {
                    if (opt?.defaultValues?.[key]) params.store_types.push(key);
                });
                // @ts-ignore
                api?.setValues(params, false);
            }
        });
    });
};

function findItemByBFS(data, targetId, childrenKey = 'children') {
    const queue = [...data]; // 初始化队列，加入所有根节点
    while (queue.length > 0) {
        const node = queue.shift(); // 取出队首节点
        if (node.code === targetId) return node; // 找到目标项
        if (node[childrenKey]?.length) {
            queue.push(...node[childrenKey]); // 将子节点加入队列尾部
        }
    }
    return null; // 未找到
}
