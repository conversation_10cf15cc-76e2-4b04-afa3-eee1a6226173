import { get } from 'lodash-es';
import { addDict } from './addDict';
import { addCustomer } from './addCustomer';
import { addEmployee } from './addEmployee';
import { addPayment } from './addPayment';
import { addCurrency } from './addCurrency';
import { addSupplier } from './addSupplier';
import { addDept } from './addDept';
import { addStore } from './addStore';
import { addWorkmanship } from './addWorkmanship';
import { addMaterials } from './addMaterials';

export interface IType {
    icon?: string;
    title?: string;
    field?: string;
    type: string;
    fn?: (params?: Recordable) => void;
    params?: Recordable;
}

export const typeMaps = {
    addDict: {
        title: '新增字典',
        icon: 'ant-design:plus-outlined',
        type: 'addDict',
        fn: addDict,
    },
    addCustomer: {
        title: '新增客户',
        icon: 'ant-design:plus-outlined',
        type: 'addCustomer',
        fn: addCustomer,
    },
    addEmployee: {
        title: '新增员工',
        icon: 'ant-design:plus-outlined',
        type: 'addEmployee',
        fn: addEmployee,
    },
    addPayment: {
        title: '新增付款条款',
        icon: 'ant-design:plus-outlined',
        type: 'addPayment',
        fn: addPayment,
    },
    addCurrency: {
        title: '新增币种',
        icon: 'ant-design:plus-outlined',
        type: 'addCurrency',
        fn: addCurrency,
    },
    addSupplier: {
        title: '新增供应商',
        icon: 'ant-design:plus-outlined',
        type: 'addSupplier',
        fn: addSupplier,
    },
    addDept: {
        title: '新增部门',
        icon: 'ant-design:plus-outlined',
        type: 'addSupplier',
        fn: addDept,
    },
    addStore: {
        title: '新增仓库资料',
        icon: 'ant-design:plus-outlined',
        type: 'addStore',
        fn: addStore,
    },
    addWorkmanship: {
        title: '新增工序',
        icon: 'ant-design:plus-outlined',
        type: 'addWorkmanship',
        fn: addWorkmanship,
    },
    addMaterials: {
        title: '新增物料',
        icon: 'ant-design:plus-outlined',
        type: 'addMaterials',
        fn: addMaterials,
    },
};

export const generateType = (record: IType): IType => {
    const { title, icon, type, params, field } = record;
    const mode = get(typeMaps, type) || {};
    return {
        field: field,
        title: title || mode.title,
        icon: icon || mode.icon,
        type,
        fn: function () {
            mode.fn(params, field, this);
        },
    };
};
