<template>
    <Tabs v-model="activeId" class="w-[400px]">
        <TabsList>
            <TabsTrigger
                @click="() => smoothScroll(item.id)"
                v-for="item in scroll"
                :value="item.id"
                :key="item.id"
                :class="{ 'pointer-events-none': isScrolling }"
            >
                {{ item.title }}
            </TabsTrigger>
        </TabsList>
    </Tabs>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';
import { throttle } from 'lodash-es';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

const props = defineProps({
    scroll: {
        type: Array,
        default: () => [],
    },
    offsetY: {
        type: Number,
        default: 140,
    },
});

gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

const activeId = ref(props.scroll[0]?.id);
const isScrolling = ref(false);
let scrollAnimation = null;
let lastScrollTime = 0;

// 带重试机制的DOM查询
const getTargetElement = (targetId, retry = 5) => {
    let el = document.getElementById(targetId);
    if (!el && retry > 0) {
        setTimeout(() => (el = getTargetElement(targetId, retry - 1)), 50);
    }
    return el;
};

// 优化滚动动画
const smoothScroll = throttle((targetId) => {
    if (isScrolling.value) return;
    window.removeEventListener('scroll', handleScroll); // 禁用滚动监听

    const target = getTargetElement(targetId);
    if (!target) return;

    isScrolling.value = true;
    const targetY = target.offsetTop - props.offsetY;

    if (scrollAnimation?.isActive()) scrollAnimation.kill();

    scrollAnimation = gsap.to(window, {
        duration: 0.5,
        scrollTo: { y: targetY },
        ease: 'power2.inOut',
        onUpdate: () => {
            // 实时同步位置
            const currentY = window.scrollY + props.offsetY;
            if (Math.abs(currentY - targetY) < 5) {
                activeId.value = targetId;
            }
        },
        onComplete: () => {
            isScrolling.value = false;
            activeId.value = targetId; // 最终确认状态
            window.addEventListener('scroll', handleScroll);
        },
    });
}, 500);

// 优化可见区域检测
const handleScroll = throttle(() => {
    if (isScrolling.value) return;

    const visibleSection = props.scroll.find((item) => {
        const el = getTargetElement(item.id);
        if (!el) return false;
        const { top, bottom } = el.getBoundingClientRect();
        return (
            top <= window.innerHeight * 0.5 &&
            bottom >= window.innerHeight * 0.3
        );
    });

    if (visibleSection && !isScrolling.value) {
        activeId.value = visibleSection.id;
    }
}, 150);

onMounted(() => {
    window.addEventListener('scroll', handleScroll);

    // 初始化动画
    gsap.utils.toArray('.content-section').forEach((section) => {
        ScrollTrigger.create({
            trigger: section,
            start: 'top 90%',
            onEnter: () =>
                gsap.to(section, {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                }),
        });
    });
});

onUnmounted(() => {
    ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    window.removeEventListener('scroll', handleScroll);
    if (scrollAnimation?.isActive()) scrollAnimation.kill();
});
</script>

<style scoped>
/* 滚动锚定优化 */
.content-section {
    scroll-margin-top: 120px;
    opacity: 0;
    transform: translateY(20px);
}

/* 禁用点击时的指针反馈 */
.pointer-events-none {
    pointer-events: none;
    opacity: 0.7;
}
</style>
