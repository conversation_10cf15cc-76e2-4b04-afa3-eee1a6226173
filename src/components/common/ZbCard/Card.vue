<template>
    <Card :class="cardClass">
        <!-- 头部区域 -->
        <CardHeader v-if="hasHeader" :class="headerClass">
            <!-- 自定义标题插槽 -->
            <CardTitle v-if="slots?.title" :class="titleClass">
                <slot name="title"></slot>
            </CardTitle>
            <!-- 默认标题 -->
            <CardTitle v-else-if="props.title" :class="titleClass">
                {{ props.title }}
            </CardTitle>

            <!-- 自定义描述插槽 -->
            <CardDescription v-if="slots?.description" :class="descriptionClass">
                <slot name="description"></slot>
            </CardDescription>
            <!-- 默认描述 -->
            <CardDescription v-else-if="props.description" :class="descriptionClass">
                {{ props.description }}
            </CardDescription>

            <!-- 头部额外内容插槽 -->
            <slot name="header-extra"></slot>
        </CardHeader>

        <!-- 主要内容区域 -->
        <CardContent :class="contentClass">
            <slot></slot>
        </CardContent>

        <!-- 底部区域 -->
        <CardFooter v-if="slots?.footer" :class="footerClass">
            <slot name="footer"></slot>
        </CardFooter>
    </Card>
</template>

<script setup lang="ts">
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { computed, useSlots } from 'vue';
import type { HTMLAttributes } from 'vue';

// Card组件的Props类型定义
interface CardProps {
    /** 卡片标题 */
    title?: string;
    /** 卡片描述 */
    description?: string;
    /** 自定义样式类 */
    class?: HTMLAttributes['class'];
    /** 头部样式类 */
    headerClass?: HTMLAttributes['class'];
    /** 标题样式类 */
    titleClass?: HTMLAttributes['class'];
    /** 描述样式类 */
    descriptionClass?: HTMLAttributes['class'];
    /** 内容样式类 */
    contentClass?: HTMLAttributes['class'];
    /** 底部样式类 */
    footerClass?: HTMLAttributes['class'];
}

const props = withDefaults(defineProps<CardProps>(), {
    title: ''
});

// 接收插槽数据
const slots = useSlots();

// 判断是否有头部内容
const hasHeader = computed(() => {
    return props.title || props.description || slots?.title || slots?.description || slots?.['header-extra'];
});

// 计算卡片样式类
const cardClass = computed(() => {
    const classes = ['mb-4'];

    // 自定义样式类
    if (props.class) {
        classes.push(props.class);
    }

    return classes.join(' ');
});
</script>

<style scoped>
/* 确保卡片内容正确布局 */
.relative {
    width: 100%;
}
</style>