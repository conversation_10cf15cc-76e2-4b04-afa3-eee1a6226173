<template>
    <div :class="containerClass" :style="containerStyle">
        <!-- 标签区域 -->
        <div :class="labelClass" :style="labelStyle">
            <!-- 自定义标签插槽 -->
            <slot name="label" v-if="slots?.label">
                <slot name="label"></slot>
            </slot>
            <!-- 默认标签 -->
            <template v-else>
                <span>{{ props.label }}</span>
                <!-- 必填标识 -->
                <span v-if="props.required" class="text-red-500 ml-1">*</span>
                <!-- 冒号 -->
                <span v-if="props.colon">{{ props.colon }}</span>
            </template>
        </div>

        <!-- 值区域 -->
        <div :class="valueClass" :style="valueStyle">
            <!-- 自定义值插槽 -->
            <slot name="value" v-if="slots?.value || slots?.default">
                <slot name="value"></slot>
                <slot></slot>
            </slot>
            <!-- 默认值显示 -->
            <template v-else>
                <!-- 空值处理 -->
                <span v-if="displayValue === null || displayValue === undefined || displayValue === ''"
                    :class="emptyClass">
                    {{ props.emptyText }}
                </span>
                <!-- 正常值显示 -->
                <span v-else :class="valueTextClass">
                    {{ displayValue }}
                </span>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, useSlots } from 'vue';
import type { HTMLAttributes, CSSProperties } from 'vue';

// 布局方向类型
type Direction = 'horizontal' | 'vertical';
// 标签位置类型
type LabelAlign = 'left' | 'right' | 'center';
// 值对齐方式
type ValueAlign = 'left' | 'right' | 'center';
// 尺寸类型
type Size = 'large' | 'middle' | 'small' | 'mini';

// DetailItem组件的Props类型定义
interface DetailItemProps {
    /** 标签文本 */
    label?: string;
    /** 显示的值 */
    value?: any;
    /** 布局方向 */
    direction?: Direction;
    /** 标签对齐方式 */
    labelAlign?: LabelAlign;
    /** 值对齐方式 */
    valueAlign?: ValueAlign;
    /** 组件尺寸 */
    size?: Size;
    /** 标签宽度 */
    labelWidth?: string | number;
    /** 是否必填（显示红色*号） */
    required?: boolean;
    /** 是否显示冒号 */
    colon?: string | boolean;
    /** 空值时显示的文本 */
    emptyText?: string;
    /** 值格式化函数 */
    formatter?: (value: any) => string;
    /** 是否可复制 */
    copyable?: boolean;
    /** 容器样式类 */
    containerClass?: HTMLAttributes['class'];
    /** 标签样式类 */
    labelClass?: HTMLAttributes['class'];
    /** 值样式类 */
    valueClass?: HTMLAttributes['class'];
    /** 空值样式类 */
    emptyClass?: HTMLAttributes['class'];
    /** 值文本样式类 */
    valueTextClass?: HTMLAttributes['class'];
    /** 容器自定义样式 */
    containerStyle?: CSSProperties;
    /** 标签自定义样式 */
    labelStyle?: CSSProperties;
    /** 值自定义样式 */
    valueStyle?: CSSProperties;
}

const props = withDefaults(defineProps<DetailItemProps>(), {
    direction: 'horizontal',
    labelAlign: 'right',
    valueAlign: 'left',
    size: 'middle',
    labelWidth: '120px',
    required: false,
    colon: '：',
    emptyText: '',
    copyable: false,
});

// 定义事件
const emit = defineEmits<{
    copy: [value: string];
}>();

// 接收插槽数据
const slots = useSlots();

// 格式化后的显示值
const displayValue = computed(() => {
    if (props.formatter && typeof props.formatter === 'function') {
        return props.formatter(props.value);
    }
    return props.value;
});

// 容器样式类
const containerClass = computed(() => {
    const classes = [];

    // 基础布局
    if (props.direction === 'horizontal') {
        classes.push('flex items-center');
    } else {
        classes.push('flex flex-col');
    }

    // 尺寸相关
    switch (props.size) {
        case 'large':
            classes.push('py-3');
            break;
        case 'middle':
            classes.push('py-2');
            break;
        case 'small':
            classes.push('py-1');
            break;
        case 'mini':
            classes.push('py-0.5');
            break;
    }

    // 自定义样式类
    if (props.containerClass) {
        classes.push(props.containerClass);
    }

    return classes.join(' ');
});

// 标签样式类
const labelClass = computed(() => {
    const classes = [];

    // 基础样式
    classes.push('flex items-center flex-shrink-0');

    // 对齐方式
    switch (props.labelAlign) {
        case 'left':
            classes.push('text-left justify-start');
            break;
        case 'right':
            classes.push('text-right justify-end');
            break;
        case 'center':
            classes.push('text-center justify-center');
            break;
    }

    // 尺寸相关的字体大小
    switch (props.size) {
        case 'large':
            classes.push('text-sm font-medium');
            break;
        case 'middle':
            classes.push('text-xs font-medium');
            break;
        case 'small':
            classes.push('text-xs');
            break;
        case 'mini':
            classes.push('text-xs');
            break;
    }

    // 垂直布局时的样式
    if (props.direction === 'vertical') {
        classes.push('w-full pb-1');
    }

    // 自定义样式类
    if (props.labelClass) {
        classes.push(props.labelClass);
    }

    return classes.join(' ');
});

// 值样式类
const valueClass = computed(() => {
    const classes = [];

    // 基础样式
    classes.push('flex items-center');

    // 对齐方式
    switch (props.valueAlign) {
        case 'left':
            classes.push('text-left justify-start');
            break;
        case 'right':
            classes.push('text-right justify-end');
            break;
        case 'center':
            classes.push('text-center justify-center');
            break;
    }

    // 水平布局时占据剩余空间
    if (props.direction === 'horizontal') {
        classes.push('flex-1');
    } else {
        classes.push('w-full');
    }

    // 可复制样式
    if (props.copyable) {
        classes.push('cursor-pointer hover:bg-gray-50 rounded px-1 transition-colors');
    }

    // 自定义样式类
    if (props.valueClass) {
        classes.push(props.valueClass);
    }

    return classes.join(' ');
});

// 空值样式类
const emptyClass = computed(() => {
    const classes = ['text-gray-400'];

    if (props.emptyClass) {
        classes.push(props.emptyClass);
    }

    return classes.join(' ');
});

// 值文本样式类
const valueTextClass = computed(() => {
    const classes = [];

    // 尺寸相关的字体大小
    switch (props.size) {
        case 'large':
            classes.push('text-sm');
            break;
        case 'middle':
            classes.push('text-xs');
            break;
        case 'small':
            classes.push('text-xs');
            break;
        case 'mini':
            classes.push('text-xs');
            break;
    }

    // 自定义样式类
    if (props.valueTextClass) {
        classes.push(props.valueTextClass);
    }

    return classes.join(' ');
});

// 标签样式（宽度）
const labelStyle = computed(() => {
    const style: CSSProperties = {};

    // 水平布局时设置标签宽度
    if (props.direction === 'horizontal' && props.labelWidth) {
        if (typeof props.labelWidth === 'number') {
            style.width = `${props.labelWidth}px`;
        } else {
            style.width = props.labelWidth;
        }
    }

    // 合并自定义样式
    if (props.labelStyle) {
        Object.assign(style, props.labelStyle);
    }

    return style;
});

// 值样式
const valueStyle = computed(() => {
    const style: CSSProperties = {};

    // 合并自定义样式
    if (props.valueStyle) {
        Object.assign(style, props.valueStyle);
    }

    return style;
});

// 容器样式
const containerStyle = computed(() => {
    const style: CSSProperties = {};

    // 合并自定义样式
    if (props.containerStyle) {
        Object.assign(style, props.containerStyle);
    }

    return style;
});

// 处理复制功能
const handleCopy = async () => {
    if (props.copyable && displayValue.value) {
        try {
            await navigator.clipboard.writeText(String(displayValue.value));
            emit('copy', String(displayValue.value));
        } catch (err) {
            console.error('复制失败:', err);
        }
    }
};
</script>

<style scoped>
/* 确保文本不会换行 */
.flex-shrink-0 {
    flex-shrink: 0;
}
</style>