<template>
    <div v-if="mode === 'empty'">
        <div v-if="title" class="font-semibold leading-none tracking-tight">
            {{ title }}
        </div>
        <slot />
    </div>
    <Card v-else>
        <Collapsible
            type="single"
            class="w-full"
            collapsible
            v-model="defaultValue"
        >
            <CollapsibleItem name="1" class="border-none">
                <template #header>
                    <div class="flex justify-between">
                        <template v-if="$slots['header']">
                            <slot name="header"></slot>
                        </template>
                        <template v-else>
                            <CardHeader class="pl-0 pr-0">
                                <CardTitle>{{ title }}</CardTitle>
                            </CardHeader>
                        </template>
                    </div>
                </template>
                <CardContent class="pb-0">
                    <div class="pb-6 pt-0">
                        <slot></slot>
                    </div>
                </CardContent>
            </CollapsibleItem>
        </Collapsible>
    </Card>
</template>

<script lang="ts">
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion';
import { defineComponent, type PropType } from 'vue';
import { Collapsible, CollapsibleItem } from '@/components/common/Collapsible';

type Mode = 'default' | 'empty';

export default defineComponent({
    components: {
        Card,
        CardHeader,
        CardTitle,
        CardContent,
        Accordion,
        AccordionContent,
        AccordionItem,
        AccordionTrigger,
        Collapsible,
        CollapsibleItem,
    },
    props: {
        title: {
            type: String,
        },
        mode: {
            type: String as PropType<Mode>,
            default: 'default',
        },
    },
    setup() {
        const defaultValue = ['1'];

        return {
            defaultValue,
        };
    },
});
</script>

<style scoped>
.border-none {
    border: unset;
}
</style>
