<template>
    <div v-if="mode === 'empty'">
        <div v-if="title" class="font-semibold leading-none tracking-tight">
            {{ title }}
        </div>
        <slot />
    </div>
    <Card v-else>
        <Accordion
            type="single"
            class="w-full"
            collapsible
            :default-value="defaultValue"
        >
            <AccordionItem
                :key="defaultValue"
                :value="defaultValue"
                class="border-none"
            >
                <CardContent class="pb-0">
                    <div class="flex justify-between">
                        <template v-if="$slots['header']">
                            <slot name="header"></slot>
                        </template>
                        <template v-else>
                            <CardHeader class="pl-0 pr-0">
                                <CardTitle>{{ title }}</CardTitle>
                            </CardHeader>
                        </template>
                        <AccordionTrigger></AccordionTrigger>
                    </div>
                    <AccordionContent class="pb-6 pt-0">
                        <slot></slot>
                    </AccordionContent>
                </CardContent>
            </AccordionItem>
        </Accordion>
    </Card>
</template>

<script lang="ts">
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion';
import { defineComponent, type PropType } from 'vue';

type Mode = 'default' | 'empty';

export default defineComponent({
    components: {
        Card,
        CardHeader,
        CardTitle,
        CardContent,
        Accordion,
        AccordionContent,
        AccordionItem,
        AccordionTrigger,
    },
    props: {
        title: {
            type: String,
        },
        mode: {
            type: String as PropType<Mode>,
            default: 'default',
        },
    },
    setup() {
        const defaultValue = 'Accordion-default-value';

        return {
            defaultValue,
        };
    },
});
</script>

<style scoped>
.border-none {
    border: unset;
}
</style>
