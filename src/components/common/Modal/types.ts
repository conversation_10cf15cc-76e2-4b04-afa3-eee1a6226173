import { z } from 'zod';
import type { FormFieldConfig } from '@/components/common/FormEngine/types/formEngine';
import type { Component } from 'vue';
// 下拉菜单 API 配置
export interface SelectApiConfig {
    url: string; // API 地址
    method?: 'GET' | 'POST'; // 请求方法，默认为 GET
    transformData?: (data: any) => { label: string; value: string }[]; // 数据转换函数
}

export type Size = 'large' | 'middle' | 'small' | 'mini';

export interface BaseConfig {
    size: Size;
    title?: string;
    description?: string;
    footer?: string;
    hideFooter?: boolean;
    fields?: FormFieldConfig[];
    initValues?: Record<string, any>;
    customContent?: () => Component;
}
// 弹窗配置
export interface ModalConfig {
    config: BaseConfig;
    visible: boolean;
    onSubmit?: (formData: Record<string, any>) => void;
    onCancel?: () => void;
}
