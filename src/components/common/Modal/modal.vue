<template>
    <Dialog :open="visible">
        <!-- 触发器 -->
        <DialogTrigger v-if="slots?.trigger">
            <slot name="trigger"></slot>
        </DialogTrigger>
        <DialogContent :class="sizeClass">
            <DialogHeader>
                <!-- 标题 -->
                <DialogTitle v-if="slots?.title">
                    <slot name="title"></slot>
                </DialogTitle>
                <DialogTitle v-else class="text-title">{{
                    config.title
                }}</DialogTitle>
                <DialogDescription v-if="props.config.description">{{
                    props.config.description
                }}</DialogDescription>
            </DialogHeader>
            <slot>
                <component
                    ref="customContentRef"
                    v-if="props.config.customContent"
                    :is="
                        props.config.customContent &&
                        props.config.customContent()
                    "
                />
            </slot>
            <div
                v-if="props.config.fields && props.config.fields.length > 0"
                class="p-4"
                :class="props.config.size"
            >
                <!-- 如果需要，可以为表单内容添加一个组件 -->
                <FormEngine
                    ref="FormEngineRef"
                    :fields="props.config.fields"
                    v-model="FormData"
                    :init-values="props.config.initValues"
                />
            </div>
            <!-- 底部 -->
            <div v-if="!props.config.hideFooter" class="flex justify-end gap-2">
                <Button variant="outline" @click="onCancel" class="text-xs">
                    {{ $t('common.button.cancel') }}
                </Button>
                <Button variant="default" @click="onSubmit" class="text-xs">
                    {{ $t('common.button.confirm') }}
                </Button>
            </div>
        </DialogContent>
    </Dialog>
</template>
<script setup lang="ts">
defineOptions({
    name: 'GlobalModal',
});
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { computed, useSlots, ref, watch } from 'vue';
import type { ModalConfig } from './types';
import FormEngine from '@/components/common/FormEngine/FormEngine.vue';
import { useModalStore } from '@/store/modal';
const modalStore = useModalStore();
const props = defineProps<ModalConfig>();
const FormData = ref<any>({});

const sizeClass = computed(() => {
    return {
        'min-w-[100%] md:min-w-[760px] lg:min-w-[1000px] grid-rows-[auto_1fr]':
            props.config.size === 'large',
        'min-w-[100%] md:min-w-[760px] grid-rows-[auto_1fr]':
            props.config.size === 'middle',
        'min-w-[100%] sm:min-w-[500px] grid-rows-[auto_1fr]':
            props.config.size === 'small',
    };
});

const onCancel = () => {
    modalStore.hideModal();
};

const FormEngineRef = ref<InstanceType<typeof FormEngine>>();
const customContentRef = ref<any>();

const onSubmit = async () => {
    if (props.config.customContent && customContentRef.value) {
        const data = await customContentRef.value.validateAllInfo();
        if (data) {
            const res = await props.onSubmit?.(data);
            // @ts-ignore
            if (res === false) return;
            modalStore.hideModal();
        }
        return;
    }
    let isValid = true;
    // debugger
    if (FormEngineRef.value) {
        console.log('FormEngineRef', FormEngineRef.value);
        isValid = await FormEngineRef.value?.validateForm();
    }
    if (isValid) {
        console.log('FormEngineRef', FormEngineRef.value?.methods?.values);
        // props.onSubmit?.(FormData.value);
        const res = await props.onSubmit?.(
            FormEngineRef.value?.methods?.values
        );
        // @ts-ignore
        if (res === false) return;
        modalStore.hideModal();
    }
};

// 接收插槽数据
const slots = useSlots();
</script>
<style scoped>
.p-4.small form {
    display: grid;
    /* grid-template-columns: 1fr !important; */
    gap: 16px;
}

.p-4.middle form {
    display: grid;
    /* grid-template-columns: 1fr 1fr !important; */
    gap: 16px;
}

.p-4.large form {
    display: grid;
    /* grid-template-columns: 1fr 1fr 1fr !important; */
    gap: 16px;
}
</style>
