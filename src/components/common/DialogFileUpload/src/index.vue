<!-- eslint-disable no-useless-catch -->
<script lang="ts">
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
    DialogClose,
} from '@/components/ui/dialog';
import {
    computed,
    ref,
    unref,
    type ComputedRef,
    defineComponent,
    nextTick,
    type PropType,
} from 'vue';
import type { BaseDialogFormConfig, DialogFormActions, Size } from '../types';
import { cloneDeep, isFunction, omit } from 'lodash-es';
import { useForm } from '@/components';
import { FileUpload } from '@/components/common/FileUpload';
import { deepMerge } from '@/utils/object';
export default defineComponent({
    name: '',
    components: {
        Dialog,
        DialogContent,
        DialogDescription,
        DialogFooter,
        DialogHeader,
        DialogTitle,
        DialogTrigger,
        DialogClose,
        Button,
        FileUpload,
    },
    emits: ['register', 'open', 'close', 'submit', 'cancel'],
    props: {
        size: {
            type: String as PropType<Size>,
        },
        height: {
            type: Number,
        },
        title: {
            type: String,
        },
        description: {
            type: String,
        },
        hideFooter: {
            type: Boolean,
        },
        submitText: {
            type: String,
        },
        cancelText: {
            type: String,
        },
    },
    setup(props, { emit }) {
        const propsRef = ref<Partial<BaseDialogFormConfig>>();

        const getBind = computed(() => {
            return unref(propsRef);
        }) as ComputedRef<BaseDialogFormConfig>;

        const visible = ref(false);
        const relatedId = ref(null);
        const relatedModel = ref(null);
        const sizeClass = computed(() => {
            return {
                'w-[90%]': unref(getBind).dialogProps?.size === 'large',
                'w-[70%]': unref(getBind).dialogProps?.size === 'middle',
                'w-[50%]': unref(getBind).dialogProps?.size === 'small',
                'w-[30%]': unref(getBind).dialogProps?.size === 'mini',
                'min-h-[420px]': unref(getBind).dialogProps?.size !== 'mini',
            };
        });

        init();

        const open = async (params?: Recordable) => {
            console.log('open', getBind.value, params);
            visible.value = true;
            relatedId.value = params.relatedId;
            relatedModel.value = params.relatedModel;
        };
        const close = () => {
            visible.value = false;
        };

        emit('open', open);
        emit('close', close);

        const [register, formApi] = useForm({});
        const uploadList = ref({});
        const handleUpload = (res) => {
            console.log('handleUpload', res);
            uploadList.value = res;
        };
        async function handleSubmit() {
            const submit = unref(getBind).submit;

            try {
                const res = await submit(cloneDeep(uploadList.value));
                if (res === false) return;
                close();
            } catch (error) {
                close();
                throw error;
            }
        }

        function handleCancel() {
            const { cancel } = unref(getBind);
            if (cancel && isFunction(cancel)) cancel();
            emit('cancel');
            close();
        }

        // 初始化表单
        // 1.通过useForm方式使用表单 formProps 必然有值
        // 2.常规方式使用表单 formProps 必然无值，直接取值props
        function init(formProps?: Partial<BaseDialogFormConfig>) {
            if (!formProps) {
                propsRef.value = props;
            } else {
                propsRef.value = deepMerge(unref(propsRef) || {}, formProps);
            }
        }

        const dialogMethods: DialogFormActions = {
            // dialogProps: {},
            open,
            close,
            init,
            ...formApi,
        };
        emit('register', dialogMethods);

        return {
            getBind,
            visible,
            sizeClass,
            register,
            handleSubmit,
            handleCancel,
            open,
            close,
            relatedId,
            relatedModel,
            handleUpload,
        };
    },
});
</script>

<template>
    <Dialog :open="visible" append-to-body>
        <DialogContent
            class="max-w-[90%] grid-rows-[auto_1fr] z-50"
            :class="sizeClass"
            style="z-index: 51"
        >
            <DialogClose
                class="absolute right-3 top-3 rounded-sm w-6 h-6 bg-transparent z-[99]"
                @click="close"
            >
            </DialogClose>
            <DialogHeader>
                <DialogTitle>{{ getBind.dialogProps?.title }}</DialogTitle>
                <DialogDescription v-if="$slots.description">
                    <slot name="description" />
                </DialogDescription>
                <DialogDescription v-else>
                    {{ getBind.dialogProps?.description }}
                </DialogDescription>
            </DialogHeader>
            <div class="overflow-y-auto">
                <div
                    :class="
                        getBind.dialogProps?.height
                            ? `h-[${getBind.dialogProps?.height}px]`
                            : ''
                    "
                    class="max-h-[60dvh]"
                >
                    <FileUpload
                        :relatedId="relatedId"
                        :relatedModel="relatedModel"
                        @success="handleUpload"
                    ></FileUpload>
                </div>
            </div>
            <template v-if="getBind.dialogProps?.hideFooter !== false">
                <DialogFooter v-if="$slots.footer">
                    <template #footer>
                        <slot name="footer" />
                    </template>
                </DialogFooter>
                <DialogFooter v-else>
                    <Button variant="outline" @click="handleCancel">
                        {{
                            getBind.dialogProps?.cancelText ||
                            $t?.('common.button.cancel')
                        }}
                    </Button>
                    <Button variant="default" @click="handleSubmit">
                        {{
                            getBind.dialogProps?.submitText ||
                            $t?.('common.button.confirm')
                        }}
                    </Button>
                </DialogFooter>
            </template>
        </DialogContent>
    </Dialog>
</template>
