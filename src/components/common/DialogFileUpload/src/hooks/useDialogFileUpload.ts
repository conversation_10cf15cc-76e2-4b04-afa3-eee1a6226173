import { ref, unref } from 'vue';
import type { BaseDialogFormConfig, DialogFormActions } from '../../types';
import type {
    FieldState,
    FlattenAndSetPathsType,
    FormState,
    Path,
    ResetFormOpts,
} from 'vee-validate';
import type { PartialDeep } from 'type-fest';
import type { FormEngineProps, FormFieldConfig } from '@/components';

type IUseDialogForm = [
    register: (api: any) => void,
    methods: DialogFormActions,
];

export function useDialogFileUpload(props: BaseDialogFormConfig): IUseDialogForm {
    const dialogFormApi = ref();

    const register = (api: any) => {
        dialogFormApi.value = api;

        init(props);
    };
    console.log("useDialogFileUpload",dialogFormApi,props)
    function init(props: BaseDialogFormConfig) {
        unref(dialogFormApi)!.init(props);
    }

    function open(params: Recordable) {
        unref(dialogFormApi)!.open(params);
    }

    function close() {
        unref(dialogFormApi)!.close();
    }

    function setFieldValue(
        field: string,
        value: Recordable,
        shouldValidate?: boolean
    ) {
        unref(dialogFormApi)!.setFieldValue(field, value, shouldValidate);
    }

    function setFieldError(
        field: string,
        message: string | string[] | undefined
    ) {
        unref(dialogFormApi)!.setFieldError(field, message);
    }

    function setErrors(
        fields: Partial<
            FlattenAndSetPathsType<Recordable, string | string[] | undefined>
        >
    ) {
        unref(dialogFormApi)!.setErrors(fields);
    }

    function setValues(
        fields: PartialDeep<Recordable>,
        shouldValidate?: boolean
    ) {
        unref(dialogFormApi)!.setValues(fields, shouldValidate);
    }

    function getValues() {
        return unref(dialogFormApi)!.getValues();
    }

    function setFieldTouched(field: Path<Recordable>, isTouched: boolean) {
        unref(dialogFormApi)!.setValues(field, isTouched);
    }

    function setTouched(
        fields: Partial<Record<Path<Recordable>, boolean>> | boolean
    ) {
        unref(dialogFormApi)!.setTouched(fields);
    }

    function resetForm(
        state?: Partial<FormState<Recordable>>,
        opts?: Partial<ResetFormOpts>
    ) {
        unref(dialogFormApi)!.resetForm(state, opts);
    }

    function resetField(field: Path<Recordable>, state?: Partial<FieldState>) {
        unref(dialogFormApi)!.resetField(field, state);
    }

    function updateSchema(data: FormFieldConfig | FormFieldConfig[]) {
        unref(dialogFormApi)!.updateSchema(data);
    }

    function updateAllSchema(data: FormFieldConfig) {
        unref(dialogFormApi)!.updateSchema(data);
    }

    function submit() {
        return unref(dialogFormApi)!.submit();
    }

    function setProps(props: FormEngineProps) {
        return unref(dialogFormApi)!.setProps(props);
    }

    return [
        register,
        {
            init,
            open,
            close,

            setFieldValue,
            setFieldError,
            setErrors,
            setValues,
            getValues,
            setFieldTouched,
            setTouched,
            resetForm,
            resetField,
            updateSchema,
            updateAllSchema,
            submit,
            setProps,
        },
    ];
}
