import type { FormActions, FormEngineProps } from '@/components';

export type Size = 'large' | 'middle' | 'small' | 'mini';

export interface BaseDialogConfig {
    size?: Size;
    title?: string;
    height?: number;
    submitText?: string;
    cancelText?: string;
    description?: string;
    footer?: string;
    hideFooter?: boolean;
}

export interface BaseDialogFormConfig extends FormEngineProps {
    dialogProps?: BaseDialogConfig;
    submit?: (
        formData: Recordable | undefined
    ) => Promise<boolean | undefined> | boolean | undefined;
    cancel?: () => void;
}

export interface DialogFormActions extends FormActions {
    open: (params?: Recordable) => void;
    close: () => void;
    init: (props: BaseDialogFormConfig) => void;
}
