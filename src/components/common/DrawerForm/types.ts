import type { FormActions, FormEngineProps } from '@/components';

export type DrawerSize = 'large' | 'middle' | 'small' | 'mini';

export interface BaseDrawerConfig {
    size?: DrawerSize;
    title?: string;
    description?: string;
    headerClass?: string;
    bodyClass?: string;
    footerClass?: string;
    position?: 'left' | 'right' | 'top' | 'bottom';
    showFooter?: boolean;
    showClose?: boolean;
    disableClickOutside?: boolean;
    height?: number;
    submitText?: string;
    cancelText?: string;
}

export interface BaseDrawerFormConfig extends FormEngineProps {
    dialogProps?: BaseDrawerConfig;
    submit?: (
        formData: Recordable | undefined
    ) => Promise<boolean | undefined> | boolean | undefined;
    cancel?: () => void;
}

export interface DrawerFormActions extends FormActions {
    open: (params?: Recordable) => void;
    close: () => void;
    init: (props: BaseDrawerFormConfig) => void;
    getVisible: () => boolean;
}
