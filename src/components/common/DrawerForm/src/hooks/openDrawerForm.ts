// services/drawer.js
import { createApp, h, ref, render, inject, nextTick } from 'vue';
import { BaseDrawerFormConfig, DrawerFormActions } from '../../types';
import { useDrawerFormStoreWithOut } from '@/store/drawerForm';

export function useGlobDrawerForm() {
    try {
        const { getDrawerFormApi } = useDrawerFormStoreWithOut();

        const openDrawerForm = (
            options: BaseDrawerFormConfig
        ): DrawerFormActions => {
            const globDrawerFormApi = getDrawerFormApi();
            globDrawerFormApi?.init(null);
            globDrawerFormApi?.init(options);
            globDrawerFormApi?.open();

            return globDrawerFormApi;
        };
        return {
            openDrawerForm,
        };
    } catch (error) {
        console.error(error);
        return {
            openDrawerForm: () => {
                console.error(error);
                console.error('openDrawerForm is not available');
            },
        };
    }
}
