<!-- eslint-disable no-useless-catch -->
<script lang="ts">
import { Button } from '@/components/ui/button'
import { Drawer } from '@/components/common/Drawer'
import {
  computed,
  ref,
  unref,
  type ComputedRef,
  defineComponent,
  nextTick,
  type PropType,
} from 'vue'
import type {
  BaseDrawerConfig,
  BaseDrawerFormConfig,
  DrawerFormActions,
  DrawerSize,
} from '../types'
import { FormEngine, useForm } from '@/components'
import { cloneDeep, isFunction, omit } from 'lodash-es'
import { deepMerge } from '@/utils/object'

export default defineComponent({
  name: '',
  components: {
    Drawer,
    Button,
    // FormEngine,
  },
  emits: ['register', 'open', 'close', 'submit', 'cancel'],
  props: {
    open: {
      type: Boolean,
    },
    size: {
      type: String as PropType<DrawerSize>,
    },
    height: {
      type: Number,
    },
    title: {
      type: String,
    },
    description: {
      type: String,
    },
    hideFooter: {
      type: <PERSON><PERSON><PERSON>,
    },
    submitText: {
      type: String,
    },
    cancelText: {
      type: String,
    },
  },
  setup(props, { emit, slots }) {
    const propsRef = ref<Partial<BaseDrawerFormConfig>>()

    const getBind = computed(() => {
      return unref(propsRef)
    }) as ComputedRef<BaseDrawerFormConfig>

    const visible = ref(props.open)
    const loading = ref(false)

    init()

    const open = (params?: Recordable) => {
      visible.value = true

      nextTick(() => {
        formApi.setProps(
          // @ts-ignore
          omit(unref(getBind), ['cancel', 'open', 'init', 'dialogProps'])
        )

        if (params) formApi.setValues(params)
      })
    }
    const close = () => {
      visible.value = false
    }

    emit('open', open)
    emit('close', close)

    const [register, formApi] = useForm({})

    async function handleSubmit() {
      const submit = unref(getBind)?.submit
      loading.value = true

      if (submit && isFunction(submit)) {
        if (slots['main']) {
          submit(null)
          loading.value = false
          close()
          return
        }

        try {
          const params = await formApi?.submit()
          const res = await submit(cloneDeep(params))
          loading.value = false

          if (res === false) return
          close()
        } catch (error) {
          loading.value = false
          // close();
          throw error
        }
      } else {
        loading.value = false
        close()
      }
    }

    function handleCancel() {
      const { cancel } = unref(getBind)
      if (cancel && isFunction(cancel)) cancel()
      emit('cancel')
      close()
    }

    // 初始化表单
    // 1.通过useForm方式使用表单 formProps 必然有值
    // 2.常规方式使用表单 formProps 必然无值，直接取值props
    function init(formProps?: Partial<BaseDrawerFormConfig>) {
      if (!formProps) {
        // @ts-ignore
        propsRef.value = props
      } else {
        propsRef.value = deepMerge(unref(propsRef) || {}, formProps)
      }
    }

    const dialogMethods: DrawerFormActions = {
      // dialogProps: {},
      open,
      close,
      init,
      getVisible: () => visible.value,
      ...formApi,
    }

    emit('register', dialogMethods)

    return {
      getBind,
      visible,
      register,
      handleSubmit,
      handleCancel,
      open,
      close,
      loading,
    }
  },
})
</script>

<template>
  <Drawer
    v-model="visible"
    v-bind="getBind.dialogProps"
    :loading="loading"
    @submit="handleSubmit"
  >
    <FormEngine @register="register">
      <template v-for="item in Object.keys($slots)" #[item]="data">
        <slot :name="item" v-bind="data || {}" />
      </template>
    </FormEngine>
  </Drawer>
</template>
