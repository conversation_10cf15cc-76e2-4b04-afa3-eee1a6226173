<template>
    <div
        class="flex item-center gap-1 hover:text-blue-500 cursor-pointer"
        @click="isShow = !isShow"
    >
        <svg
            t="1748314414300"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="12992"
            width="18"
            height="18"
        >
            <path
                d="M435.35 156c0 8.15 3.3 15.5 8.6 20.8 5.3 5.3 12.7 8.6 20.8 8.6 16.25 0 29.45-13.15 29.45-29.45V63.4c0-16.25-13.2-29.45-29.45-29.45-16.3 0-29.45 13.2-29.45 29.45V156zM258.85 157.65l65.45 65.45c11.5 11.5 30.15 11.5 41.65 0s11.5-30.15 0-41.65L300.5 116c-11.5-11.5-30.15-11.5-41.65 0s-11.5 30.15 0 41.65zM200.7 278.95c-16.25 0-29.45 13.2-29.45 29.45 0 8.15 3.3 15.5 8.6 20.8 5.3 5.3 12.7 8.6 20.8 8.6h92.55c16.25 0 29.45-13.2 29.45-29.45s-13.2-29.45-29.45-29.45l-92.5 0.05zM629.05 116l-65.45 65.45c-11.5 11.5-11.5 30.15 0 41.65s30.15 11.5 41.65 0l65.45-65.45c11.5-11.5 11.5-30.15 0-41.65s-30.15-11.5-41.65 0zM636.3 337.8h92.55c16.25 0 29.45-13.15 29.45-29.45 0-16.25-13.2-29.45-29.45-29.45h-92.55c-16.25 0-29.45 13.2-29.45 29.45 0 8.15 3.3 15.5 8.65 20.8 5.3 5.35 12.7 8.65 20.8 8.65z"
                fill="#2B5DEB"
                p-id="12993"
            ></path>
            <path
                d="M384.7 346.7v227.35c-13.5-17.25-29.75-32.3-48.6-40.05-21.05-8.65-43.2-7.65-64.05 3-38.35 19.5-54.2 66.55-35.25 104.8l95.9 194.1c2.7 5.3 67.7 130.4 185.35 130.4h137.25c108.25 0 196.3-88.5 196.3-197.35l-0.1-142.35h-0.1V619.5c0-42.55-34.65-77.2-77.2-77.2-9.6 0-18.85 1.8-27.35 5-7.2-35.1-38.4-61.6-75.6-61.6-10.1 0-19.75 2-28.65 5.55-9.35-32.4-40.35-57-76.05-57-9.6 0-18.85 1.85-27.45 5.2V346.7c0-42.55-34.65-77.2-77.2-77.2-42.55 0-77.2 34.6-77.2 77.2z m51.5 0c0-14.2 11.55-25.75 25.75-25.75s25.75 11.55 25.75 25.75V643.5h51.95v-130.4c0-14.4 12.85-27.45 26.95-27.45 14.65 0 27.45 12.05 27.45 25.75v140.7H645.5v-89.2c0-14.2 11.55-25.75 25.75-25.75s25.75 11.55 25.75 25.75v113.25h51.45V619.5c0-14.2 11.55-25.75 25.75-25.75s25.75 11.55 25.75 25.75v36.8h0.1l0.1 112.75c0 80.4-64.95 145.85-144.8 145.85h-137.25c-84.75 0-137.55-98.6-139.5-102.25L282.9 619c-6.6-13.35-1.1-29.25 12.45-36.15 7.5-3.8 14-4.2 21.15-1.3 28.4 11.65 56.3 67.8 67.2 100.45l8.3 24.9 44.2-15.65V346.7z"
                fill="#2B5DEB"
                p-id="12994"
            ></path>
        </svg>
        筛选
    </div>
    <div
        class="list absolute px-4 py-4 right-0 top-full mt-1 w-auto bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible iscurrent:opacity-100 iscurrent:visible transition-all duration-200 z-50"
        :class="{ iscurrent: isShow === true }"
    >
        <Input
            v-model="nameModel"
            :placeholder="`请输入名称`"
            class="w-[100%] h-[38px]"
        />
        <div class="mt-4">
            <DateRangerPicker
                :modelValue="dateRange"
                @update:modelValue="dateRangeFun"
                :placeholder="`请选择日期`"
                class="w-[100%] h-[38px]"
            ></DateRangerPicker>
        </div>
        <div class="mt-4">
            <Button @click="sureSearch">确定</Button>
            <Button @click="isShow = !isShow" class="ml-2">取消</Button>
        </div>
    </div>
</template>
<script setup lang="ts">
import {
    ref,
    computed,
    watch,
    onUnmounted,
    onMounted,
    inject,
    type Ref,
} from 'vue';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import DateRangerPicker from '@/components/common/FormEngine/FormComponents/DateRangerPicker.vue';
import { dataListFun } from '@/utils/dateFormat';
// 定义组件发出的事件
const emit = defineEmits(['success']);
//筛选条件
const isShow = ref(false);
const nameModel = ref('');
const dateRange = ref([new Date(), new Date()]);
//确定搜索
const dateRangeFun = (event: any) => {
    dateRange.value = event;
    console.log('dateRangeFun', dateRange.value, event);
};
const sureSearch = () => {
    isShow.value = false;
    emit('success', {
        name: nameModel.value,
        startTime: dataListFun(dateRange.value[0], 'yyyy-mm-dd hh:ss:mm'),
        endTime: dataListFun(dateRange.value[1], 'yyyy-mm-dd hh:ss:mm'),
    });
};
</script>
