// ... existing code ...

## 📋 组件库

### 🎯 通用组件

#### 1. Dialog 对话框组件 (`src/components/common/Dialog.vue`)

基于 shadcn/vue 的 Dialog 组件封装，提供统一的对话框解决方案。

**特性**:

- 支持 v-model:visible 双向绑定
- 多种尺寸选择 (large/middle/small/mini)
- 灵活的插槽系统
- 自动事件处理

**使用方法**:

```vue
<template>
    <Dialog
        v-model:visible="showDialog"
        title="对话框标题"
        description="对话框描述"
        size="middle"
        @close="handleClose"
    >
        <!-- 触发器插槽 -->
        <template #trigger>
            <Button>打开对话框</Button>
        </template>

        <!-- 主要内容 -->
        <div>对话框内容</div>

        <!-- 底部操作栏 -->
        <template #footer>
            <Button variant="outline" @click="showDialog = false">取消</Button>
            <Button @click="handleConfirm">确认</Button>
        </template>
    </Dialog>
</template>
```

#### 2. Sheet 侧拉组件 (`src/components/common/Sheet.vue`)

基于 shadcn/vue 的 Sheet 组件封装，提供统一的侧拉面板解决方案。

**特性**:

- 支持 v-model:visible 双向绑定
- 四个方向滑出 (top/right/bottom/left)
- 多种尺寸选择 (large/middle/small/mini)
- 灵活的插槽系统
- 响应式设计，适配移动端
- 自动滚动处理

**Props 参数**:

| 参数        | 类型                                     | 默认值   | 说明         |
| ----------- | ---------------------------------------- | -------- | ------------ |
| visible     | boolean                                  | false    | 控制显隐状态 |
| side        | 'top' \| 'right' \| 'bottom' \| 'left'   | 'right'  | 侧拉方向     |
| size        | 'large' \| 'middle' \| 'small' \| 'mini' | 'middle' | 侧拉尺寸     |
| title       | string                                   | -        | 标题文本     |
| description | string                                   | -        | 描述文本     |

**事件**:

| 事件名         | 参数    | 说明         |
| -------------- | ------- | ------------ |
| update:visible | boolean | 显隐状态变化 |
| close          | -       | 关闭时触发   |

**插槽**:

| 插槽名  | 说明         |
| ------- | ------------ |
| trigger | 触发器内容   |
| title   | 自定义标题   |
| default | 主要内容区域 |
| footer  | 底部操作栏   |

**尺寸说明**:

- **水平方向** (left/right):

    - large: 90vw (桌面端: 384px)
    - middle: 70vw (桌面端: 320px)
    - small: 50vw (桌面端: 256px)
    - mini: 30vw (桌面端: 192px)

- **垂直方向** (top/bottom):
    - large: 90vh
    - middle: 70vh
    - small: 50vh
    - mini: 30vh

**使用示例**:

```vue
<template>
    <!-- 基础用法 -->
    <Sheet
        v-model:visible="showSheet"
        title="侧拉标题"
        description="侧拉描述"
        side="right"
        size="middle"
        @close="handleClose"
    >
        <div>侧拉内容</div>
    </Sheet>

    <!-- 带触发器 -->
    <Sheet v-model:visible="showTriggerSheet" title="用户信息">
        <template #trigger>
            <Button>编辑用户</Button>
        </template>

        <template #footer>
            <Button variant="outline" @click="showTriggerSheet = false"
                >取消</Button
            >
            <Button @click="handleSave">保存</Button>
        </template>

        <div class="space-y-4">
            <div class="grid gap-2">
                <label class="text-sm font-medium">用户名</label>
                <input
                    type="text"
                    class="px-3 py-2 border rounded-md"
                    placeholder="请输入用户名"
                />
            </div>
            <div class="grid gap-2">
                <label class="text-sm font-medium">邮箱</label>
                <input
                    type="email"
                    class="px-3 py-2 border rounded-md"
                    placeholder="请输入邮箱"
                />
            </div>
        </div>
    </Sheet>

    <!-- 不同方向示例 -->
    <Sheet
        v-model:visible="showLeftSheet"
        side="left"
        title="左侧导航"
        size="small"
    >
        <nav class="space-y-2">
            <a href="#" class="block p-2 hover:bg-gray-100 rounded">首页</a>
            <a href="#" class="block p-2 hover:bg-gray-100 rounded">用户管理</a>
            <a href="#" class="block p-2 hover:bg-gray-100 rounded">系统设置</a>
        </nav>
    </Sheet>

    <Sheet
        v-model:visible="showBottomSheet"
        side="bottom"
        title="操作面板"
        size="middle"
    >
        <template #footer>
            <Button variant="outline" @click="showBottomSheet = false"
                >关闭</Button
            >
            <Button>确认操作</Button>
        </template>

        <div class="grid grid-cols-3 gap-4">
            <Button variant="outline">选项 1</Button>
            <Button variant="outline">选项 2</Button>
            <Button variant="outline">选项 3</Button>
        </div>
    </Sheet>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Sheet from '@/components/common/Sheet.vue';
import { Button } from '@/components/ui/button';

const showSheet = ref(false);
const showTriggerSheet = ref(false);
const showLeftSheet = ref(false);
const showBottomSheet = ref(false);

const handleClose = () => {
    console.log('侧拉关闭');
};

const handleSave = () => {
    console.log('保存数据');
    showTriggerSheet.value = false;
};
</script>
```

**最佳实践**:

1. **内容滚动**: 组件内部已处理内容区域滚动，无需额外设置
2. **响应式设计**: 在移动端会自动调整尺寸以适配屏幕
3. **键盘导航**: 支持 ESC 键关闭侧拉
4. **焦点管理**: 打开时自动聚焦，关闭时恢复焦点
5. **性能优化**: 使用 v-model 避免不必要的重渲染

**注意事项**:

- 侧拉内容过多时会自动显示滚动条
- 建议在移动端使用 bottom 方向以提供更好的用户体验
- 避免在侧拉中嵌套过多层级的滚动容器

// ... existing code ...
