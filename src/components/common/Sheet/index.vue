<template>
    <!-- 使用 v-model:open 实现双向绑定 -->
    <Sheet v-model:open="internalVisible">
        <!-- 触发器 -->
        <SheetTrigger v-if="slots?.trigger">
            <slot name="trigger"></slot>
        </SheetTrigger>
        <SheetContent :side="props.side" :class="sizeClass">
            <SheetHeader>
                <!-- 标题 -->
                <SheetTitle v-if="slots?.title">
                    <slot name="title"></slot>
                </SheetTitle>
                <SheetTitle v-else-if="props.title" class="text-title">
                    {{ props.title }}
                </SheetTitle>
                <SheetDescription v-if="props.description">
                    {{ props.description }}
                </SheetDescription>
            </SheetHeader>

            <!-- 主要内容区域 -->
            <div class="flex-1 overflow-y-auto">
                <slot></slot>
            </div>

            <!-- 底部 -->
            <SheetFooter v-if="slots?.footer">
                <slot name="footer"></slot>
            </SheetFooter>
        </SheetContent>
    </Sheet>
</template>

<script setup lang="ts">
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetFooter,
    SheetHeader,
    SheetTitle,
    SheetTrigger,
} from '@/components/ui/sheet';
import { computed, useSlots, watch, ref } from 'vue';

// 侧拉方向类型
type Side = 'top' | 'right' | 'bottom' | 'left';
// 侧拉组件的尺寸类型
type Size = 'large' | 'middle' | 'small' | 'mini';

// Sheet组件的Props类型定义
interface SheetProps {
    /** 控制显隐 */
    visible?: boolean;
    /** 侧拉方向 */
    side?: Side;
    /** 侧拉尺寸 */
    size?: Size;
    /** 标题 */
    title?: string;
    /** 侧拉描述 */
    description?: string;
}

const props = withDefaults(defineProps<SheetProps>(), {
    visible: false,
    side: 'right',
    size: 'middle',
});

// 定义 emits，包括 update:visible 事件
const emit = defineEmits<{
    close: [];
    'update:visible': [value: boolean];
}>();

// 内部状态管理
const internalVisible = ref(props.visible);

// 监听 props.visible 的变化，同步到内部状态
watch(() => props.visible, (newValue) => {
    internalVisible.value = newValue;
});

// 监听内部状态的变化，向外发射事件
watch(internalVisible, (newValue) => {
    emit('update:visible', newValue);
    if (!newValue) {
        emit('close');
    }
});

// 根据侧拉方向和尺寸计算样式类
const sizeClass = computed(() => {
    const isHorizontal = props.side === 'left' || props.side === 'right';
    const isVertical = props.side === 'top' || props.side === 'bottom';

    const baseClasses = 'flex flex-col';

    if (isHorizontal) {
        // 水平方向的侧拉（左右）
        return {
            [baseClasses]: true,
            'w-[90vw] max-w-[90vw]': props.size === 'large',
            'w-[70vw] max-w-[70vw]': props.size === 'middle',
            'w-[50vw] max-w-[50vw]': props.size === 'small',
            'w-[30vw] max-w-[30vw]': props.size === 'mini',
            'lg:w-[90vw] lg:max-w-[90vw]': props.size === 'large',
            'lg:w-[70vw] lg:max-w-[70vw]': props.size === 'middle',
            'lg:w-[50vw] lg:max-w-[50vw]': props.size === 'small',
            'lg:w-[30vw] lg:max-w-[30vw]': props.size === 'mini',
            'sm:w-96 sm:max-w-96': props.size === 'large',
            'sm:w-80 sm:max-w-80': props.size === 'middle',
            'sm:w-64 sm:max-w-64': props.size === 'small',
            'sm:w-48 sm:max-w-48': props.size === 'mini',
        };
    } else if (isVertical) {
        // 垂直方向的侧拉（上下）
        return {
            [baseClasses]: true,
            'h-[90vh] max-h-[90vh]': props.size === 'large',
            'h-[70vh] max-h-[70vh]': props.size === 'middle',
            'h-[50vh] max-h-[50vh]': props.size === 'small',
            'h-[30vh] max-h-[30vh]': props.size === 'mini',
        };
    }

    return baseClasses;
});

// 接收插槽数据
const slots = useSlots();
</script>

<style scoped>
/* 确保内容区域可以正确滚动 */
.flex-1 {
    min-height: 0;
}
</style>