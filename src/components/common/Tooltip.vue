<template>
    <TooltipProvider>
        <Tooltip :delayDuration="delayDuration">
            <TooltipTrigger asChild>
                <slot></slot>
            </TooltipTrigger>
            <TooltipContent :class="[
                'z-50 overflow-hidden rounded-md border bg-popover px-2 py-1 text-xs text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
                contentClass
            ]" :side="side" :align="align">
                <slot name="content">{{ content }}</slot>
            </TooltipContent>
        </Tooltip>
    </TooltipProvider>
</template>

<script setup lang="ts">
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip'

// types/tooltip.d.ts
export interface TooltipProps {
    content?: string
    delayDuration?: number
    side?: 'top' | 'right' | 'bottom' | 'left'
    align?: 'start' | 'center' | 'end'
    contentClass?: string
}

withDefaults(defineProps<TooltipProps>(), {
    content: '',
    delayDuration: 0,
    side: 'top',
    align: 'center',
    contentClass: ''
})
</script>