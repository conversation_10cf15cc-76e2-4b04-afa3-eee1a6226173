<template>
    <Sheet :open="modelValue" @update:open="updateVisible">
        <SheetContent :class="contentClass">
            <div class="h-full flex flex-col bg-white rounded-l-lg shadow-lg">
                <SheetHeader v-if="$slots.header">
                    <slot name="header" />
                </SheetHeader>
                <div class="flex-1 overflow-y-auto">
                    <slot />
                </div>
                <SheetFooter v-if="$slots.footer">
                    <slot name="footer" />
                </SheetFooter>
            </div>
        </SheetContent>
    </Sheet>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
    Sheet,
    Sheet<PERSON>ontent,
    SheetHeader,
    SheetFooter,
} from '@/components/ui/sheet';

export default defineComponent({
    name: 'SideDrawer',
    components: {
        Sheet,
        She<PERSON><PERSON>ontent,
        She<PERSON><PERSON>eader,
        SheetFooter,
    },
    props: {
        modelValue: {
            type: Boolean,
            required: true,
        },
        contentClass: {
            type: String,
            default: '',
        },
    },
    setup() {},
});
</script>

<style scoped></style>
