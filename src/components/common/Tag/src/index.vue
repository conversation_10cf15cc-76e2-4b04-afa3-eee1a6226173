<template>
    <span
        class="z-tag"
        :class="[
            getType ? `z-tag--${getType}` : '',
            effect ? `is-${effect}` : '',
            round ? 'is-round' : '',
            size ? size : 'sm',
        ]"
        :style="{ backgroundColor: color }"
        @click="handleClick"
    >
        <slot>{{ label }}</slot>
        <i
            v-if="closable"
            class="el-tag__close el-icon-close"
            @click.stop="handleClose"
        ></i>
    </span>
</template>

<script lang="ts" setup>
import { get } from 'lodash-es';
import { computed, PropType } from 'vue';
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
    type: {
        type: String as PropType<
            'default' | 'info' | 'primary' | 'success' | 'warning' | 'danger'
        >,
        default: 'primary',
    },
    closable: {
        type: Boolean,
        default: false,
    },
    color: {
        type: String,
        default: '',
    },
    effect: {
        type: String as PropType<'light' | 'dark'>,
        default: 'dark',
    },
    label: {
        type: [String, Number],
        default: '',
    },

    round: {
        type: Boolean,
        default: true,
    },
    size: {
        type: String as PropType<'sm' | 'md'>,
        default: 'sm',
    },
});

const emit = defineEmits(['close', 'click']);

const typeMaps = {
    默认: 'default',
    主要: 'primary',
    成功: 'success',
    信息: 'info',
    警告: 'warning',
    危险: 'danger',
};

const getType = computed(() => {
    if (get(typeMaps, props.type)) return get(typeMaps, props.type);

    return props.type || 'primary';
});

const handleClose = () => {
    emit('close');
};

const handleClick = () => {
    emit('click');
};
</script>

<style lang="scss" scoped>
.z-tag {
    display: inline-block;
    padding: 0 10px;
    height: 28px;
    line-height: 26px;
    font-size: 12px;
    // color: #1f2d3d;
    // background-color: #f4f4f4;
    border-radius: 4px;
    box-sizing: border-box;
    border: 1px solid transparent;
    white-space: nowrap;

    &.is-round {
        border-radius: 13px;
    }

    &.sm {
        height: 20px;
        line-height: 18px;

        &.is-round {
            border-radius: 9px;
        }
    }

    .el-tag__close {
        margin-left: 6px;
        cursor: pointer;
        font-size: 12px;
        color: inherit;
        transition: all 0.3s ease;
        &:hover {
            opacity: 0.8;
        }
    }

    &--default {
        &.is-dark {
            background-color: #909399;
            color: #fff;
        }
        &.is-light {
            background-color: rgba(233, 233, 235, 0.7);
            color: #909399;
        }
    }
    &--info {
        &.is-dark {
            background-color: #409eff;
            color: #fff;
        }
        &.is-light {
            background-color: rgba(64, 158, 255, 0.2);
            color: rgb(64, 158, 255);
        }
    }

    &--primary {
        &.is-dark {
            background-color: rgb(102, 152, 255);
            color: #fff;
        }
        &.is-light {
            background-color: rgba(102, 152, 255, 0.2);
            color: rgb(102, 152, 255);
        }
    }

    &--success {
        &.is-dark {
            background-color: rgb(115, 216, 151);
            color: #fff;
        }
        &.is-light {
            background-color: rgba(115, 216, 151, 0.1);
            color: rgb(115, 216, 151);
        }
    }

    &--warning {
        &.is-dark {
            background-color: rgb(255, 159, 115);
            color: #fff;
        }
        &.is-light {
            background-color: rgba(255, 159, 115, 0.2);
            color: rgb(255, 159, 115);
        }
    }

    &--danger {
        &.is-dark {
            background-color: rgb(255, 117, 117);
            color: #fff;
        }
        &.is-light {
            background-color: rgba(255, 117, 117, 0.2);
            color: rgb(255, 117, 117);
        }
    }
}
</style>
