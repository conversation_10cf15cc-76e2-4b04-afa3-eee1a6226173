.tree {
  @apply w-full;
}

.tree-node {
  @apply w-full;
}

.tree-node-content {
  @apply flex items-center gap-2 py-1 px-2 hover:bg-gray-100 cursor-pointer;
}

.tree-node-expand-icon {
  @apply text-gray-500 transition-transform duration-200 inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
}

.tree-node-children {
  @apply pl-6;
}

.tree-node-label {
  @apply text-sm;
}

.tree-node input[type="checkbox"] {
  @apply w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary;
} 