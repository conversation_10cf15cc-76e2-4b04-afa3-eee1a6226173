# NTree 树形组件

一个功能完整的Vue3树形组件，支持展开/折叠、复选框选择、下拉菜单操作等功能。

## 功能特性

- ✅ 树形数据展示
- ✅ 展开/折叠节点
- ✅ 复选框选择（支持严格模式和联动模式）
- ✅ 自定义下拉菜单操作
- ✅ 鼠标悬停显示操作图标
- ✅ 递归结构支持无限层级
- ✅ TypeScript 类型支持

## 基本用法

```vue
<template>
    <Tree
        :data="treeData"
        :check-strictly="false"
        :expanded-keys="expandedKeys"
        :checked-keys="checkedKeys"
        :dropdown-menu-items="menuItems"
        @update:expanded-keys="expandedKeys = $event"
        @update:checked-keys="checkedKeys = $event"
        @menu-item-click="handleMenuClick"
        @checkbox-change="handleCheckboxChange"
    />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Tree from '@/components/common/NTree/Tree.vue';
import type {
    TreeNode,
    DropdownMenuItem,
} from '@/components/common/NTree/types';

// 树形数据
const treeData = ref<TreeNode[]>([
    {
        id: 1,
        name: '根节点1',
        children: [
            {
                id: 2,
                name: '子节点1-1',
                children: [{ id: 3, name: '子节点1-1-1' }],
            },
        ],
    },
]);

// 展开的节点ID数组
const expandedKeys = ref<number[]>([1]);

// 选中的节点ID数组
const checkedKeys = ref<number[]>([]);

// 下拉菜单配置
const menuItems = ref<DropdownMenuItem[]>([
    { key: 'add', label: '新增' },
    { key: 'edit', label: '编辑' },
    { key: 'delete', label: '删除', danger: true },
    { key: 'copy', label: '复制' },
    { key: 'move', label: '移动', disabled: true },
]);

// 处理菜单项点击
const handleMenuClick = (menuKey: string, node: TreeNode) => {
    console.log('菜单操作:', menuKey, '节点:', node);

    switch (menuKey) {
        case 'add':
            // 处理新增逻辑
            console.log('新增节点:', node.name);
            break;
        case 'edit':
            // 处理编辑逻辑
            console.log('编辑节点:', node.name);
            break;
        case 'delete':
            // 处理删除逻辑
            console.log('删除节点:', node.name);
            break;
        case 'copy':
            // 处理复制逻辑
            console.log('复制节点:', node.name);
            break;
        case 'move':
            // 处理移动逻辑
            console.log('移动节点:', node.name);
            break;
    }
};

// 处理checkbox变化事件
const handleCheckboxChange = (data: {
    node: TreeNode;
    checked: boolean;
    timestamp: number;
    allCheckedKeys: number[];
}) => {
    console.log('Checkbox变化:', {
        节点ID: data.node.id,
        节点名称: data.node.name,
        选中状态: data.checked,
        变化时间: new Date(data.timestamp),
        所有选中ID: data.allCheckedKeys,
    });

    // 在这里可以处理各种业务逻辑，如：
    // 1. 记录用户操作日志
    // 2. 实时保存选中状态到服务器
    // 3. 触发其他相关业务逻辑
    // 4. 发送统计数据等

    // 示例：自动保存选中状态
    localStorage.setItem(
        'treeCheckedKeys',
        JSON.stringify(data.allCheckedKeys)
    );

    // 示例：记录操作日志
    const logEntry = {
        action: 'checkbox_change',
        nodeId: data.node.id,
        nodeName: data.node.name,
        checked: data.checked,
        timestamp: data.timestamp,
        user: 'current_user', // 替换为实际用户信息
    };
    console.log('操作日志:', logEntry);
};
</script>
```

## Props 属性

| 属性名            | 类型               | 默认值 | 说明                                   |
| ----------------- | ------------------ | ------ | -------------------------------------- |
| data              | TreeNode[]         | []     | 树形数据源                             |
| checkStrictly     | boolean            | false  | 是否严格模式（父子节点选中状态不关联） |
| expandedKeys      | number[]           | []     | 展开节点的ID数组                       |
| checkedKeys       | number[]           | []     | 选中节点的ID数组                       |
| dropdownMenuItems | DropdownMenuItem[] | []     | 下拉菜单项配置                         |

## Events 事件

| 事件名               | 参数                                                                                      | 说明               |
| -------------------- | ----------------------------------------------------------------------------------------- | ------------------ |
| update:expanded-keys | (keys: number[])                                                                          | 展开状态变化时触发 |
| update:checked-keys  | (keys: number[])                                                                          | 选中状态变化时触发 |
| menu-item-click      | (menuKey: string, node: TreeNode)                                                         | 菜单项点击时触发   |
| checkbox-change      | (data: { node: TreeNode; checked: boolean; timestamp: number; allCheckedKeys: number[] }) | Checkbox变化时触发 |

## 类型定义

### TreeNode

```typescript
interface TreeNode {
    id: number; // 节点唯一标识
    name: string; // 节点显示名称
    children?: TreeNode[]; // 子节点数组
    parentId?: number; // 父节点ID
    level?: number; // 节点层级
    isLeaf?: boolean; // 是否为叶子节点
    disabled?: boolean; // 是否禁用
    checked?: boolean; // 是否选中
    indeterminate?: boolean; // 是否半选状态
    expanded?: boolean; // 是否展开
}
```

### DropdownMenuItem

```typescript
interface DropdownMenuItem {
    key: string; // 菜单项唯一标识
    label: string; // 显示文本
    icon?: string; // 图标（预留）
    disabled?: boolean; // 是否禁用
    class?: string; // 自定义CSS类名
    danger?: boolean; // 是否为危险操作（红色文字）
}
```

## 方法

通过 ref 可以访问组件实例的方法：

```typescript
const treeRef = ref<InstanceType<typeof Tree>>();

// 展开所有节点
treeRef.value?.expandAll(true);

// 折叠所有节点
treeRef.value?.expandAll(false);

// 设置选中节点
treeRef.value?.setCheckedKeys([1, 2, 3]);

// 获取选中节点
const checkedKeys = treeRef.value?.getCheckedKeys();
```

## 自定义样式

组件使用 Tailwind CSS 类名，可以通过以下方式自定义样式：

```css
/* 自定义节点悬停效果 */
.tree-node-content:hover {
    background-color: #f3f4f6;
}

/* 自定义下拉菜单样式 */
.tree-node-dropdown {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
```

## 注意事项

1. 下拉菜单项的 `key` 必须唯一
2. 危险操作建议设置 `danger: true` 以红色文字显示
3. 禁用的菜单项不会触发点击事件
4. 组件支持无限层级的树形结构
5. 选中状态支持严格模式和联动模式两种
