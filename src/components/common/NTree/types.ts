export interface TreeNode {
  id: number
  name: string
  children?: TreeNode[]
  parentId?: number
  level?: number
  isLeaf?: boolean
  disabled?: boolean
  checked?: boolean
  indeterminate?: boolean
  expanded?: boolean
}

// 下拉菜单项的类型定义
export interface MenuItem {
  key: string // 菜单项的唯一标识
  label: string // 显示的文本
  permission?: string[] // 权限
  icon?: string // 图标（可选）
  disabled?: boolean // 是否禁用
  class?: string // 自定义CSS类名
  danger?: boolean // 是否为危险操作（如删除）
}

export interface DropdownMenuItem {
  header?: {
    title?: string
    menu?: MenuItem[]
  }
  menuItem?: MenuItem[]
}
export interface TreeConfig {
  expandAll: boolean
  selectAll: boolean
  checkStrictly: boolean
}
