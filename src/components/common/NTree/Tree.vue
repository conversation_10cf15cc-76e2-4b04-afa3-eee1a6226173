<template>
    <!-- 树形组件的根容器 -->
    <div class="tree">
        <!-- 遍历数据源中的每个节点 -->
        <div v-for="node in data" :key="node.id" class="tree-node">
            <!-- 节点的内容部分 -->
            <div class="tree-node-content h-8">
                <!-- 左侧内容：展开图标、复选框、节点名称 -->
                <div class="flex items-center gap-2">
                    <!-- 展开/折叠图标区域 -->
                    <div class="tree-node-icon" @click="toggleExpand(node.id)">
                        <!-- 如果节点有子节点，显示可点击的展开/折叠图标 -->
                        <ChevronRight v-if="node.children?.length" class="h-4 w-4 transition-transform duration-200"
                            :class="{
                                'rotate-90': expandedKeys.includes(node.id),
                            }" />
                        <!-- 如果节点没有子节点，显示一个占位符以保持对齐 -->
                        <div v-else class="w-4"></div>
                    </div>
                    <!-- 节点的标签区域，包含复选框和文本 -->
                    <div class="tree-node-label">
                        <!-- 复选框组件，用于选择/取消选择节点 -->
                        <!-- 根据checkedKeys判断是否选中 -->
                        <!-- 处理选中状态变化 -->
                        <!-- 支持禁用状态 -->
                        <Checkbox :checked="checkedKeys.includes(node.id)" @update:checked="
                            (checked) => handleCheck(node, checked)
                        " :disabled="node.disabled" />
                        <!-- 显示节点名称 -->
                        <span class="text-xs transition-colors" :class="[
                            node.disabled
                                ? 'opacity-50 cursor-not-allowed'
                                : 'cursor-pointer hover:text-blue-600',
                        ]" @click="handleNodeNameClick(node)">{{ getNodeType(node) }}{{ node.name }}</span>
                    </div>
                </div>
                <!-- 右侧操作图标 -->
                <div class="relative h-6 pt-1 pb-1">
                    <span v-if="props.dropdownMenuItems?.menuItem?.length"
                        class="text-xs tree-node-actions cursor-pointer hover:bg-gray-100"
                        @click.stop="toggleDropdown(node.id)">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
                            <g fill="none" stroke="#8c8b8b" stroke-linecap="round" stroke-linejoin="round"
                                stroke-width="2">
                                <circle cx="12" cy="12" r="1" />
                                <circle cx="12" cy="5" r="1" />
                                <circle cx="12" cy="19" r="1" />
                            </g>
                        </svg>
                    </span>
                    <!-- 下拉菜单 -->
                    <div v-if="
                        activeDropdown === node.id &&
                        props.dropdownMenuItems?.menuItem?.length
                    "
                        class="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 py-1 min-w-20"
                        @click.stop>
                        <div v-for="menuItem in props.dropdownMenuItems
                            ?.menuItem" :key="menuItem.key"
                            class="px-3 py-2 text-xs cursor-pointer hover:bg-gray-50" :class="[
                                menuItem.class,
                                { 'text-red-600': menuItem.danger },
                                {
                                    'opacity-50 cursor-not-allowed':
                                        menuItem.disabled,
                                },
                            ]" @click="handleMenuItemClick(menuItem, node)">
                            {{ menuItem.label }}
                        </div>
                    </div>
                </div>
            </div>
            <!-- 子节点容器，仅当节点有子节点且处于展开状态时显示 -->
            <div v-if="node.children?.length && expandedKeys.includes(node.id)" class="tree-node-children">
                <!-- 递归渲染子节点，形成树形结构 -->
                <!-- :data="node.children" 传递子节点数据 -->
                <!--:check-strictly="checkStrictly"  传递选择模式配置 -->
                <!--:expanded-keys="expandedKeys" 传递展开状态 -->
                <!--:checked-keys="checkedKeys" 传递选中状态 -->
                <!-- @update:expanded-keys="updateExpandedKeys" 子组件展开状态变化时的回调 -->
                <!-- @update:checked-keys="updateCheckedKeys" 子组件选中状态变化时的回调 -->
                <!-- 这个组件是递归引用自身的Tree组件，用于渲染子节点 -->
                <!-- 这是一个自定义的树形组件，来源于当前文件本身，通过递归方式构建多层级树结构 -->
                <Tree :data="node.children" :check-strictly="checkStrictly" :expanded-keys="expandedKeys"
                    :checked-keys="checkedKeys" :dropdown-menu-items="props.dropdownMenuItems"
                    @update:expanded-keys="updateExpandedKeys" @update:checked-keys="updateCheckedKeys"
                    @menu-item-click="
                        (menuKey, node) =>
                            emit('menu-item-click', menuKey, node)
                    " @checkbox-change="(data) => emit('checkbox-change', data)" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ChevronRight } from 'lucide-vue-next';
import { Checkbox } from '@/components/ui/checkbox';
import type { TreeNode, DropdownMenuItem, MenuItem } from './types';
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps<{
    data: TreeNode[];
    checkStrictly: boolean;
    expandedKeys: number[];
    checkedKeys: number[];
    dropdownMenuItems?: DropdownMenuItem; // 下拉菜单选项
}>();

const emit = defineEmits<{
    'update:expanded-keys': [value: number[]];
    'update:checked-keys': [value: number[]];
    'menu-item-click': [menuKey: string, node: TreeNode]; // 统一的菜单项点击事件
    'checkbox-change': [
        data: {
            node: TreeNode;
            checked: boolean;
            timestamp: number;
            allCheckedKeys: number[];
        },
    ]; // checkbox变化事件
}>();

// 下拉菜单的活动状态
const activeDropdown = ref<number | null>(null);

// 切换下拉菜单显示
const toggleDropdown = (nodeId: number) => {
    // 如果点击的是当前已打开的下拉菜单，则关闭它
    if (activeDropdown.value === nodeId) {
        activeDropdown.value = null;
    } else {
        // 如果点击的是其他节点，则关闭当前打开的下拉菜单，并打开新点击的下拉菜单
        activeDropdown.value = nodeId;
    }
};

// 关闭下拉菜单
const closeDropdown = () => {
    activeDropdown.value = null;
};

// 处理点击事件，点击其他地方关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
    closeDropdown();
};

// 监听点击事件
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});

// 获取所有节点的 ID
const getAllKeys = (nodes: TreeNode[]): number[] => {
    return nodes.reduce((keys: number[], node) => {
        keys.push(node.id);
        if (node.children) {
            keys.push(...getAllKeys(node.children));
        }
        return keys;
    }, []);
};

// 获取所有子节点的 ID
const getChildrenKeys = (node: TreeNode): number[] => {
    const keys: number[] = [];
    if (node.children) {
        node.children.forEach((child) => {
            keys.push(child.id);
            if (child.children) {
                keys.push(...getChildrenKeys(child));
            }
        });
    }
    return keys;
};

// 更新展开状态
const updateExpandedKeys = (keys: number[]) => {
    emit('update:expanded-keys', keys);
};

// 更新选中状态
const updateCheckedKeys = (keys: number[]) => {
    emit('update:checked-keys', keys);
};

// 展开/折叠节点
const toggleExpand = (nodeId: number): void => {
    const newValue = [...props.expandedKeys];
    const index = newValue.indexOf(nodeId);
    if (index > -1) {
        newValue.splice(index, 1);
    } else {
        newValue.push(nodeId);
    }
    emit('update:expanded-keys', newValue);
};

// 展开所有节点
const expandAll = (value: boolean): void => {
    const newValue = value ? getAllKeys(props.data) : [];
    emit('update:expanded-keys', newValue);
};

const getNodeType = (node: TreeNode): string => {
    // 添加类型保护，确保属性存在
    const nodeAny = node as any;
    if (nodeAny.type === 'menu' && nodeAny.route_component) {
        return '[菜单] ';
    } else if (nodeAny.type === 'api') {
        return '[按钮] ';
    } else if (nodeAny.type === 'menu' && !nodeAny.route_component) {
        return '[目录] ';
    }
    return '';
};

// 处理新增节点
const handleAdd = (node: TreeNode) => {
    emit('menu-item-click', 'add', node);
};

// 处理编辑节点
const handleEdit = (node: TreeNode) => {
    emit('menu-item-click', 'edit', node);
};

// 处理删除节点
const handleDelete = (node: TreeNode) => {
    emit('menu-item-click', 'delete', node);
};

// 处理节点选中状态
const handleCheck = (node: TreeNode, checked: boolean) => {
    if (node.disabled) return;

    const newCheckedKeys = [...props.checkedKeys];
    const index = newCheckedKeys.indexOf(node.id);

    if (!props.checkStrictly && node.children?.length) {
        // 非严格模式下，选中父节点时同时选中所有子节点
        const childrenKeys = getChildrenKeys(node);
        if (checked) {
            // 选中当前节点和所有子节点
            if (index === -1) {
                newCheckedKeys.push(node.id);
            }
            childrenKeys.forEach((key) => {
                if (!newCheckedKeys.includes(key)) {
                    newCheckedKeys.push(key);
                }
            });
        } else {
            // 取消选中当前节点和所有子节点
            if (index > -1) {
                newCheckedKeys.splice(index, 1);
            }
            childrenKeys.forEach((key) => {
                const childIndex = newCheckedKeys.indexOf(key);
                if (childIndex > -1) {
                    newCheckedKeys.splice(childIndex, 1);
                }
            });
        }
    } else {
        // 严格模式下，只处理当前节点
        if (checked && index === -1) {
            newCheckedKeys.push(node.id);
        } else if (!checked && index > -1) {
            newCheckedKeys.splice(index, 1);
        }
    }
    console.log('handleCheck', newCheckedKeys);

    // 发射checkbox变化事件，包含详细信息
    emit('checkbox-change', {
        node,
        checked,
        timestamp: Date.now(),
        allCheckedKeys: newCheckedKeys,
    });

    emit('update:checked-keys', newCheckedKeys);
};

// 设置选中状态
const setCheckedKeys = (keys: number[]): void => {
    emit('update:checked-keys', keys);
};

// 获取选中的节点
const getCheckedKeys = (): number[] => {
    return [...props.checkedKeys];
};

// 处理新增节点点击
const handleAddClick = (node: TreeNode) => {
    closeDropdown();
    handleAdd(node);
};

// 处理编辑节点点击
const handleEditClick = (node: TreeNode) => {
    closeDropdown();
    handleEdit(node);
};

// 处理删除节点点击
const handleDeleteClick = (node: TreeNode) => {
    closeDropdown();
    handleDelete(node);
};

// 处理菜单项点击
const handleMenuItemClick = (menuItem: MenuItem, node: TreeNode) => {
    closeDropdown();
    if (!menuItem.disabled) {
        emit('menu-item-click', menuItem.key, node);
    }
};

// 处理节点名称点击
const handleNodeNameClick = (node: TreeNode) => {
    // 如果节点被禁用，则不处理点击
    if (node.disabled) return;

    // 获取当前节点的选中状态
    const currentChecked = props.checkedKeys.includes(node.id);

    // 切换选中状态：如果当前选中则取消选中，否则选中
    const newCheckedState = !currentChecked;

    // 调用handleCheck函数处理选中状态变化
    handleCheck(node, newCheckedState);

    console.log('节点名称点击，切换选中状态:', {
        节点: node.name,
        原状态: currentChecked ? '选中' : '未选中',
        新状态: newCheckedState ? '选中' : '未选中',
    });
};

// 暴露方法给父组件
defineExpose({
    expandAll,
    setCheckedKeys,
    getCheckedKeys,
});
</script>

<style>
.tree {
    @apply space-y-1;
}

.tree-node {
    @apply relative;
}

.tree-node-content {
    @apply flex items-center justify-between gap-2 py-0.5 px-2 hover:bg-accent/50 rounded-md transition-colors;
}

.tree-node-icon {
    @apply flex items-center justify-center w-4 h-4 cursor-pointer text-muted-foreground hover:text-foreground;
}

.tree-node-label {
    @apply flex items-center gap-2;
}

.tree-node-children {
    @apply pl-6;
}

.tree-node-actions {
    @apply opacity-0 transition-opacity duration-200;
}

.tree-node:hover .tree-node-actions {
    @apply opacity-100;
}
</style>
