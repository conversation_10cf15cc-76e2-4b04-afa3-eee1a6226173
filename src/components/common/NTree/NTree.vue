<template>
    <div class="space-y-2 h-full">
        <div v-if="!hideCheck" class="flex flex-col sm:flex-row gap-2">
            <div class="flex items-center">
                <Switch :checked="treeConfig.expandAll" @update:checked="updateExpandAll" id="expand-all" />
                <Label for="expand-all" class="ml-2 text-[11px]">展开/折叠</Label>
            </div>

            <div class="flex items-center">
                <Switch :checked="treeConfig.selectAll" @update:checked="updateSelectAll" id="select-all" />
                <Label for="select-all" class="ml-2 text-[11px]">全选/全不选</Label>
            </div>

            <div class="flex items-center">
                <Switch :checked="treeConfig.checkStrictly" @update:checked="updateCheckStrictly" id="check-strictly" />
                <Label for="check-strictly" class="ml-2 text-[11px]">父子级联动</Label>
            </div>
        </div>

        <div class="border rounded-md overflow-y-auto" style="height: calc(100% - 28px)">
            <!--标题 右侧操作图标-->
            <div v-if="menuItems?.header"
                class="relative flex items-center justify-between px-2 py-1 border-b bg-gray-50"
                @mouseenter="showHeaderIcon = true" @mouseleave="showHeaderIcon = false">
                <span @click="() => updateSelectAll(!treeConfig.selectAll)"
                    class="pl-2 flex-1 text-xs font-medium cursor-pointer hover:text-blue-600">{{
                        menuItems?.header?.title }}</span>
                <div class="relative w-5 h-5">
                    <span v-show="menuItems?.header?.menu?.length &&
                        (showHeaderIcon || showHeaderDropdown)
                        " class="cursor-pointer rounded hover:bg-gray-200 transition-colors"
                        @click="toggleHeaderDropdown">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">
                            <g fill="none" stroke="#8c8b8b" stroke-linecap="round" stroke-linejoin="round"
                                stroke-width="2">
                                <circle cx="12" cy="12" r="1" />
                                <circle cx="12" cy="5" r="1" />
                                <circle cx="12" cy="19" r="1" />
                            </g>
                        </svg>
                    </span>
                    <!-- 下拉菜单 -->
                    <div v-show="showHeaderDropdown"
                        class="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 py-1 min-w-20"
                        @click.stop>
                        <div v-for="menuItem in menuItems?.header
                            ?.menu" :key="menuItem.key" class="px-3 py-2 text-xs cursor-pointer hover:bg-gray-50"
                            @click="handleMenuClick(menuItem.key)">
                            {{ menuItem.label }}
                        </div>
                    </div>
                </div>
            </div>
            <Tree ref="permissionTree" :data="props.data" :check-strictly="!treeConfig.checkStrictly"
                v-model:expanded-keys="expandedKeys" v-model:checked-keys="checkedKeys" :dropdown-menu-items="menuItems"
                @checkbox-change="handleCheckboxChange" @menu-item-click="handleMenuClick" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onUnmounted, nextTick, computed } from 'vue';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import Tree from './Tree.vue';
import type { TreeNode, DropdownMenuItem, MenuItem, TreeConfig } from './types';
import { useUserStore } from '@/store/user';
import { deepClone } from '@/utils/util';

const props = defineProps<{
    // 树形数据
    data?: TreeNode[];
    // 是否严格模式
    checkStrictly?: boolean;
    /** 是否全部展开 */
    expandAll?: boolean;
    // 展开的节点keys
    expandedKeys?: number[];
    // 选中的节点keys
    checkedKeys?: number[];
    // 下拉菜单项
    dropdownMenuItems?: DropdownMenuItem;
    /** 是否显示顶部操作栏 */
    hideCheck?: boolean;
}>();
watch(
    () => props.checkedKeys,
    (newVal) => {
        console.log('checkedKeys', newVal);
        checkedKeys.value = newVal;
    }
);

const userStore = useUserStore();
const permissions = userStore.userBtnPermissions;

const menuItems = computed(() => {
    // 这里需要根据权限过滤树形 dropdownMenuItems
    if (!props.dropdownMenuItems) {
        return {} as DropdownMenuItem;
    }
    const cloneItems = deepClone(props.dropdownMenuItems);
    const filterMenuItems = (menuItems: MenuItem[]) => {
        return menuItems.filter((item) => {
            if (item.permission) {
                return item.permission.some((permission) => permissions.includes(permission));
            }
            return true;
        });
    };
    const filterDropdownMenuItems = (data: DropdownMenuItem) => {
        if (data.header) {
            data.header.menu = filterMenuItems(data.header.menu);
        }
        if (data.menuItem) {
            data.menuItem = filterMenuItems(data.menuItem);
        }
        return data;
    };
    return filterDropdownMenuItems(cloneItems);
})


const emit = defineEmits<{
    'update:modelValue': [value: boolean];
    'checkbox-change': [
        data: {
            node: TreeNode;
            checked: boolean;
            timestamp: number;
            allCheckedKeys: number[];
            selectAll?: boolean;
        },
    ];
    'menu-item-click': [menuKey: string, node: TreeNode];
    //'header-menu-click': [menuKey: string];
    submit: [
        data: {
            id: number | null;
            name: string;
            sort_order: number;
            description: string | null;
            code: string;
            status: '启用' | '禁用';
            permissions: number[];
        },
    ];
}>();

// 树形配置
const treeConfig = reactive<TreeConfig>({
    expandAll: false,
    selectAll: false,
    checkStrictly: true,
});

// 控制标题区域图标显示
const showHeaderIcon = ref(false);
// 控制下拉菜单显示
const showHeaderDropdown = ref(false);

const permissionTree = ref();
const expandedKeys = ref<number[]>([]);
const checkedKeys = ref<number[]>([]);

// 获取所有节点的 ID
const getAllKeys = (nodes: TreeNode[] = []): number[] => {
    return nodes.reduce((keys: number[], node) => {
        keys.push(node.id);
        if (node.children) {
            keys.push(...getAllKeys(node.children));
        }
        return keys;
    }, []);
};

const updateExpandAll = (value: boolean) => {
    treeConfig.expandAll = value;
    const currentData = props.data || [];

    if (value) {
        // 如果treeConfig.expandAll为true，表示需要展开所有节点
        // 调用getAllKeys函数获取权限树中所有节点的ID
        const allKeys = getAllKeys(currentData);
        // 将所有节点ID设置到expandedKeys中，使所有节点展开
        expandedKeys.value = allKeys;
    } else {
        // 如果treeConfig.expandAll为false，表示需要折叠所有节点
        // 清空expandedKeys，使所有节点折叠
        expandedKeys.value = [];
    }
};

watch(() => props.data, (newVal) => {
    if (newVal && props.expandAll) {
        updateExpandAll(props.expandAll);
    }
}, { immediate: true });

const updateSelectAll = (value: boolean) => {
    treeConfig.selectAll = value;
    const currentData = props.data || [];

    if (value) {
        const allKeys = getAllKeys(currentData);
        checkedKeys.value = allKeys;
    } else {
        checkedKeys.value = [];
    }
    console.log('updateSelectAll', checkedKeys.value);
    // 将事件传递给父组件
    emit('checkbox-change', {
        selectAll: checkedKeys.value.length > 0 ? true : false,
        node: { id: 0, name: '全选' },
        checked: value,
        timestamp: Date.now(),
        allCheckedKeys: checkedKeys.value,
    });
};

const updateChecked = (data: TreeNode[]) => {
    checkedKeys.value = getAllKeys(data);
    console.log('updateChecked', checkedKeys.value);
};

const updateCheckStrictly = (value: boolean) => {
    treeConfig.checkStrictly = value;
};

// 处理checkbox变化事件
const handleCheckboxChange = (data: {
    node: TreeNode;
    checked: boolean;
    timestamp: number;
    allCheckedKeys: number[];
}) => {
    console.log('NTree接收到checkbox变化:', data);

    // 将事件传递给父组件
    emit('checkbox-change', data);

    // 这里可以添加NTree组件特有的处理逻辑
    // 比如自动更新某些状态等
};

// 处理checkbox变化事件
const handleMenuClick = (menuKey: string, node?: TreeNode) => {
    console.log('NTree接收到menu-item-click:', menuKey, node);
    // 隐藏下拉菜单
    showHeaderDropdown.value = false;
    // 将事件传递给父组件
    emit('menu-item-click', menuKey, node);

    // 这里可以添加NTree组件特有的处理逻辑
    // 比如自动更新某些状态等
};

const toggleHeaderDropdown = () => {
    showHeaderDropdown.value = !showHeaderDropdown.value;
};

// const handleHeaderClick = (menuItem: Menu) => {
//     console.log('标题菜单点击:', menuItem);

//     // 隐藏下拉菜单
//     showHeaderDropdown.value = false;

//     // 将事件传递给父组件
//     emit('header-menu-click', menuItem.key);
// };

// 点击外部区域关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
    const target = event.target as HTMLElement;
    if (!target.closest('.relative')) {
        showHeaderDropdown.value = false;
    }
};

// 添加全局点击事件监听
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
});

// 清理事件监听
onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
});

defineExpose({
    updateChecked,
});
// 在父组件中使用示例：
//
// <template>
//   <NTree
//     :data="treeData"
//     :dropdown-menu-items="menuItems"
//     @checkbox-change="handleTreeCheckboxChange"
//   />
// </template>
//
// <script setup lang="ts">
// import { ref } from 'vue';
// import NTree from '@/components/common/NTree/NTree.vue';
// import type { TreeNode } from '@/components/common/NTree/types';
//
// const treeData = ref<TreeNode[]>([
//   // 你的树形数据
// ]);
//
// // 处理树形组件的checkbox变化
// const handleTreeCheckboxChange = (data: {
//   node: TreeNode;
//   checked: boolean;
//   timestamp: number;
//   allCheckedKeys: number[];
// }) => {
//   console.log('父组件接收到checkbox变化:', {
//     节点ID: data.node.id,
//     节点名称: data.node.name,
//     选中状态: data.checked ? '选中' : '取消选中',
//     变化时间: new Date(data.timestamp).toLocaleString(),
//     当前所有选中节点: data.allCheckedKeys
//   });
//
//   // 在这里处理你的业务逻辑：
//   // 1. 保存到数据库
//   // 2. 更新其他组件状态
//   // 3. 发送API请求
//   // 4. 记录操作日志等
//
//   // 示例：发送到服务器
//   // saveUserPermissions(data.allCheckedKeys);
//
//   // 示例：更新本地状态
//   // selectedPermissions.value = data.allCheckedKeys;
// };
</script>
