@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;

        --muted: 240 4.8% 95.9%;
        --muted-foreground: 240 3.8% 46.1%;

        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;

        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;

        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;

        --primary: 240 5.9% 10%;
        --primary-foreground: 0 0% 98%;

        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;

        --accent: 240 4.8% 95.9%;
        --accent-foreground: 240 5.9% 10%;

        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;

        --ring: 240 10% 3.9%;

        --radius: 0.5rem;
    }

    .dark {
        --background: 240 10% 3.9%;
        --foreground: 0 0% 98%;

        --muted: 240 3.7% 15.9%;
        --muted-foreground: 240 5% 64.9%;

        --popover: 240 10% 3.9%;
        --popover-foreground: 0 0% 98%;

        --card: 240 10% 3.9%;
        --card-foreground: 0 0% 98%;

        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;

        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;

        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;

        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;

        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;

        --ring: 240 4.9% 83.9%;
    }

    /* 蓝色主题 (默认) */
    .theme-blue {
        --theme-primary: 227 100% 26%; /* #20469B 蓝色 */
        --theme-primary-hover: 227 100% 55%;
        --theme-primary-foreground: 0 0% 98%;
        --theme-accent: 51 100% 64%; /* #ffb44b 橙色强调色 */
    }

    /* 绿色主题 */
    .theme-green {
        --theme-primary: 142 76% 36%; /* #22C55E 绿色 */
        --theme-primary-hover: 142 76% 30%;
        --theme-primary-foreground: 0 0% 98%;
        --theme-accent: 47 96% 53%; /* #EAB308 黄色强调色 */
    }

    /* 黑色主题 */
    .theme-black {
        --theme-primary: 240 6% 10%; /* #1F2937 深灰黑色 */
        --theme-primary-hover: 240 6% 15%;
        --theme-primary-foreground: 0 0% 98%;
        --theme-accent: 217 91% 60%; /* #3B82F6 蓝色强调色 */
    }
}

@layer base {
    * {
        @apply border-border;
    }

    html,
    body {
        @apply h-full;
    }

    body {
        @apply bg-background text-foreground font-sans text-sm;
    }

    /* 确保应用根元素占满视口高度 */
    #app {
        @apply h-full;
    }
}

@layer components {
    .text-title {
        @apply font-sans text-[14px] font-bold;
    }

    .text-content {
        @apply font-sans text-[12px] font-normal;
    }

    .text-action {
        @apply font-sans text-[12px] font-normal text-[#3399FF];
    }

    .text-title-en {
        @apply font-arial text-[14px] font-bold text-[#203251];
    }

    .text-content-en {
        @apply font-arial text-[12px] font-normal;
    }

    .text-action-en {
        @apply font-arial text-[12px] font-normal text-[#3399FF];
    }

    /* 主题颜色样式类 */
    .bg-theme-primary {
        background-color: hsl(var(--theme-primary));
    }

    .bg-theme-primary-hover {
        background-color: hsl(var(--theme-primary-hover));
    }

    .text-theme-primary {
        color: hsl(var(--theme-primary));
    }

    .text-theme-accent {
        color: hsl(var(--theme-accent));
    }

    .border-theme-primary {
        border-color: hsl(var(--theme-primary));
    }

    .hover\:bg-theme-primary-hover:hover {
        background-color: hsl(var(--theme-primary-hover));
    }

    .hover\:text-theme-accent:hover {
        color: hsl(var(--theme-accent));
    }
}
