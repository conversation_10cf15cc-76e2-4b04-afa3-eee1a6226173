/* CSS 实现 */
:root {
    --font-safe-sans: 'HarmonyOS Sans', 'MiSans', 'Source Han <PERSON>', sans-serif;
}

body {
    font-family:
        var(--font-safe-sans), ui-sans-serif, system-ui, sans-serif,
        'Noto Color Emoji';
}

/* v-loading 全局样式 */
.v-loading-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.85);
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.v-loading-text {
    margin-top: 12px;
    color: #3498db;
    font-size: 14px;
}

.v-loading-relative {
    position: relative !important;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
