#!/usr/bin/env node

/**
 * 表格代码生成器 CLI 工具
 * 用于根据后端元数据接口自动生成 Vue 表格组件代码
 */

import { readFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createInterface } from 'readline';
import axios from 'axios';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 命令行接口
const rl = createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: true,
});

// 获取用户输入的辅助函数
function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, (answer) => {
            resolve(answer.trim());
        });
    });
}

// 显示帮助信息
function showHelp() {
    console.log(`
🚀 表格代码生成器 CLI 工具

用法:
  npm run generate:table [options]

选项:
  --help, -h     显示帮助信息
  --interactive  交互式配置（默认）

示例:
  npm run generate:table
  npm run generate:table --interactive

交互式配置将引导您输入以下参数：
  1. 模块名称 (例: sys/user, sal/customer)
  2. 模型名称 (例: User, Customer)
  3. 认证 TOKEN (Bearer token)
  4. 输出文件路径（可选）

API 路径将自动按照标准 CRUD 格式生成：
  - 元数据接口: /{module}/get_metadata
  - 数据查询接口: /{module}/query
`);
}

// 字段类型映射配置
const FIELD_TYPE_MAPPING = {
    // 基础数据类型
    string: { type: 'text', helper: 'text' },
    varchar: { type: 'text', helper: 'text' },
    char: { type: 'text', helper: 'text' },
    text: { type: 'text', helper: 'text' },

    // 数字类型
    integer: { type: 'number', helper: 'number' },
    int: { type: 'number', helper: 'number' },
    bigint: { type: 'number', helper: 'number' },
    float: { type: 'number', helper: 'currency' },
    decimal: { type: 'number', helper: 'currency' },
    numeric: { type: 'number', helper: 'currency' },
    money: { type: 'currency', helper: 'currency' },

    // 布尔类型
    boolean: { type: 'boolean', helper: 'boolean' },
    bool: { type: 'boolean', helper: 'boolean' },

    // 日期时间类型
    datetime: { type: 'datetime', helper: 'datetime' },
    timestamp: { type: 'datetime', helper: 'datetime' },
    date: { type: 'date', helper: 'dateOnly' },
    time: { type: 'time', helper: 'timeOnly' },

    // 特殊类型
    enum: { type: 'status', helper: 'status' },
    json: { type: 'text', helper: 'text' },
    uuid: { type: 'text', helper: 'text' },
};

// 特殊字段名映射
const SPECIAL_FIELD_MAPPING = {
    // 用户相关字段
    created_by: { type: 'user', helper: 'userMapping' },
    updated_by: { type: 'user', helper: 'userMapping' },
    creator_id: { type: 'user', helper: 'userMapping' },
    updater_id: { type: 'user', helper: 'userMapping' },

    // 状态字段
    status: { type: 'status', helper: 'status' },
    state: { type: 'status', helper: 'status' },
    valid: { type: 'boolean', helper: 'boolean' },
    is_active: { type: 'boolean', helper: 'boolean' },
    enabled: { type: 'boolean', helper: 'boolean' },

    // 时间字段
    created_at: { type: 'datetime', helper: 'dateOnly' },
    updated_at: { type: 'datetime', helper: 'relativeTime' },
    deleted_at: { type: 'datetime', helper: 'dateOnly' },

    // 邮箱和电话
    email: { type: 'email', helper: 'emailLink' },
    phone: { type: 'phone', helper: 'phoneLink' },
    mobile: { type: 'phone', helper: 'phoneLink' },

    // 价格和金额字段（优化：支持更多业务字段）
    price: { type: 'currency', helper: 'currency' },
    amount: { type: 'currency', helper: 'currency' },
    cost: { type: 'currency', helper: 'currency' },
    quota: { type: 'currency', helper: 'currency' },
    total_amount: { type: 'currency', helper: 'currency' },
    tax_amount: { type: 'currency', helper: 'currency' },
    discount_amount: { type: 'currency', helper: 'currency' },
    subtotal: { type: 'currency', helper: 'currency' },
    grand_total: { type: 'currency', helper: 'currency' },

    // 税率和折扣率字段
    tax_rate: { type: 'percentage', helper: 'percentage' },
    discount_rate: { type: 'percentage', helper: 'percentage' },
    rate: { type: 'percentage', helper: 'percentage' },

    // 订单编号和其他业务编号
    order_no: { type: 'code', helper: 'text' },
    order_number: { type: 'code', helper: 'text' },
    invoice_no: { type: 'code', helper: 'text' },
    contract_no: { type: 'code', helper: 'text' },
    serial_no: { type: 'code', helper: 'text' },

    // 客户和供应商名称
    customer_name: { type: 'name', helper: 'text' },
    supplier_name: { type: 'name', helper: 'text' },
    vendor_name: { type: 'name', helper: 'text' },

    // 版本号和租户
    version: { type: 'number', helper: 'number' },
    tenant_id: { type: 'number', helper: 'number' },
};

// 获取字段类型配置
function getFieldTypeConfig(field) {
    const fieldName = field.name?.toLowerCase();
    const fieldType = field.type?.toLowerCase();

    // 先检查特殊字段名
    if (fieldName && SPECIAL_FIELD_MAPPING[fieldName]) {
        return SPECIAL_FIELD_MAPPING[fieldName];
    }

    // 检查关系字段
    if (field.relation_info) {
        const direction = field.relation_info.direction;
        if (
            direction === 'RelationshipDirection.MANYTOONE' ||
            direction === 'MANYTOONE'
        ) {
            return { type: 'relation', helper: 'manyToOne' };
        } else if (
            direction === 'RelationshipDirection.ONETOMANY' ||
            direction === 'ONETOMANY'
        ) {
            return { type: 'relation', helper: 'oneToMany' };
        }
    }

    // 检查枚举字段
    if (field.enum_info && field.enum_info.enum_values) {
        return { type: 'status', helper: 'status' };
    }

    // 根据数据类型映射
    if (fieldType && FIELD_TYPE_MAPPING[fieldType]) {
        return FIELD_TYPE_MAPPING[fieldType];
    }

    // 默认为文本类型
    return { type: 'text', helper: 'text' };
}

// 生成列配置代码
function generateColumnConfig(field, isComposite = false) {
    const config = getFieldTypeConfig(field);
    const fieldName = field.name;
    const title = field.comment || fieldName;

    let columnCode = '';

    switch (config.helper) {
        case 'text':
            columnCode = `column.text('${fieldName}', '${title}')`;
            break;

        case 'number':
            columnCode = `column.number('${fieldName}', '${title}')`;
            break;

        case 'currency':
            columnCode = `column.currency('${fieldName}', '${title}', '¥')`;
            break;

        case 'percentage':
            columnCode = `column.percentage('${fieldName}', '${title}', {
    precision: 2,
    showSymbol: true
  })`;
            break;

        case 'boolean':
            const trueText = field.name.includes('valid') ? '启用' : '是';
            const falseText = field.name.includes('valid') ? '禁用' : '否';
            columnCode = `column.boolean('${fieldName}', '${title}', {
    style: 'switch',
    trueText: '${trueText}',
    falseText: '${falseText}'
  })`;
            break;

        case 'datetime':
            columnCode = `column.datetime('${fieldName}', '${title}')`;
            break;

        case 'dateOnly':
            columnCode = `column.dateOnly('${fieldName}', '${title}')`;
            break;

        case 'relativeTime':
            columnCode = `column.relativeTime('${fieldName}', '${title}')`;
            break;

        case 'userMapping':
            columnCode = `column.userMapping('${fieldName}', '${title}')`;
            break;

        case 'status':
            if (field.enum_info && field.enum_info.enum_values) {
                const enumValues = field.enum_info.enum_values;
                const statusMapCode = Object.entries(enumValues)
                    .map(
                        ([key, value]) =>
                            `      '${key}': { text: '${value}', color: '#10b981' }`
                    )
                    .join(',\n');
                columnCode = `column.status('${fieldName}', '${title}', {
    statusMap: {
${statusMapCode}
    }
  })`;
            } else {
                columnCode = `column.status('${fieldName}', '${title}')`;
            }
            break;

        case 'manyToOne':
            const displayField = field.relation_info?.display_field || 'name';
            columnCode = `column.manyToOne('${fieldName}', '${title}', {
    displayField: '${displayField}',
    clickable: true
  })`;
            break;

        case 'oneToMany':
            columnCode = `column.oneToMany('${fieldName}', '${title}', {
    itemName: '条',
    clickable: true
  })`;
            break;

        case 'emailLink':
            columnCode = `column.emailLink('${fieldName}', '${title}')`;
            break;

        case 'phoneLink':
            columnCode = `column.phoneLink('${fieldName}', '${title}')`;
            break;

        default:
            columnCode = `column.text('${fieldName}', '${title}')`;
    }

    return columnCode;
}

// 生成复合列配置
function generateCompositeColumn(mainField, subFields) {
    const mainTitle = mainField.comment || mainField.name;

    const subsCode = subFields
        .map((field) => {
            const fieldName = field.name;
            const comment = field.comment || fieldName;
            return `      {
        field: '${fieldName}',
        template: '${comment}: \${value}',
        style: { color: '#6b7280', fontSize: '12px' },
        condition: (record) => !!record.${fieldName}
      }`;
        })
        .join(',\n');

    return `  column.composite('${mainField.name}', '${mainTitle}', {
    main: {
      field: '${mainField.name}',
      formatter: (value) => value || '未设置',
      style: { fontWeight: 'bold', fontSize: '14px' }
    },
    subs: [
${subsCode}
    ],
    layout: 'vertical'
  })`;
}

// 新增：智能识别复合列字段
function identifyCompositeFields(fields) {
    const nameFields = [];
    const codeFields = [];
    const compositeGroups = [];

    // 分类 name 和 code 字段
    fields.forEach((field) => {
        const fieldName = field.name.toLowerCase();
        if (
            fieldName === 'name' ||
            fieldName.endsWith('_name') ||
            fieldName.endsWith('name')
        ) {
            nameFields.push(field);
        } else if (
            fieldName === 'code' ||
            fieldName.endsWith('_code') ||
            fieldName.endsWith('code')
        ) {
            codeFields.push(field);
        }
    });

    // 尝试匹配 name 和 code 字段为复合列
    nameFields.forEach((nameField) => {
        const baseName = nameField.name.replace(/_?name$/i, '').toLowerCase();

        // 查找对应的 code 字段
        const matchingCodeField = codeFields.find((codeField) => {
            const codeBaseName = codeField.name
                .replace(/_?code$/i, '')
                .toLowerCase();
            return (
                baseName === codeBaseName ||
                (baseName === '' && codeBaseName === '') || // name 和 code
                baseName.includes(codeBaseName) ||
                codeBaseName.includes(baseName)
            );
        });

        if (matchingCodeField) {
            compositeGroups.push({
                main: nameField,
                subs: [matchingCodeField],
                type: 'name_code',
            });
        }
    });

    return compositeGroups;
}

// 新增：检查字段是否已被复合列使用
function isFieldUsedInComposite(field, compositeGroups) {
    return compositeGroups.some(
        (group) =>
            group.main.name === field.name ||
            group.subs.some((sub) => sub.name === field.name)
    );
}

// 修改：从元数据生成列配置
function generateColumnsFromMetadata(metadata) {
    const fields = metadata.fields || metadata.data?.fields || metadata;

    if (!Array.isArray(fields)) {
        throw new Error('元数据格式不正确：未找到有效的 fields 数组');
    }

    console.log(`📊 解析到 ${fields.length} 个字段`);

    const columns = [];

    // 智能识别复合列
    const compositeGroups = identifyCompositeFields(fields);
    console.log(`🔗 识别到 ${compositeGroups.length} 个复合列组合`);

    // 分类字段
    const systemFields = ['id', 'version', 'tenant_id'];
    const timestampFields = ['created_at', 'updated_at'];
    const userFields = ['created_by', 'updated_by'];

    // 生成 ID 列
    const idField = fields.find((f) => f.name === 'id');
    if (idField) {
        columns.push(`column.id('ID', { width: 80, fixed: 'left' })`);
    }

    // 生成复合列
    compositeGroups.forEach((group) => {
        console.log(
            `📋 生成复合列: ${group.main.name} + ${group.subs.map((s) => s.name).join(', ')}`
        );
        const compositeCode = generateCompositeColumn(group.main, group.subs);
        columns.push(compositeCode);
    });

    // 生成业务字段列（排除系统字段和已用于复合列的字段）
    const businessFields = fields.filter(
        (field) =>
            !systemFields.includes(field.name) &&
            !timestampFields.includes(field.name) &&
            !userFields.includes(field.name) &&
            !isFieldUsedInComposite(field, compositeGroups)
    );

    console.log(`💼 业务字段: ${businessFields.map((f) => f.name).join(', ')}`);

    businessFields.forEach((field) => {
        const columnCode = generateColumnConfig(field);
        columns.push(columnCode);
    });

    // 生成用户字段列
    userFields.forEach((fieldName) => {
        const field = fields.find((f) => f.name === fieldName);
        if (field) {
            const columnCode = generateColumnConfig(field);
            columns.push(columnCode);
        }
    });

    // 生成时间字段列
    timestampFields.forEach((fieldName) => {
        const field = fields.find((f) => f.name === fieldName);
        if (field) {
            const columnCode = generateColumnConfig(field);
            columns.push(columnCode);
        }
    });

    console.log(`✅ 共生成 ${columns.length} 个列配置`);
    return columns;
}

// 从模块名称生成API路径
function generateApiPaths(moduleName) {
    const basePath = `/${moduleName}`;
    return {
        columnsApi: `${basePath}/get_metadata`,
        fetchApi: `${basePath}/query`,
        createApi: `${basePath}/create`,
        updateApi: `${basePath}/update`,
        deleteApi: `${basePath}/delete`,
        detailApi: `${basePath}/get`,
    };
}

// 获取 API 导入语句
function generateApiImports(moduleName) {
    return `import { getMetadata, getList } from '@/api/${moduleName}';`;
}

// 从后端获取元数据
async function fetchMetadata(columnsApiUrl, token) {
    try {
        console.log(`📡 正在获取元数据: ${columnsApiUrl}`);

        // 构建完整的 URL
        const baseUrl =
            process.env.VITE_GLOB_API_URL + '/v1' || 'http://localhost:8000';
        const fullUrl = `${baseUrl}${columnsApiUrl}`;
        console.log('fullUrl:', fullUrl);

        const response = await axios.get(fullUrl, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            timeout: 10000,
        });

        console.log('✅ 元数据获取成功');
        return response.data;
    } catch (error) {
        console.error('❌ 元数据获取失败:', error.message);

        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应内容:', error.response.data);
        }

        throw error;
    }
}

/** 获取token */
async function getToken() {
    const baseUrl =
        process.env.VITE_GLOB_API_URL + '/v1' || 'http://localhost:8000';
    const loginUrl = `${baseUrl}/sys/user/login`;
    const response = await axios.post(loginUrl, {
        username: 'admin',
        password: '123456',
        timeout: 10000,
    });
    return response.data.data.access_token;
}

// 生成 Vue 组件代码
function generateVueComponent(config) {
    const { componentName, columnsApi, fetchApi, columns, apiImports } = config;

    const columnsCode = columns.map((col) => `  ${col}`).join(',\n\n');

    return `<script setup lang="ts">
import {
  VTable,
  useTable,
  useTableDebug,
  createPluginManager,
  PluginPresets
} from '@/components/common/Table';
${apiImports}

// 使用插件化架构
const pluginManager = createPluginManager(PluginPresets.full);
const column = pluginManager.getColumnHelper();

// 定义表格列配置
const columns = [
${columnsCode}
];

// 使用 useTable 配置表格
const [register, tableApi] = useTable({
  fetchApi: async (searchParams: any, pageParams: any, otherParams?: any, reloadParams?: any) => {
    console.log('查询参数:', { searchParams, pageParams, otherParams, reloadParams });
    return getList({ ...searchParams, ...pageParams, ...otherParams, ...reloadParams });
  },
  columnsApi: () => getMetadata(),
  columns: columns,
  height: '100%',
  checkType: 'checkbox',
  showSearch: true,
  showPagination: true,
  toolbarConfig: {
    isReload: true,
    isZoom: true,
    isFull: true,
  },

  // 表格样式配置
  tableStyle: {
    striped: true,
    border: false,
    compact: true,
    headerStyle: 'gray',
    rowHeight: 'medium',
    rounded: 'md',
    shadow: 'sm',
    responsive: true,
    theme: 'light'
  },

  // 启用默认样式
  enableDefaultStyle: true,

  // VxeTable自适应行高配置
  showOverflow: false,

  // 事件配置
  on: {
    'checkbox-change': ({ records }: any) => {
      console.log('选中的行:', records);
    },
    'cell-click': ({ row }: any) => {
      console.log('点击的行:', row);
    },
    'data-loaded': (params: any) => {
      console.log('数据加载完成事件触发:', params);

      if (params.success) {
        console.log(\`✅ 数据加载成功 - 耗时: \${params.loadTime}ms, 数据量: \${params.data?.length || 0}条, 总数: \${params.total}\`);
      } else {
        console.error('❌ 数据加载失败:', params.error);
        console.log(\`⏱️ 失败耗时: \${params.loadTime}ms\`);
      }
    }
  }
});

// 调试工具
const debug = useTableDebug('${componentName.toLowerCase()}-table');
debug.recordRenderTime(Date.now());

const demoActions = {
  showTableInfo() {
    console.log('表格列配置:', columns);
    console.log('表格API:', tableApi);
    alert('请查看控制台输出的表格配置信息');
  },

  reloadTable() {
    tableApi.reload({});
  },

  getSelectedRows() {
    const selected = tableApi.getSelectedRows();
    console.log('选中的行:', selected);
  },

  exportData() {
    const data = tableApi.getTableData().tableData;
    console.log('导出数据:', data);
    alert(\`准备导出 \${data.length} 条数据\`);
  }
};
</script>

<template>
  <VTable @register="register" />
</template>

<style scoped>
/* 自定义样式可以在这里添加 */
</style>
`;
}

// 主函数
async function main() {
    console.log('🚀 欢迎使用表格代码生成器!');
    console.log('═'.repeat(50));

    try {
        // 检查命令行参数
        const args = process.argv.slice(2);

        if (args.includes('--help') || args.includes('-h')) {
            showHelp();
            rl.close();
            return;
        }

        // 交互式获取用户输入
        console.log('📋 请提供以下信息来生成表格代码：\n');

        const moduleName = await question(
            '1️⃣  请输入模块名称 (例: sys/user, sal/customer): '
        );
        if (!moduleName.trim()) {
            console.log('❌ 模块名称不能为空');
            rl.close();
            return;
        }

        const modelName = await question(
            '2️⃣  请输入模型名称 (例: User, Customer): '
        );
        if (!modelName.trim()) {
            console.log('❌ 模型名称不能为空');
            rl.close();
            return;
        }
        const token = await getToken();

        if (!token.trim()) {
            console.log('❌ TOKEN 不能为空');
            rl.close();
            return;
        }

        const outputPath =
            (await question(
                `4️⃣  请输入输出文件路径 (例: src/views/${moduleName}/${modelName}Table.vue): `
            )) || `src/views/${moduleName}/${modelName}Table.vue`;

        console.log('\n🔄 开始生成代码...');
        console.log('═'.repeat(50));

        // 获取元数据
        const metadata = await fetchMetadata(
            generateApiPaths(moduleName).columnsApi,
            token
        );

        // 生成列配置
        console.log('🔧 正在解析元数据并生成列配置...');
        const columns = generateColumnsFromMetadata(metadata);

        // 生成 API 导入
        console.log('📦 正在生成 API 导入语句...');
        const apiImports = generateApiImports(moduleName);

        // 生成完整组件代码
        console.log('🎨 正在生成 Vue 组件代码...');
        const componentCode = generateVueComponent({
            componentName: `${modelName}Table`,
            columnsApi: 'getMetadata',
            fetchApi: 'getList',
            columns,
            apiImports,
        });

        // 输出代码
        console.log('\n✨ 代码生成完成!');
        console.log('═'.repeat(50));
        console.log(`📝 生成的组件代码:\n`);
        console.log('```vue');
        console.log(componentCode);
        console.log('```\n');

        // 保存到文件 (可选)
        const saveFile = await question('💾 是否要保存到文件? (y/n): ');
        if (
            saveFile.toLowerCase() === 'y' ||
            saveFile.toLowerCase() === 'yes'
        ) {
            try {
                const { writeFile, mkdir } = await import('fs/promises');
                const { dirname } = await import('path');

                // 确保目录存在
                await mkdir(dirname(outputPath), { recursive: true });

                // 写入文件
                await writeFile(outputPath, componentCode, 'utf8');
                console.log(`✅ 文件已保存到: ${outputPath}`);
            } catch (error) {
                console.error('❌ 保存文件失败:', error.message);
            }
        }

        console.log('\n🎉 表格代码生成完成!');
        console.log('📄 您可以直接复制上面的代码到 Vue 文件中使用');
        console.log('🔧 如需调整样式或功能，请参考 Table 组件文档');
    } catch (error) {
        console.error('\n❌ 生成过程中出现错误:');
        console.error(error.message);

        if (error.code === 'ECONNREFUSED') {
            console.error(
                '\n💡 提示: 请检查后端服务是否启动，以及 VITE_GLOB_API_URL 环境变量是否正确设置'
            );
        }
    } finally {
        rl.close();
    }
}

// 执行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}
main();
export {
    main,
    generateVueComponent,
    generateColumnsFromMetadata,
    fetchMetadata,
    generateApiImports,
    generateApiPaths,
};
