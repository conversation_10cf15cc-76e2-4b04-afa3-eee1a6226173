#!/usr/bin/env node

/**
 * API 代码生成器 CLI 工具
 * 用于根据后端元数据接口自动生成 TypeScript API 请求代码和数据类型定义
 */

import { readFile } from 'fs/promises';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createInterface } from 'readline';
import axios from 'axios';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 命令行接口
const rl = createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: true,
});

// 获取用户输入的辅助函数
function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, (answer) => {
            resolve(answer.trim());
        });
    });
}

// 显示帮助信息
function showHelp() {
    console.log(`
🚀 API 代码生成器 CLI 工具

用法:
  npm run generate:api [options]

选项:
  --help, -h     显示帮助信息
  --interactive  交互式配置（默认）

示例:
  npm run generate:api
  npm run generate:api --interactive

交互式配置将引导您输入以下参数：
  1. 模块名称 (例: sys/user, sal/customer)
  2. 模型名称 (例: User, Customer)
  3. 认证 TOKEN (Bearer token)
  4. 输出目录（可选）

API 路径将自动按照标准 CRUD 格式生成：
  - 查询列表: POST /{module}/query
  - 获取详情: GET /{module}/get/{id}
  - 创建记录: POST /{module}/create
  - 更新记录: PUT /{module}/update
  - 删除记录: DELETE /{module}/delete/{id}
  - 获取元数据: GET /{module}/get_metadata

🎯 枚举类型生成：
  - 直接从后端元数据 (get_metadata) 中提取枚举定义
  - 支持 enum_info.enum_class 作为枚举类型名
  - 支持 enum_info.enum_values 的键值对结构
  - 自动识别 type="enum" 的字段
  - 生成标准的 TypeScript 枚举代码

生成内容包括：
  ✅ TypeScript 数据接口定义
  ✅ 完整的 CRUD API 请求函数
  ✅ 请求参数类型定义
  ✅ 响应数据类型定义
  ✅ 从元数据生成的枚举类型
`);
}

// TypeScript 类型映射配置
const TS_TYPE_MAPPING = {
    // 基础数据类型
    string: 'string',
    varchar: 'string',
    char: 'string',
    text: 'string',

    // 数字类型
    integer: 'number',
    int: 'number',
    bigint: 'number',
    float: 'number',
    decimal: 'number',
    numeric: 'number',
    money: 'number',

    // 布尔类型
    boolean: 'boolean',
    bool: 'boolean',

    // 日期时间类型
    datetime: 'string',
    timestamp: 'string',
    date: 'string',
    time: 'string',

    // 特殊类型
    enum: 'string',
    json: 'Record<string, any>',
    uuid: 'string',
    relation: 'any', // 关系字段需要特殊处理
};

// 获取 TypeScript 类型 - 基础版本，不考虑 Swagger 枚举
function getBasicTypeScriptType(field) {
    const fieldType = field.type?.toLowerCase();

    // 1. 处理后端元数据中明确标记的枚举类型
    if (field.enum_info && field.enum_info.enum_values) {
        const enumValues = Object.keys(field.enum_info.enum_values);
        return {
            type: enumValues.map((value) => `'${value}'`).join(' | '),
            isEnum: true,
            enumInfo: field.enum_info,
        };
    }

    // 2. 处理关系字段
    if (field.relation_info) {
        const relatedModel = field.relation_info.related_model;
        const direction = field.relation_info.direction;

        if (
            direction === 'RelationshipDirection.ONETOMANY' ||
            direction === 'ONETOMANY'
        ) {
            return { type: `${relatedModel}[]`, isEnum: false };
        } else if (
            direction === 'RelationshipDirection.MANYTOONE' ||
            direction === 'MANYTOONE'
        ) {
            return { type: `${relatedModel}`, isEnum: false };
        }
    }

    // 3. 根据字段类型映射基础类型
    if (fieldType && TS_TYPE_MAPPING[fieldType]) {
        return { type: TS_TYPE_MAPPING[fieldType], isEnum: false };
    }

    // 4. 默认为 any 类型
    return { type: 'any', isEnum: false };
}

// 从元数据中直接提取枚举定义
function extractEnumsFromMetadata(metadata) {
    const fields = metadata.fields || metadata.data?.fields || metadata;
    const enums = {};

    if (!Array.isArray(fields)) {
        console.log('⚠️ 元数据格式错误，无法提取枚举信息');
        return enums;
    }

    fields.forEach((field) => {
        // 检查是否为枚举类型且包含枚举信息
        if (
            field.type === 'enum' &&
            field.enum_info &&
            field.enum_info.enum_class &&
            field.enum_info.enum_values
        ) {
            const enumClass = field.enum_info.enum_class;
            const enumValues = field.enum_info.enum_values;

            console.log(
                `🎯 发现枚举字段: ${field.name} → ${enumClass} (${field.comment || '无描述'})`
            );
            console.log(`   枚举值: ${Object.keys(enumValues).join(', ')}`);

            enums[enumClass] = {
                values: enumValues, // 直接使用键值对对象
                description: field.comment || `${enumClass} 枚举`,
                relatedField: field.name,
                fieldName: field.name,
            };
        }
    });

    const enumCount = Object.keys(enums).length;
    console.log(
        `🔍 当前模型 "${metadata.name || 'Unknown'}" 中提取到 ${enumCount} 个枚举类型`
    );

    if (enumCount === 0) {
        console.log('📋 当前模型无枚举字段');
    } else {
        console.log('📋 枚举类型列表:', Object.keys(enums).join(', '));
    }

    return enums;
}

// 生成枚举类型定义 - 支持键值对格式
function generateEnumTypes(enums) {
    if (!enums || Object.keys(enums).length === 0) {
        return '';
    }

    const enumDefinitions = Object.entries(enums).map(
        ([enumName, enumData]) => {
            let values;

            if (
                typeof enumData.values === 'object' &&
                !Array.isArray(enumData.values)
            ) {
                // 处理键值对格式 {"KEY": "显示值"}
                values = Object.entries(enumData.values)
                    .map(([key, value]) => {
                        return `  ${key} = '${value}'`;
                    })
                    .join(',\n');
            } else if (Array.isArray(enumData.values)) {
                // 处理数组格式 ["value1", "value2"]
                values = enumData.values
                    .map((value) => {
                        if (typeof value === 'string') {
                            let key = value
                                .toUpperCase()
                                .replace(/[^\w\u4e00-\u9fff]/g, '_')
                                .replace(/^_+|_+$/g, '')
                                .replace(/_+/g, '_');

                            if (!key || key === '_') {
                                key = `VAL_${value.replace(/[^\w]/g, '_')}`;
                            }

                            if (/^\d/.test(key)) {
                                key = `VAL_${key}`;
                            }

                            return `  ${key} = '${value}'`;
                        } else {
                            return `  VALUE_${value} = ${JSON.stringify(value)}`;
                        }
                    })
                    .join(',\n');
            } else {
                values = '  // 枚举值格式错误';
            }

            return `/**
 * ${enumData.description}
 */
export enum ${enumName} {
${values}
}`;
        }
    );

    return enumDefinitions.join('\n\n');
}

// 获取最终的 TypeScript 类型（使用提取的枚举）
function getFinalTypeScriptType(field, extractedEnums) {
    const basicTypeInfo = getBasicTypeScriptType(field);

    // 如果不是枚举字段，直接返回基础类型
    if (!basicTypeInfo.isEnum || field.type !== 'enum') {
        return basicTypeInfo.type;
    }

    // 如果是枚举字段，使用对应的枚举类型
    if (field.enum_info && field.enum_info.enum_class) {
        const enumClass = field.enum_info.enum_class;
        if (extractedEnums && extractedEnums[enumClass]) {
            console.log(`✅ 使用枚举类型: ${field.name} → ${enumClass}`);
            return enumClass;
        }
    }

    // 如果没有找到对应的枚举类，使用后端元数据的联合类型
    console.log(`⚠️ 未找到枚举类 ${field.enum_info?.enum_class}，使用联合类型`);
    return basicTypeInfo.type;
}

// 生成 TypeScript 接口定义
function generateTypeScriptInterface(
    metadata,
    modelName,
    extractedEnums = null
) {
    const fields = metadata.fields || metadata.data?.fields || metadata;

    if (!Array.isArray(fields)) {
        throw new Error('元数据格式不正确：未找到有效的 fields 数组');
    }

    const interfaceFields = fields
        .map((field) => {
            const fieldName = field.name;
            const fieldType = getFinalTypeScriptType(field, extractedEnums);
            const isOptional = field.nullable ? '?' : '';
            const comment = field.comment ? ` // ${field.comment}` : '';

            return `  ${fieldName}${isOptional}: ${fieldType};${comment}`;
        })
        .join('\n');

    return `// ${modelName} 数据接口定义
export interface ${modelName} extends ModelBase {
${interfaceFields}
}

export interface ${modelName}Create extends Omit<${modelName}, 'id'> {

}

export interface ${modelName}Update extends ${modelName} {

}`;
}

// 生成响应数据接口
function generateResponseInterface(modelName) {
    return `// API 响应接口定义
`;
}

// 生成 API 请求函数
function generateApiFunctions(modelName, apiPath) {
    const _apiPath = `/v1${apiPath}`;
    const camelModelName =
        modelName.charAt(0).toLowerCase() + modelName.slice(1);

    return `import alovaInstance from '@/api';
import type { ModelBase, ResponseListModel } from '@/types/core';
import type { QueryParams } from '@/types/api/queryParams';

import type {
  ${modelName},
  ${modelName}Create,
  ${modelName}Update,
} from './types';

/**
 * @description 获取${modelName}元数据
 * @returns {Promise<any>} 返回元数据信息
 */
const get${modelName}Metadata = () => {
  return alovaInstance.Get<any>('${_apiPath}/get_metadata');
};

/**
 * @description 获取${modelName}列表
 * @param {${modelName}QueryParams} params 查询参数
 * @returns {Promise<ResponseListModel<${modelName}>>} 返回包含${modelName}信息的Promise对象
 * @example
 * // 使用示例
 * const ${camelModelName}List = await get${modelName}List({ start: 1, limit: 20 });
 */
const get${modelName}List = (params: QueryParams) => {
  return alovaInstance.Post<ResponseListModel<${modelName}>>('${_apiPath}/query', params);
};

/**
 * @description 获取${modelName}详情
 * @param {number} id ${modelName} ID
 * @returns {Promise<${modelName}>} 返回${modelName}详情信息
 */
const get${modelName} = (id?: number) => {
  const url = id ? \`${_apiPath}/get/\${id}\` : '${_apiPath}/get';
  return alovaInstance.Get<${modelName}>(url);
};

/**
 * @description 创建${modelName}
 * @param {${modelName}Create} data 创建数据
 * @returns {Promise<${modelName}>} 返回创建的${modelName}信息
 */
const create${modelName} = (data: ${modelName}Create) => {
  return alovaInstance.Post<${modelName}>('${_apiPath}/create', data);
};

/**
 * @description 更新${modelName}
 * @param {${modelName}Update} data 更新数据
 * @returns {Promise<${modelName}>} 返回更新后的${modelName}信息
 */
const update${modelName} = (data: ${modelName}Update) => {
  return alovaInstance.Put<${modelName}>('${_apiPath}/update', data);
};

/**
 * @description 删除${modelName}
 * @param {number} id ${modelName} ID
 * @returns {Promise<any>} 返回删除结果
 */
const remove${modelName} = (id: number) => {
  return alovaInstance.Delete<any>(\`${_apiPath}/delete/\${id}\`);
};

/**
 * @description 批量删除${modelName}
 * @param {number[]} ids  ID数组
 * @returns {Promise<any>} 返回批量删除结果
 */
const bulkDelete${modelName} = (ids: number[]) => {
  return alovaInstance.Delete<any>('${apiPath}/bulk_delete', ids);
};

// /**
//  * @description 导出${modelName}数据
//  * @param {${modelName}QueryParams} params 查询参数
//  * @returns {Promise<Blob>} 返回导出文件
//  */
// const export${modelName} = (params?: ${modelName}QueryParams) => {
//   return alovaInstance.Post<Blob>('${_apiPath}/export', params, {
//     responseType: 'blob'
//   });
// };

// /**
//  * @description 导入${modelName}数据
//  * @param {File} file 导入文件
//  * @returns {Promise<any>} 返回导入结果
//  */
// const import${modelName} = (file: File) => {
//   const formData = new FormData();
//   formData.append('file', file);

//   return alovaInstance.Post<any>('${_apiPath}/import', formData, {
//     headers: {
//       'Content-Type': 'multipart/form-data',
//     },
//   });
// };

export {
  get${modelName}Metadata,
  get${modelName}List,
  get${modelName},
  create${modelName},
  update${modelName},
  remove${modelName},
  bulkDelete${modelName},
  // export${modelName},
  // import${modelName},
};`;
}

// 生成完整的 API 代码文件
async function generateCompleteApiCode(config) {
    const { modelName, apiPath, metadata } = config;

    // 1. 首先提取枚举定义
    const extractedEnums = extractEnumsFromMetadata(metadata);

    // 2. 生成各个部分
    const apiImports = `import type { ModelBase } from "@/types/core"`;
    const enumsCode = generateEnumTypes(extractedEnums);
    const interfaceCode = generateTypeScriptInterface(
        metadata,
        modelName,
        extractedEnums
    );
    const responseCode = generateResponseInterface(modelName);
    const apiCode = generateApiFunctions(modelName, apiPath);

    // 生成类型文件
    const typesCode =
        `${apiImports}` +
        '\n\n' +
        `${enumsCode ? enumsCode + '\n\n' : ''}${interfaceCode}

${responseCode}`;

    // 生成 API 文件
    const apiFileCode = `${apiCode}`;

    return {
        typesCode,
        apiFileCode,
        enumsCount: Object.keys(extractedEnums).length,
        identifiedEnumsCount: Object.keys(extractedEnums).length,
    };
}

// 从后端获取元数据
async function fetchMetadata(columnsApiUrl, token) {
    try {
        // 构建完整的 URL
        const baseUrl =
            process.env.VITE_GLOB_API_URL + '/v1' || 'http://localhost:8000';
        const fullUrl = `${baseUrl}${columnsApiUrl}`;

        console.log(`📡 正在获取元数据: ${fullUrl}`);
        const response = await axios.get(fullUrl, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            timeout: 10000,
        });

        console.log('✅ 元数据获取成功');
        return response.data;
    } catch (error) {
        console.error('❌ 元数据获取失败:', error.message);

        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应内容:', error.response.data);
        }

        throw error;
    }
}

/** 获取token */
async function getToken() {
    const baseUrl =
        process.env.VITE_GLOB_API_URL + '/v1' || 'http://localhost:8000';
    const loginUrl = `${baseUrl}/sys/user/login`;
    const response = await axios.post(loginUrl, {
        username: 'admin',
        password: '123456',
        timeout: 10000,
    });
    return response.data.data.access_token;
}

// 从模块名称生成API路径
function generateApiPaths(moduleName) {
    const basePath = `/${moduleName}`;
    return {
        basePath,
        metadata: `${basePath}/get_metadata`,
        query: `${basePath}/query`,
        create: `${basePath}/create`,
        update: `${basePath}/update`,
        delete: `${basePath}/delete`,
        get: `${basePath}/get`,
    };
}

// 从 API 路径推导模块信息 - 已简化，现在直接使用传入的参数
function deriveModuleInfo(moduleName, modelName) {
    const apiPaths = generateApiPaths(moduleName);

    return {
        moduleName,
        modelName,
        apiPath: apiPaths.basePath,
    };
}

// 主函数
async function main() {
    console.log('🚀 欢迎使用 API 代码生成器!');
    console.log('═'.repeat(50));

    try {
        // 检查命令行参数
        const args = process.argv.slice(2);

        if (args.includes('--help') || args.includes('-h')) {
            showHelp();
            rl.close();
            return;
        }

        // 交互式获取用户输入
        console.log('📋 请提供以下信息来生成 API 代码：\n');

        const moduleName = await question(
            '1️⃣  请输入模块名称 (例: sys/user, sal/customer): '
        );
        if (!moduleName.trim()) {
            console.log('❌ 模块名称不能为空');
            rl.close();
            return;
        }

        const modelName = await question(
            '2️⃣  请输入模型名称 (例: User, Customer): '
        );
        if (!modelName.trim()) {
            console.log('❌ 模型名称不能为空');
            rl.close();
            return;
        }

        const token = await getToken();

        if (!token.trim()) {
            console.log('❌ TOKEN 不能为空');
            rl.close();
            return;
        }

        // 自动推导模块信息
        const moduleInfo = deriveModuleInfo(moduleName, modelName);
        console.log(`\n🔍 自动识别模块信息:`);
        console.log(`   模块名称: ${moduleInfo.moduleName}`);
        console.log(`   模型名称: ${moduleInfo.modelName}`);
        console.log(`   API 路径: ${moduleInfo.apiPath}`);

        const customModelName =
            (await question(
                `4️⃣  确认模型名称 (默认: ${moduleInfo.modelName}): `
            )) || moduleInfo.modelName;
        const outputDir =
            (await question(
                `5️⃣  请输入输出目录 (例: src/api/${moduleInfo.moduleName}): `
            )) || `src/api/${moduleInfo.moduleName}`;

        console.log('\n🔄 开始生成 API 代码...');
        console.log('═'.repeat(50));

        // 获取元数据
        const metadata = await fetchMetadata(
            generateApiPaths(moduleName).metadata,
            token
        );

        // 生成代码
        console.log('🔧 正在解析元数据并生成 TypeScript 代码...');
        const { typesCode, apiFileCode, enumsCount, identifiedEnumsCount } =
            await generateCompleteApiCode({
                modelName: customModelName,
                apiPath: moduleInfo.apiPath,
                metadata: metadata.data || metadata,
            });

        // 输出代码
        console.log('\n✨ API 代码生成完成!');
        if (identifiedEnumsCount > 0) {
            console.log(`🔍 识别到 ${identifiedEnumsCount} 个枚举字段`);
            if (enumsCount > 0) {
                console.log(`🎯 成功从元数据提取 ${enumsCount} 个枚举类型`);
            } else {
                console.log(`📋 使用元数据中的枚举定义`);
            }
        } else {
            console.log(`📊 当前模型无枚举字段`);
        }
        console.log('═'.repeat(50));

        console.log(`\n📝 types.ts 文件内容:\n`);
        console.log('```typescript');
        console.log(typesCode);
        console.log('```\n');

        console.log(`\n📝 index.ts 文件内容:\n`);
        console.log('```typescript');
        console.log(apiFileCode);
        console.log('```\n');

        // 保存到文件 (可选)
        const saveFiles = await question('💾 是否要保存到文件? (y/n): ');
        if (
            saveFiles.toLowerCase() === 'y' ||
            saveFiles.toLowerCase() === 'yes'
        ) {
            try {
                const { writeFile, mkdir } = await import('fs/promises');
                const { dirname } = await import('path');

                // 确保目录存在
                await mkdir(outputDir, { recursive: true });

                // 写入类型文件
                const typesPath = `${outputDir}/types.ts`;
                await writeFile(typesPath, typesCode, 'utf8');
                console.log(`✅ 类型文件已保存到: ${typesPath}`);

                // 写入 API 文件
                const apiPath = `${outputDir}/index.ts`;
                await writeFile(apiPath, apiFileCode, 'utf8');
                console.log(`✅ API 文件已保存到: ${apiPath}`);

                // 生成 index.ts 导出文件
                const indexContent = `// ${customModelName} API 模块导出
export * from './types';
export * from './index';`;

                const mainIndexPath = `${outputDir}/main.ts`;
                await writeFile(mainIndexPath, indexContent, 'utf8');
                console.log(`✅ 主导出文件已保存到: ${mainIndexPath}`);
            } catch (error) {
                console.error('❌ 保存文件失败:', error.message);
            }
        }

        console.log('\n🎉 API 代码生成完成!');
        console.log(
            '📄 您可以直接复制上面的代码到对应的 TypeScript 文件中使用'
        );
        console.log('🔧 生成的代码包含完整的 CRUD 操作、类型定义和错误处理');
        console.log('💡 建议配合表格生成器一起使用，实现完整的业务功能');
    } catch (error) {
        console.error('\n❌ 生成过程中出现错误:');
        console.error(error.message);

        if (error.code === 'ECONNREFUSED') {
            console.error(
                '\n💡 提示: 请检查后端服务是否启动，以及 VITE_GLOB_API_URL 环境变量是否正确设置'
            );
        }
    } finally {
        rl.close();
    }
}

// 执行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}
main();
export {
    main,
    generateCompleteApiCode,
    generateTypeScriptInterface,
    generateApiFunctions,
    fetchMetadata,
    deriveModuleInfo,
    extractEnumsFromMetadata,
    generateEnumTypes,
    getBasicTypeScriptType,
    getFinalTypeScriptType,
};

