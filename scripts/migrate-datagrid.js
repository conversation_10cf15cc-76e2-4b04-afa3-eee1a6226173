#!/usr/bin/env node

/**
 * DataGrid 自动化迁移脚本
 * 
 * 这个脚本将帮助您从旧的插件系统迁移到新的简化架构
 */

const fs = require('fs')
const path = require('path')
const { glob } = require('glob')

class DataGridMigrator {
  constructor() {
    this.migrationReport = {
      filesProcessed: 0,
      filesModified: 0,
      errors: [],
      warnings: [],
      changes: []
    }
  }

  /**
   * 运行迁移
   */
  async migrate() {
    console.log('🚀 开始 DataGrid 迁移...\n')

    try {
      // 1. 查找所有相关文件
      const files = await this.findRelevantFiles()
      console.log(`📁 找到 ${files.length} 个相关文件`)

      // 2. 处理每个文件
      for (const file of files) {
        await this.processFile(file)
      }

      // 3. 生成迁移报告
      this.generateReport()

    } catch (error) {
      console.error('❌ 迁移失败:', error.message)
      process.exit(1)
    }
  }

  /**
   * 查找相关文件
   */
  async findRelevantFiles() {
    const patterns = [
      'src/**/*.vue',
      'src/**/*.ts',
      'src/**/*.js'
    ]

    const files = []
    for (const pattern of patterns) {
      const matches = await glob(pattern, { ignore: ['node_modules/**', 'dist/**'] })
      files.push(...matches)
    }

    // 过滤包含 DataGrid 相关代码的文件
    const relevantFiles = []
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf-8')
      if (this.isDataGridRelated(content)) {
        relevantFiles.push(file)
      }
    }

    return relevantFiles
  }

  /**
   * 检查文件是否与 DataGrid 相关
   */
  isDataGridRelated(content) {
    const patterns = [
      /import.*DataGrid.*from.*data-grid/,
      /import.*useDataGrid/,
      /@\/components\/data-grid/,
      /GlobalManager/,
      /ModernPluginManager/,
      /plugins.*renderers/
    ]

    return patterns.some(pattern => pattern.test(content))
  }

  /**
   * 处理单个文件
   */
  async processFile(filePath) {
    this.migrationReport.filesProcessed++
    
    try {
      const originalContent = fs.readFileSync(filePath, 'utf-8')
      let modifiedContent = originalContent

      // 应用各种转换
      modifiedContent = this.updateImports(modifiedContent, filePath)
      modifiedContent = this.updateColumnConfigs(modifiedContent, filePath)
      modifiedContent = this.updateComponentUsage(modifiedContent, filePath)
      modifiedContent = this.updateComposableUsage(modifiedContent, filePath)

      // 如果内容有变化，写入文件
      if (modifiedContent !== originalContent) {
        // 创建备份
        const backupPath = filePath + '.backup'
        fs.writeFileSync(backupPath, originalContent)

        // 写入修改后的内容
        fs.writeFileSync(filePath, modifiedContent)
        
        this.migrationReport.filesModified++
        this.migrationReport.changes.push({
          file: filePath,
          backup: backupPath,
          type: 'modified'
        })

        console.log(`✅ 已迁移: ${filePath}`)
      }

    } catch (error) {
      this.migrationReport.errors.push({
        file: filePath,
        error: error.message
      })
      console.error(`❌ 处理文件失败: ${filePath} - ${error.message}`)
    }
  }

  /**
   * 更新导入语句
   */
  updateImports(content, filePath) {
    const replacements = [
      // 更新 DataGrid 导入
      {
        pattern: /import\s+DataGrid\s+from\s+['"]@\/components\/data-grid\/core\/DataGrid\.vue['"]/g,
        replacement: "import { SimpleDataGrid } from '@/components/data-grid/simple'"
      },
      // 更新 useDataGrid 导入
      {
        pattern: /import\s+\{\s*useDataGrid\s*\}\s+from\s+['"]@\/components\/data-grid\/composables\/useDataGrid['"]/g,
        replacement: "import { useSimpleDataGrid } from '@/components/data-grid/simple'"
      },
      // 更新其他相关导入
      {
        pattern: /import.*from\s+['"]@\/components\/data-grid\/plugins.*['"]/g,
        replacement: "// 插件系统已移除，请使用新的 column API"
      }
    ]

    let result = content
    for (const { pattern, replacement } of replacements) {
      if (pattern.test(result)) {
        result = result.replace(pattern, replacement)
        this.migrationReport.changes.push({
          file: filePath,
          type: 'import_update',
          pattern: pattern.toString(),
          replacement
        })
      }
    }

    return result
  }

  /**
   * 更新列配置
   */
  updateColumnConfigs(content, filePath) {
    // 这里实现列配置的自动转换
    // 由于列配置的复杂性，这里提供基础的转换示例
    
    let result = content

    // 转换基础列配置
    result = result.replace(
      /\{\s*field:\s*['"](\w+)['"]\s*,\s*title:\s*['"]([^'"]+)['"]\s*\}/g,
      "column.basic('$1', '$2')"
    )

    // 添加 column 导入（如果需要）
    if (result !== content && !result.includes('import { column }')) {
      result = result.replace(
        /(import\s+\{[^}]*)\}\s+from\s+['"]@\/components\/data-grid\/simple['"]/,
        '$1, column } from \'@/components/data-grid/simple\''
      )
    }

    return result
  }

  /**
   * 更新组件使用
   */
  updateComponentUsage(content, filePath) {
    let result = content

    // 更新组件名称
    result = result.replace(/<DataGrid\b/g, '<SimpleDataGrid')
    result = result.replace(/<\/DataGrid>/g, '</SimpleDataGrid>')

    // 更新属性绑定
    result = result.replace(
      /:columns="([^"]+)"\s*:data="([^"]+)"/g,
      'v-bind="$1.computed.dataGridProps.value"'
    )

    return result
  }

  /**
   * 更新 Composable 使用
   */
  updateComposableUsage(content, filePath) {
    let result = content

    // 更新 useDataGrid 调用
    result = result.replace(/useDataGrid\(/g, 'useSimpleDataGrid(')

    // 更新状态访问
    result = result.replace(/\.state\./g, '.state.')
    result = result.replace(/\.actions\./g, '.actions.')

    return result
  }

  /**
   * 生成迁移报告
   */
  generateReport() {
    const reportPath = 'migration-report.json'
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        filesProcessed: this.migrationReport.filesProcessed,
        filesModified: this.migrationReport.filesModified,
        errorsCount: this.migrationReport.errors.length,
        warningsCount: this.migrationReport.warnings.length
      },
      details: this.migrationReport
    }

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    console.log('\n📊 迁移完成！')
    console.log(`📁 处理文件: ${report.summary.filesProcessed}`)
    console.log(`✅ 修改文件: ${report.summary.filesModified}`)
    console.log(`❌ 错误数量: ${report.summary.errorsCount}`)
    console.log(`⚠️  警告数量: ${report.summary.warningsCount}`)
    console.log(`📄 详细报告: ${reportPath}`)

    if (report.summary.errorsCount > 0) {
      console.log('\n❌ 发现错误，请检查以下文件:')
      this.migrationReport.errors.forEach(error => {
        console.log(`  - ${error.file}: ${error.error}`)
      })
    }

    if (report.summary.filesModified > 0) {
      console.log('\n💾 已创建备份文件，如需回滚请运行:')
      console.log('  npm run rollback:datagrid')
    }

    console.log('\n📚 下一步:')
    console.log('  1. 检查修改的文件')
    console.log('  2. 运行测试确保功能正常')
    console.log('  3. 手动调整复杂的配置')
    console.log('  4. 删除备份文件')
  }
}

// 回滚功能
class DataGridRollback {
  async rollback() {
    console.log('🔄 开始回滚 DataGrid 迁移...\n')

    try {
      const backupFiles = await glob('**/*.backup', { ignore: ['node_modules/**'] })
      
      if (backupFiles.length === 0) {
        console.log('📁 没有找到备份文件')
        return
      }

      for (const backupFile of backupFiles) {
        const originalFile = backupFile.replace('.backup', '')
        
        if (fs.existsSync(backupFile)) {
          const backupContent = fs.readFileSync(backupFile, 'utf-8')
          fs.writeFileSync(originalFile, backupContent)
          fs.unlinkSync(backupFile)
          console.log(`✅ 已回滚: ${originalFile}`)
        }
      }

      console.log('\n🎉 回滚完成！')

    } catch (error) {
      console.error('❌ 回滚失败:', error.message)
      process.exit(1)
    }
  }
}

// 命令行处理
const command = process.argv[2]

if (command === 'rollback') {
  const rollback = new DataGridRollback()
  rollback.rollback()
} else {
  const migrator = new DataGridMigrator()
  migrator.migrate()
}
