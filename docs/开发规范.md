# 前端开发规范

## 目录

1. [引言](#引言)
2. [项目结构](#项目结构)
3. [编码规范](#编码规范)
    - [HTML规范](#html规范)
    - [CSS/Tailwind规范](#csstailwind规范)
    - [JavaScript/TypeScript规范](#javascripttypescript规范)
    - [Vue3组件规范](#vue3组件规范)
    - [Linting与格式化](#linting与格式化)
4. [Git工作流](#git工作流)
5. [组件与UI库](#组件与ui库)
6. [性能优化](#性能优化)
7. [测试规范](#测试规范)
8. [国际化](#国际化)
9. [错误处理与日志](#错误处理与日志)
10. [构建与部署](#构建与部署)
11. [其他](#其他)
12. [参考资料](#参考资料)

## 引言

### 目的

本规范旨在为我们的ERP系统前端开发团队提供一套统一的开发标准和最佳实践。通过遵循这些规范，我们可以：

- 提高代码质量和可维护性
- 减少bug和技术债务
- 提升团队协作效率
- 确保产品的一致性和稳定性

### 适用范围

本规范适用于基于Vue3、TypeScript、Pinia、Vue Router、Shadcn/Vue UI、Tailwind CSS、Alova.js和VTable等技术栈的前端开发工作。

## 项目结构

### 目录命名规范

- 使用**kebab-case**（短横线连接）命名目录，例如：`user-management`
- 使用有意义的英文名称，避免使用拼音或无意义的缩写
- 单文件目录名使用单数形式，多文件目录名使用复数形式

### 文件命名规范

- **Vue通用组件**：使用**UpperCamelCase**（大驼峰）命名，例如：`UserProfile.vue`
- **普通页面Vue组件**：使用**kebab-case**（小写短横线）命名，例如：`user-profile.vue`
- **JavaScript/TypeScript文件**：使用**kebab-case**（小写短横线）命名，例如：`auth-service.ts`
- **工具类文件**：使用**kebab-case**命名，例如：`date-utils.ts`
- **类型定义文件**：使用**kebab-case**命名，例如：`user-types.ts`
- **hooks文件**：使用**lowerCamelCase**（小驼峰）命名，例如：`useDialog.ts`

### 推荐的项目结构

```
src/
├── assets/              # 静态资源文件，如图片、字体等
├── components/          # 全局通用组件
│   ├── common/          # 基础通用组件（多页面引用）
│   ├── ui/              # shadcn/vue组件
├── directives/          # 自定义指令
├── hooks/               # 自定义Hooks、可组合函数（Composables）
├── layouts/             # 布局组件
├── lib/                 # 工具库集成
├── locales/             # 国际化资源文件
├── router/              # 路由配置
│   ├── index.ts         # 路由入口
│   ├── routes/          # 路由模块
├── store/               # Pinia 状态管理
│   ├── index.ts         # Store入口
│   └── modules/         # Store模块
├── api/                 # API接口定义和请求
│   ├── index.ts         # API入口
│   └── modules          # API模块目录
├── utils/               # 工具函数
├── types/               # TypeScript全局类型定义
├── views/               # 页面视图组件
│   ├── dashboard/       # 一级菜单目录
│   ├── user/            # 一级菜单目录
│   └── system/          # 一级菜单目录
├── App.vue              # 根组件
├── main.ts              # 应用入口
└── env.d.ts             # 环境变量类型定义
```

## 编码规范

### HTML规范

#### 基本原则

- 使用语义化标签，如`<header>`, `<footer>`等
- 确保HTML结构层次清晰，避免过深的嵌套
- 添加必要的WAI-ARIA属性以提高可访问性

#### 代码示例

```html
<!-- 推荐 -->
<article class="card">
    <header class="card-header">
        <h2 class="text-title">标题</h2>
    </header>
    <div class="card-body">
        <p class="text-content">内容</p>
    </div>
    <footer class="card-footer">
        <button type="button" aria-label="保存" class="btn-primary">
            保存
        </button>
    </footer>
</article>
```

### CSS/Tailwind规范

#### 基本原则

- 优先使用Tailwind CSS实现样式
- 对于复杂的自定义样式，使用CSS模块或Scoped CSS
- 遵循Tailwind CSS的响应式设计原则
- 使用项目预设的字体样式类（参见README中的Font Styles部分）

#### 代码示例

```vue
<template>
    <div class="flex flex-col p-4 rounded-lg shadow-md bg-white">
        <h3 class="text-title mb-2">用户信息</h3>
        <p class="text-content mb-4">显示用户基本信息</p>
        <button class="text-action hover:underline">编辑</button>
    </div>
</template>

<style scoped>
/* 仅在Tailwind无法满足需求时使用自定义CSS */
.custom-animation {
    transition: transform 0.3s ease;
}
.custom-animation:hover {
    transform: scale(1.05);
}
</style>
```

### 字体规范

### 中文字体

- **标题统一字体大小和颜色**: 宋体 (SimSun), 14px, Bold, Color: #203251
    - Use the `text-title` class
- **文本统一大小字体颜色**: 宋体 (SimSun), 12px, Normal, Color: #333333
    - Use the `text-content` class
- **操作和状态类字体大小和颜色**: 宋体 (SimSun), 12px, Normal, Color: #3399FF
    - Use the `text-action` class

#### 英文字体

- **标题统一字体大小和颜色**: Arial, 14px, Bold, Color: #203251
    - Use the `text-title-en` class
- **文本统一大小字体颜色**: Arial, 12px, Normal, Color: #333333
    - Use the `text-content-en` class
- **操作和状态类字体大小和颜色**: Arial, 12px, Normal, Color: #3399FF
    - Use the `text-action-en` class

### Examples

```vue
<template>
    <!-- 中文字体 -->
    <h1 class="text-title">这是标题</h1>
    <p class="text-content">这是内容文本</p>
    <span class="text-action">点击查看</span>

    <!-- 英文字体 -->
    <h1 class="text-title-en">This is a title</h1>
    <p class="text-content-en">This is content text</p>
    <span class="text-action-en">Click to view</span>
</template>
```

### JavaScript/TypeScript规范

#### 基本原则

- 使用TypeScript编写所有代码，确保类型安全
- 使用ES6+的特性和语法
- 使用async/await处理异步操作，避免回调地狱
- 保持函数功能单一，避免过长的函数体
- 使用有意义的变量名，避免魔术数字和字符串

#### 命名规范

- **变量和函数**：使用camelCase（小驼峰），如`userProfile`, `getUserData()`
- **常量**：使用UPPER_SNAKE_CASE，如`MAX_RETRY_COUNT`
- **类和接口**：使用PascalCase（大驼峰），如`UserService`, `ApiResponse`
- **类型和枚举**：使用PascalCase，如`UserStatus`, `ApiResponseType`

#### 代码示例

```typescript
// 类型定义
interface User {
    id: number;
    name: string;
    email: string;
    role: UserRole;
    isActive: boolean;
}

enum UserRole {
    Admin = 'admin',
    Editor = 'editor',
    Viewer = 'viewer',
}

// 常量
const MAX_USERS_PER_PAGE = 20;

// 函数
async function fetchUserById(userId: number): Promise<User | null> {
    try {
        const response = await api.get(`/users/${userId}`);
        return response.data;
    } catch (error) {
        console.error('Failed to fetch user:', error);
        return null;
    }
}

// 使用类型保护
function isAdmin(user: User): boolean {
    return user.role === UserRole.Admin;
}
```

### Vue3组件规范

#### 基本原则

- 使用Composition API和`<script setup>`语法
- 组件应职责单一，专注于解决一个特定问题
- 使用props进行组件通信，避免复杂的事件链
- 组件应自包含，减少对全局状态的依赖
- 使用TypeScript定义props类型和组件返回类型

#### 文件结构

```vue
<script setup lang="ts">
// 导入
import { ref, computed, onMounted } from 'vue';
import type { PropType } from 'vue';
import { useUserStore } from '@/store/user';

// 类型定义
interface TableColumn {
    key: string;
    title: string;
    width?: number;
}

// Props定义
const props = defineProps({
    columns: {
        type: Array as PropType<TableColumn[]>,
        required: true,
    },
    loading: {
        type: Boolean,
        default: false,
    },
});

// Emits定义
const emit = defineEmits<{
    (e: 'update', value: any): void;
    (e: 'delete', id: number): void;
}>();

// 状态和逻辑
const userStore = useUserStore();
const tableData = ref([]);

// 计算属性
const hasData = computed(() => tableData.value.length > 0);

// 方法
function handleDelete(id: number) {
    emit('delete', id);
}

// 生命周期钩子
onMounted(async () => {
    // 初始化逻辑
});
</script>

<template>
    <div class="data-table">
        <!-- 组件模板 -->
    </div>
</template>

<style scoped>
/* 仅在Tailwind无法满足需求时使用自定义CSS */
</style>
```

#### Pinia状态管理

- 按业务领域划分store
- 使用组合式API创建store
- 定义清晰的状态、getter和action
- 使用TypeScript确保类型安全

```typescript
// store/modules/user.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { userApi } from '@/api/modules/user';
import type { User } from '@/types/user';

export const useUserStore = defineStore(
    'user',
    () => {
        // 状态（state）
        const currentUser = ref<User | null>(null);
        const users = ref<User[]>([]);
        const loading = ref(false);

        // Getter（使用 computed）
        const isAdmin = computed(() => currentUser.value?.role === 'admin');
        const userCount = computed(() => users.value.length);

        // Action（普通函数）
        const fetchUsers = async () => {
            loading.value = true;
            try {
                const response = await userApi.getUsers();
                users.value = response.data;
            } catch (error) {
                console.error('Failed to fetch users:', error);
            } finally {
                loading.value = false;
            }
        };

        const login = async (username: string, password: string) => {
            // 登录逻辑
        };

        // 返回 store 的状态、getter 和 action
        return {
            currentUser,
            users,
            loading,
            isAdmin,
            userCount,
            fetchUsers,
            login,
        };
    },
    {
        // 持久化配置
        persist: {
            key: 'user-store',
            storage: localStorage,
            paths: ['currentUser'],
        },
    }
);
```

### Linting与格式化

- 使用ESLint进行代码质量检查
- 使用Prettier进行代码格式化
- 提交前强制进行lint检查
- 配置编辑器自动格式化

#### ESLint配置示例

```javascript
// eslint.config.js
import { defineConfig } from 'eslint/config';
import js from '@eslint/js';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import pluginVue from 'eslint-plugin-vue';
import json from '@eslint/json';

export default defineConfig([
    {
        files: ['**/*.{js,mjs,cjs,ts,vue}'],
        plugins: { js },
        extends: ['js/recommended'],
    },
    {
        files: ['**/*.{js,mjs,cjs,ts,vue}'],
        languageOptions: { globals: globals.browser },
    },
    tseslint.configs.recommended,
    pluginVue.configs['flat/essential'],
    {
        files: ['**/*.vue'],
        languageOptions: { parserOptions: { parser: tseslint.parser } },
    },
    {
        files: ['**/*.json'],
        plugins: { json },
        language: 'json/json',
        extends: ['json/recommended'],
    },
    {
        files: ['**/*.{ts,vue}'],
        rules: {
            '@typescript-eslint/no-explicit-any': 'off',
            // 添加更多自定义规则
        },
    },
]);
```

#### Prettier配置示例

```json
// .prettierrc.json
{
    "semi": true,
    "singleQuote": true,
    "tabWidth": 2,
    "trailingComma": "es5",
    "printWidth": 100,
    "endOfLine": "lf",
    "arrowParens": "always",
    "bracketSpacing": true,
    "vueIndentScriptAndStyle": false
}
```

## Git工作流

### 分支管理

我们采用以下分支策略：

- `main`：主分支，始终包含可部署到生产环境的代码
- `dev`：开发分支，包含最新的功能开发
- `feature/*`：功能分支，用于开发新功能
- `fix/*`：修复分支，用于修复bug
- `release/*`：发布分支，用于准备发布

### 提交信息规范

采用Angular风格的提交信息：

```
<type>(<scope>): <subject>

<body>

<footer>
```

其中：

- **type**：提交类型，包括以下几种：
    - `feat`：新功能
    - `fix`：bug修复
    - `docs`：文档更新
    - `style`：代码格式（不影响功能）
    - `refactor`：代码重构
    - `perf`：性能优化
    - `test`：测试相关
    - `chore`：构建过程或辅助工具的变动
- **scope**：变更范围，如`auth`, `user`, `dashboard`等
- **subject**：简短描述
- **footer**：关闭issue或breaking changes说明

示例：

```
feat(user): 添加用户角色管理功能

- 新增角色分配页面
- 添加角色权限配置界面
- 实现角色切换功能

Closes #123
```

### 代码审查流程

1. 开发者提交Pull Request到dev分支
2. 至少一名团队成员进行代码审查
3. CI/CD流程自动运行测试和构建
4. 解决所有代码审查意见和CI/CD问题
5. 审查者批准PR并合并到dev分支

### 冲突解决

- 定期将dev分支合并到功能分支以减少冲突
- 使用rebase而非merge以保持提交历史清晰
- 对于复杂冲突，团队成员一起讨论解决

## 组件与UI库

### Shadcn UI组件使用规范

- 优先使用Shadcn UI提供的组件，保持界面一致性
- 遵循Shadcn UI的使用模式和最佳实践
- 对Shadcn UI组件进行二次封装时，保持API的一致性
- 使用Tailwind CSS classes进行样式调整，而非直接修改组件源码

### 图标使用规范

- 使用Iconify/Lucide图标库作为项目的主要图标来源
- 保持图标风格统一，避免混用不同风格的图标
- 使用语义化的图标命名和使用方式

```vue
<template>
    <!-- 推荐用法 -->
    <div class="flex items-center">
        <Icon icon="lucide:user" class="mr-2" />
        <span>用户信息</span>
    </div>
</template>
```

### VTable使用规范

- 使用TypeScript定义表格列和数据结构
- 遵循VTable的配置和API规范
- 封装通用的表格组件，以减少重复代码
- 使用懒加载和虚拟滚动处理大数据量表格

```vue
<script setup lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { VTable } from '@visactor/vtable';
import type { ITableOption } from '@visactor/vtable';

const tableOptions = ref<ITableOption>({
    columns: [
        { field: 'id', title: 'ID', width: 80 },
        { field: 'name', title: '名称', width: 150 },
        { field: 'status', title: '状态', width: 100 },
    ],
    records: [],
    widthMode: 'standard',
    heightMode: 'standard',
});

onMounted(() => {
    // 初始化表格
    const tableInstance = new VTable({
        container: document.getElementById('vtable-container'),
        ...tableOptions.value,
    });
});
</script>

<template>
    <div id="vtable-container" style="width: 100%; height: 400px;"></div>
</template>
```

### 自定义组件规范

- 组件应该是可重用的，并且API应清晰明了
- 使用TypeScript定义prop类型和事件
- 组件应该有充分的文档说明
- 对于复杂组件，创建使用示例

## 性能优化

### 基本策略

- 组件懒加载
    ```js
    const UserDashboard = () => import('@/views/user/Dashboard.vue');
    ```
- 代码分割，减少初始加载体积
- 使用keep-alive缓存频繁使用的组件
- 合理使用虚拟列表处理大数据量展示

### Vue特定优化

- 使用`v-memo`减少不必要的组件重渲染
    ```html
    <div v-memo="[item.id, item.updated]">{{ item.name }}</div>
    ```
- 避免在模板中使用复杂的计算，使用计算属性代替
- 使用`shallowRef`和`shallowReactive`处理大型对象
- 合理使用异步组件

### 防抖与节流

- alova.js组件库自带防抖与节流功能

## 测试规范

### 单元测试

【前期要求自测】

- 使用Vitest进行单元测试
- 重点测试业务逻辑和复杂计算
- 使用Vue Test Utils测试组件
- 模拟外部依赖，如API请求

```ts
// 组件测试示例
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import UserList from '@/components/UserList.vue';

describe('UserList.vue', () => {
    it('renders users correctly', async () => {
        const wrapper = mount(UserList, {
            props: {
                users: [
                    { id: 1, name: '张三' },
                    { id: 2, name: '李四' },
                ],
            },
        });

        expect(wrapper.findAll('.user-item')).toHaveLength(2);
        expect(wrapper.text()).toContain('张三');
    });
});
```

### E2E测试

- 使用Cypress进行E2E测试
- 测试关键用户流程
- 根据用户故事编写测试用例
- 建议测试覆盖率达到70%以上

## 国际化

### 实现方式

- 使用vue-i18n管理多语言支持
- 按功能模块组织翻译文件
- 支持中英文切换

### 翻译文件组织

```
src/
└── locales/
    ├── index.ts        # i18n配置
    ├── langs/en-US/             # 英文翻译
    │   ├── common.json # 通用翻译
    │   ├── user.json   # 用户模块翻译
    │   └── ...
    └── langs/zh_US/             # 中文翻译
        ├── common.json
        ├── user.json
        └── ...
```

### 使用示例

```vue
<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n();

function toggleLanguage() {
    locale.value = locale.value === 'zh' ? 'en' : 'zh';
}
</script>

<template>
    <h1>{{ t('user.profile.title') }}</h1>
    <p>{{ t('user.profile.description') }}</p>
    <button @click="toggleLanguage">
        {{ t('common.switchLanguage') }}
    </button>
</template>
```

## 错误处理与日志

### 错误捕获策略

- 使用全局错误处理器捕获未处理的错误
- 在API请求中使用try/catch处理错误
- 使用Vue的errorCaptured钩子捕获组件错误

```ts
// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
    // 记录错误并通知用户
    console.error(err);
    // 上报到错误监控系统
    reportError(err, info);
};
```

### 日志记录

- 定义不同级别的日志：error、warn、info、debug
- 在开发环境显示详细日志，生产环境只记录重要日志
- 考虑使用第三方服务如Sentry进行错误监控

```ts
// 日志服务
const LogService = {
    error(message: string, error?: Error) {
        console.error(message, error);
        // 上报错误
    },

    warn(message: string) {
        console.warn(message);
    },

    info(message: string) {
        if (import.meta.env.DEV) {
            console.info(message);
        }
    },

    debug(message: string) {
        if (import.meta.env.DEV) {
            console.debug(message);
        }
    },
};
```

## 构建与部署

### Vite配置规范

- 针对不同环境使用不同的配置
- 优化构建性能和输出体积
- 使用环境变量管理不同环境的配置

```ts
// vite.config.ts
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd());

    return {
        plugins: [vue()],
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src'),
            },
        },
        build: {
            target: 'es2015',
            outDir: 'dist',
            assetsDir: 'assets',
            minify: 'terser',
            terserOptions: {
                compress: {
                    drop_console: mode === 'production',
                    drop_debugger: mode === 'production',
                },
            },
            rollupOptions: {
                output: {
                    manualChunks: {
                        vendor: ['vue', 'vue-router', 'pinia'],
                        ui: ['shadcn-vue'],
                    },
                },
            },
        },
        server: {
            port: 3000,
            proxy: {
                '/api': {
                    target: env.VITE_API_BASE_URL,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/api/, ''),
                },
            },
        },
    };
});
```

### 环境变量管理

- 使用.env文件管理不同环境的变量
- 敏感信息不应该直接写在环境变量中
- 环境变量命名应加上VITE\_前缀

```
# .env.development
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=10000

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_API_TIMEOUT=5000
```

### 部署流程

1. 运行测试确保功能正常
2. 构建生产版本：`npm run build`
3. 根据需要进行CDN部署或服务器部署
4. 使用CI/CD自动化部署流程

## 其他

### 代码注释规范

- 使用JSDoc风格的注释
- 对公共API和复杂逻辑添加详细注释
- 避免无意义的注释

```ts
/**
 * 根据用户ID获取用户信息
 * @param {number} userId - 用户ID
 * @returns {Promise<User>} 用户信息
 * @throws {ApiError} 当API请求失败时抛出
 */
async function getUserById(userId: number): Promise<User> {
    // 实现逻辑
}
```

### 组件文档

- 使用注释说明组件的用途、props和事件
- 为复杂组件创建使用示例
- 考虑使用Histoire或Storybook进行组件文档化

## 参考资料

- [Vue 3官方文档](https://vuejs.org/guide/introduction.html)
- [TypeScript官方文档](https://www.typescriptlang.org/docs/)
- [Pinia官方文档](https://pinia.vuejs.org/)
- [Vue Router官方文档](https://router.vuejs.org/)
- [Tailwind CSS官方文档](https://tailwindcss.com/docs)
- [ESLint官方文档](https://eslint.org/docs/latest/)
- [Prettier官方文档](https://prettier.io/docs/en/)
- [VTable官方文档](https://visactor.io/vtable)
