Serena 可用命令列表
📁 项目和目录管理
mcp_serena_list_dir - 列出目录内容（支持递归）
mcp_serena_find_file - 根据文件名或通配符查找文件
mcp_serena_activate_project - 激活指定的项目
mcp_serena_remove_project - 从配置中删除项目
🔍 代码搜索和符号查找
mcp_serena_search_for_pattern - 在代码库中搜索任意模式（支持正则表达式）
mcp_serena_get_symbols_overview - 获取文件或目录的符号概览
mcp_serena_find_symbol - 根据名称路径查找代码符号（类、方法等）
mcp_serena_find_referencing_symbols - 查找引用指定符号的其他符号
✏️ 代码编辑
mcp_serena_replace_regex - 使用正则表达式替换文件内容
mcp_serena_replace_symbol_body - 替换符号的主体内容
mcp_serena_insert_after_symbol - 在符号定义后插入内容
mcp_serena_insert_before_symbol - 在符号定义前插入内容
🧠 项目记忆和知识管理
mcp_serena_write_memory - 写入项目相关信息到记忆中
mcp_serena_read_memory - 读取记忆文件内容
mcp_serena_list_memories - 列出所有可用的记忆
mcp_serena_delete_memory - 删除指定的记忆文件
🚀 项目初始化和配置
mcp_serena_get_current_config - 显示当前配置（项目、工具、模式等）
mcp_serena_switch_modes - 切换工作模式
mcp_serena_check_onboarding_performed - 检查是否已完成项目入门
mcp_serena_onboarding - 开始项目入门流程
mcp_serena_initial_instructions - 获取当前项目的初始指令
🤔 思考和分析
mcp_serena_think_about_collected_information - 分析收集的信息是否充分
mcp_serena_think_about_task_adherence - 检查是否偏离任务目标
mcp_serena_think_about_whether_you_are_done - 评估任务是否完成
mcp_serena_summarize_changes - 总结对代码库所做的更改
🛠️ 系统维护
mcp_serena_restart_language_server - 重启语言服务器
mcp_serena_prepare_for_new_conversation - 为新对话做准备
使用建议
开始新项目时：先调用 initial_instructions 获取项目指引
探索代码库：使用 list_dir 和 get_symbols_overview 了解结构
查找代码：优先使用 find_symbol 进行精确查找，search_for_pattern 进行模式搜索
编辑代码：根据需要选择合适的编辑工具
记录重要信息：使用 write_memory 保存项目知识
