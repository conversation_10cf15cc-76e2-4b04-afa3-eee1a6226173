# DataGrid 内存优化和监控指南

## 🔍 发现的问题

在 `DataGridExample.vue` 中发现了内存占用过高（250+M）的问题，主要原因包括：

### 1. PerformanceMonitor 内存泄露

- **定时器泄露**：每1秒执行的内存采样定时器未被清理
- **PerformanceObserver 积累**：长任务监控持续收集数据
- **单例模式问题**：静态实例永不被垃圾回收

### 2. useDataGrid 缓存积累

- **LRU 缓存**：API 和列配置缓存可能积累大量数据
- **Promise 对象**：缓存的 Promise 持有大量闭包引用
- **activeInstances**：实例跟踪集合清理不完整

### 3. 响应式对象过度创建

- **复杂的列配置**：每次创建大量响应式对象
- **重复引用**：btnActions 被多处引用形成复杂依赖

## 🛠️ 优化措施

### 1. 完善组件清理机制

```typescript
onUnmounted(async () => {
  // 1. 取消事件订阅
  if ((dataGridInstance as any)._unsubscribe) {
    ;(dataGridInstance as any)._unsubscribe()
  }

  // 2. 清理性能监控
  performanceMonitor.stopMonitoring('demo/demo')

  // 3. 清理全局资源（最后一个实例时）
  const metricsCount = performanceMonitor.getAllMetrics().size
  if (metricsCount <= 1) {
    performanceMonitor.stopMonitoring() // 清理定时器和Observer
  }

  // 4. 清理缓存
  const { clearCacheForModule } = await import(
    '@/components/data-grid/composables/useDataGrid'
  )
  clearCacheForModule('demo/demo')
})
```

### 2. 优化对象创建

- **btnActions** 移到组件外部，避免重复创建
- **列配置** 使用 `as const` 减少响应式包装
- **直接赋值** 避免 `Object.assign` 触发额外的响应式更新

### 3. 内存监控工具

新增 `memoryMonitor` 工具用于实时监控内存使用：

```typescript
import { memoryMonitor } from '@/utils/memoryMonitor'

// 创建内存快照
memoryMonitor.takeSnapshot('DataGridExample', 'mounted')

// 生成内存报告
memoryMonitor.generateMemoryReport()

// 分析内存趋势
const analysis = memoryMonitor.analyzeMemoryTrend()
```

## 📊 监控和调试

### 开发环境调试

在浏览器控制台中使用：

```javascript
// 查看内存监控数据
window.memoryMonitor.generateMemoryReport()

// 强制垃圾回收（需要在启动时添加 --js-flags="--expose-gc"）
window.memoryMonitor.forceGC()

// 查看内存趋势
window.memoryMonitor.analyzeMemoryTrend()
```

### Chrome DevTools 分析

1. **Memory 面板**：拍摄堆快照，对比前后差异
2. **Performance 面板**：录制内存使用情况
3. **垃圾回收**：在 DevTools 中手动触发 GC

## ⚠️ 警告阈值

- **警告阈值**：200MB
- **严重警告**：500MB
- **自动报告**：内存使用增长超过10%时生成报告

## 🎯 预期效果

经过优化后，内存使用应该：

1. **初始加载**：从 250+M 降至 50-80M
2. **运行期间**：保持相对稳定，无明显增长趋势
3. **组件卸载**：内存能够被正确释放
4. **多次操作**：不会出现内存持续增长

## 📝 检查清单

- [ ] 组件卸载时正确清理事件监听器
- [ ] 停止所有定时器和 Observer
- [ ] 清理缓存和静态引用
- [ ] 避免闭包持有大对象引用
- [ ] 使用内存监控工具跟踪内存使用
- [ ] 定期运行内存分析确认无泄露

## 🔧 进一步优化建议

1. **虚拟滚动**：对于大数据集，使用虚拟滚动减少 DOM 节点
2. **懒加载**：按需加载插件和组件
3. **对象池**：复用频繁创建的对象
4. **WeakMap/WeakSet**：使用弱引用避免内存泄露
5. **定期清理**：设置定时器定期清理过期缓存
