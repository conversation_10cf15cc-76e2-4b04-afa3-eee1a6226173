# 代码清理中发现的向后兼容性问题

## 重复的index.ts文件
每个渲染器目录都有一个index.ts文件，这些文件只是简单地重新导出modern.ts中的内容：
- `src/components/data-grid/plugins/renderers/status/index.ts`
- `src/components/data-grid/plugins/renderers/boolean/index.ts`
- `src/components/data-grid/plugins/renderers/link/index.ts`
- 等等...

这些文件可能是为了向后兼容性而保留的，但现在主入口文件已经直接从modern.ts导入，所以这些index.ts文件是多余的。

## 建议
- 可以考虑删除这些重复的index.ts文件
- 或者更新主入口文件使用这些index.ts文件作为中间层
- 需要确认是否有其他代码依赖这些index.ts文件

## 导出别名
主入口文件使用了别名导出（如 ModernStatusPlugin as StatusPlugin），这保持了API的一致性，但可能会造成混淆。