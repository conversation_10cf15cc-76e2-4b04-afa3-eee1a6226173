@/components/data-grid 组件分析：

1. 目录结构与核心文件：
- types/index.ts：定义所有核心类型，是类型体系基础。
- composables/useDataGrid.ts：核心组合式函数，封装全部业务逻辑。
- constants/index.ts：统一表格默认配置。
- core/：包含主组件、工具栏、搜索、按钮、分页等所有UI和交互核心。
- utils/：封装数据、列、事件、合并、API等通用辅助方法。
- core/DGToolbarSearch/：实现全功能搜索（输入建议、标签、面板、高级搜索、QueryBuilder等）。

2. 主要功能模块与职责：
- DataGrid.vue：主表格组件，负责渲染表格、工具栏、数据加载与响应式布局。
- useDataGrid：组合式核心，负责数据请求、列构建、事件注册、选择管理、刷新等。
- DGToolbar.vue：表格工具栏，集成标题、按钮、操作、分页、搜索等。
- DGToolbarSearch.vue：全功能搜索入口，支持输入建议、标签化、引导面板、高级搜索。
- QueryBuilder.ts：统一的查询条件构建器，支持条件/组的增删改查。
- utils/columnHelpers.ts：自动构建列，支持类型增强、格式化、宽度、对齐等。
- utils/dataHelpers.ts：统一数据请求与结果处理。
- utils/eventHelpers.ts：封装选择相关辅助方法。

3. 关键数据流与交互流程：
- 数据流：useDataGrid 统一管理，数据请求、列配置、事件、选择、刷新等全部响应式绑定到 gridOptions。
- 搜索流：DGToolbarSearch 提供字段建议、标签化、面板引导、高级搜索，所有搜索条件通过 QueryBuilder 统一增删改查，最终同步到 gridOptions.toolbarOptions.queryParams.filters。
- 选择流：selectionHelpers 支持多种选择模式，所有选择状态和事件都可自定义扩展。

4. 组件间关系与可扩展性：
- 高度解耦，核心逻辑通过 composables 与 UI 组件分离，便于复用和扩展。
- 类型安全，所有 props、事件、数据结构均有详细类型定义。
- 可插拔，工具栏、按钮、搜索、分页、列配置等均支持自定义和扩展。
- PRD 文档详细描述所有用户场景、功能需求、数据结构和交互流程。

5. 典型用法与自定义扩展建议：
- 快速集成：useDataGrid + DataGrid.vue 即可实现全功能表格。
- 自定义列/操作/搜索：通过 gridOptions.columns、toolbarOptions、searchPanelConfig 灵活配置。
- 高级搜索与标签化：支持多条件组合、标签可编辑、收藏、快速搜索等。
- 二次开发建议：如需扩展特殊搜索、复杂交互、权限控制等，建议基于 types 和 composables 进行自定义。