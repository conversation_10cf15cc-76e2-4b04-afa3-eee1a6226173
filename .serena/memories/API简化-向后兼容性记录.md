# API简化过程中的向后兼容性记录

## 当前插件管理器创建方法分析

### 现有的多个入口点：
1. **createPluginManager(config?)** - 主要创建方法，支持ExtendedPluginConfig
2. **createPluginManagerSync(config?)** - 已标记为deprecated，建议使用createPluginManager({ sync: true })
3. **getGlobalPluginManager()** - 异步获取全局管理器
4. **getGlobalPluginManagerSync()** - 同步获取全局管理器
5. **PluginFactory.getInstance().createModernManager(options)** - 工厂方法
6. **PluginDevelopmentKit.createEnhancedManager(options)** - 开发工具包方法

### 配置对象复杂性：
- ExtendedPluginConfig有多个可选属性：plugins, enableDefaults, debug, simplePlugins, enableAutoAdapter, sync, simpleOptions
- 缺乏预设配置，用户需要手动配置所有选项
- 没有智能默认值，常用场景配置繁琐

### 需要保持的向后兼容性：
- createPluginManagerSync() 方法（已deprecated但需保持功能）
- ExtendedPluginConfig 接口的所有现有属性
- PluginPresets 对象的现有预设
- 所有现有的导出函数和类型