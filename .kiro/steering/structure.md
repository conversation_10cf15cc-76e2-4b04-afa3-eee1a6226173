# Project Structure & Organization

## Root Directory Structure
```
src/
├── api/              # Backend API service layer, organized by business modules
├── assets/           # Static assets (CSS, images, fonts)
├── components/       # Reusable Vue components
├── core/             # Core configuration and constants
├── directives/       # Custom Vue directives
├── enums/            # TypeScript enums and constants
├── hooks/            # Custom composition functions
├── layouts/          # Application layout components
├── lib/              # Third-party library configurations
├── locales/          # Internationalization files
├── router/           # Vue Router configuration
├── store/            # Pinia state management stores
├── types/            # Global TypeScript type definitions
├── utils/            # Utility functions and helpers
├── views/            # Page components organized by business modules
├── main.ts           # Application entry point
└── App.vue           # Root Vue component
```

## Key Architectural Patterns

### API Organization
- **Centralized API Service**: `src/api/apiService.ts` provides unified API access
- **Module-based Structure**: APIs organized by business domains (sal/, fin/, hr/, wms/, mes/)
- **Standardized Interface**: All APIs follow ModelApi interface pattern
- **Dynamic Loading**: APIs are loaded on-demand with caching

### Component Architecture
- **UI Components**: `src/components/ui/` - Shadcn UI based components
- **Common Components**: `src/components/common/` - Shared business components
- **Data Grid System**: `src/components/data-grid/` - Advanced table functionality
- **Business Components**: Domain-specific components in respective modules

### State Management
- **Store Organization**: Each business domain has dedicated Pinia stores
- **Authentication**: `src/store/auth.ts` handles user authentication
- **Theme Management**: `src/store/theme.ts` manages UI themes
- **Persistence**: Critical state persisted to localStorage

### View Organization
Views are organized by business modules:
- `src/views/workflow/` - Workflow management and configuration
- `src/views/sal/` - Sales-related pages
- `src/views/fin/` - Financial management pages
- `src/views/hr/` - Human resources pages
- `src/views/wms/` - Warehouse management pages
- `src/views/mes/` - Manufacturing execution pages

### Configuration Files
- **Core Config**: `src/core/conf.ts` - Application constants
- **Router Config**: Dynamic route loading with authentication
- **Theme Config**: CSS custom properties with Tailwind integration
- **Build Config**: Vite configuration with optimized bundling

### Naming Conventions
- **Files**: kebab-case for components and utilities
- **Components**: PascalCase for Vue components
- **Functions**: camelCase for functions and methods
- **Constants**: UPPER_SNAKE_CASE for constants
- **CSS Classes**: Tailwind utility classes with custom theme classes

### Import Patterns
- **Absolute Imports**: Use `@/` alias for src directory
- **Barrel Exports**: Index files for clean imports
- **Dynamic Imports**: Lazy loading for routes and large components
- **Type-only Imports**: Use `import type` for TypeScript types