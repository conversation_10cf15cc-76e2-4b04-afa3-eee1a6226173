# Technology Stack & Build System

## Core Framework
- **Vue 3** with Composition API and `<script setup>` syntax
- **TypeScript** for type safety (strict mode disabled for flexibility)
- **Vite** as build tool and development server

## State Management & Routing
- **Pinia** for state management with persistence support
- **Vue Router 4** with dynamic route loading
- **vue-i18n** for internationalization

## UI & Styling
- **Tailwind CSS** as primary CSS framework
- **Shadcn UI** components (Radix Vue based)
- **Element Plus** for additional UI components
- **Lucide Vue Next** for icons
- **VXE Table** for advanced data grids

## Specialized Libraries
- **LogicFlow 2.0** for workflow visualization and design
- **Alova** for HTTP client and API management
- **VueUse** for composition utilities
- **Vee-Validate + Zod** for form validation
- **Day.js** for date manipulation
- **Lodash-es** for utility functions

## Development Tools
- **ESLint** with TypeScript and Vue plugins
- **Prettier** for code formatting
- **Vue DevTools** for debugging
- **PNPM** as package manager

## Common Commands

### Development
```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Type checking
pnpm type:check
```

### Building
```bash
# Production build
pnpm build

# Development build
pnpm build:dev

# Preview production build
pnpm preview
```

### Code Quality
```bash
# Run all linting
pnpm lint

# ESLint only
pnpm lint:eslint

# Prettier formatting
pnpm lint:prettier

# Stylelint for CSS
pnpm lint:stylelint

# Full check (type + lint)
pnpm check
```

### Code Generation
```bash
# Generate table components
pnpm generate:table

# Generate API interfaces
pnpm generate:api
```

## Build Configuration
- **Target**: ESNext with Terser minification
- **Code Splitting**: Disabled manual chunks (commented out)
- **Assets**: Inlined assets under 4KB
- **Source Maps**: Enabled in development only
- **Auto Import**: Element Plus components and Vue APIs